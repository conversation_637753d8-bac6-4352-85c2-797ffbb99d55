#!/bin/bash

my_project=cwsm
go_project_path=/home/<USER>
my_project_path=$go_project_path/src/$my_project


begin(){
    str="      $my_project: Log begin      "
    echo
    echo
    echo "@@@@@@${str//[a-zA-z_ :]/@}@@@@@@"
    echo "@@@@@@${str//[a-zA-z_ :]/ }@@@@@@"
    echo "@@@@@@${str}@@@@@@"
    echo "@@@@@@${str//[a-zA-z_ :]/ }@@@@@@"
    echo
    echo    
    start=$(date +"%Y-%m-%d %H:%M:%S")
    echo "start :" "$start"
    echo
    echo
}

end(){
    echo
    echo
    end=$(date +"%Y-%m-%d %H:%M:%S")
    interval=$(( $(date +%s -d "$end") - $(date +%s -d "$start") ))
    minute=$((interval / 60))
    second=$((interval % 60))
    echo "start :" "$start"
    echo "  end :" "$end"
    echo "total :" "$minute"m"$second"s

    str="      $my_project: Log end        "
    echo
    echo
    echo "@@@@@@${str//[a-zA-z_ :]/ }@@@@@@"
    echo "@@@@@@${str}@@@@@@"
    echo "@@@@@@${str//[a-zA-z_ :]/ }@@@@@@"
    echo "@@@@@@${str//[a-zA-z_ :]/@}@@@@@@"
    echo
    echo
}

make_app(){
    echo -e "cd $my_project_path"
    cd $my_project_path || exit
 
    echo -e "make all\n\n"
    make_start=$(date +%s)
    make all
    cd $go_project_path/src/cwsm/ci
    python lcoverage.py $my_project_path/go_tool_report/gocoverng_log $my_project_path/go_tool_report/gocoverng_log/gocoverng.log
    cp $my_project_path/go_tool_report/gocoverng_log/ut_result.json $my_project_path
    cp $my_project_path/go_tool_report/gocoverng_log/ut_result.json $my_project_path/go_tool_report
    make_end=$(date +%s)
}

main(){
    begin

    make_app

    end
}

main 
