#!/usr/bin/env bash

set -e -x

CURRENT_DIR=$(cd $(dirname "$0")||exit;pwd)
OPERATOR_DIR=$(cd "$CURRENT_DIR"/..||exit ;pwd)

export GOPATH=`pwd`
export GOROOT=/usr/local/go

cd $OPERATOR_DIR

LDFLAGS="-w -s"

STATIC_EXTLDFLAGS="-extldflags '-static -lm -ldl -lpthread'"


export GO111MODULE=on
export GOSUMDB=off
export GOPROXY="https://zxnp_pict-ci:<EMAIL>/artifactory/api/go/zxnp-pict-go-virtual"

go env
if [ "$1" == "amd64" ]; then
    GOARCH=amd64
    OUTPUT_BIN="output/bin/cwsm_director_amd64"    
elif [ "$1" == "arm64" ]; then
    GOARCH=arm64
    OUTPUT_BIN="output/bin/cwsm_director_arm64"
else
    echo "Please specify target architecture: amd64 or arm64"
    exit 1
fi

CGO_ENABLED=0 GOARCH=$GOARCH go build --mod=mod -tags "apparmor netgo osusergo static_build" -installsuffix netgo -ldflags "${LDFLAGS} ${STATIC_EXTLDFLAGS}" -o $OUTPUT_BIN main.go

echo "" > empty_buildinfo
objcopy --update-section .go.buildinfo=empty_buildinfo output/bin/cwsm_director_amd64