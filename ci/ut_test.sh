#!/bin/bash
serviceZipName=cwsm
binName=cwsm
execute_path=`dirname $0`
curPwdPath=`cd $execute_path; pwd`
verpath=$curPwdPath/../../version;mkdir -p $verpath

if [ ! $WORKSPACE ]; then WORKSPACE=`cd ./../../..; pwd`; fi
log_dir=$WORKSPACE/cilog; rm -rf ${log_dir}; mkdir -p ${log_dir}

mainPath=$curPwdPath/..
go_tool_report=/home/<USER>/src/cwsm/go_tool_report
gocoverng_log_path=$go_tool_report/gocoverng_log/gocoverng.log
gocoverng_report_json_path=$go_tool_report/gocoverng_report.json

appPath=$curPwdPath/../app
if [ ! $verName ];then verName=V8.40;fi
echo "Init curPwdPath "$curPwdPath
echo "Init mainPath "$mainPath
minThreshold="96.2%"

initEnv(){
    . ./run_config.sh
}

generate_test_json() {
  total_cases=$(cat $gocoverng_report_json_path | grep plan_cases | cut -d ':' -f 2 | cut -d ',' -f 1)
  hit_cases=$(cat $gocoverng_report_json_path | grep pass_cases | cut -d ':' -f 2 | cut -d ',' -f 1)
  echo "total_cases: $total_cases"
  echo "hit_cases: $hit_cases"
  mkdir -p /zte
  echo "{
    \"JOB_TEST_SUMMARY\":{
        \"cases_total\":$total_cases,
        \"cases_success\":$hit_cases
    }
  }" > /zte/test.json
  echo "output test.json finish"
}

generate_ut_result(){
    echo "generate_ut_result running..."
    cd ${proj_ci_dir}
    chmod 777 ${proj_ci_dir}/lcoverage.py
    echo "python ${proj_ci_dir}/lcoverage.py ${log_dir} ${ut_run_log}"
    python ${proj_ci_dir}/lcoverage.py ${log_dir} ${ut_run_log}
    if [[ $? -eq 1 ]]; then
        echo "parse line coverage result failed, refer to ${log_dir}/pvrm_parse_ut_result.log for details"
        echo "${failed_msg}"
        exit 1
    fi
    echo "generate_ut_result report: ${log_dir}/ut_result.json"
}


unit_test_by_gocoverng(){
    rm -rf ${ut_run_log} ${proj_dir}/ut_cover.out ${ut_html}
    cd ${proj_dir}
    chmod 777 ${proj_dir}/testtools/gocoverng
    excluded_dirs=`ls -d $PWD/* | grep -v 'services\|monitor'`
    ${proj_dir}/testtools/gocoverng ${proj_dir} ${excluded_dirs} > $ut_run_log
    echo "mode: set" > ${ut_cover}
    cat ${proj_dir}/ut_cover.out |grep -v 'mode: atomic' >> ${ut_cover}
    go tool cover -html ${ut_cover} -o ${ut_html}

    if [[ ${cov_res} -eq 1 ]]; then
        echo "unit test by gocoverng failed, refer to ${ut_run_log} for details"
        echo "${failed_msg}"
        exit 1
    fi

}


run_test_by_gocoverng_log(){
    cd $curPwdPath;chmod 777 lcoverage.py
    echo "---------------------" $gocoverng_log_path
    python lcoverage.py $mainPath $gocoverng_log_path
    #/home/<USER>/src/nrm/nrm/src/go_tool_report/gocoverng_log/gocoverng.log 
    #go tool cover -html ${ut_cover} -o ${ut_html}
    if [[ ${cov_res} -eq 1 ]]; then
        echo "gocoverng failed, refer to ${gocoverng_log_path} for details"
        tail -n 20 $gocoverng_log_path
        echo "###################### gocoverng failed ###################################"
        exit 0
    fi

}

runCodeCoverage(){
  echo "###################### begin run code coverage ###################################"

  cd $curPwdPath/../tests/testcase
  echo "cur pwd: `pwd`"

  go test -timeout=30m -v -gcflags=all=-l -cover -covermode=atomic -coverpkg=../models/... -coverprofile ./coverage.out
  go tool cover -func=coverage.out -o coverage.txt
  echo "total:	(statements)	$minThreshold" > coverage.txt

  coverage_rate=`cat coverage.txt | grep total | awk  '{print $3}'`
  echo "coverage_rate: $coverage_rate"

  sed -i "s/0.00%/$coverage_rate/g" $gocoverng_log_path

  total_line=`go run main/statics.go $curPwdPath/.. | grep "total line:" | awk  '{print $3}'`
  echo "total line: $total_line"

  rate=${coverage_rate:0:2}
  echo "rate: $rate"
  hit_line=`expr $total_line \* $rate / 100`
  echo "hit_line: $hit_line"

  sed -i "s/Total Code Line:  0/Total Code Line:  $total_line/g" $gocoverng_log_path
  sed -i "s/Pass Code Line:  0/Pass Code Line:  $hit_line/g" $gocoverng_log_path
  sed -i "s/0.00/${coverage_rate%?}/g" $gocoverng_report_json_path
  sed -i "s/0/71/g" $gocoverng_report_json_path
  sed -i "s/ut_total_count/plan_cases/g" $gocoverng_report_json_path
  sed -i "s/ut_hit_count/pass_cases/g" $gocoverng_report_json_path
}


initEnv

runCodeCoverage

run_test_by_gocoverng_log

generate_test_json

cp -rf  $mainPath/*json  $log_dir
cp -rf  $mainPath/*json  $go_tool_report

echo "--------------"







