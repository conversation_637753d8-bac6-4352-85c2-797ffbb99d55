/zte/SOURCES/cwsm/tools/commontools/configure/configure_dto.go: FTPUser     string `json:"ftpuser"`
/zte/SOURCES/cwsm/tools/commontools/db/dbutil/table-operator/base_sql_table_operator.go: func (b *baseTableOperator) Connect(ip string, port int, user string, pwd string, db string) error {
/zte/SOURCES/cwsm/tools/commontools/db/dbutil/table-operator/base_sql_table_operator.go: conn, err := dbcommon.ConnectDB(b.driver, b.ConnectionString(ip, port, user, pwd, db))
/zte/SOURCES/cwsm/tools/commontools/db/dbutil/db-dialect/sql_db_dialect.go: ConnectionString(ip string, port int, user string, pwd string, db string) string
/zte/SOURCES/cwsm/tools/commontools/db/dbutil/db-dialect/pg_dialect.go: func (p *PgDialect) ConnectionString(ip string, port int, user string, pwd string, db string) string {
/zte/SOURCES/cwsm/tools/commontools/db/dbutil/db-dialect/pg_dialect.go: pwd = nurl.QueryEscape(pwd)
/zte/SOURCES/cwsm/tools/commontools/db/dbutil/db-dialect/pg_dialect.go: url = fmt.Sprintf("postgres://%s:%d/%s?user=%s&password=%s&sslmode=require", ip, port, db, user, pwd)
/zte/SOURCES/cwsm/tools/commontools/db/dbutil/db-operator/base_sql_db_operator.go: func (b *baseDbOperator) Connect(ip string, port int, user string, pwd string) error {
/zte/SOURCES/cwsm/tools/commontools/db/dbutil/db-operator/base_sql_db_operator.go: conn, err := dbcommon.ConnectDB(b.driver, b.ConnectionString(ip, port, user, pwd, ""))
/zte/SOURCES/cwsm/tools/commontools/infa/util/aes/aesapi.go: logger.Errorf("gcmPreprocess decode secret key failed: %v", err)
/zte/SOURCES/cwsm/tools/commontools/cloudtoken/maintainer.go: logger.Errorf("[Create] url: %s ToCloudTokenREQ user %s or pwd is empty.", unir.URL, unir.User)
/zte/SOURCES/cwsm/tools/commontools/cloudtoken/maintainer.go: return fmt.Errorf("user or pwd is empty")
/zte/SOURCES/cwsm/tools/commontools/director/iks/iks_provider.go: pwd, err := aes.AesDecodeBase64EnCode(kmsPassword, globalcv.AesKey, aes.IV)
/zte/SOURCES/cwsm/tools/commontools/director/iks/iks_provider.go: return pwd, nil
/zte/SOURCES/cwsm/tools/commontools/domainservice/cryptodomain/en_de_cryption.go: pwd, err := aes.AesEncode(string(pass), globalcv.AesKey, aes.IV)
/zte/SOURCES/cwsm/tools/commontools/domainservice/cryptodomain/en_de_cryption.go: return pwd
/zte/SOURCES/cwsm/tools/commontools/domainservice/cryptodomain/en_de_cryption.go: pwd, err := aes.AesDecodeBase64EnCode(kmsPassword, globalcv.AesKey, aes.IV)
/zte/SOURCES/cwsm/tools/commontools/domainservice/cryptodomain/en_de_cryption.go: return pwd
/zte/SOURCES/cwsm/tools/commontools/opeanpalette/kms/kms_provider.go: logger.Errorf("PutSecretKeyToCache empty secret key id")
/zte/SOURCES/cwsm/tools/commontools/opeanpalette/kms/kms_provider.go: return nil, errors.New("empty secret key id")
/zte/SOURCES/cwsm/tools/commontools/opeanpalette/kms/kms_provider.go: return fmt.Errorf("put secret key to cache failed: %v", err)
/zte/SOURCES/cwsm/tools/commontools/opeanpalette/kms/kms_provider.go: logger.Infof("[UpdateLatestKey] put secret key %s to cache and set it as latest key", res.KeyID)
/zte/SOURCES/cwsm/tools/commontools/opeanpalette/kms/kms_provider.go: logger.Errorf("RegisterSecretKeyType response secret key type is empty")
/zte/SOURCES/cwsm/tools/commontools/opeanpalette/kms/kms_provider.go: return nil, errors.New("empty secret key type")
