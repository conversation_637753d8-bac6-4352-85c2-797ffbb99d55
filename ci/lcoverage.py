# encoding:utf8
import re
import sys
import os
import json
import time
import logging

reload(sys)
sys.setdefaultencoding('utf8')

dict_var = {
    "summary": {
        "duration": 0,
        "total_cases": 0,
        "hit_cases": 0,
        "total_lines": 0,
        "hit_lines": 0,
        "line_coverage": 0,
        "result": "fail",
        "status": "No Data"
    },
    "testcase": []
}
re_run = re.compile(r'=== RUN(\s*)(.*)')
re_end = re.compile(r'--- (PASS|FAIL|SKIP):(\s*)(.*)(\s*)[(](\d+(\.\d+)?)s[)]')
re_result = re.compile(r'(ok|FAIL)(\s*)(.*?)(\s*)(\d+(\.\d+)?)s')
re_total_line = re.compile(r'Total Code Line:(\s*)([1-9]\d*)')
re_pass_line = re.compile(r'Pass Code Line:(\s*)([1-9]\d*)')
re_coverage = re.compile(r'Total Coverage:(\s*)(\d+(\.\d+)?)%')
re_all_result = re.compile(r'Result:(\s*)(.*)')
re_convey_case = re.compile(r'goconvey--- (Passed|Failed|Skip|Error):(.*)')
re_convey_case_ginkgo = re.compile(r'\[(Passed|Failed)\](\s*)(.*)')

duration = 0
all_test_cases = []
convey_flag = False
final_result = "fail"  # failed?
test_func_name, pkg_name = '', ''
line_cover, total_line_cover, coverage = 0, 0, .0
passed_count, failed_count, success_count = 0, 0, 0


class Logger:
    def __init__(self, logpath):
        self.filename = logpath + "/parse_ut_result.log"
        self.logger = logging.getLogger(self.filename)
        self.logger.setLevel(logging.DEBUG)
        fh = logging.FileHandler(self.filename)
        fh.setFormatter(logging.Formatter('%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s'))
        self.logger.addHandler(fh)

    def getlogger(self):
        return self.logger


class SingleCase(object):
    def __init__(self, case_name, test_result):
        self.testcasename = case_name
        self.result = test_result


def append_test_case(func_name, one_case_name, test_result):
    case_name = func_name if one_case_name == "" else func_name + "." + one_case_name
    if test_result == "Passed":
        global passed_count
        passed_count += 1
        all_test_cases.append(SingleCase(case_name, "Passed"))
    elif test_result == "Failed":
        global failed_count
        failed_count = failed_count + 1
        all_test_cases.append(SingleCase(case_name, "Failed"))

def append_test_case_ginkgo(func_name, one_case_name, test_result):
    case_name = func_name if one_case_name == "" else one_case_name
    if test_result == "Passed":
        global passed_count
        passed_count += 1
        all_test_cases.append(SingleCase(one_case_name, "Passed"))
    elif test_result == "Failed":
        global failed_count
        failed_count = failed_count + 1
        all_test_cases.append(SingleCase(one_case_name, "Failed"))


def dump_json(ci_path):
    res_path = ci_path + "/ut_result.json"
    with open(res_path, 'w') as j:
        json.dump(dict_var, j, default=lambda obj: obj.__dict__, sort_keys=True, indent=4, separators=(',', ': '),
                  ensure_ascii=False)


if __name__ == '__main__':
    times = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
    print('parse line coverage result starting at {}'.format(str(times)))
    if len(sys.argv) < 3:
        logger = Logger(os.getcwd()).getlogger()
        logger.error('please enter log path as argv[1]')
        logger.error('please enter ut file path as argv[2]')
        # error file No Data
        dump_json("")
        sys.exit(1)
    # second para is the log path, third para is ut path
    log_path = sys.argv[1]
    logger = Logger(log_path).getlogger()
    logger.info("ci log path is {}".format(log_path))
    test_log_name = sys.argv[2]
    logger.info("test log file is {}".format(test_log_name))
    if not (os.path.exists(test_log_name) and os.path.isfile(test_log_name)):
        logger.error("{} don't exists".format(test_log_name))
        dump_json(log_path)
        sys.exit(1)

    f = open(test_log_name, 'r')
    lines = f.readlines()
    sep_index = 0
    for line in lines:
        line = line.strip('\n').strip()
        if re_run.match(line):
            convey_flag = False
            run_result = re_run.match(line)
            test_func_name = run_result.group(2)
        elif re_convey_case.match(line):
            convey_case_res = re_convey_case.match(line)
            convey_flag = True
            append_test_case(test_func_name, convey_case_res.group(2), convey_case_res.group(1))
        elif re_convey_case_ginkgo.match(line):
            convey_case_res4Ginkgo = re_convey_case_ginkgo.match(line)
            convey_flag = True
            append_test_case_ginkgo(test_func_name, convey_case_res4Ginkgo.group(3), convey_case_res4Ginkgo.group(1))
        elif re_end.match(line):
            end_result = re_end.match(line)
            if not convey_flag:
                func_result = end_result.group(1)
                append_test_case(test_func_name, "", func_result)
        elif re_result.match(line):
            result = re_result.match(line)
            pkg_name, duration = result.group(3), result.group(5)
            for i in range(sep_index, len(all_test_cases)):
                case_full_name = pkg_name + "." + all_test_cases[i].testcasename
                all_test_cases[i].testcasename = case_full_name
            sep_index = len(all_test_cases)
            logger.info("package {}: test duration is {}, test result is {}".format(pkg_name, duration, final_result))
        elif re_all_result.match(line):
            result = re_all_result.match(line)
            final_result = "fail" if result.group(2) == "FAIL" else result.group(2)
        elif re_total_line.match(line):
            result = re_total_line.match(line)
            total_line_cover = result.group(2)
        elif re_pass_line.match(line):
            result = re_pass_line.match(line)
            line_cover = result.group(2)
        elif re_coverage.match(line):
            result = re_coverage.match(line)
            coverage = result.group(2)
        else:
            pass

    f.close()
    logger.info("total line coverage is {}, line coverage is {}, coverage is {}".format(
        total_line_cover, line_cover, coverage))
    logger.info("passed count is {},failed count is {}".format(passed_count, failed_count))
    times = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
    print('parse line coverage result finished at {}'.format(str(times)))

    if pkg_name == "":
        logger.error("Failed to get package name")
        dump_json(log_path)
        sys.exit(1)
    elif final_result == "fail":
        logger.error("unit test FAILED")
        failed_cases = "\n".join(["    " + case.testcasename for case in all_test_cases if case.result == "Failed"])
        print("\nThe following test cases FAILED:")
        print(failed_cases + "\n")
        dump_json(log_path)
        sys.exit(1)
    else:
        # data
        dict_var["summary"]["duration"] = duration
        dict_var["summary"]["total_cases"] = len(all_test_cases)
        dict_var["summary"]["hit_cases"] = passed_count
        dict_var["summary"]["total_lines"] = total_line_cover
        dict_var["summary"]["hit_lines"] = line_cover
        dict_var["summary"]["line_coverage"] = coverage
        dict_var["summary"]["result"] = final_result
        dict_var["summary"]["status"] = "OK"
        dict_var["testcase"] = all_test_cases

        dump_json(log_path)
        sys.exit(0)
