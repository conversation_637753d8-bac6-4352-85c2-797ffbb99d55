#!/bin/bash
currentPath=`pwd`
all_micro_services=("cwsm")
service_name=cwsm
project_names=${service_name}
src_names=${service_name}
bin_names=${service_name}

max_function_length=75
max_function_deep=7
max_dupl_threshold=52
max_function_cyclo=12
min_ut_cover=81


Set_go_config(){
  export GOROOT=/usr/local/go
  export GOPATH=${currentPath}/../../..
  export GOPRIVATE=""
  export GOSUMDB=off
  unset GO111MODULE
  export GO111MODULE=on
  export GOPROXY=https://zxnp_pict-ci:<EMAIL>/artifactory/api/go/zxnp-pict-go-virtual
}


Get_micro_params(){
    echo "currentPath---"
    echo $currentPath
    micro_service=$1
    micro_project_path=$currentPath/../..
    micro_ci_path=$currentPath
    micro_testtools_path=$currentPath/testtools
    micro_src_path=$currentPath/..
    cd $micro_src_path;
    cwsm_path=$(pwd)
    echo "----------------------" $cwsm_path
    cd -

}

Set_go_config

echo "GOROOT------"$GOROOT
echo "GOPATH------"$GOPATH
echo "WORKSPACE----"$WORKSPACE