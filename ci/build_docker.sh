#!/usr/bin/env bash

set -x
CURRENT_DIR=$(cd $(dirname "$0")||exit;pwd)
WORKSPACE_DIR=$(cd "$CURRENT_DIR"/..||exit;pwd)
WORKSPACE_BUILD_IMAGE="zxnp-pict-release-docker.artsh.zte.com.cn/stage/goimages:1.11.1-go1.22.5"


build_workspace_bin(){
    echo "---------- Start build workspace binary -----------"
    docker stop build_cwsm && docker rm build_cwsm
    docker run \
    --name=build_cwsm \
    --entrypoint="" \
    -v ${WORKSPACE_DIR}:/go/src/work_space \
    ${WORKSPACE_BUILD_IMAGE} \
    /bin/sh /go/src/work_space/ci/gobuild.sh 
    sleep 3
    rs=`docker inspect build_cwsm --format='{{.State.ExitCode}}'`
    if [ $rs != 0 ]; then
        echo "ERROR, container exited abnormally, failed to build workspace binary"
        exit 1
    fi
    docker stop build_cwsm && docker rm build_cwsm
    echo "---------- End build workspace binary success-----------"
}

build_workspace_bin
if [ $? != 0 ]; then
    exit 1
fi