#!/usr/bin/env bash

set -x

version=$1
GERRIT_CHANGE_NUMBER=$2

CURRENT_DIR=$(cd $(dirname "$0")||exit;pwd)
WORKSPACE_DIR=$(cd "$CURRENT_DIR"/..||exit;pwd)
cd $WORKSPACE_DIR
GIT_COMMIT=$(git rev-parse HEAD)
if [ "$version" == "" ];then
        version="v7.23.20.01"
fi
if [ "$GERRIT_CHANGE_NUMBER" == "" ];then
    GERRIT_CHANGE_NUMBER="$(echo ${GIT_COMMIT} | cut -c 1-7)"
fi

image_version="v23.20.01-amd64-${GERRIT_CHANGE_NUMBER}"

build_bins(){
    echo "---------- Start build binary -----------"
    bash ${CURRENT_DIR}/build_docker.sh
    if [ $? != 0 ]; then
        echo "ERROR, build bins fail"
        exit 1
    fi
    echo "---------- End build binary success-----------"
}

build_cwsm_image_amd64(){
    echo "---------- Start build cwsm image amd64-----------"

    rm -rf ${WORKSPACE_DIR}/output/images
    mkdir -p ${WORKSPACE_DIR}/output/images
    if [ $? != 0 ]; then
        echo "ERROR, not found folder: ${WORKSPACE_DIR}/output/images"
        exit 1
    fi
    cd ${WORKSPACE_DIR}
    
    image_name="cwsm_director_amd64"
    docker build -t ${image_name}:${image_version} -f  Dockerfile.amd64 .
    if [ $? != 0 ]; then
        echo "ERROR, failed to build workspace images amd64"
        rm -rf ${WORKSPACE_DIR}/output/images
        exit 1
    fi
    docker save -o ${WORKSPACE_DIR}/output/images/cwsm.tar ${image_name}:${image_version}
    if [ $? != 0 ]; then
        exit 1
    fi
    docker rmi ${image_name}:${image_version}
    echo "---------- End build cwsm image amd64 success-----------"
}

build_cwsm_image_arm64(){
    echo "---------- Start build cwsm image amd64-----------"

    rm -rf ${WORKSPACE_DIR}/output/images
    mkdir -p ${WORKSPACE_DIR}/output/images
    if [ $? != 0 ]; then
        echo "ERROR, not found folder: ${WORKSPACE_DIR}/output/images"
        exit 1
    fi
    cd ${WORKSPACE_DIR}
    
    image_name="cwsm_director_arm64"
    docker build -t ${image_name}:${image_version} -f  Dockerfile.arm64 .
    if [ $? != 0 ]; then
        echo "ERROR, failed to build workspace images arm64"
        rm -rf ${WORKSPACE_DIR}/output/images
        exit 1
    fi
    docker save -o ${WORKSPACE_DIR}/output/images/cwsm.tar ${image_name}:${image_version}
    if [ $? != 0 ]; then
        exit 1
    fi
    docker rmi ${image_name}:${image_version}
    echo "---------- End build cwsm image arm64 success-----------"
}

build_bins
build_cwsm_image_amd64
build_cwsm_image_arm64