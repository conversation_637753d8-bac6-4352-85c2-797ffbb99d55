package controllers

import (
	"cwsm/infra/wsm"
	"errors"
	"fmt"

	gomonkey "github.com/agiledragon/gomonkey/v2"

	. "github.com/onsi/ginkgo"

	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:2276eue7a048df314ab30b53700db77534c1e59c */
var _ = Describe("TestGetScclinspection", func() {
	var (
		reqParam wsm.ScclinspectionWsmKeywords
		patcher  *gomonkey.Patches
	)

	BeforeEach(func() {
		reqParam = wsm.ScclinspectionWsmKeywords{
			ClusterId: "cluster123",
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.ScclinspectionHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.ScclinspectionHandler, func(keywords wsm.ScclinspectionWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := GetScclinspection(reqParam.ClusterId)

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.ScclinspectionHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"id": 1, "name": "scclinspection1"}
			patcher.ApplyFunc(wsm.ScclinspectionHandler, func(keywords wsm.ScclinspectionWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := GetScclinspection(reqParam.ClusterId)

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.ScclinspectionHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.ScclinspectionHandler, func(keywords wsm.ScclinspectionWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := GetScclinspection(reqParam.ClusterId)

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.ScclinspectionHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.ScclinspectionHandler, func(keywords wsm.ScclinspectionWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := GetScclinspection(reqParam.ClusterId)

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:2276eue7a048df314ab30b53700db77534c1e59c */

/* Started by AICoder, pid:687c1l1eda0aa72147b90baff0e85a700e317ed7 */
var _ = Describe("TestActivateScclinspection", func() {
	var (
		clusterId string
		reqBody   []byte
		patcher   *gomonkey.Patches
	)

	BeforeEach(func() {
		clusterId = "cluster123"
		reqBody = []byte(`{"key": "value"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.ScclinspectionHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.ScclinspectionHandler, func(keywords wsm.ScclinspectionWsmKeywords, body []byte, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := ActivateScclinspection(clusterId, reqBody)

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.ScclinspectionHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"status": "activated"}
			patcher.ApplyFunc(wsm.ScclinspectionHandler, func(keywords wsm.ScclinspectionWsmKeywords, body []byte, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := ActivateScclinspection(clusterId, reqBody)

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.ScclinspectionHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.ScclinspectionHandler, func(keywords wsm.ScclinspectionWsmKeywords, body []byte, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := ActivateScclinspection(clusterId, reqBody)

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.ScclinspectionHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.ScclinspectionHandler, func(keywords wsm.ScclinspectionWsmKeywords, body []byte, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := ActivateScclinspection(clusterId, reqBody)

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:687c1l1eda0aa72147b90baff0e85a700e317ed7 */

/* Started by AICoder, pid:44109gb3b0z756b141cf083b209d6452e8f21294 */
var _ = Describe("TestStopScclinspection", func() {
	var (
		clusterId string
		patcher   *gomonkey.Patches
	)

	BeforeEach(func() {
		clusterId = "cluster123"
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.ScclinspectionHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.ScclinspectionHandler, func(keywords wsm.ScclinspectionWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			queryErr := StopScclinspection(clusterId)

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
		})
	})

	Context("When wsm.ScclinspectionHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.ScclinspectionHandler, func(keywords wsm.ScclinspectionWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			queryErr := StopScclinspection(clusterId)

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
		})
	})

	Context("When wsm.ScclinspectionHandler returns success", func() {
		It("should return no error", func() {
			patcher.ApplyFunc(wsm.ScclinspectionHandler, func(keywords wsm.ScclinspectionWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			queryErr := StopScclinspection(clusterId)

			Expect(queryErr).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:44109gb3b0z756b141cf083b209d6452e8f21294 */
