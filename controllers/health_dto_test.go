package controllers

import (
	"cwsm/infra/wsm"
	"errors"
	"fmt"

	gomonkey "github.com/agiledragon/gomonkey/v2"

	. "github.com/onsi/ginkgo"

	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:g05afx3adb4730c1466e090c90f33e6aa9779ceb */
var _ = Describe("TestGetHealth", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.HealthHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := GetHealth("cluster123")

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.HealthHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"status": "healthy"}
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := GetHealth("cluster123")

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.HealthHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := GetHealth("cluster123")

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.HealthHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := GetHealth("cluster123")

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:g05afx3adb4730c1466e090c90f33e6aa9779ceb */

/* Started by AICoder, pid:228149a6c9u87f6143bd09b2008a9860d6a7909c */
var _ = Describe("TestGetHealthCfg", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.HealthHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := GetHealthCfg("cluster123")

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.HealthHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"config": "default"}
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := GetHealthCfg("cluster123")

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.HealthHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := GetHealthCfg("cluster123")

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.HealthHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := GetHealthCfg("cluster123")

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:228149a6c9u87f6143bd09b2008a9860d6a7909c */

/* Started by AICoder, pid:cda018eea1iad1914d800bd7d0c22265bbc75df6 */
var _ = Describe("TestGetHealthCheckGPUNodesCfg", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.HealthHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := GetHealthCheckGPUNodesCfg("cluster123")

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.HealthHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"nodes": []string{"GPU1", "GPU2"}}
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := GetHealthCheckGPUNodesCfg("cluster123")

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.HealthHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := GetHealthCheckGPUNodesCfg("cluster123")

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.HealthHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := GetHealthCheckGPUNodesCfg("cluster123")

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:cda018eea1iad1914d800bd7d0c22265bbc75df6 */

/* Started by AICoder, pid:nab1f67009lfaa014c4a0ab0300af771d6911da6 */
var _ = Describe("TestActivateHealth", func() {
	var (
		clusterId string
		reqBody   []byte
		patcher   *gomonkey.Patches
	)

	BeforeEach(func() {
		clusterId = "cluster123"
		reqBody = []byte(`{"key": "value"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.HealthHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := ActivateHealth(clusterId, reqBody)

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.HealthHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"status": "activated"}
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := ActivateHealth(clusterId, reqBody)

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.HealthHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := ActivateHealth(clusterId, reqBody)

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.HealthHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.HealthHandler, func(keywords wsm.HealthWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := ActivateHealth(clusterId, reqBody)

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:nab1f67009lfaa014c4a0ab0300af771d6911da6 */
