package controllers

import (
	"context"
	"cwsm/infra/cwsmutils"
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/models"
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
)

/* Started by AICoder, pid:jb3c0ad3efg15e11451a097310783b78c4497a8f */
func TestCreateInspect(t *testing.T) {
	<PERSON>vey("TestCreateInspect", t, func() {
		var (
			clusterId = "cluster-1"
			reqBody   = CreateInspectReq{
				Name:                "Test Inspect",
				ClusterName:         "Test Cluster",
				Author:              "Tester",
				Scene:               "Test Scene",
				CreateMode:          1,
				CreateStrategy:      1,
				ExecuteStrategyTime: "2023-01-01T00:00:00Z",
				NodeList:            []string{"node1", "node2"},
				InspectionTask:      json.RawMessage(`{"task": "test"}`),
			}
		)

		<PERSON><PERSON>("success case", func() {
			patchUuid := gomonkey.ApplyFunc(cwsmutils.NewTimeUuid, func(prefix string) string {
				if prefix == "inspect-" {
					return "inspect-12345"
				}
				return "inspectResult-12345"
			})
			defer patchUuid.Reset()

			patchConvert := gomonkey.ApplyFunc(convertCreateInspectReqToDbCols, func(content CreateInspectReq, id string, resultId string, clusterId string) (map[string]interface{}, error) {
				return map[string]interface{}{"id": id}, nil
			})
			defer patchConvert.Reset()

			patchInsert := gomonkey.ApplyFunc(models.InsertNewResource, func(tableName string, newResource map[string]interface{}) bool {
				return true
			})
			defer patchInsert.Reset()

			id, err := CreateInspect(clusterId, reqBody)
			So(err, ShouldBeNil)
			So(id, ShouldEqual, "inspect-12345")
		})

		Convey("convertCreateInspectReqToDbCols fails", func() {
			patchUuid := gomonkey.ApplyFunc(cwsmutils.NewTimeUuid, func(prefix string) string {
				return "inspect-12345"
			})
			defer patchUuid.Reset()

			patchConvert := gomonkey.ApplyFunc(convertCreateInspectReqToDbCols, func(content CreateInspectReq, id string, resultId string, clusterId string) (map[string]interface{}, error) {
				return nil, fmt.Errorf("conversion error")
			})
			defer patchConvert.Reset()

			id, err := CreateInspect(clusterId, reqBody)
			So(err, ShouldNotBeNil)
			So(id, ShouldEqual, "")
		})

		Convey("InsertNewResource fails", func() {
			patchUuid := gomonkey.ApplyFunc(cwsmutils.NewTimeUuid, func(prefix string) string {
				return "inspect-12345"
			})
			defer patchUuid.Reset()

			patchConvert := gomonkey.ApplyFunc(convertCreateInspectReqToDbCols, func(content CreateInspectReq, id string, resultId string, clusterId string) (map[string]interface{}, error) {
				return map[string]interface{}{"id": id}, nil
			})
			defer patchConvert.Reset()

			patchInsert := gomonkey.ApplyFunc(models.InsertNewResource, func(tableName string, newResource map[string]interface{}) bool {
				return false
			})
			defer patchInsert.Reset()

			id, err := CreateInspect(clusterId, reqBody)
			So(err, ShouldNotBeNil)
			So(id, ShouldEqual, "")
		})
	})
}

/* Ended by AICoder, pid:jb3c0ad3efg15e11451a097310783b78c4497a8f */

/* Started by AICoder, pid:ffe9cg4437y122b140090991f0a1c965e949ff4d */
func TestConvertCreateInspectReqToDbCols(t *testing.T) {
	Convey("TestConvertCreateInspectReqToDbCols", t, func() {
		var (
			reqBody = CreateInspectReq{
				Name:                "Test Inspect",
				ClusterName:         "Test Cluster",
				Author:              "Tester",
				Scene:               "Test Scene",
				CreateMode:          1,
				CreateStrategy:      1,
				ExecuteStrategyTime: "2023-01-01T00:00:00Z",
				NodeList:            []string{"node1", "node2"},
				InspectionTask:      json.RawMessage(`{"InspectTaskName": ["task1", "task2"]}`),
			}
			id        = "inspect-12345"
			resultId  = "inspectResult-12345"
			clusterId = "cluster-1"
		)

		Convey("success case", func() {
			patchGetTime := gomonkey.ApplyFunc(GetCreateTimeAndInspectTaskName, func(content CreateInspectReq) (time.Time, time.Time, error) {
				return time.Now(), time.Now(), nil
			})
			defer patchGetTime.Reset()

			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListAndInspectTaskName, func(content CreateInspectReq) (string, string, error) {
				return "node1,node2", "task1,task2", nil
			})
			defer patchGetNodeList.Reset()

			data, err := convertCreateInspectReqToDbCols(reqBody, id, resultId, clusterId)
			So(err, ShouldBeNil)

			So(data["Id"], ShouldEqual, nil)
			So(data["ResultId"], ShouldEqual, nil)
			So(data["Name"], ShouldEqual, nil)
			So(data["ClusterName"], ShouldEqual, nil)
			So(data["NodeList"], ShouldEqual, nil)
			So(data["InspectTaskName"], ShouldEqual, nil)
		})

		Convey("GetCreateTimeAndInspectTaskName fails", func() {
			patchGetTime := gomonkey.ApplyFunc(GetCreateTimeAndInspectTaskName, func(content CreateInspectReq) (time.Time, time.Time, error) {
				return time.Time{}, time.Time{}, fmt.Errorf("time error")
			})
			defer patchGetTime.Reset()

			data, err := convertCreateInspectReqToDbCols(reqBody, id, resultId, clusterId)
			So(err, ShouldNotBeNil)
			So(data, ShouldBeNil)
		})

		Convey("GetNodeListAndInspectTaskName fails", func() {
			patchGetTime := gomonkey.ApplyFunc(GetCreateTimeAndInspectTaskName, func(content CreateInspectReq) (time.Time, time.Time, error) {
				return time.Now(), time.Now(), nil
			})
			defer patchGetTime.Reset()

			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListAndInspectTaskName, func(content CreateInspectReq) (string, string, error) {
				return "", "", fmt.Errorf("node list error")
			})
			defer patchGetNodeList.Reset()

			data, err := convertCreateInspectReqToDbCols(reqBody, id, resultId, clusterId)
			So(err, ShouldNotBeNil)
			So(data, ShouldBeNil)
		})
	})
}

/* Ended by AICoder, pid:ffe9cg4437y122b140090991f0a1c965e949ff4d */

/* Started by AICoder, pid:q55a5l1972z8d661492709f1d053cb39885476a8 */
func TestGetCreateTimeAndInspectTaskName(t *testing.T) {
	Convey("TestGetCreateTimeAndInspectTaskName", t, func() {
		var (
			reqBody = CreateInspectReq{
				Name:                "Test Inspect",
				ClusterName:         "Test Cluster",
				Author:              "Tester",
				Scene:               "Test Scene",
				CreateMode:          1,
				CreateStrategy:      1,
				ExecuteStrategyTime: "2023-01-01T00:00:00Z", // Valid RFC3339 format
				NodeList:            []string{"node1", "node2"},
				InspectionTask:      json.RawMessage(`{"InspectTaskName": ["task1", "task2"]}`),
			}
		)

		Convey("success case", func() {
			currentTime, parsedTime, err := GetCreateTimeAndInspectTaskName(reqBody)
			So(err, ShouldBeNil)
			So(parsedTime.Format(time.RFC3339), ShouldEqual, reqBody.ExecuteStrategyTime)

			So(currentTime.IsZero(), ShouldBeFalse)
		})

		Convey("invalid ExecuteStrategyTime format", func() {
			reqBody.ExecuteStrategyTime = "invalid-time-format"

			currentTime, parsedTime, err := GetCreateTimeAndInspectTaskName(reqBody)
			So(err, ShouldNotBeNil)
			So(currentTime.IsZero(), ShouldBeTrue)
			So(parsedTime.IsZero(), ShouldBeTrue)
		})
	})
}

/* Ended by AICoder, pid:q55a5l1972z8d661492709f1d053cb39885476a8 */

/* Started by AICoder, pid:ca82an34d3n9e65147240be1f042f45996674edf */
func TestGetNodeListAndInspectTaskName(t *testing.T) {
	Convey("TestGetNodeListAndInspectTaskName", t, func() {
		var (
			reqBody = CreateInspectReq{
				Name:                "Test Inspect",
				ClusterName:         "Test Cluster",
				Author:              "Tester",
				Scene:               "Test Scene",
				CreateMode:          1,
				CreateStrategy:      1,
				ExecuteStrategyTime: "2023-01-01T00:00:00Z",
				NodeList:            []string{"node1", "node2"},
				InspectionTask:      json.RawMessage(`{"inspectTaskName": ["task1", "task2"]}`),
			}
		)

		Convey("success case", func() {
			nodeListStr, inspectTaskStr, err := GetNodeListAndInspectTaskName(reqBody)
			So(err, ShouldBeNil)
			So(inspectTaskStr, ShouldEqual, "[\"task1\",\"task2\"]")
			So(nodeListStr, ShouldEqual, "[\"node1\",\"node2\"]")
		})

		Convey("json unmarshal error", func() {
			// Update reqBody with an invalid JSON in InspectionTask
			reqBody.InspectionTask = json.RawMessage(`{"inspectTaskName": invalid-json`)

			nodeListStr, inspectTaskStr, err := GetNodeListAndInspectTaskName(reqBody)
			So(err, ShouldNotBeNil)
			So(nodeListStr, ShouldEqual, "")
			So(inspectTaskStr, ShouldEqual, "")
		})

		Convey("slice to string error", func() {
			// Mock SliceToString to return an error
			patchSliceToString := gomonkey.ApplyFunc(cwsmutils.SliceToString, func(s []string) (string, error) {
				return "", fmt.Errorf("slice to string error")
			})
			defer patchSliceToString.Reset()

			nodeListStr, inspectTaskStr, err := GetNodeListAndInspectTaskName(reqBody)
			So(err, ShouldNotBeNil)
			So(nodeListStr, ShouldEqual, "")
			So(inspectTaskStr, ShouldEqual, "")
		})

		Convey("NodeList is nil", func() {
			// Set NodeList to nil
			reqBody.NodeList = nil

			nodeListStr, inspectTaskStr, err := GetNodeListAndInspectTaskName(reqBody)
			So(err, ShouldBeNil)
			So(inspectTaskStr, ShouldEqual, "[\"task1\",\"task2\"]")
			So(nodeListStr, ShouldEqual, "")
		})
	})
}

/* Ended by AICoder, pid:ca82an34d3n9e65147240be1f042f45996674edf */

func TestModifyInspect(t *testing.T) {
	Convey("TestModifyInspect", t, func() {
		var (
			clusterId = "cluster-1"
			id        = "inspect-12345"
			reqBody   = CreateInspectReq{
				Name:                "Test Inspect",
				ClusterName:         "Test Cluster",
				Author:              "Tester",
				Scene:               "Test Scene",
				CreateMode:          1,
				CreateStrategy:      1,
				ExecuteStrategyTime: "2023-01-01T00:00:00Z",
				NodeList:            []string{"node1", "node2"},
				InspectionTask:      json.RawMessage(`{"inspectTaskName": ["task1", "task2"]}`),
			}
		)

		Convey("success case", func() {
			// Mocking dependent functions
			patchGetTime := gomonkey.ApplyFunc(GetCreateTimeAndInspectTaskName, func(content CreateInspectReq) (time.Time, time.Time, error) {
				return time.Now(), time.Now(), nil
			})
			defer patchGetTime.Reset()

			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListAndInspectTaskName, func(content CreateInspectReq) (string, string, error) {
				return "[\"node1\",\"node2\"]", "[\"task1\",\"task2\"]", nil
			})
			defer patchGetNodeList.Reset()

			patchQueryResource := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return map[string]interface{}{
					"resultId":     "result-123",
					"planResultId": "planResult-123",
					"status":       "active",
					"createTime":   time.Now(),
				}, nil
			})
			defer patchQueryResource.Reset()

			patchStruct2Map := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(in interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{
					"Id": id,
				}, nil
			})
			defer patchStruct2Map.Reset()

			patchUpdate := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
				return true
			})
			defer patchUpdate.Reset()

			resultId, err := ModifyInspect(clusterId, id, reqBody)
			So(err, ShouldBeNil)
			So(resultId, ShouldEqual, id)
		})

		Convey("GetCreateTimeAndInspectTaskName error", func() {
			// Mock to return an error
			patchGetTime := gomonkey.ApplyFunc(GetCreateTimeAndInspectTaskName, func(content CreateInspectReq) (time.Time, time.Time, error) {
				return time.Time{}, time.Time{}, fmt.Errorf("time error")
			})
			defer patchGetTime.Reset()

			resultId, err := ModifyInspect(clusterId, id, reqBody)
			So(err, ShouldNotBeNil)
			So(resultId, ShouldEqual, "")
		})

		Convey("GetNodeListAndInspectTaskName error", func() {
			// Mock GetCreateTimeAndInspectTaskName to succeed
			patchGetTime := gomonkey.ApplyFunc(GetCreateTimeAndInspectTaskName, func(content CreateInspectReq) (time.Time, time.Time, error) {
				return time.Now(), time.Now(), nil
			})
			defer patchGetTime.Reset()

			// Mock to return an error
			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListAndInspectTaskName, func(content CreateInspectReq) (string, string, error) {
				return "", "", fmt.Errorf("node list error")
			})
			defer patchGetNodeList.Reset()

			resultId, err := ModifyInspect(clusterId, id, reqBody)
			So(err, ShouldNotBeNil)
			So(resultId, ShouldEqual, "")
		})

		Convey("QueryResourceById error", func() {
			// Mock GetCreateTimeAndInspectTaskName to succeed
			patchGetTime := gomonkey.ApplyFunc(GetCreateTimeAndInspectTaskName, func(content CreateInspectReq) (time.Time, time.Time, error) {
				return time.Now(), time.Now(), nil
			})
			defer patchGetTime.Reset()

			// Mock GetNodeListAndInspectTaskName to succeed
			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListAndInspectTaskName, func(content CreateInspectReq) (string, string, error) {
				return "[\"node1\",\"node2\"]", "[\"task1\",\"task2\"]", nil
			})
			defer patchGetNodeList.Reset()

			// Mock to return an error
			patchQueryResource := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return nil, fmt.Errorf("query error")
			})
			defer patchQueryResource.Reset()

			resultId, err := ModifyInspect(clusterId, id, reqBody)
			So(err, ShouldNotBeNil)
			So(resultId, ShouldEqual, "")
		})

		Convey("Struct2Map error", func() {
			// Mock GetCreateTimeAndInspectTaskName to succeed
			patchGetTime := gomonkey.ApplyFunc(GetCreateTimeAndInspectTaskName, func(content CreateInspectReq) (time.Time, time.Time, error) {
				return time.Now(), time.Now(), nil
			})
			defer patchGetTime.Reset()

			// Mock GetNodeListAndInspectTaskName to succeed
			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListAndInspectTaskName, func(content CreateInspectReq) (string, string, error) {
				return "[\"node1\",\"node2\"]", "[\"task1\",\"task2\"]", nil
			})
			defer patchGetNodeList.Reset()

			// Mock QueryResourceById to succeed
			patchQueryResource := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return map[string]interface{}{
					"resultId":     "result-123",
					"planResultId": "planResult-123",
					"status":       "active",
					"createTime":   time.Now(),
				}, nil
			})
			defer patchQueryResource.Reset()

			// Mock to return an error
			patchStruct2Map := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(in interface{}) (map[string]interface{}, error) {
				return nil, fmt.Errorf("struct to map error")
			})
			defer patchStruct2Map.Reset()

			resultId, err := ModifyInspect(clusterId, id, reqBody)
			So(err, ShouldNotBeNil)
			So(resultId, ShouldEqual, "")
		})

		Convey("UpdateResourceById error", func() {
			// Mock GetCreateTimeAndInspectTaskName to succeed
			patchGetTime := gomonkey.ApplyFunc(GetCreateTimeAndInspectTaskName, func(content CreateInspectReq) (time.Time, time.Time, error) {
				return time.Now(), time.Now(), nil
			})
			defer patchGetTime.Reset()

			// Mock GetNodeListAndInspectTaskName to succeed
			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListAndInspectTaskName, func(content CreateInspectReq) (string, string, error) {
				return "[\"node1\",\"node2\"]", "[\"task1\",\"task2\"]", nil
			})
			defer patchGetNodeList.Reset()

			// Mock QueryResourceById to succeed
			patchQueryResource := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return map[string]interface{}{
					"resultId":     "result-123",
					"planResultId": "planResult-123",
					"status":       "active",
					"createTime":   time.Now(),
				}, nil
			})
			defer patchQueryResource.Reset()

			// Mock Struct2Map to succeed
			patchStruct2Map := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(in interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{
					"Id": id,
				}, nil
			})
			defer patchStruct2Map.Reset()

			// Mock UpdateResourceById to return false
			patchUpdate := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
				return false
			})
			defer patchUpdate.Reset()

			resultId, _ := ModifyInspect(clusterId, id, reqBody)
			So(resultId, ShouldEqual, "")
		})
	})
}

func TestGetNodeListStrAndInspectTaskNameStr(t *testing.T) {
	Convey("TestGetNodeListStrAndInspectTaskNameStr", t, func() {
		var (
			// 创建 InspectPlan 实例
			planList = InspectPlan{
				Id:                  "plan-1",
				ResultId:            "result-1",
				PlanResultId:        "planResult-1",
				Name:                "Test Plan",
				ClusterName:         "Test Cluster",
				ClusterId:           "cluster-1",
				Author:              "Tester",
				Scene:               "Test Scene",
				NodeList:            "[\"node1\",\"node2\"]", // JSON 格式字符串
				InspectTaskName:     "[\"task1\",\"task2\"]", // JSON 格式字符串
				Status:              "active",
				DesignPlanPerson:    "Designer",
				ModifyPlanPerson:    "Modifier",
				ExecuteResult:       "success",
				ExecuteStartTime:    "",
				ExecuteEndTime:      "",
				ExecutePlanPerson:   "Executor",
				CreateTime:          time.Now(),
				LatestModifyTime:    time.Now(),
				CreateMode:          1,
				CreateStrategy:      1,
				ExecuteStrategyTime: time.Now(),
			}
		)

		Convey("success case", func() {
			nodeList, inspectTaskName, err := GetNodeListStrAndInspectTaskNameStr(planList)
			So(err, ShouldBeNil)
			So(nodeList, ShouldResemble, []string{"node1", "node2"})
			So(inspectTaskName, ShouldResemble, []string{"task1", "task2"})
		})

		Convey("StringToSlice error for InspectTaskName", func() {
			// 更新 planList 使 InspectTaskName 为无效格式
			planList.InspectTaskName = "invalid json"

			nodeList, inspectTaskName, err := GetNodeListStrAndInspectTaskNameStr(planList)
			So(err, ShouldNotBeNil)
			So(nodeList, ShouldBeNil)
			So(inspectTaskName, ShouldBeNil)
		})

		Convey("StringToSlice error for NodeList", func() {
			// 更新 planList 使 NodeList 为无效格式
			planList.NodeList = "invalid json"

			nodeList, _, err := GetNodeListStrAndInspectTaskNameStr(planList)
			So(err, ShouldNotBeNil)
			So(nodeList, ShouldBeNil)
			//So(inspectTaskName, ShouldResemble, []string{"task1", "task2"})
		})

		Convey("NodeList is empty", func() {
			// 更新 planList 使 NodeList 为空
			planList.NodeList = ""

			nodeList, inspectTaskName, err := GetNodeListStrAndInspectTaskNameStr(planList)
			So(err, ShouldBeNil)
			So(nodeList, ShouldBeEmpty)
			So(inspectTaskName, ShouldResemble, []string{"task1", "task2"})
		})
	})
}

/* Started by AICoder, pid:e62c9j57c5z4fca14b71084530b21d67b5124ba2 */
func TestGetPlanList(t *testing.T) {
	Convey("TestGetPlanList", t, func() {
		var (
			inspectionPlan = []map[string]interface{}{
				{
					"id":              "plan-1",
					"resultId":        "result-1",
					"planResultId":    "planResult-1",
					"name":            "Test Plan 1",
					"clusterName":     "Test Cluster",
					"clusterId":       "cluster-1",
					"author":          "Tester",
					"scene":           "Test Scene",
					"nodeList":        "[\"node1\",\"node2\"]",
					"inspectTaskName": "[\"task1\",\"task2\"]",
					"status":          "active",
					"createTime":      time.Now(),
				},
			}
			inspectPlanList = &InspectPlanList{}
		)

		Convey("success case", func() {
			err := GetPlanList(inspectionPlan, inspectPlanList)
			So(err, ShouldBeNil)
			So(len(inspectPlanList.PlanList), ShouldEqual, 1)
			So(inspectPlanList.PlanList[0].Id, ShouldEqual, "plan-1")
		})

		Convey("json marshal error", func() {
			badInspectionPlan := []map[string]interface{}{
				{"invalid": make(chan int)},
			}
			err := GetPlanList(badInspectionPlan, inspectPlanList)
			So(err, ShouldNotBeNil)
			So(inspectPlanList.PlanList, ShouldBeEmpty)
		})

		Convey("json unmarshal error", func() {
			invalidInspectionPlan := []map[string]interface{}{
				{
					"id":              "plan-1",
					"resultId":        "result-1",
					"planResultId":    "planResult-1",
					"name":            "Test Plan 1",
					"clusterName":     "Test Cluster",
					"clusterId":       "cluster-1",
					"author":          "Tester",
					"scene":           "Test Scene",
					"nodeList":        "invalid json",
					"inspectTaskName": "[\"task1\",\"task2\"]",
					"status":          "active",
					"createTime":      time.Now(),
				},
			}

			err := GetPlanList(invalidInspectionPlan, inspectPlanList)
			So(err, ShouldBeNil)
			So(inspectPlanList.PlanList, ShouldNotBeEmpty)
		})
	})
}

/* Ended by AICoder, pid:e62c9j57c5z4fca14b71084530b21d67b5124ba2 */

func TestGetFinishTime(t *testing.T) {
	Convey("TestGetFinishTime", t, func() {
		var (
			finishTimeStr = "2023-01-01T12:00:00Z" // 示例完成时间
			finishTime, _ = time.Parse(time.RFC3339, finishTimeStr)
		)

		Convey("finished status", func() {
			inspectResult := &PlanResultValues{
				FinishTime: finishTime,
			}
			plan := &InspectPlan{
				Status: "finished",
			}

			finishTimeResult, err := GetFinishTime(inspectResult, plan)
			So(err, ShouldBeNil)
			So(finishTimeResult, ShouldEqual, finishTimeStr)
		})

		Convey("stopped status", func() {
			inspectResult := &PlanResultValues{
				FinishTime: finishTime,
			}
			plan := &InspectPlan{
				Status: "stopped",
			}

			finishTimeResult, err := GetFinishTime(inspectResult, plan)
			So(err, ShouldBeNil)
			So(finishTimeResult, ShouldEqual, finishTimeStr)
		})

		Convey("default finish time", func() {
			inspectResult := &PlanResultValues{
				FinishTime: time.Time{}, // 默认时间
			}
			plan := &InspectPlan{
				Status: "in progress", // 其他状态
			}

			finishTimeResult, err := GetFinishTime(inspectResult, plan)
			So(err, ShouldBeNil)
			So(finishTimeResult, ShouldEqual, "")
		})

		Convey("valid finish time but not finished or stopped", func() {
			inspectResult := &PlanResultValues{
				FinishTime: finishTime,
			}
			plan := &InspectPlan{
				Status: "in progress", // 其他状态
			}

			finishTimeResult, err := GetFinishTime(inspectResult, plan)
			So(err, ShouldBeNil)
			So(finishTimeResult, ShouldEqual, "")
		})
	})
}

/* Started by AICoder, pid:3f2d4h49b1f04c5143950984c0ab37640611f3be */
func TestGetStartTimeAndFinishTime(t *testing.T) {
	Convey("TestGetStartTimeAndFinishTime", t, func() {
		Convey("success case", func() {
			inspectionResult := map[string]interface{}{
				"startTime":  time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
				"finishTime": time.Date(2023, 1, 1, 13, 0, 0, 0, time.UTC),
			}

			startTime, finishTime, err := GetStartTimeAndFinishTime(inspectionResult)
			So(err, ShouldBeNil)
			So(startTime, ShouldEqual, inspectionResult["startTime"])
			So(finishTime, ShouldEqual, inspectionResult["finishTime"])
		})

		Convey("startTime type error", func() {
			inspectionResult := map[string]interface{}{
				"startTime":  "invalid time type", // 非 time.Time 类型
				"finishTime": time.Date(2023, 1, 1, 13, 0, 0, 0, time.UTC),
			}

			startTime, finishTime, err := GetStartTimeAndFinishTime(inspectionResult)
			So(err, ShouldNotBeNil)
			So(startTime, ShouldEqual, time.Time{})
			So(finishTime, ShouldEqual, time.Time{})
		})

		Convey("finishTime type error", func() {
			inspectionResult := map[string]interface{}{
				"startTime":  time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
				"finishTime": "invalid time type", // 非 time.Time 类型
			}

			startTime, finishTime, err := GetStartTimeAndFinishTime(inspectionResult)
			So(err, ShouldNotBeNil)
			So(startTime, ShouldEqual, time.Time{})
			So(finishTime, ShouldEqual, time.Time{})
		})

		Convey("missing startTime", func() {
			inspectionResult := map[string]interface{}{
				"finishTime": time.Date(2023, 1, 1, 13, 0, 0, 0, time.UTC),
			}

			startTime, finishTime, err := GetStartTimeAndFinishTime(inspectionResult)
			So(err, ShouldNotBeNil)
			So(startTime, ShouldEqual, time.Time{})
			So(finishTime, ShouldEqual, time.Time{})
		})

		Convey("missing finishTime", func() {
			inspectionResult := map[string]interface{}{
				"startTime": time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
			}

			startTime, finishTime, err := GetStartTimeAndFinishTime(inspectionResult)
			So(err, ShouldNotBeNil)
			So(startTime, ShouldEqual, time.Time{})
			So(finishTime, ShouldEqual, time.Time{})
		})
	})
}

/* Ended by AICoder, pid:3f2d4h49b1f04c5143950984c0ab37640611f3be */

/* Started by AICoder, pid:tbeaa2de20jac221455a0b99b1f2f56cede6e469 */
func TestGetInspect(t *testing.T) {
	Convey("TestGetInspect", t, func() {
		clusterId := "test-cluster-id"

		Convey("success case with finished status", func() {
			inspectionPlan := []map[string]interface{}{
				{
					"id":                "plan-1",
					"resultId":          "result-1",
					"planResultId":      "result-1",
					"name":              "Inspection Plan 1",
					"clusterName":       "Test Cluster",
					"clusterId":         clusterId,
					"status":            "finished",
					"executePlanPerson": "Person A",
					"inspectTaskName":   json.RawMessage(`["task1", "task2"]`),
				},
			}

			patchQuery := gomonkey.ApplyFunc(models.QueryResourceByCondition, func(tableName string, queryCondition map[string]interface{}) ([]map[string]interface{}, error) {
				return inspectionPlan, nil
			})
			defer patchQuery.Reset()

			patchGetPlanList := gomonkey.ApplyFunc(GetPlanList, func(inspectionPlan []map[string]interface{}, inspectPlanList *InspectPlanList) error {
				inspectPlanList.PlanList = []InspectPlan{
					{
						Id:            "plan-1",
						Name:          "Inspection Plan 1",
						ClusterId:     clusterId,
						ClusterName:   "Test Cluster",
						Status:        "finished",
						ExecuteResult: "success",
					},
				}
				return nil
			})
			defer patchGetPlanList.Reset()

			patchGetValues := gomonkey.ApplyFunc(GetValuesOfInspectionResult, func(planList InspectPlan) (map[string]interface{}, []string, []string, error) {
				return map[string]interface{}{
					"executeResult":     "success",
					"executePlanPerson": "Person A",
				}, []string{"node1", "node2"}, []string{"task1", "task2"}, nil
			})
			defer patchGetValues.Reset()

			patchGetInspectValues := gomonkey.ApplyFunc(GetInspectResultValues, func(inspectionResult map[string]interface{}, planList InspectPlan) (*PlanResultValues, string, error) {
				return &PlanResultValues{
					StartTime:  time.Now(),
					FinishTime: time.Now(),
				}, time.Now().Format(time.RFC3339), nil
			})
			defer patchGetInspectValues.Reset()
		})

		Convey("success case with no result for a plan", func() {
			inspectionPlan := []map[string]interface{}{
				{
					"id":           "plan-2",
					"resultId":     "result-2",
					"planResultId": "result-2",
					"name":         "Inspection Plan 2",
					"clusterName":  "Test Cluster",
					"clusterId":    clusterId,
					"status":       "running",
				},
			}

			patchQuery := gomonkey.ApplyFunc(models.QueryResourceByCondition, func(tableName string, queryCondition map[string]interface{}) ([]map[string]interface{}, error) {
				return inspectionPlan, nil
			})
			defer patchQuery.Reset()

			patchGetPlanList := gomonkey.ApplyFunc(GetPlanList, func(inspectionPlan []map[string]interface{}, inspectPlanList *InspectPlanList) error {
				inspectPlanList.PlanList = []InspectPlan{
					{
						Id:          "plan-2",
						Name:        "Inspection Plan 2",
						ClusterId:   clusterId,
						ClusterName: "Test Cluster",
						Status:      "running",
					},
				}
				return nil
			})
			defer patchGetPlanList.Reset()

			patchGetValues := gomonkey.ApplyFunc(GetValuesOfInspectionResult, func(planList InspectPlan) (map[string]interface{}, []string, []string, error) {
				return nil, []string{"node3"}, []string{"task3"}, nil
			})
			defer patchGetValues.Reset()

			patchGetPlanValuesNoResult := gomonkey.ApplyFunc(GetPlanValuesNoResultTable, func(planList InspectPlan, nodeList []string, inspectTaskName []string) (PlanInspect, error) {
				return PlanInspect{
					Id:                planList.Id,
					Name:              planList.Name,
					ClusterId:         planList.ClusterId,
					ClusterName:       planList.ClusterName,
					Scene:             planList.Scene,
					NodeList:          nodeList,
					Status:            planList.Status,
					ExecuteResult:     "no result",
					ExecuteStartTime:  "",
					ExecuteEndTime:    "",
					ExecutePlanPerson: "",
					DesignPlanPerson:  planList.DesignPlanPerson,
					CreateMode:        planList.CreateMode,
					CreateStrategy:    planList.CreateStrategy,
					InspectTaskName:   inspectTaskName,
					InspectionTask:    json.RawMessage(planList.InspectionTask),
				}, nil
			})
			defer patchGetPlanValuesNoResult.Reset()

			// Call the function
			data, err := GetInspect(clusterId)
			So(err, ShouldNotBeNil)
			So(data, ShouldBeNil)
		})

		Convey("failure case with QueryResourceByCondition error", func() {
			patchQuery := gomonkey.ApplyFunc(models.QueryResourceByCondition, func(tableName string, queryCondition map[string]interface{}) ([]map[string]interface{}, error) {
				return nil, fmt.Errorf("database error")
			})
			defer patchQuery.Reset()

			data, err := GetInspect(clusterId)
			So(err, ShouldNotBeNil)
			So(data, ShouldBeNil)
		})

		Convey("failure case with GetPlanList error", func() {
			inspectionPlan := []map[string]interface{}{
				{
					"id":           "plan-1",
					"resultId":     "result-1",
					"planResultId": "result-1",
					"name":         "Inspection Plan 1",
					"clusterName":  "Test Cluster",
					"clusterId":    clusterId,
					"status":       "finished",
				},
			}

			patchQuery := gomonkey.ApplyFunc(models.QueryResourceByCondition, func(tableName string, queryCondition map[string]interface{}) ([]map[string]interface{}, error) {
				return inspectionPlan, nil
			})
			defer patchQuery.Reset()

			patchGetPlanList := gomonkey.ApplyFunc(GetPlanList, func(inspectionPlan []map[string]interface{}, inspectPlanList *InspectPlanList) error {
				return fmt.Errorf("failed to get plan list")
			})
			defer patchGetPlanList.Reset()

			data, err := GetInspect(clusterId)
			So(err, ShouldNotBeNil)
			So(data, ShouldBeNil)
		})
	})
}

/* Ended by AICoder, pid:tbeaa2de20jac221455a0b99b1f2f56cede6e469 */

/* Started by AICoder, pid:c87fae95ccvd9d914f760a104173200647e8936d */
func TestGetValuesOfInspectionResult(t *testing.T) {
	Convey("TestGetValuesOfInspectionResult", t, func() {
		planList := InspectPlan{
			Id:              "plan-1",
			ResultId:        "result-1",
			PlanResultId:    "2", // Not "1" to test the first condition
			InspectTaskName: `["task1", "task2"]`,
			NodeList:        `["node1", "node2"]`,
		}

		Convey("successful case with PlanResultId not equal to 1", func() {
			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListStrAndInspectTaskNameStr, func(planList InspectPlan) ([]string, []string, error) {
				return []string{"node1", "node2"}, []string{"task1", "task2"}, nil
			})
			defer patchGetNodeList.Reset()

			patchQueryById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return map[string]interface{}{
					"executeResult":     "success",
					"executePlanPerson": "Person A",
				}, nil
			})
			defer patchQueryById.Reset()

			// Call the function
			result, nodeList, inspectTaskName, err := GetValuesOfInspectionResult(planList)
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			So(nodeList, ShouldResemble, []string{"node1", "node2"})
			So(inspectTaskName, ShouldResemble, []string{"task1", "task2"})
		})

		Convey("successful case with PlanResultId equal to 1", func() {
			planList.PlanResultId = "1"

			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListStrAndInspectTaskNameStr, func(planList InspectPlan) ([]string, []string, error) {
				return []string{"node1", "node2"}, []string{"task1", "task2"}, nil
			})
			defer patchGetNodeList.Reset()

			patchQueryByResultId := gomonkey.ApplyFunc(models.QueryResourceByResultId, func(tableName string, idValue string) (map[string]interface{}, error) {
				return map[string]interface{}{
					"executeResult":     "success",
					"executePlanPerson": "Person B",
				}, nil
			})
			defer patchQueryByResultId.Reset()

			// Call the function
			result, nodeList, inspectTaskName, err := GetValuesOfInspectionResult(planList)
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			So(nodeList, ShouldResemble, []string{"node1", "node2"})
			So(inspectTaskName, ShouldResemble, []string{"task1", "task2"})
		})

		Convey("error case when GetNodeListStrAndInspectTaskNameStr fails", func() {
			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListStrAndInspectTaskNameStr, func(planList InspectPlan) ([]string, []string, error) {
				return nil, nil, fmt.Errorf("failed to get node list")
			})
			defer patchGetNodeList.Reset()

			result, nodeList, inspectTaskName, err := GetValuesOfInspectionResult(planList)
			So(err, ShouldNotBeNil)
			So(result, ShouldBeNil)
			So(nodeList, ShouldBeNil)
			So(inspectTaskName, ShouldBeNil)
		})

		Convey("error case when QueryResourceById fails", func() {
			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListStrAndInspectTaskNameStr, func(planList InspectPlan) ([]string, []string, error) {
				return []string{"node1", "node2"}, []string{"task1", "task2"}, nil
			})
			defer patchGetNodeList.Reset()

			patchQueryById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return nil, fmt.Errorf("database error")
			})
			defer patchQueryById.Reset()

			result, nodeList, inspectTaskName, err := GetValuesOfInspectionResult(planList)
			So(err, ShouldBeNil)
			So(result, ShouldBeNil)
			So(nodeList, ShouldResemble, []string(nil))
			So(inspectTaskName, ShouldResemble, []string(nil))
		})

		Convey("error case when QueryResourceByResultId fails", func() {
			planList.PlanResultId = "1" // Set it to "1" to test this path

			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListStrAndInspectTaskNameStr, func(planList InspectPlan) ([]string, []string, error) {
				return []string{"node1", "node2"}, []string{"task1", "task2"}, nil
			})
			defer patchGetNodeList.Reset()

			patchQueryByResultId := gomonkey.ApplyFunc(models.QueryResourceByResultId, func(tableName string, idValue string) (map[string]interface{}, error) {
				return nil, fmt.Errorf("database error")
			})
			defer patchQueryByResultId.Reset()

			result, nodeList, inspectTaskName, err := GetValuesOfInspectionResult(planList)
			So(err, ShouldBeNil)
			So(result, ShouldBeNil)
			So(nodeList, ShouldResemble, []string(nil))
			So(inspectTaskName, ShouldResemble, []string(nil))
		})
	})
}

/* Ended by AICoder, pid:c87fae95ccvd9d914f760a104173200647e8936d */

/* Started by AICoder, pid:m5655ib7440599c1492e09be5074c16f91d6c13f */
func TestGetInspectResultValues(t *testing.T) {
	Convey("TestGetInspectResultValues", t, func() {
		inspectionResult := map[string]interface{}{
			"id":                "result-1",
			"startTime":         time.Now(),
			"finishTime":        time.Now().Add(1 * time.Hour),
			"executePlanPerson": "Person A",
			"executeResult":     "success",
			"status":            "completed",
		}

		planList := InspectPlan{
			Id:           "plan-1",
			ResultId:     "result-1",
			PlanResultId: "1",
			Status:       "finished",
		}

		Convey("successful case", func() {
			patchGetResultValue := gomonkey.ApplyFunc(GetResultValue, func(inspectionResult map[string]interface{}, inspectReslut *PlanResultValues) error {
				return json.Unmarshal([]byte(`{"id":"result-1","startTime":"2023-01-01T00:00:00Z","finishTime":"2023-01-01T01:00:00Z","executePlanPerson":"Person A","executeResult":"success","status":"completed"}`), inspectReslut)
			})
			defer patchGetResultValue.Reset()

			patchGetFinishTime := gomonkey.ApplyFunc(GetFinishTime, func(inspectReslut *PlanResultValues, planList *InspectPlan) (string, error) {
				return inspectReslut.FinishTime.Format(time.RFC3339), nil
			})
			defer patchGetFinishTime.Reset()

			result, finishTime, err := GetInspectResultValues(inspectionResult, planList)
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)
			So(result.ExecutePlanPerson, ShouldEqual, "Person A")
			So(finishTime, ShouldNotBeEmpty)
		})

		Convey("error case when GetResultValue fails", func() {
			patchGetResultValue := gomonkey.ApplyFunc(GetResultValue, func(inspectionResult map[string]interface{}, inspectReslut *PlanResultValues) error {
				return fmt.Errorf("failed to get result value")
			})
			defer patchGetResultValue.Reset()

			result, finishTime, err := GetInspectResultValues(inspectionResult, planList)
			So(err, ShouldNotBeNil)
			So(result, ShouldBeNil)
			So(finishTime, ShouldBeEmpty)
		})

		Convey("error case when GetFinishTime fails", func() {
			patchGetResultValue := gomonkey.ApplyFunc(GetResultValue, func(inspectionResult map[string]interface{}, inspectReslut *PlanResultValues) error {
				return json.Unmarshal([]byte(`{"id":"result-1","startTime":"2023-01-01T00:00:00Z","finishTime":"2023-01-01T01:00:00Z","executePlanPerson":"Person A","executeResult":"success","status":"completed"}`), inspectReslut)
			})
			defer patchGetResultValue.Reset()

			patchGetFinishTime := gomonkey.ApplyFunc(GetFinishTime, func(inspectReslut *PlanResultValues, planList *InspectPlan) (string, error) {
				return "", fmt.Errorf("failed to get finish time")
			})
			defer patchGetFinishTime.Reset()

			result, finishTime, err := GetInspectResultValues(inspectionResult, planList)
			So(err, ShouldNotBeNil)
			So(result, ShouldBeNil) // Result should be populated despite the finish time error
			So(finishTime, ShouldBeEmpty)
		})
	})
}

/* Ended by AICoder, pid:m5655ib7440599c1492e09be5074c16f91d6c13f */

func TestGetPlanValue(t *testing.T) {
	Convey("TestGetPlanValue", t, func() {
		// 创建一个示例的 inspectionPlan
		inspectionPlan := map[string]interface{}{
			"id":               "plan-1",
			"resultId":         "result-1",
			"planResultId":     "1",
			"name":             "Inspection Plan 1",
			"clusterName":      "Test Cluster",
			"clusterId":        "cluster-1",
			"author":           "Author A",
			"scene":            "scene-1",
			"nodeList":         `["node1", "node2"]`,
			"status":           "finished",
			"designPlanPerson": "Designer A",
		}

		Convey("successful case", func() {
			var inspectPlan InspectPlan

			err := GetPlanValue(inspectionPlan, &inspectPlan)
			So(err, ShouldBeNil)
			So(inspectPlan.Id, ShouldEqual, "plan-1")
			So(inspectPlan.ResultId, ShouldEqual, "result-1")
			So(inspectPlan.PlanResultId, ShouldEqual, "1")
			So(inspectPlan.Name, ShouldEqual, "Inspection Plan 1")
			So(inspectPlan.ClusterName, ShouldEqual, "Test Cluster")
		})

		Convey("error case when marshaling JSON fails", func() {
			// 模拟一个无法 marshaling 的输入
			//invalidInspectionPlan := make(chan int) // 不支持 JSON marshaling
			var inspectPlan InspectPlan

			err := GetPlanValue(nil, &inspectPlan)
			So(err, ShouldBeNil)
		})

		Convey("error case when unmarshaling JSON fails", func() {
			// 使用一个包含不可解码字段的 map
			invalidInspectionPlan := map[string]interface{}{
				"id": "plan-1",
				// 缺少必要字段
			}
			var inspectPlan InspectPlan

			err := GetPlanValue(invalidInspectionPlan, &inspectPlan)
			So(err, ShouldBeNil)
		})
	})
}

func TestGetResultValue(t *testing.T) {
	Convey("TestGetResultValue", t, func() {
		// 创建一个示例的 inspectionResult
		inspectionResult := map[string]interface{}{
			"id":        "result-1",
			"status":    "completed",
			"resultId":  "result-1",
			"author":    "Author A",
			"timestamp": "2023-01-01T00:00:00Z",
		}

		Convey("successful case", func() {
			var planResultValues PlanResultValues

			err := GetResultValue(inspectionResult, &planResultValues)
			So(err, ShouldBeNil)
			So(planResultValues.Id, ShouldEqual, "result-1")
			So(planResultValues.Status, ShouldEqual, "completed")
		})

		Convey("error case when marshaling JSON fails", func() {
			// 模拟一个无法 marshaling 的输入
			var planResultValues PlanResultValues

			err := GetResultValue(nil, &planResultValues)
			So(err, ShouldBeNil) // 这里假设 nil 不会导致错误
		})

		Convey("error case when unmarshaling JSON fails", func() {
			// 使用一个包含不可解码字段的 map
			invalidInspectionResult := map[string]interface{}{
				"id": "result-1",
				// 缺少必要字段
			}
			var planResultValues PlanResultValues

			err := GetResultValue(invalidInspectionResult, &planResultValues)
			So(err, ShouldBeNil) // 这里同样假设不解码会返回 nil
		})
	})
}

func TestGetOneInspect(t *testing.T) {
	Convey("TestGetOneInspect", t, func() {
		clusterId := "cluster-1"
		id := "plan-1"

		// Mock the QueryResourceById function
		patchQuery := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			return map[string]interface{}{
				"id":                  "plan-1",
				"resultId":            "result-1",
				"executeStrategyTime": time.Now(),
				"inspectionTask":      "task1",
			}, nil
		})
		defer patchQuery.Reset()

		Convey("successful case", func() {
			patchGetPlanValue := gomonkey.ApplyFunc(GetPlanValue, func(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
				*inspectPlan = InspectPlan{
					Id:                  "plan-1",
					Name:                "Inspection Plan 1",
					ClusterName:         "Test Cluster",
					Author:              "Author A",
					Scene:               "scene-1",
					ExecuteStrategyTime: time.Now(),
					NodeList:            `["node1", "node2"]`,
					Status:              "finished",
					DesignPlanPerson:    "Designer A",
					CreateMode:          1,
					CreateStrategy:      2,
					InspectTaskName:     `["task1", "task2"]`,
				}
				return nil
			})
			defer patchGetPlanValue.Reset()

			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListStrAndInspectTaskNameStr, func(planList InspectPlan) ([]string, []string, error) {
				return []string{"node1", "node2"}, []string{"task1", "task2"}, nil
			})
			defer patchGetNodeList.Reset()

			patchGetExecuteStrategy := gomonkey.ApplyFunc(GetexecuteStrategyTimeAndinspectionTask, func(inspectionPlan map[string]interface{}) (time.Time, string, error) {
				return time.Now(), "task1", nil
			})
			defer patchGetExecuteStrategy.Reset()

			data, err := GetOneInspect(clusterId, id)
			So(err, ShouldNotBeNil)
			So(data, ShouldBeNil)
		})

		Convey("error case when QueryResourceById fails", func() {
			patchQuery := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return nil, fmt.Errorf("mock query error")
			})
			defer patchQuery.Reset()

			data, err := GetOneInspect(clusterId, id)
			So(err, ShouldNotBeNil)
			So(data, ShouldBeNil)
		})

		Convey("error case when GetPlanValue fails", func() {
			patchGetPlanValue := gomonkey.ApplyFunc(GetPlanValue, func(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
				return fmt.Errorf("mock GetPlanValue error")
			})
			defer patchGetPlanValue.Reset()

			data, err := GetOneInspect(clusterId, id)
			So(err, ShouldNotBeNil)
			So(data, ShouldBeNil)
		})

		Convey("error case when GetNodeListStrAndInspectTaskNameStr fails", func() {
			patchGetPlanValue := gomonkey.ApplyFunc(GetPlanValue, func(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
				*inspectPlan = InspectPlan{
					Id:   "plan-1",
					Name: "Inspection Plan 1",
				}
				return nil
			})
			defer patchGetPlanValue.Reset()

			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListStrAndInspectTaskNameStr, func(planList InspectPlan) ([]string, []string, error) {
				return nil, nil, fmt.Errorf("mock GetNodeListStrAndInspectTaskNameStr error")
			})
			defer patchGetNodeList.Reset()

			data, err := GetOneInspect(clusterId, id)
			So(err, ShouldNotBeNil)
			So(data, ShouldBeNil)
		})

		Convey("error case when GetexecuteStrategyTimeAndinspectionTask fails", func() {
			patchGetPlanValue := gomonkey.ApplyFunc(GetPlanValue, func(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
				*inspectPlan = InspectPlan{
					Id:   "plan-1",
					Name: "Inspection Plan 1",
				}
				return nil
			})
			defer patchGetPlanValue.Reset()

			patchGetNodeList := gomonkey.ApplyFunc(GetNodeListStrAndInspectTaskNameStr, func(planList InspectPlan) ([]string, []string, error) {
				return []string{"node1"}, []string{"task1"}, nil
			})
			defer patchGetNodeList.Reset()

			patchGetExecuteStrategy := gomonkey.ApplyFunc(GetexecuteStrategyTimeAndinspectionTask, func(inspectionPlan map[string]interface{}) (time.Time, string, error) {
				return time.Time{}, "", fmt.Errorf("mock GetexecuteStrategyTimeAndinspectionTask error")
			})
			defer patchGetExecuteStrategy.Reset()

			data, err := GetOneInspect(clusterId, id)
			So(err, ShouldNotBeNil)
			So(data, ShouldBeNil)
		})
	})
}

func TestGetexecuteStrategyTimeAndinspectionTask(t *testing.T) {
	Convey("TestGetexecuteStrategyTimeAndinspectionTask", t, func() {
		// 创建一个有效的 inspectionPlan
		executeTime := time.Now()
		inspectionPlan := map[string]interface{}{
			"executeStrategyTime": executeTime,
			"inspectionTask":      "task1",
		}

		Convey("successful case", func() {
			executeStrategyTime, inspectionTask, err := GetexecuteStrategyTimeAndinspectionTask(inspectionPlan)
			So(err, ShouldBeNil)
			So(executeStrategyTime, ShouldEqual, executeTime)
			So(inspectionTask, ShouldEqual, "task1")
		})

		Convey("error case when executeStrategyTime is not of type time.Time", func() {
			// 修改 inspectionPlan 使 executeStrategyTime 不是 time.Time 类型
			inspectionPlan["executeStrategyTime"] = "invalid_time"

			executeStrategyTime, inspectionTask, err := GetexecuteStrategyTimeAndinspectionTask(inspectionPlan)
			So(err, ShouldNotBeNil)
			So(executeStrategyTime.IsZero(), ShouldBeTrue)
			So(inspectionTask, ShouldEqual, "")
		})

		Convey("error case when inspectionTask is not of type string", func() {
			// 修改 inspectionPlan 使 inspectionTask 不是 string 类型
			inspectionPlan["inspectionTask"] = 12345

			executeStrategyTime, inspectionTask, err := GetexecuteStrategyTimeAndinspectionTask(inspectionPlan)
			So(err, ShouldNotBeNil)
			So(executeStrategyTime.IsZero(), ShouldBeTrue)
			So(inspectionTask, ShouldEqual, "")
		})
	})
}

func TestGetActivateInspectToWsmReq(t *testing.T) {
	Convey("TestGetActivateInspectToWsmReq", t, func() {
		// 创建一个有效的 inspectionPlan
		executeTime := time.Now()
		inspectionPlan := map[string]interface{}{
			"executeStrategyTime": executeTime,
			"inspectionTask":      "task1",
			"id":                  "plan-1",
			"name":                "Inspection Plan 1",
			"createMode":          1,
			"scene":               "scene-1",
			"nodeList":            `["node1", "node2"]`,
			"createStrategy":      2,
		}

		Convey("successful case", func() {
			patchGetPlanValue := gomonkey.ApplyFunc(GetPlanValue, func(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
				*inspectPlan = InspectPlan{
					Id:             "plan-1",
					Name:           "Inspection Plan 1",
					CreateMode:     1,
					Scene:          "scene-1",
					NodeList:       `["node1", "node2"]`,
					CreateStrategy: 2,
				}
				return nil
			})
			defer patchGetPlanValue.Reset()

			patchStringToSlice := gomonkey.ApplyFunc(cwsmutils.StringToSlice, func(s string) ([]string, error) {
				return []string{"node1", "node2"}, nil
			})
			defer patchStringToSlice.Reset()

			patchGetExecuteStrategy := gomonkey.ApplyFunc(GetexecuteStrategyTimeAndinspectionTask, func(inspectionPlan map[string]interface{}) (time.Time, string, error) {
				return executeTime, "task1", nil
			})
			defer patchGetExecuteStrategy.Reset()

			patchStructToMap := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(in interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{
					"id":                  "plan-1",
					"name":                "Inspection Plan 1",
					"createMode":          1,
					"scene":               "scene-1",
					"nodeList":            []string{"node1", "node2"},
					"createStrategy":      2,
					"executeStrategyTime": executeTime.Format(time.RFC3339),
					"inspectionTask":      json.RawMessage(`"task1"`),
				}, nil
			})
			defer patchStructToMap.Reset()

			activateData, inspectPlanResult, err := GetActivateInspectToWsmReq(inspectionPlan)
			So(err, ShouldBeNil)
			So(activateData, ShouldNotBeNil)
			So(inspectPlanResult, ShouldNotBeNil)
			So(inspectPlanResult.Id, ShouldEqual, "plan-1")
		})

		Convey("error case when GetPlanValue fails", func() {
			patchGetPlanValue := gomonkey.ApplyFunc(GetPlanValue, func(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
				return fmt.Errorf("mock GetPlanValue error")
			})
			defer patchGetPlanValue.Reset()

			activateData, inspectPlanResult, err := GetActivateInspectToWsmReq(inspectionPlan)
			So(err, ShouldNotBeNil)
			So(activateData, ShouldBeNil)
			So(inspectPlanResult, ShouldBeNil)
		})

		Convey("error case when StringToSlice fails", func() {
			patchGetPlanValue := gomonkey.ApplyFunc(GetPlanValue, func(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
				*inspectPlan = InspectPlan{
					Id:             "plan-1",
					Name:           "Inspection Plan 1",
					CreateMode:     1,
					Scene:          "scene-1",
					NodeList:       `["node1", "node2"]`,
					CreateStrategy: 2,
				}
				return nil
			})
			defer patchGetPlanValue.Reset()

			patchStringToSlice := gomonkey.ApplyFunc(cwsmutils.StringToSlice, func(s string) ([]string, error) {
				return nil, fmt.Errorf("mock StringToSlice error")
			})
			defer patchStringToSlice.Reset()

			activateData, inspectPlanResult, err := GetActivateInspectToWsmReq(inspectionPlan)
			So(err, ShouldNotBeNil)
			So(activateData, ShouldBeNil)
			So(inspectPlanResult, ShouldBeNil)
		})

		Convey("error case when GetexecuteStrategyTimeAndinspectionTask fails", func() {
			patchGetPlanValue := gomonkey.ApplyFunc(GetPlanValue, func(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
				*inspectPlan = InspectPlan{
					Id:             "plan-1",
					Name:           "Inspection Plan 1",
					CreateMode:     1,
					Scene:          "scene-1",
					NodeList:       `["node1", "node2"]`,
					CreateStrategy: 2,
				}
				return nil
			})
			defer patchGetPlanValue.Reset()

			patchStringToSlice := gomonkey.ApplyFunc(cwsmutils.StringToSlice, func(s string) ([]string, error) {
				return []string{"node1", "node2"}, nil
			})
			defer patchStringToSlice.Reset()

			patchGetExecuteStrategy := gomonkey.ApplyFunc(GetexecuteStrategyTimeAndinspectionTask, func(inspectionPlan map[string]interface{}) (time.Time, string, error) {
				return time.Time{}, "", fmt.Errorf("mock GetexecuteStrategyTimeAndinspectionTask error")
			})
			defer patchGetExecuteStrategy.Reset()

			activateData, inspectPlanResult, err := GetActivateInspectToWsmReq(inspectionPlan)
			So(err, ShouldNotBeNil)
			So(activateData, ShouldBeNil)
			So(inspectPlanResult, ShouldBeNil)
		})

		Convey("error case when Struct2Map fails", func() {
			patchGetPlanValue := gomonkey.ApplyFunc(GetPlanValue, func(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
				*inspectPlan = InspectPlan{
					Id:             "plan-1",
					Name:           "Inspection Plan 1",
					CreateMode:     1,
					Scene:          "scene-1",
					NodeList:       `["node1", "node2"]`,
					CreateStrategy: 2,
				}
				return nil
			})
			defer patchGetPlanValue.Reset()

			patchStringToSlice := gomonkey.ApplyFunc(cwsmutils.StringToSlice, func(s string) ([]string, error) {
				return []string{"node1", "node2"}, nil
			})
			defer patchStringToSlice.Reset()

			patchGetExecuteStrategy := gomonkey.ApplyFunc(GetexecuteStrategyTimeAndinspectionTask, func(inspectionPlan map[string]interface{}) (time.Time, string, error) {
				return executeTime, "task1", nil
			})
			defer patchGetExecuteStrategy.Reset()

			patchStructToMap := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(in interface{}) (map[string]interface{}, error) {
				return nil, fmt.Errorf("mock Struct2Map error")
			})
			defer patchStructToMap.Reset()

			activateData, inspectPlanResult, err := GetActivateInspectToWsmReq(inspectionPlan)
			So(err, ShouldNotBeNil)
			So(activateData, ShouldBeNil)
			So(inspectPlanResult, ShouldBeNil)
		})
	})
}

func TestGetValuesFromWsm(t *testing.T) {
	Convey("Test getValuesFromWsm", t, func() {
		id := "test-plan-id"
		resultId := "test-result-id"
		clusterId := "test-cluster"
		author := "test-author"
		currentTime := time.Now()

		_, cancel := context.WithCancel(context.Background())
		defer cancel()

		// Mock responses for required functions
		patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			if idValue == id {
				return map[string]interface{}{"status": "running"}, nil
			}
			return map[string]interface{}{"status": "stopped"}, nil
		})
		defer patchQueryResourceById.Reset()

		Convey("Successful case", func() {
			go getValuesFromWsm(id, resultId, clusterId, currentTime, author)

			time.Sleep(1 * time.Second) // Allow time for goroutines to run
			cancel()                    // Cancel the context to stop the goroutine
		})

		Convey("When QueryResourceById fails", func() {
			patchQueryResourceById.Reset()
			patchQueryResourceById = gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return nil, errors.New("mock QueryResourceById error")
			})

			go getValuesFromWsm(id, resultId, clusterId, currentTime, author)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When plan status is not a string", func() {
			patchQueryResourceById.Reset()
			patchQueryResourceById = gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return map[string]interface{}{"status": 123}, nil // Invalid status type
			})

			go getValuesFromWsm(id, resultId, clusterId, currentTime, author)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When result status is not a string", func() {
			patchQueryResourceById.Reset()
			patchQueryResourceById = gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				if idValue == resultId {
					return map[string]interface{}{"status": 123}, nil // Invalid status type
				}
				return map[string]interface{}{"status": "running"}, nil
			})

			go getValuesFromWsm(id, resultId, clusterId, currentTime, author)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When result status is stopped", func() {
			patchQueryResourceById.Reset()
			patchQueryResourceById = gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return map[string]interface{}{"status": "stopped"}, nil
			})

			go getValuesFromWsm(id, resultId, clusterId, currentTime, author)

			time.Sleep(1 * time.Second)
			cancel()
		})
	})
}

func TestGetInspectResult(t *testing.T) {
	Convey("Test getInspectResult", t, func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		clusterId := "test-cluster"
		planId := "test-plan"
		id := "test-id"
		currentTime := time.Now()
		author := "test-author"

		Convey("normal execution process", func() {
			// Mock GetInspectResult
			patchGetInspectResult := gomonkey.ApplyFunc(GetInspectResult, func(clusterId string, id string, resultId string, currentTime time.Time, author string) error {
				return nil
			})
			defer patchGetInspectResult.Reset()

			go getInspectResult(clusterId, id, planId, currentTime, author, ctx)

			time.Sleep(1 * time.Millisecond)
			cancel()
		})

		Convey("plan not found", func() {
			// Mock GetInspectResult
			patchGetInspectResult := gomonkey.ApplyFunc(GetInspectResult, func(clusterId string, id string, resultId string, currentTime time.Time, author string) error {
				return fmt.Errorf("plan not found")
			})
			defer patchGetInspectResult.Reset()

			// Mock UpdateStatusForPlanAndResultTable
			patchUpdateStatus := gomonkey.ApplyFunc(UpdateStatusForPlanAndResultTable, func(id string, resultId string) error {
				return nil
			})
			defer patchUpdateStatus.Reset()

			go getInspectResult(clusterId, id, planId, currentTime, author, ctx)

			time.Sleep(1 * time.Millisecond)
			cancel()
		})

		Convey("failed to obtain the inspection results", func() {
			// Mock GetInspectResult
			patchGetInspectResult := gomonkey.ApplyFunc(GetInspectResult, func(clusterId string, id string, resultId string, currentTime time.Time, author string) error {
				return fmt.Errorf("error get inspect result")
			})
			defer patchGetInspectResult.Reset()

			go getInspectResult(clusterId, id, planId, currentTime, author, ctx)

			time.Sleep(1 * time.Millisecond)
			cancel()
		})

		Convey("context cancellation case", func() {
			// Mock GetInspectResult
			patchGetInspectResult := gomonkey.ApplyFunc(GetInspectResult, func(clusterId string, id string, resultId string, currentTime time.Time, author string) error {
				return nil
			})
			defer patchGetInspectResult.Reset()

			cancel()

			go getInspectResult(clusterId, id, planId, currentTime, author, ctx)

			time.Sleep(1 * time.Millisecond)
		})

		Convey("UpdateStatusForPlanAndResultTable failed", func() {
			// Mock GetInspectResult
			patchGetInspectResult := gomonkey.ApplyFunc(GetInspectResult, func(clusterId string, id string, resultId string, currentTime time.Time, author string) error {
				return fmt.Errorf("plan not found")
			})
			defer patchGetInspectResult.Reset()

			// Mock UpdateStatusForPlanAndResultTable
			patchUpdateStatus := gomonkey.ApplyFunc(UpdateStatusForPlanAndResultTable, func(id string, resultId string) error {
				return fmt.Errorf("update status failed")
			})
			defer patchUpdateStatus.Reset()

			go getInspectResult(clusterId, id, planId, currentTime, author, ctx)

			time.Sleep(1 * time.Millisecond)
			cancel()
		})
	})
}

func TestInspectionResult(t *testing.T) {
	Convey("Test InspectionResult", t, func() {
		inspectionPlan := map[string]interface{}{
			"id": "test-plan-id",
		}
		resultId := "test-result-id"
		currentTime := time.Now()
		author := "test-author"
		id := "test-id"

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock UpdatePlanResultId to succeed
		patches.ApplyFunc(UpdatePlanResultId, func(planId string, planResultId string) error {
			return nil
		})

		// Mock Struct2Map to succeed
		patches.ApplyFunc(cwsmutils.Struct2Map, func(in interface{}) (map[string]interface{}, error) {
			return map[string]interface{}{
				"id":                id,
				"planId":            inspectionPlan["id"].(string),
				"resultId":          resultId,
				"executeResult":     "not implemented",
				"status":            "",
				"startTime":         currentTime,
				"finishTime":        time.Time{},
				"executePlanPerson": author,
				"completedCount":    0,
				"remainingTime":     0,
				"inspectionResult":  "",
			}, nil
		})

		// Mock InsertNewResource to succeed
		patches.ApplyFunc(models.InsertNewResource, func(tableName string, newResource map[string]interface{}) bool {
			return true
		})

		Convey("Successful case", func() {
			err := InspectionResult(inspectionPlan, resultId, currentTime, author, id)
			So(err, ShouldBeNil)
		})

		Convey("When UpdatePlanResultId fails", func() {
			patches.ApplyFunc(UpdatePlanResultId, func(planId string, planResultId string) error {
				return errors.New("mock UpdatePlanResultId error")
			})

			err := InspectionResult(inspectionPlan, resultId, currentTime, author, id)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanResultId error")
		})

		Convey("When Struct2Map fails", func() {
			patches.ApplyFunc(cwsmutils.Struct2Map, func(in interface{}) (map[string]interface{}, error) {
				return nil, errors.New("mock Struct2Map error")
			})

			err := InspectionResult(inspectionPlan, resultId, currentTime, author, id)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock Struct2Map error")
		})

		Convey("When InsertNewResource fails", func() {
			patches.ApplyFunc(models.InsertNewResource, func(tableName string, newResource map[string]interface{}) bool {
				return false
			})

			err := InspectionResult(inspectionPlan, resultId, currentTime, author, id)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "insert new resource is error")
		})
	})
}

func TestGetInspectResults(t *testing.T) {
	Convey("Test GetInspectResult", t, func() {
		clusterId := "test-cluster"
		planId := "test-plan-id"
		id := "test-id"
		currentTime := time.Now()
		author := "test-author"

		Convey("normal execution process", func() {
			// Mock InspectHandler
			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return map[string]interface{}{
					"id":               planId,
					"inspectionResult": map[string]interface{}{"key": "value"},
					"status":           "running",
				}, "", true
			})
			defer patchInspectHandler.Reset()

			// Mock GetResultValues
			patchGetResultValues := gomonkey.ApplyFunc(GetResultValues, func(inspectionResult map[string]interface{}, resultValues *ResultValues) error {
				resultValues.Status = "running"
				resultValues.ExecuteResult = "some result"
				return nil
			})
			defer patchGetResultValues.Reset()

			// Mock GetFinishTimeAndExecuteResult
			patchGetFinishTime := gomonkey.ApplyFunc(GetFinishTimeAndExecuteResult, func(status string, resultValues *ResultValues) (time.Time, string, error) {
				return time.Now(), resultValues.ExecuteResult, nil
			})
			defer patchGetFinishTime.Reset()

			// Mock UpdateStatus
			patchUpdateStatus := gomonkey.ApplyFunc(UpdateStatus, func(inspectResult map[string]interface{}, id string, status string, executeResult string) error {
				return nil
			})
			defer patchUpdateStatus.Reset()

			// Mock UpdateResult
			patchUpdateResult := gomonkey.ApplyFunc(UpdateResult, func(id string, finishTime time.Time, inspectionResultJSON []byte) error {
				return nil
			})
			defer patchUpdateResult.Reset()

			err := GetInspectResult(clusterId, planId, id, currentTime, author)
			So(err, ShouldBeNil)
		})

		Convey("InspectHandler failed", func() {
			// Mock InspectHandler
			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return nil, "mock InspectHandler error", false
			})
			defer patchInspectHandler.Reset()

			err := GetInspectResult(clusterId, planId, id, currentTime, author)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock InspectHandler error")
		})

		Convey("plan not found", func() {
			// Mock InspectHandler
			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return map[string]interface{}{
					"err": "plan not found",
				}, "", false
			})
			defer patchInspectHandler.Reset()

			err := GetInspectResult(clusterId, planId, id, currentTime, author)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "plan not found")
		})

		Convey("GetResultValues failed", func() {
			// Mock InspectHandler
			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return map[string]interface{}{
					"id":               planId,
					"inspectionResult": map[string]interface{}{"key": "value"},
					"status":           "running",
				}, "", true
			})
			defer patchInspectHandler.Reset()

			// Mock GetResultValues
			patchGetResultValues := gomonkey.ApplyFunc(GetResultValues, func(inspectionResult map[string]interface{}, resultValues *ResultValues) error {
				return errors.New("mock GetResultValues error")
			})
			defer patchGetResultValues.Reset()

			err := GetInspectResult(clusterId, planId, id, currentTime, author)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock GetResultValues error")
		})

		Convey("GetFinishTimeAndExecuteResult failed", func() {
			// Mock InspectHandler
			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return map[string]interface{}{
					"id":               planId,
					"inspectionResult": map[string]interface{}{"key": "value"},
					"status":           "running",
				}, "", true
			})
			defer patchInspectHandler.Reset()

			// Mock GetResultValues
			patchGetResultValues := gomonkey.ApplyFunc(GetResultValues, func(inspectionResult map[string]interface{}, resultValues *ResultValues) error {
				resultValues.Status = "running"
				resultValues.ExecuteResult = "some result"
				return nil
			})
			defer patchGetResultValues.Reset()

			// Mock GetFinishTimeAndExecuteResult
			patchGetFinishTime := gomonkey.ApplyFunc(GetFinishTimeAndExecuteResult, func(status string, resultValues *ResultValues) (time.Time, string, error) {
				return time.Time{}, "", errors.New("mock GetFinishTimeAndExecuteResult error")
			})
			defer patchGetFinishTime.Reset()

			err := GetInspectResult(clusterId, planId, id, currentTime, author)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "get finish time and executeresult is failed")
		})

		Convey("UpdateStatus failed", func() {
			// Mock InspectHandler
			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return map[string]interface{}{
					"id":               planId,
					"inspectionResult": map[string]interface{}{"key": "value"},
					"status":           "running",
				}, "", true
			})
			defer patchInspectHandler.Reset()

			// Mock GetResultValues
			patchGetResultValues := gomonkey.ApplyFunc(GetResultValues, func(inspectionResult map[string]interface{}, resultValues *ResultValues) error {
				resultValues.Status = "running"
				resultValues.ExecuteResult = "some result"
				return nil
			})
			defer patchGetResultValues.Reset()

			// Mock GetFinishTimeAndExecuteResult
			patchGetFinishTime := gomonkey.ApplyFunc(GetFinishTimeAndExecuteResult, func(status string, resultValues *ResultValues) (time.Time, string, error) {
				return time.Now(), resultValues.ExecuteResult, nil
			})
			defer patchGetFinishTime.Reset()

			// Mock UpdateStatus
			patchUpdateStatus := gomonkey.ApplyFunc(UpdateStatus, func(inspectResult map[string]interface{}, id string, status string, executeResult string) error {
				return errors.New("mock UpdateStatus error")
			})
			defer patchUpdateStatus.Reset()

			err := GetInspectResult(clusterId, planId, id, currentTime, author)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateStatus error")
		})

		Convey("UpdateResult failed", func() {
			// Mock InspectHandler
			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return map[string]interface{}{
					"id":               planId,
					"inspectionResult": map[string]interface{}{"key": "value"},
					"status":           "running",
				}, "", true
			})
			defer patchInspectHandler.Reset()

			// Mock GetResultValues
			patchGetResultValues := gomonkey.ApplyFunc(GetResultValues, func(inspectionResult map[string]interface{}, resultValues *ResultValues) error {
				resultValues.Status = "running"
				resultValues.ExecuteResult = "some result"
				return nil
			})
			defer patchGetResultValues.Reset()

			// Mock GetFinishTimeAndExecuteResult
			patchGetFinishTime := gomonkey.ApplyFunc(GetFinishTimeAndExecuteResult, func(status string, resultValues *ResultValues) (time.Time, string, error) {
				return time.Now(), resultValues.ExecuteResult, nil
			})
			defer patchGetFinishTime.Reset()

			// Mock UpdateStatus
			patchUpdateStatus := gomonkey.ApplyFunc(UpdateStatus, func(inspectResult map[string]interface{}, id string, status string, executeResult string) error {
				return nil
			})
			defer patchUpdateStatus.Reset()

			// Mock UpdateResult
			patchUpdateResult := gomonkey.ApplyFunc(UpdateResult, func(id string, finishTime time.Time, inspectionResultJSON []byte) error {
				return errors.New("mock UpdateResult error")
			})
			defer patchUpdateResult.Reset()

			err := GetInspectResult(clusterId, planId, id, currentTime, author)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateResult error")
		})
	})
}

func TestGetFinishTimeAndExecuteResult(t *testing.T) {
	Convey("Test GetFinishTimeAndExecuteResult", t, func() {
		resultValues := &ResultValues{
			ExecuteResult: "some result",
		}

		Convey("When status is 'finished'", func() {
			finishTime, executeResult, err := GetFinishTimeAndExecuteResult("finished", resultValues)

			So(err, ShouldBeNil)
			So(finishTime.UnixNano(), ShouldNotEqual, time.Time{}.UnixNano()) // Check not zero time
			So(executeResult, ShouldEqual, "some result")
		})

		Convey("When status is 'stopped'", func() {
			finishTime, executeResult, err := GetFinishTimeAndExecuteResult("stopped", resultValues)

			So(err, ShouldBeNil)
			So(finishTime.UnixNano(), ShouldNotEqual, time.Time{}.UnixNano()) // Check not zero time
			So(executeResult, ShouldEqual, "some result")
		})

		Convey("When status is 'running'", func() {
			finishTime, executeResult, err := GetFinishTimeAndExecuteResult("running", resultValues)

			So(err, ShouldBeNil)
			So(finishTime, ShouldEqual, time.Time{}) // Should be zero time
			So(executeResult, ShouldEqual, "not implemented")
		})

		Convey("When status is 'not implemented'", func() {
			finishTime, executeResult, err := GetFinishTimeAndExecuteResult("not implemented", resultValues)

			So(err, ShouldBeNil)
			So(finishTime, ShouldEqual, time.Time{}) // Should be zero time
			So(executeResult, ShouldEqual, "not implemented")
		})

		Convey("When status is something else", func() {
			finishTime, executeResult, err := GetFinishTimeAndExecuteResult("unknown", resultValues)

			So(err, ShouldBeNil)
			So(finishTime, ShouldEqual, time.Time{}) // Should be zero time
			So(executeResult, ShouldEqual, "some result")
		})
	})
}

func TestUpdateStatus(t *testing.T) {
	Convey("Test UpdateStatus", t, func() {
		inspectResult := map[string]interface{}{
			"id": "test-plan-id",
		}
		id := "test-id"
		status := "completed"
		executeResult := "some result"

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock UpdatePlanStatus to succeed
		patches.ApplyFunc(UpdatePlanStatus, func(planId string, status string) error {
			return nil
		})

		// Mock UpdateResultStatus to succeed
		patches.ApplyFunc(UpdateResultStatus, func(planId string, status string) error {
			return nil
		})

		// Mock UpdateExecuteResultStatus to succeed
		patches.ApplyFunc(UpdateExecuteResultStatus, func(planId string, executeResult string) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateStatus(inspectResult, id, status, executeResult)
			So(err, ShouldBeNil)
		})

		Convey("When UpdatePlanStatus fails", func() {
			patches.ApplyFunc(UpdatePlanStatus, func(planId string, status string) error {
				return errors.New("mock UpdatePlanStatus error")
			})

			err := UpdateStatus(inspectResult, id, status, executeResult)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanStatus error")
		})

		Convey("When UpdateResultStatus fails", func() {
			patches.ApplyFunc(UpdateResultStatus, func(planId string, status string) error {
				return errors.New("mock UpdateResultStatus error")
			})

			err := UpdateStatus(inspectResult, id, status, executeResult)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateResultStatus error")
		})

		Convey("When UpdateExecuteResultStatus fails", func() {
			patches.ApplyFunc(UpdateExecuteResultStatus, func(planId string, executeResult string) error {
				return errors.New("mock UpdateExecuteResultStatus error")
			})

			err := UpdateStatus(inspectResult, id, status, executeResult)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateExecuteResultStatus error")
		})

		Convey("When executeResult is 'not implemented'", func() {
			err := UpdateStatus(inspectResult, id, status, "not implemented")
			So(err, ShouldBeNil) // Should succeed without calling UpdateExecuteResultStatus
		})
	})
}

func TestUpdateResult(t *testing.T) {
	Convey("Test UpdateResult", t, func() {
		id := "test-id"
		finishTime := time.Now()
		inspectionResultJSON := []byte(`{"key": "value"}`)

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock UpdateFinishTimeValues to succeed
		patches.ApplyFunc(UpdateFinishTimeValues, func(id string, finishTime time.Time) error {
			return nil
		})

		// Mock UpdateInspectionResultValues to succeed
		patches.ApplyFunc(UpdateInspectionResultValues, func(id string, inspectionResult string) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateResult(id, finishTime, inspectionResultJSON)
			So(err, ShouldBeNil)
		})

		Convey("When UpdateFinishTimeValues fails", func() {
			patches.ApplyFunc(UpdateFinishTimeValues, func(id string, finishTime time.Time) error {
				return errors.New("mock UpdateFinishTimeValues error")
			})

			err := UpdateResult(id, finishTime, inspectionResultJSON)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateFinishTimeValues error")
		})

		Convey("When UpdateInspectionResultValues fails", func() {
			patches.ApplyFunc(UpdateInspectionResultValues, func(id string, inspectionResult string) error {
				return errors.New("mock UpdateInspectionResultValues error")
			})

			err := UpdateResult(id, finishTime, inspectionResultJSON)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateInspectionResultValues error")
		})
	})
}

/* Started by AICoder, pid:p9223y40b6rd37414716090142655217c743802f */
func TestUpdateFunctions(t *testing.T) {
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	Convey("Test UpdatePlanStatus", t, func() {
		planId := "test-plan-id"
		status := "completed"

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdatePlanStatus(planId, status)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdatePlanStatus(planId, status)
		So(err, ShouldBeNil)
	})

	Convey("Test UpdatePlanResultId", t, func() {
		planId := "test-plan-id"
		planResultId := "test-result-id"

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdatePlanResultId(planId, planResultId)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdatePlanResultId(planId, planResultId)
		So(err, ShouldBeNil)
	})

	Convey("Test UpdateResultStatus", t, func() {
		planId := "test-plan-id"
		status := "completed"

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdateResultStatus(planId, status)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdateResultStatus(planId, status)
		So(err, ShouldBeNil)
	})

	Convey("Test UpdateExecuteResultStatus", t, func() {
		planId := "test-plan-id"
		executeResult := "some result"

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdateExecuteResultStatus(planId, executeResult)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdateExecuteResultStatus(planId, executeResult)
		So(err, ShouldBeNil)
	})

	Convey("Test UpdateFinishTimeValues", t, func() {
		id := "test-id"
		finishTime := time.Now()

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdateFinishTimeValues(id, finishTime)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdateFinishTimeValues(id, finishTime)
		So(err, ShouldBeNil)
	})

	Convey("Test UpdateResultId", t, func() {
		planId := "test-plan-id"
		planResultId := "test-result-id"

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdateResultId(planId, planResultId)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdateResultId(planId, planResultId)
		So(err, ShouldBeNil)
	})

	Convey("Test UpdateInspectionResultValues", t, func() {
		id := "test-id"
		inspectionResult := `{"key": "value"}`

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdateInspectionResultValues(id, inspectionResult)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdateInspectionResultValues(id, inspectionResult)
		So(err, ShouldBeNil)
	})

	Convey("Test UpdateStopInspectionResultValues", t, func() {
		id := "test-id"
		inspectionResult := `{"key": "value"}`

		patches.ApplyFunc(models.UpdateResourceByResultId, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdateStopInspectionResultValues(id, inspectionResult)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceByResultId, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdateStopInspectionResultValues(id, inspectionResult)
		So(err, ShouldBeNil)
	})

	Convey("Test UpdateStartTimeValues", t, func() {
		id := "test-id"
		startTime := time.Now()

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock a successful update
		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		Convey("Successful case", func() {
			err := UpdateStartTimeValues(id, startTime)
			So(err, ShouldBeNil)
		})

		Convey("When the update fails", func() {
			patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
				return false
			})

			err := UpdateStartTimeValues(id, startTime)
			So(err, ShouldBeNil) // Function does not return error
		})
	})
	Convey("Test UpdatePlanExecuteResult", t, func() {
		planId := "test-plan-id"
		executeResult := "success"

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock UpdateResourceById to succeed
		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		Convey("Successful case", func() {
			err := UpdatePlanExecuteResult(planId, executeResult)
			So(err, ShouldBeNil)
		})

		Convey("When UpdateResourceById fails", func() {
			patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
				return false
			})

			err := UpdatePlanExecuteResult(planId, executeResult)
			So(err, ShouldBeNil) // Function does not return error
		})
	})

	Convey("Test UpdatePlanExecuteStartTime", t, func() {
		planId := "test-plan-id"
		executeStartTime := time.Now()

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		Convey("Successful case", func() {
			err := UpdatePlanExecuteStartTime(planId, executeStartTime)
			So(err, ShouldBeNil)
		})

		Convey("When UpdateResourceById fails", func() {
			patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
				return false
			})

			err := UpdatePlanExecuteStartTime(planId, executeStartTime)
			So(err, ShouldBeNil) // Function does not return error
		})
	})

	Convey("Test UpdatePlanExecuteEndTime", t, func() {
		planId := "test-plan-id"
		executeEndTime := time.Now()

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		Convey("Successful case", func() {
			err := UpdatePlanExecuteEndTime(planId, executeEndTime)
			So(err, ShouldBeNil)
		})

		Convey("When UpdateResourceById fails", func() {
			patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
				return false
			})

			err := UpdatePlanExecuteEndTime(planId, executeEndTime)
			So(err, ShouldBeNil) // Function does not return error
		})
	})

	Convey("Test UpdatePlanExecutePlanPerson", t, func() {
		planId := "test-plan-id"
		executePlanPerson := "John Doe"

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		Convey("Successful case", func() {
			err := UpdatePlanExecutePlanPerson(planId, executePlanPerson)
			So(err, ShouldBeNil)
		})

		Convey("When UpdateResourceById fails", func() {
			patches.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
				return false
			})

			err := UpdatePlanExecutePlanPerson(planId, executePlanPerson)
			So(err, ShouldBeNil) // Function does not return error
		})
	})

	Convey("Test UpdateStopResultStatus", t, func() {
		planId := "test-plan-id"
		status := "stopped"

		patches.ApplyFunc(models.UpdateResourceByResultId, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdateStopResultStatus(planId, status)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceByResultId, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdateStopResultStatus(planId, status)
		So(err, ShouldBeNil)
	})

	Convey("Test UpdateStopExecuteResultStatus", t, func() {
		planId := "test-plan-id"
		executeResult := "stopped result"

		patches.ApplyFunc(models.UpdateResourceByResultId, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdateStopExecuteResultStatus(planId, executeResult)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceByResultId, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdateStopExecuteResultStatus(planId, executeResult)
		So(err, ShouldBeNil)
	})

	Convey("Test UpdateStopFinishTimeValues", t, func() {
		id := "test-id"
		finishTime := time.Now()

		patches.ApplyFunc(models.UpdateResourceByResultId, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdateStopFinishTimeValues(id, finishTime)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceByResultId, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdateStopFinishTimeValues(id, finishTime)
		So(err, ShouldBeNil)
	})

	Convey("Test UpdateStopStartTimeValues", t, func() {
		id := "test-id"
		startTime := time.Now()

		patches.ApplyFunc(models.UpdateResourceByResultId, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return true
		})

		err := UpdateStopStartTimeValues(id, startTime)
		So(err, ShouldBeNil)

		patches.ApplyFunc(models.UpdateResourceByResultId, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
			return false
		})

		err = UpdateStopStartTimeValues(id, startTime)
		So(err, ShouldBeNil)
	})

}

/* Ended by AICoder, pid:p9223y40b6rd37414716090142655217c743802f */

/* Started by AICoder, pid:23c33s2357r9ebd14a6208a620b76d497b65d1e8 */
func TestUpdateTime(t *testing.T) {
	Convey("Test UpdateTime", t, func() {
		id := "test-id"
		finishTime := time.Now()
		startTime := time.Now().Add(-1 * time.Hour) // 假设开始时间比结束时间早

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock UpdateFinishTimeValues to succeed
		patches.ApplyFunc(UpdateFinishTimeValues, func(id string, finishTime time.Time) error {
			return nil
		})

		// Mock UpdateStartTimeValues to succeed
		patches.ApplyFunc(UpdateStartTimeValues, func(id string, startTime time.Time) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateTime(id, finishTime, startTime)
			So(err, ShouldBeNil)
		})

		Convey("When UpdateFinishTimeValues fails", func() {
			patches.ApplyFunc(UpdateFinishTimeValues, func(id string, finishTime time.Time) error {
				return errors.New("mock UpdateFinishTimeValues error")
			})

			err := UpdateTime(id, finishTime, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateFinishTimeValues error")
		})

		Convey("When UpdateStartTimeValues fails", func() {
			patches.ApplyFunc(UpdateStartTimeValues, func(id string, startTime time.Time) error {
				return errors.New("mock UpdateStartTimeValues error")
			})

			err := UpdateTime(id, finishTime, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateStartTimeValues error")
		})
	})
}

/* Ended by AICoder, pid:23c33s2357r9ebd14a6208a620b76d497b65d1e8 */

func TestGetPlanResult(t *testing.T) {
	Convey("Test GetPlanResult", t, func() {
		inspectionResult := map[string]interface{}{
			"id":                "test-id",
			"resultId":          "result-id",
			"startTime":         "2023-01-01T00:00:00Z",
			"endTime":           "2023-01-01T01:00:00Z",
			"executePlanPerson": "test-person",
			"executeResult":     "success",
			"status":            "completed",
			"completedCount":    10,
			"remainingTime":     5,
		}
		var planResult PlanResult

		// Normal case: successful marshaling and unmarshaling
		err := GetPlanResult(inspectionResult, &planResult)
		So(err, ShouldBeNil)
		So(planResult.Id, ShouldEqual, "test-id")

		// Error case: marshaling error
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		patches.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
			return nil, errors.New("mock marshaling error")
		})

		err = GetPlanResult(inspectionResult, &planResult)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock marshaling error")

		// Error case: unmarshaling error
		patches.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
			return []byte(`{"id": "test-id"}`), nil
		})
		patches.ApplyFunc(json.Unmarshal, func(data []byte, v interface{}) error {
			return errors.New("mock unmarshaling error")
		})

		err = GetPlanResult(inspectionResult, &planResult)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock unmarshaling error")
	})
}

func TestGetResultValues(t *testing.T) {
	Convey("Test GetResultValues", t, func() {
		inspectionResult := map[string]interface{}{
			"id":               "test-id",
			"name":             "Test Result",
			"executeResult":    "success",
			"status":           "completed",
			"completedCount":   10,
			"inspectionResult": map[string]interface{}{"key": "value"},
		}
		var resultValues ResultValues

		// Normal case: successful marshaling and unmarshaling
		err := GetResultValues(inspectionResult, &resultValues)
		So(err, ShouldBeNil)
		So(resultValues.Id, ShouldEqual, "test-id")

		// Error case: marshaling error
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		patches.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
			return nil, errors.New("mock marshaling error")
		})

		err = GetResultValues(inspectionResult, &resultValues)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock marshaling error")

		// Error case: unmarshaling error
		patches.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
			return []byte(`{"id": "test-id"}`), nil
		})
		patches.ApplyFunc(json.Unmarshal, func(data []byte, v interface{}) error {
			return errors.New("mock unmarshaling error")
		})

		err = GetResultValues(inspectionResult, &resultValues)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock unmarshaling error")
	})
}

func TestGetPlanResultValues(t *testing.T) {
	Convey("Test GetPlanResultValues", t, func() {
		inspectResults := PlanResultValues{
			StartTime:         time.Now(),
			FinishTime:        time.Now(),
			ExecutePlanPerson: "test-person",
			ExecuteResult:     "success",
			Status:            "completed",
			CompletedCount:    10,
			RemainingTime:     5,
		}

		inspectionResult := []map[string]interface{}{
			{
				"id":                "test-id",
				"executePlanPerson": "test-person",
				"executeResult":     "success",
				"status":            "completed",
			},
		}

		// Normal case
		planResults, err := GetPlanResultValues(inspectResults, inspectionResult)
		So(err, ShouldBeNil)
		So(planResults, ShouldNotBeNil)
		So(len(*planResults), ShouldEqual, 1)
		So((*planResults)[0].Id, ShouldEqual, "test-id")

		// Error case: GetResultValue failure
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock GetResultValue to return an error
		patches.ApplyFunc(GetResultValue, func(inspectionResult map[string]interface{}, inspectReslut *PlanResultValues) error {
			return errors.New("mock GetResultValue error")
		})

		planResults, err = GetPlanResultValues(inspectResults, inspectionResult)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock GetResultValue error")
		So(planResults, ShouldBeNil)

		// Error case: marshaling error in GetResultValue
		patches.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
			return nil, errors.New("mock marshaling error")
		})

		err = GetResultValue(inspectionResult[0], &inspectResults)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock marshaling error")
	})
}

func TestGetPlanResultListFromDb(t *testing.T) {
	Convey("Test GetPlanResultListFromDb", t, func() {
		clusterId := "test-cluster-id"
		id := "test-id"

		// Mock data for QueryResourceById
		inspectionPlan := map[string]interface{}{
			"id":                "test-id",
			"executePlanPerson": "test-person",
			"inspectTaskName":   "task1,task2",
			"nodeList":          "node1,node2",
		}

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock models.QueryResourceById to return inspectionPlan
		patches.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			return inspectionPlan, nil
		})

		// Mock GetPlanValue to succeed
		patches.ApplyFunc(GetPlanValue, func(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
			inspectPlan.Id = "test-id"
			inspectPlan.Name = "Test Plan"
			inspectPlan.NodeList = "node1,node2"
			inspectPlan.InspectTaskName = "task1,task2"
			return nil
		})

		// Mock GetNodeListStrAndInspectTaskNameStr
		patches.ApplyFunc(GetNodeListStrAndInspectTaskNameStr, func(planList InspectPlan) ([]string, []string, error) {
			return []string{"node1", "node2"}, []string{"task1", "task2"}, nil
		})

		// Mock QueryResourceByCondition to return inspection results
		inspectionResult := []map[string]interface{}{
			{
				"id":                "result-id",
				"executePlanPerson": "test-person",
				"executeResult":     "success",
				"status":            "completed",
			},
		}
		patches.ApplyFunc(models.QueryResourceByCondition, func(tableName string, queryCondition map[string]interface{}) ([]map[string]interface{}, error) {
			return inspectionResult, nil
		})

		// Mock GetPlanResultValues to succeed
		//planResultValues := PlanResultValues{}
		patches.ApplyFunc(GetPlanResultValues, func(inspectResults PlanResultValues, inspectionResult []map[string]interface{}) (*[]PlanResult, error) {
			planResults := []PlanResult{
				{
					Id:                "result-id",
					StartTime:         time.Now().Format(time.RFC3339),
					EndTime:           time.Now().Format(time.RFC3339),
					ExecutePlanPerson: "test-person",
					ExecuteResult:     "success",
					Status:            "completed",
					CompletedCount:    10,
					RemainingTime:     5,
				},
			}
			return &planResults, nil
		})

		// Mock GetPlanResultList to return data
		patches.ApplyFunc(GetPlanResultList, func(inspectPlan *InspectPlan, nodeList []string, inspectTaskName []string, planResults *[]PlanResult) (map[string]interface{}, error) {
			return map[string]interface{}{"result": "some data"}, nil
		})

		// Call the function
		data, err := GetPlanResultListFromDb(clusterId, id)
		So(err, ShouldBeNil)
		So(data, ShouldNotBeNil)
		So(data.(map[string]interface{})["result"], ShouldEqual, "some data")

		// Error case: GetPlanResultValues fails
		patches.ApplyFunc(GetPlanResultValues, func(inspectResults PlanResultValues, inspectionResult []map[string]interface{}) (*[]PlanResult, error) {
			return nil, errors.New("mock GetPlanResultValues error")
		})

		data, err = GetPlanResultListFromDb(clusterId, id)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock GetPlanResultValues error")
		So(data, ShouldBeNil)

		// Reset patches for next error case
		patches.Reset()

		// Mock QueryResourceById to return the plan successfully again
		patches.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			return inspectionPlan, nil
		})

		// Mock GetPlanValue to return an error
		patches.ApplyFunc(GetPlanValue, func(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
			return errors.New("mock GetPlanValue error")
		})

		data, err = GetPlanResultListFromDb(clusterId, id)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock GetPlanValue error")
		So(data, ShouldBeNil)

		// Reset patches for next error case
		patches.Reset()

		// Mock GetPlanResultValues to succeed again
		patches.ApplyFunc(GetPlanResultValues, func(inspectResults PlanResultValues, inspectionResult []map[string]interface{}) (*[]PlanResult, error) {
			planResults := []PlanResult{
				{
					Id:                "result-id",
					StartTime:         time.Now().Format(time.RFC3339),
					EndTime:           time.Now().Format(time.RFC3339),
					ExecutePlanPerson: "test-person",
					ExecuteResult:     "success",
					Status:            "completed",
					CompletedCount:    10,
					RemainingTime:     5,
				},
			}
			return &planResults, nil
		})

		// Mock GetPlanResultList to return an error
		patches.ApplyFunc(GetPlanResultList, func(inspectPlan *InspectPlan, nodeList []string, inspectTaskName []string, planResults *[]PlanResult) (map[string]interface{}, error) {
			return nil, errors.New("mock GetPlanResultList error")
		})

		data, err = GetPlanResultListFromDb(clusterId, id)
		So(err, ShouldNotBeNil)
		So(data, ShouldBeNil)
	})
}

func TestGetPlanResultList(t *testing.T) {
	Convey("Test GetPlanResultList", t, func() {
		// Prepare test data
		inspectPlan := &InspectPlan{
			Id:                  "test-id",
			Name:                "Test Plan",
			ClusterId:           "cluster-id",
			ClusterName:         "Test Cluster",
			Scene:               "Test Scene",
			CreateTime:          time.Now(),
			LatestModifyTime:    time.Now(),
			CreateMode:          1,
			CreateStrategy:      1,
			ExecuteStrategyTime: time.Now(),
			DesignPlanPerson:    "designer",
			ModifyPlanPerson:    "modifier",
		}

		nodeList := []string{"node1", "node2"}
		inspectTaskName := []string{"task1", "task2"}
		planResults := []PlanResult{
			{
				Id:                "result-id",
				StartTime:         time.Now().Format(time.RFC3339),
				EndTime:           time.Now().Format(time.RFC3339),
				ExecutePlanPerson: "test-person",
				ExecuteResult:     "success",
				Status:            "completed",
				CompletedCount:    10,
				RemainingTime:     5,
			},
		}

		// Mock GetModifyTime to succeed
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		patches.ApplyFunc(GetModifyTime, func(inspectPlan *InspectPlan) (string, error) {
			return time.Now().Format(time.RFC3339), nil
		})

		// Mock Struct2Map to succeed
		patches.ApplyFunc(cwsmutils.Struct2Map, func(in interface{}) (map[string]interface{}, error) {
			return map[string]interface{}{"key": "value"}, nil
		})

		// Call the function
		data, err := GetPlanResultList(inspectPlan, nodeList, inspectTaskName, &planResults)
		So(err, ShouldBeNil)
		So(data, ShouldNotBeNil)
		So(data["key"], ShouldEqual, "value")

		// Error case: GetModifyTime fails
		patches.ApplyFunc(GetModifyTime, func(inspectPlan *InspectPlan) (string, error) {
			return "", errors.New("mock GetModifyTime error")
		})

		data, err = GetPlanResultList(inspectPlan, nodeList, inspectTaskName, &planResults)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock GetModifyTime error")
		So(data, ShouldBeNil)

		// Reset patches for next error case
		patches.Reset()

		// Mock GetModifyTime to succeed again
		patches.ApplyFunc(GetModifyTime, func(inspectPlan *InspectPlan) (string, error) {
			return time.Now().Format(time.RFC3339), nil
		})

		// Error case: Struct2Map fails
		patches.ApplyFunc(cwsmutils.Struct2Map, func(in interface{}) (map[string]interface{}, error) {
			return nil, errors.New("mock Struct2Map error")
		})

		data, err = GetPlanResultList(inspectPlan, nodeList, inspectTaskName, &planResults)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock Struct2Map error")
		So(data, ShouldBeNil)
	})
}

func TestGetModifyTime(t *testing.T) {
	Convey("Test GetModifyTime", t, func() {
		// Test case with a valid LatestModifyTime
		inspectPlan := &InspectPlan{
			LatestModifyTime: time.Now(),
		}

		modifyTime, err := GetModifyTime(inspectPlan)
		So(err, ShouldBeNil)
		So(modifyTime, ShouldNotBeEmpty)

		// Test case with LatestModifyTime set to the zero value
		inspectPlan = &InspectPlan{
			LatestModifyTime: time.Time{},
		}

		modifyTime, err = GetModifyTime(inspectPlan)
		So(err, ShouldBeNil)
		So(modifyTime, ShouldEqual, "")

		// Test case with a specific date
		specificTime := time.Date(2023, 10, 1, 0, 0, 0, 0, time.UTC)
		inspectPlan = &InspectPlan{
			LatestModifyTime: specificTime,
		}

		modifyTime, err = GetModifyTime(inspectPlan)
		So(err, ShouldBeNil)
		So(modifyTime, ShouldEqual, specificTime.Format(time.RFC3339))
	})
}

func TestGetnodeListAndinspectTaskNameValue(t *testing.T) {
	Convey("Test GetnodeListAndinspectTaskNameValue", t, func() {
		// Mock inspection plan with valid nodeList and inspectTaskName
		inspectionPlan := map[string]interface{}{
			"nodeList":        `["node1", "node2"]`,
			"inspectTaskName": `["task1", "task2"]`,
		}

		nodeList, inspectTaskName, err := GetnodeListAndinspectTaskNameValue(inspectionPlan)
		So(err, ShouldBeNil)
		So(nodeList, ShouldResemble, []string{"node1", "node2"})
		So(inspectTaskName, ShouldResemble, []string{"task1", "task2"})

		// Case with invalid nodeList
		inspectionPlan["nodeList"] = `invalid json`
		nodeList, inspectTaskName, err = GetnodeListAndinspectTaskNameValue(inspectionPlan)
		So(err, ShouldNotBeNil)
		So(nodeList, ShouldBeNil)
		So(inspectTaskName, ShouldBeNil)

		// Case with invalid inspectTaskName
		inspectionPlan["nodeList"] = `["node1", "node2"]`
		inspectionPlan["inspectTaskName"] = `invalid json`
		nodeList, inspectTaskName, err = GetnodeListAndinspectTaskNameValue(inspectionPlan)
		So(err, ShouldNotBeNil)
		So(inspectTaskName, ShouldBeNil)
	})
}

func TestGetPlanResultFromDb(t *testing.T) {
	Convey("Test GetPlanResultFromDb", t, func() {
		clusterId := "cluster-id"
		id := "plan-id"
		resultId := "result-id"

		// Mock inspection plan
		inspectionPlan := map[string]interface{}{
			"id":          id,
			"name":        "Test Plan",
			"clusterName": "Test Cluster",
			"clusterId":   clusterId,
		}

		// Mock inspection result
		inspectionResult := map[string]interface{}{
			"id":                resultId,
			"startTime":         time.Now(),
			"finishTime":        time.Now(),
			"executePlanPerson": "test-person",
			"executeResult":     "success",
			"status":            "completed",
			"completedCount":    10,
			"remainingTime":     5,
			"inspectionResult":  "some result",
		}

		// Patching QueryResourceById to return mock data
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		patches.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			if idValue == id {
				return inspectionPlan, nil
			} else if idValue == resultId {
				return inspectionResult, nil
			}
			return nil, errors.New("not found")
		})

		// Mock GetResultValue to succeed
		patches.ApplyFunc(GetResultValue, func(inspectionResult map[string]interface{}, inspectResult *PlanResultValues) error {
			*inspectResult = PlanResultValues{
				Id:                resultId,
				StartTime:         time.Now(),
				FinishTime:        time.Now(),
				ExecutePlanPerson: "test-person",
				ExecuteResult:     "success",
				Status:            "completed",
				CompletedCount:    10,
				RemainingTime:     5,
				InspectionResult:  "some result",
			}
			return nil
		})

		// Mock GetInspectionPlanResult to succeed
		patches.ApplyFunc(GetInspectionPlanResult, func(inspectResult *PlanResultValues, inspectionPlan map[string]interface{}, resultId string) (map[string]interface{}, error) {
			return map[string]interface{}{"mock": "data"}, nil
		})

		// Call the function
		data, err := GetPlanResultFromDb(clusterId, id, resultId)
		So(err, ShouldBeNil)
		So(data, ShouldNotBeNil)
		So(data, ShouldResemble, map[string]interface{}{"mock": "data"})

		// Error case: inspectionPlan not found
		patches.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			return nil, errors.New("mock QueryResourceById error")
		})

		data, err = GetPlanResultFromDb(clusterId, id, resultId)
		So(err, ShouldNotBeNil)
		So(data, ShouldBeNil)
		So(err.Error(), ShouldContainSubstring, "mock QueryResourceById error")

		// Reset patches for next error case
		patches.Reset()

		// Mock QueryResourceById to return valid inspection plan
		patches.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			if idValue == id {
				return inspectionPlan, nil
			}
			return nil, nil
		})

		// Error case: inspectionResult not found
		patches.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			if idValue == resultId {
				return nil, errors.New("mock QueryResourceById error")
			}
			return inspectionPlan, nil
		})

		data, err = GetPlanResultFromDb(clusterId, id, resultId)
		So(err, ShouldNotBeNil)
		So(data, ShouldBeNil)
		So(err.Error(), ShouldContainSubstring, "mock QueryResourceById error")

		// Reset patches
		patches.Reset()

		// Mock valid inspectionResult but GetResultValue returns an error
		patches.ApplyFunc(GetResultValue, func(inspectionResult map[string]interface{}, inspectResult *PlanResultValues) error {
			return errors.New("mock GetResultValue error")
		})

		data, err = GetPlanResultFromDb(clusterId, id, resultId)
		So(err, ShouldNotBeNil)
		So(data, ShouldBeNil)
		So(err.Error(), ShouldContainSubstring, "mock GetResultValue error")
	})
}

func TestGetInspectionPlanResult(t *testing.T) {
	Convey("Test GetInspectionPlanResult", t, func() {
		// Prepare test data
		inspectResult := &PlanResultValues{
			Id:                "result-id",
			StartTime:         time.Now(),
			FinishTime:        time.Now(),
			ExecutePlanPerson: "test-person",
			ExecuteResult:     "success",
			Status:            "completed",
			CompletedCount:    10,
			RemainingTime:     5,
			InspectionResult:  "some result",
		}

		inspectionPlan := map[string]interface{}{
			"id":              "plan-id",
			"name":            "Test Plan",
			"clusterName":     "Test Cluster",
			"clusterId":       "cluster-id",
			"scene":           "Test Scene",
			"nodeList":        `["node1", "node2"]`,
			"inspectTaskName": `["task1", "task2"]`,
		}

		// Normal case where InspectionResult is not empty
		data, err := GetInspectionPlanResult(inspectResult, inspectionPlan, "result-id")
		So(err, ShouldNotBeNil)
		So(data, ShouldBeNil)

		// Case where InspectionResult is empty
		inspectResult.InspectionResult = ""
		data, err = GetInspectionPlanResult(inspectResult, inspectionPlan, "result-id")
		So(err, ShouldBeNil)
		So(data, ShouldNotBeNil) // Assuming GetInspectionPlanResultNil is called and returns valid data

		// Case where GetnodeListAndinspectTaskNameValue fails
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		patches.ApplyFunc(GetnodeListAndinspectTaskNameValue, func(inspectionPlan map[string]interface{}) ([]string, []string, error) {
			return nil, nil, errors.New("mock GetnodeListAndinspectTaskNameValue error")
		})

		data, err = GetInspectionPlanResult(inspectResult, inspectionPlan, "result-id")
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock GetnodeListAndinspectTaskNameValue error")
		So(data, ShouldBeNil)
	})
}

func TestStopInspect(t *testing.T) {
	Convey("Test StopInspect", t, func() {
		clusterId := "cluster-id"
		id := "plan-id"
		author := "test-author"

		// Mock InspectHandler to succeed
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		patches.ApplyFunc(wsm.InspectHandler, func(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
			return nil, "", true // success case
		})

		// Mock QueryResourceById to return a valid inspection plan
		inspectionPlan := map[string]interface{}{
			"planResultId": "result-id",
		}
		patches.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			return inspectionPlan, nil
		})

		// Mock UpdateStopStatus to succeed
		patches.ApplyFunc(UpdateStopStatus, func(id string, inspectionPlan map[string]interface{}) error {
			return nil
		})

		// Call the function
		err := StopInspect(clusterId, id, author)
		So(err, ShouldBeNil)

		// Error case: InspectHandler fails
		patches.ApplyFunc(wsm.InspectHandler, func(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
			return nil, "mock error", false // failure case
		})

		err = StopInspect(clusterId, id, author)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldEqual, "mock error")

		// Reset patches for next error case
		patches.Reset()

		// Mock InspectHandler to succeed again
		patches.ApplyFunc(wsm.InspectHandler, func(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
			return nil, "", true // success case
		})

		// Error case: QueryResourceById fails
		patches.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			return nil, errors.New("mock QueryResourceById error")
		})

		err = StopInspect(clusterId, id, author)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock QueryResourceById error")

		// Reset patches
		patches.Reset()

		// Mock InspectHandler to succeed
		patches.ApplyFunc(wsm.InspectHandler, func(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
			return nil, "", true // success case
		})

		// Mock QueryResourceById to return valid inspection plan
		patches.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			return inspectionPlan, nil
		})

		// Error case: UpdateStopStatus fails
		patches.ApplyFunc(UpdateStopStatus, func(id string, inspectionPlan map[string]interface{}) error {
			return errors.New("mock UpdateStopStatus error")
		})

		err = StopInspect(clusterId, id, author)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock UpdateStopStatus error")
	})
}

func TestUpdateStopStatus(t *testing.T) {
	Convey("Test UpdateStopStatus", t, func() {
		id := "plan-id"
		inspectionPlan := map[string]interface{}{
			"planResultId": "result-id",
		}

		// Mock UpdatePlanStatus to succeed
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		patches.ApplyFunc(UpdatePlanStatus, func(planId string, status string) error {
			return nil // success case
		})

		patches.ApplyFunc(UpdateResultStatus, func(planId string, status string) error {
			return nil // success case
		})

		patches.ApplyFunc(UpdateExecuteResultStatus, func(planId string, executeResult string) error {
			return nil // success case
		})

		patches.ApplyFunc(UpdateFinishTimeValues, func(id string, finishTime time.Time) error {
			return nil // success case
		})

		// Call the function
		err := UpdateStopStatus(id, inspectionPlan)
		So(err, ShouldBeNil)

		// Error case: UpdatePlanStatus fails
		patches.ApplyFunc(UpdatePlanStatus, func(planId string, status string) error {
			return errors.New("mock UpdatePlanStatus error")
		})

		err = UpdateStopStatus(id, inspectionPlan)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock UpdatePlanStatus error")

		// Reset patches
		patches.Reset()

		// Mock UpdatePlanStatus to succeed again
		patches.ApplyFunc(UpdatePlanStatus, func(planId string, status string) error {
			return nil // success case
		})

		// Error case: UpdateResultStatus fails
		patches.ApplyFunc(UpdateResultStatus, func(planId string, status string) error {
			return errors.New("mock UpdateResultStatus error")
		})

		err = UpdateStopStatus(id, inspectionPlan)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock UpdateResultStatus error")

		// Reset patches
		patches.Reset()

		// Mock UpdateResultStatus to succeed again
		patches.ApplyFunc(UpdateResultStatus, func(planId string, status string) error {
			return nil // success case
		})

		// Error case: UpdateExecuteResultStatus fails
		patches.ApplyFunc(UpdateExecuteResultStatus, func(planId string, executeResult string) error {
			return errors.New("mock UpdateExecuteResultStatus error")
		})

		// Reset patches
		patches.Reset()

		// Mock UpdateExecuteResultStatus to succeed again
		patches.ApplyFunc(UpdateExecuteResultStatus, func(planId string, executeResult string) error {
			return nil // success case
		})

		// Error case: UpdateFinishTimeValues fails
		patches.ApplyFunc(UpdateFinishTimeValues, func(id string, finishTime time.Time) error {
			return errors.New("mock UpdateFinishTimeValues error")
		})
	})
}

func TestDeleteInspect(t *testing.T) {
	Convey("Test DeleteInspect", t, func() {
		clusterId := "test-cluster"
		author := "test-user"
		reqBody := DeleteInspectReq{
			Id: []string{"test-id-1", "test-id-2"},
		}

		Convey("case when Struct2Map fails", func() {
			patch := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(interface{}) (map[string]interface{}, error) {
				return nil, errors.New("struct to map conversion failed")
			})
			defer patch.Reset()

			err := DeleteInspect(clusterId, reqBody, author)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "struct to map conversion failed")
		})

		Convey("case when InspectHandler fails", func() {
			patchStruct2Map := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			defer patchStruct2Map.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(string, interface{}, string, string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})
			defer patchInspectHandler.Reset()

			err := DeleteInspect(clusterId, reqBody, author)
			So(err, ShouldBeNil)
		})

		Convey("case when DeleteResourceById fails", func() {
			patchStruct2Map := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			defer patchStruct2Map.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(string, interface{}, string, string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})
			defer patchInspectHandler.Reset()

			patchDeleteResource := gomonkey.ApplyFunc(models.DeleteResourceById, func(string, string) bool {
				return false
			})
			defer patchDeleteResource.Reset()

			err := DeleteInspect(clusterId, reqBody, author)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "db delete inspection result table failed")
		})

		Convey("case when all operations succeed", func() {
			patchStruct2Map := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			defer patchStruct2Map.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(string, interface{}, string, string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})
			defer patchInspectHandler.Reset()

			patchDeleteResource := gomonkey.ApplyFunc(models.DeleteResourceById, func(string, string) bool {
				return true
			})
			defer patchDeleteResource.Reset()

			err := DeleteInspect(clusterId, reqBody, author)
			So(err, ShouldBeNil)
		})
	})
}

func TestDeleteInspectResult(t *testing.T) {
	Convey("Test DeleteInspectResult", t, func() {

		clusterId := "test-cluster"
		planId := "test-plan-id"
		reqBody := DeleteInspectResultReq{
			Id: []string{"test-result-id-1", "test-result-id-2"},
		}

		Convey("case when Struct2Map fails", func() {
			patch := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(interface{}) (map[string]interface{}, error) {
				return nil, errors.New("struct to map conversion failed")
			})
			defer patch.Reset()

			err := DeleteInspectResult(clusterId, reqBody, planId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "struct to map conversion failed")
		})

		Convey("case when InspectHandler fails", func() {
			patchStruct2Map := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			defer patchStruct2Map.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(string, interface{}, string, string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})
			defer patchInspectHandler.Reset()

			err := DeleteInspectResult(clusterId, reqBody, planId)
			So(err, ShouldBeNil)
		})

		Convey("case when DeleteResourceById fails", func() {
			patchStruct2Map := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			defer patchStruct2Map.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(string, interface{}, string, string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})
			defer patchInspectHandler.Reset()

			patchDeleteResource := gomonkey.ApplyFunc(models.DeleteResourceById, func(string, string) bool {
				return false
			})
			defer patchDeleteResource.Reset()

			err := DeleteInspectResult(clusterId, reqBody, planId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "db delete inspection result table failed")
		})

		Convey("case when QueryResourceByCondition fails", func() {
			patchStruct2Map := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			defer patchStruct2Map.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(string, interface{}, string, string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})
			defer patchInspectHandler.Reset()

			patchDeleteResource := gomonkey.ApplyFunc(models.DeleteResourceById, func(string, string) bool {
				return true
			})
			defer patchDeleteResource.Reset()

			patchQueryResource := gomonkey.ApplyFunc(models.QueryResourceByCondition, func(string, map[string]interface{}) ([]map[string]interface{}, error) {
				return nil, errors.New("query resource failed")
			})
			defer patchQueryResource.Reset()

			err := DeleteInspectResult(clusterId, reqBody, planId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "query resource failed")
		})

		Convey("case when result id type assertion fails", func() {
			patchStruct2Map := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			defer patchStruct2Map.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(string, interface{}, string, string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})
			defer patchInspectHandler.Reset()

			patchDeleteResource := gomonkey.ApplyFunc(models.DeleteResourceById, func(string, string) bool {
				return true
			})
			defer patchDeleteResource.Reset()

			patchQueryResource := gomonkey.ApplyFunc(models.QueryResourceByCondition, func(string, map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{
					{
						"id": 123,
					},
				}, nil
			})
			defer patchQueryResource.Reset()

			err := DeleteInspectResult(clusterId, reqBody, planId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "get result id from inspection result table is failed")
		})

		Convey("case when all operations succeed", func() {
			patchStruct2Map := gomonkey.ApplyFunc(cwsmutils.Struct2Map, func(interface{}) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			defer patchStruct2Map.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(string, interface{}, string, string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})
			defer patchInspectHandler.Reset()

			patchDeleteResource := gomonkey.ApplyFunc(models.DeleteResourceById, func(string, string) bool {
				return true
			})
			defer patchDeleteResource.Reset()

			patchQueryResource := gomonkey.ApplyFunc(models.QueryResourceByCondition, func(string, map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{
					{
						"id": "test-result-id-1",
					},
					{
						"id": "test-result-id-2",
					},
				}, nil
			})
			defer patchQueryResource.Reset()

			patchUpdatePlanResultId := gomonkey.ApplyFunc(UpdatePlanResultIdByMaxResultIds, func(string, string) error {
				return nil
			})
			defer patchUpdatePlanResultId.Reset()

			err := DeleteInspectResult(clusterId, reqBody, planId)
			So(err, ShouldBeNil)
		})
	})
}

func TestUpdatePlanResultIdByMinResultIds(t *testing.T) {
	Convey("Test UpdatePlanResultIdByMinResultIds", t, func() {
		planId := "plan-id"
		maxResultId := "result-id-min"

		// Mock UpdateResultId to succeed
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		patches.ApplyFunc(UpdateResultId, func(planId string, planResultId string) error {
			return nil // success case
		})

		// Call the function
		err := UpdatePlanResultIdByMaxResultIds(planId, maxResultId)
		So(err, ShouldBeNil)

		// Error case: UpdateResultId fails
		patches.ApplyFunc(UpdateResultId, func(planId string, planResultId string) error {
			return errors.New("mock UpdateResultId error")
		})

		err = UpdatePlanResultIdByMaxResultIds(planId, maxResultId)
		So(err, ShouldNotBeNil)
		So(err.Error(), ShouldContainSubstring, "mock UpdateResultId error")
	})
}

func TestActivateInspect(t *testing.T) {
	Convey("TestActivateInspect", t, func() {
		clusterId := "test-cluster"
		id := "test-id"
		author := "test-author"
		currentTime := time.Now()

		mockInspectionPlan := map[string]interface{}{
			"id":                  id,
			"name":                "test-plan",
			"clusterId":           clusterId,
			"clusterName":         "test-cluster-name",
			"scene":               "test-scene",
			"nodeList":            []string{"node1", "node2"},
			"status":              "not implemented",
			"executeResult":       "not implemented",
			"createTime":          currentTime,
			"latestModifyTime":    time.Time{},
			"createMode":          1,
			"createStrategy":      1,
			"executeStrategyTime": currentTime,
			"inspectTaskName":     "task1,task2",
			"inspectionTask":      "{}",
		}

		Convey("successful case", func() {
			// Mock QueryResourceById
			patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return mockInspectionPlan, nil
			})
			defer patchQueryResourceById.Reset()

			// Mock GetActivateInspectToWsmReq
			patchGetActivateInspectToWsmReq := gomonkey.ApplyFunc(GetActivateInspectToWsmReq, func(inspectionPlan map[string]interface{}) (map[string]interface{}, *InspectPlan, error) {
				return map[string]interface{}{"test": "data"}, &InspectPlan{Id: id, ResultId: "test-result-id"}, nil
			})
			defer patchGetActivateInspectToWsmReq.Reset()

			// Mock wsm.InspectHandler
			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, data interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return map[string]interface{}{"id": "test-id"}, "", true
			})
			defer patchInspectHandler.Reset()

			// Mock InspectionResult
			patchInspectionResult := gomonkey.ApplyFunc(InspectionResult, func(inspectionPlan map[string]interface{}, resultId string, currentTime time.Time, author string, id string) error {
				return nil
			})
			defer patchInspectionResult.Reset()

			// Mock UpdatePlanStatus
			patchUpdatePlanStatus := gomonkey.ApplyFunc(UpdatePlanStatus, func(planId string, status string) error {
				return nil
			})
			defer patchUpdatePlanStatus.Reset()

			// Mock GetInspectResult
			patchGetInspectResult := gomonkey.ApplyFunc(GetInspectResult, func(clusterId string, planId string, id string, currentTime time.Time, author string) error {
				return nil
			})
			defer patchGetInspectResult.Reset()

			err := ActivateInspect(clusterId, id, author)
			So(err, ShouldBeNil)
		})

		Convey("error case when QueryResourceById fails", func() {
			patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return nil, fmt.Errorf("mock QueryResourceById error")
			})
			defer patchQueryResourceById.Reset()

			err := ActivateInspect(clusterId, id, author)
			So(err, ShouldNotBeNil)
		})

		Convey("error case when GetActivateInspectToWsmReq fails", func() {
			patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return mockInspectionPlan, nil
			})
			defer patchQueryResourceById.Reset()

			patchGetActivateInspectToWsmReq := gomonkey.ApplyFunc(GetActivateInspectToWsmReq, func(inspectionPlan map[string]interface{}) (map[string]interface{}, *InspectPlan, error) {
				return nil, nil, fmt.Errorf("mock GetActivateInspectToWsmReq error")
			})
			defer patchGetActivateInspectToWsmReq.Reset()

			err := ActivateInspect(clusterId, id, author)
			So(err, ShouldNotBeNil)
		})

		Convey("error case when wsm.InspectHandler fails", func() {
			patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return mockInspectionPlan, nil
			})
			defer patchQueryResourceById.Reset()

			patchGetActivateInspectToWsmReq := gomonkey.ApplyFunc(GetActivateInspectToWsmReq, func(inspectionPlan map[string]interface{}) (map[string]interface{}, *InspectPlan, error) {
				return map[string]interface{}{"test": "data"}, &InspectPlan{Id: id, ResultId: "test-result-id"}, nil
			})
			defer patchGetActivateInspectToWsmReq.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, data interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return nil, "mock InspectHandler error", false
			})
			defer patchInspectHandler.Reset()

			err := ActivateInspect(clusterId, id, author)
			So(err, ShouldNotBeNil)
		})

		Convey("error case when InspectionResult fails", func() {
			patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return mockInspectionPlan, nil
			})
			defer patchQueryResourceById.Reset()

			patchGetActivateInspectToWsmReq := gomonkey.ApplyFunc(GetActivateInspectToWsmReq, func(inspectionPlan map[string]interface{}) (map[string]interface{}, *InspectPlan, error) {
				return map[string]interface{}{"test": "data"}, &InspectPlan{Id: id, ResultId: "test-result-id"}, nil
			})
			defer patchGetActivateInspectToWsmReq.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, data interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return map[string]interface{}{"id": "test-id"}, "", true
			})
			defer patchInspectHandler.Reset()

			patchInspectionResult := gomonkey.ApplyFunc(InspectionResult, func(inspectionPlan map[string]interface{}, resultId string, currentTime time.Time, author string, id string) error {
				return fmt.Errorf("mock InspectionResult error")
			})
			defer patchInspectionResult.Reset()

			err := ActivateInspect(clusterId, id, author)
			So(err, ShouldNotBeNil)
		})

		Convey("error case when UpdatePlanStatus fails", func() {
			patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return mockInspectionPlan, nil
			})
			defer patchQueryResourceById.Reset()

			patchGetActivateInspectToWsmReq := gomonkey.ApplyFunc(GetActivateInspectToWsmReq, func(inspectionPlan map[string]interface{}) (map[string]interface{}, *InspectPlan, error) {
				return map[string]interface{}{"test": "data"}, &InspectPlan{Id: id, ResultId: "test-result-id"}, nil
			})
			defer patchGetActivateInspectToWsmReq.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, data interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return map[string]interface{}{"id": "test-id"}, "", true
			})
			defer patchInspectHandler.Reset()

			patchInspectionResult := gomonkey.ApplyFunc(InspectionResult, func(inspectionPlan map[string]interface{}, resultId string, currentTime time.Time, author string, id string) error {
				return nil
			})
			defer patchInspectionResult.Reset()

			patchUpdatePlanStatus := gomonkey.ApplyFunc(UpdatePlanStatus, func(planId string, status string) error {
				return fmt.Errorf("mock UpdatePlanStatus error")
			})
			defer patchUpdatePlanStatus.Reset()

			err := ActivateInspect(clusterId, id, author)
			So(err, ShouldNotBeNil)
		})

		Convey("error case when GetInspectResult fails", func() {
			patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return mockInspectionPlan, nil
			})
			defer patchQueryResourceById.Reset()

			patchGetActivateInspectToWsmReq := gomonkey.ApplyFunc(GetActivateInspectToWsmReq, func(inspectionPlan map[string]interface{}) (map[string]interface{}, *InspectPlan, error) {
				return map[string]interface{}{"test": "data"}, &InspectPlan{Id: id, ResultId: "test-result-id"}, nil
			})
			defer patchGetActivateInspectToWsmReq.Reset()

			patchInspectHandler := gomonkey.ApplyFunc(wsm.InspectHandler, func(clusterId string, data interface{}, method string, id string) (map[string]interface{}, string, bool) {
				return map[string]interface{}{"id": "test-id"}, "", true
			})
			defer patchInspectHandler.Reset()

			patchInspectionResult := gomonkey.ApplyFunc(InspectionResult, func(inspectionPlan map[string]interface{}, resultId string, currentTime time.Time, author string, id string) error {
				return nil
			})
			defer patchInspectionResult.Reset()

			patchUpdatePlanStatus := gomonkey.ApplyFunc(UpdatePlanStatus, func(planId string, status string) error {
				return nil
			})
			defer patchUpdatePlanStatus.Reset()

			patchGetInspectResult := gomonkey.ApplyFunc(GetInspectResult, func(clusterId string, planId string, id string, currentTime time.Time, author string) error {
				return fmt.Errorf("mock GetInspectResult error")
			})
			defer patchGetInspectResult.Reset()

			err := ActivateInspect(clusterId, id, author)
			So(err, ShouldNotBeNil)
		})
	})
}

/* Started by AICoder, pid:4d0d9xf972hdaba145150bf2300eaa9d2ef0f6dc */
func TestStartUpdateRunningPlan(t *testing.T) {
	Convey("Test StartUpdateRunningPlan", t, func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		patchQueryResourceAll := gomonkey.ApplyFunc(models.QueryResourceAll, func(tableName string) ([]map[string]interface{}, error) {
			return []map[string]interface{}{
				{"id": "plan-1", "status": "running", "planResultId": "result-1", "clusterId": "cluster-1", "author": "author-1", "createTime": time.Now()},
				{"id": "plan-2", "status": "finished", "planResultId": "result-2", "clusterId": "cluster-2", "author": "author-2", "createTime": time.Now()},
			}, nil
		})
		defer patchQueryResourceAll.Reset()

		patchGetPlanList := gomonkey.ApplyFunc(GetPlanList, func(inspectionPlan []map[string]interface{}, inspectPlanList *InspectPlanList) error {
			return nil
		})
		defer patchGetPlanList.Reset()

		patchGetValuesOfInspectionResult := gomonkey.ApplyFunc(GetValuesOfInspectionResult, func(planList InspectPlan) (map[string]interface{}, []string, []string, error) {
			if planList.Id == "plan-1" {
				return map[string]interface{}{"status": "running"}, nil, nil, nil
			}
			return nil, nil, nil, errors.New("mock error")
		})
		defer patchGetValuesOfInspectionResult.Reset()

		patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			if idValue == "plan-1" {
				return map[string]interface{}{"status": "running"}, nil
			}
			return nil, errors.New("mock QueryResourceById error")
		})
		defer patchQueryResourceById.Reset()

		Convey("Successful case", func() {
			go StartUpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When QueryResourceAll fails", func() {
			patchQueryResourceAll.Reset()
			patchQueryResourceAll = gomonkey.ApplyFunc(models.QueryResourceAll, func(tableName string) ([]map[string]interface{}, error) {
				return nil, errors.New("mock QueryResourceAll error")
			})

			go StartUpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When GetPlanList fails", func() {
			patchGetPlanList.Reset()
			patchGetPlanList = gomonkey.ApplyFunc(GetPlanList, func(inspectionPlan []map[string]interface{}, inspectPlanList *InspectPlanList) error {
				return errors.New("mock GetPlanList error")
			})

			go StartUpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When GetValuesOfInspectionResult fails", func() {
			patchGetValuesOfInspectionResult.Reset()
			patchGetValuesOfInspectionResult = gomonkey.ApplyFunc(GetValuesOfInspectionResult, func(planList InspectPlan) (map[string]interface{}, []string, []string, error) {
				return nil, nil, nil, errors.New("mock GetValuesOfInspectionResult error")
			})

			go StartUpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When QueryResourceById fails in goroutine", func() {
			patchQueryResourceById.Reset()
			patchQueryResourceById = gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return nil, errors.New("mock QueryResourceById error in goroutine")
			})

			go StartUpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})
	})
}

/* Ended by AICoder, pid:4d0d9xf972hdaba145150bf2300eaa9d2ef0f6dc */

/* Started by AICoder, pid:j3fcfd85a1y22b514180090dc0672792fef77f26 */
func TestUpdateRunningPlan(t *testing.T) {
	Convey("Test UpdateRunningPlan", t, func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		patchQueryResourceAll := gomonkey.ApplyFunc(models.QueryResourceAll, func(tableName string) ([]map[string]interface{}, error) {
			return []map[string]interface{}{
				{"id": "plan-1", "status": "running", "planResultId": "result-1", "clusterId": "cluster-1", "author": "author-1", "createTime": time.Now()},
				{"id": "plan-2", "status": "finished", "planResultId": "result-2", "clusterId": "cluster-2", "author": "author-2", "createTime": time.Now()},
			}, nil
		})
		defer patchQueryResourceAll.Reset()

		patchGetValuesOfInspectionResult := gomonkey.ApplyFunc(GetValuesOfInspectionResult, func(planList InspectPlan) (map[string]interface{}, []string, []string, error) {
			if planList.Id == "plan-1" {
				return map[string]interface{}{"status": "running"}, nil, nil, nil
			}
			return nil, nil, nil, errors.New("mock error")
		})
		defer patchGetValuesOfInspectionResult.Reset()

		patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			if idValue == "plan-1" {
				return map[string]interface{}{"status": "running"}, nil
			}
			return map[string]interface{}{"status": "finished"}, nil
		})
		defer patchQueryResourceById.Reset()

		Convey("Successful case", func() {
			go UpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When QueryResourceAll fails", func() {
			patchQueryResourceAll.Reset()
			patchQueryResourceAll = gomonkey.ApplyFunc(models.QueryResourceAll, func(tableName string) ([]map[string]interface{}, error) {
				return nil, errors.New("mock QueryResourceAll error")
			})

			go UpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When GetValuesOfInspectionResult fails", func() {
			patchGetValuesOfInspectionResult.Reset()
			patchGetValuesOfInspectionResult = gomonkey.ApplyFunc(GetValuesOfInspectionResult, func(planList InspectPlan) (map[string]interface{}, []string, []string, error) {
				return nil, nil, nil, errors.New("mock GetValuesOfInspectionResult error")
			})

			go UpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When plan status is not running", func() {
			patchQueryResourceById.Reset()
			patchQueryResourceById = gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return map[string]interface{}{"status": "stopped"}, nil
			})

			go UpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When QueryResourceById fails in goroutine", func() {
			patchQueryResourceById.Reset()
			patchQueryResourceById = gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return nil, errors.New("mock QueryResourceById error in goroutine")
			})

			go UpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When plan status is not a string", func() {
			patchQueryResourceById.Reset()
			patchQueryResourceById = gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return map[string]interface{}{"status": "123"}, nil // Invalid status type
			})

			go UpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})
	})
}

/* Ended by AICoder, pid:j3fcfd85a1y22b514180090dc0672792fef77f26 */

/* Started by AICoder, pid:v1aa6g9d80l40ed149720843208e4b94cd40dc9a */
func TestUpdateRunningPlanErr(t *testing.T) {
	Convey("Test UpdateRunningPlan", t, func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		patchQueryResourceAll := gomonkey.ApplyFunc(models.QueryResourceAll, func(tableName string) ([]map[string]interface{}, error) {
			return []map[string]interface{}{
				{"id": "plan-1", "status": "running", "planResultId": "result-1", "clusterId": "cluster-1", "author": "author-1", "createTime": time.Now()},
				{"id": "plan-2", "status": "finished", "planResultId": "result-2", "clusterId": "cluster-2", "author": "author-2", "createTime": time.Now()},
			}, nil
		})
		defer patchQueryResourceAll.Reset()

		patchGetPlanList := gomonkey.ApplyFunc(GetPlanList, func(inspectionPlan []map[string]interface{}, inspectPlanList *InspectPlanList) error {
			return errors.New("mock GetPlanList error")
		})
		defer patchGetPlanList.Reset()

		patchGetValuesOfInspectionResult := gomonkey.ApplyFunc(GetValuesOfInspectionResult, func(planList InspectPlan) (map[string]interface{}, []string, []string, error) {
			if planList.Id == "plan-1" {
				return map[string]interface{}{"status": "running"}, nil, nil, nil
			}
			return nil, nil, nil, errors.New("mock error")
		})
		defer patchGetValuesOfInspectionResult.Reset()

		patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
			if idValue == "plan-1" {
				return map[string]interface{}{"status": "running"}, nil
			}
			return nil, errors.New("mock QueryResourceById error")
		})
		defer patchQueryResourceById.Reset()

		Convey("When GetPlanList fails", func() {
			go UpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When GetValuesOfInspectionResult fails", func() {
			patchGetValuesOfInspectionResult.Reset()
			patchGetValuesOfInspectionResult = gomonkey.ApplyFunc(GetValuesOfInspectionResult, func(planList InspectPlan) (map[string]interface{}, []string, []string, error) {
				return nil, nil, nil, errors.New("mock GetValuesOfInspectionResult error")
			})

			go UpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When plan status is not running", func() {
			patchQueryResourceById.Reset()
			patchQueryResourceById = gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return map[string]interface{}{"status": "stopped"}, nil
			})

			go UpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When QueryResourceById fails in goroutine", func() {
			patchQueryResourceById.Reset()
			patchQueryResourceById = gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return nil, errors.New("mock QueryResourceById error in goroutine")
			})

			go UpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})

		Convey("When plan status is not a string", func() {
			patchQueryResourceById.Reset()
			patchQueryResourceById = gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, idValue string) (map[string]interface{}, error) {
				return map[string]interface{}{"status": 123}, nil // Invalid status type
			})

			go UpdateRunningPlan(ctx)

			time.Sleep(1 * time.Second)
			cancel()
		})
	})
}

/* Ended by AICoder, pid:v1aa6g9d80l40ed149720843208e4b94cd40dc9a */

func TestUpdateStatusForPlanAndResultTable(t *testing.T) {
	Convey("TestUpdateStatusForPlanAndResultTable", t, func() {
		id := "test-id"
		resultId := "test-result-id"
		currentTime := time.Now()

		mockInspectionPlan := map[string]interface{}{
			"id":                  id,
			"name":                "test-plan",
			"status":              "running",
			"executeResult":       "not implemented",
			"createTime":          currentTime,
			"latestModifyTime":    time.Time{},
			"createMode":          1,
			"createStrategy":      1,
			"executeStrategyTime": currentTime,
		}

		Convey("successful case", func() {
			// Mock models.UpdateResourceById for InspectionResultTable
			patchUpdateResultTable := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, id string, data map[string]interface{}) bool {
				return true
			})
			defer patchUpdateResultTable.Reset()

			// Mock models.QueryResourceById
			patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return mockInspectionPlan, nil
			})
			defer patchQueryResourceById.Reset()

			// Mock models.UpdateResourceById for InspectionPlanTable
			patchUpdatePlanTable := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, id string, data map[string]interface{}) bool {
				return true
			})
			defer patchUpdatePlanTable.Reset()

			err := UpdateStatusForPlanAndResultTable(id, resultId)
			So(err, ShouldBeNil)
		})

		Convey("error case when UpdateResourceById fails for InspectionResultTable", func() {
			// Mock models.UpdateResourceById for InspectionResultTable
			patchUpdateResultTable := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, id string, data map[string]interface{}) bool {
				return false
			})
			defer patchUpdateResultTable.Reset()

			err := UpdateStatusForPlanAndResultTable(id, resultId)
			So(err, ShouldBeNil) 
		})

		Convey("error case when QueryResourceById fails", func() {
			// Mock models.UpdateResourceById for InspectionResultTable
			patchUpdateResultTable := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, id string, data map[string]interface{}) bool {
				return true
			})
			defer patchUpdateResultTable.Reset()

			// Mock models.QueryResourceById
			patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return nil, fmt.Errorf("mock QueryResourceById error")
			})
			defer patchQueryResourceById.Reset()

			err := UpdateStatusForPlanAndResultTable(id, resultId)
			So(err, ShouldBeNil) 
		})

		Convey("error case when UpdateResourceById fails for InspectionPlanTable", func() {
			// Mock models.UpdateResourceById for InspectionResultTable
			patchUpdateResultTable := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, id string, data map[string]interface{}) bool {
				return true
			})
			defer patchUpdateResultTable.Reset()

			// Mock models.QueryResourceById
			patchQueryResourceById := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return mockInspectionPlan, nil
			})
			defer patchQueryResourceById.Reset()

			// Mock models.UpdateResourceById for InspectionPlanTable
			patchUpdatePlanTable := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, id string, data map[string]interface{}) bool {
				return false
			})
			defer patchUpdatePlanTable.Reset()

			err := UpdateStatusForPlanAndResultTable(id, resultId)
			So(err, ShouldBeNil) 
		})
	})
}

/* Started by AICoder, pid:a6274vaf8buca8214d09097c4056a861c5617c80 */
func TestUpdateResultTableValues(t *testing.T) {
	Convey("Test UpdateResultTableValues", t, func() {
		id := "test-id"
		status := "completed"
		finishTime := time.Now()
		inspectionResultJSON := []byte(`{"key": "value"}`)

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock UpdateResultStatus to succeed
		patches.ApplyFunc(UpdateResultStatus, func(id string, status string) error {
			return nil
		})

		// Mock UpdateFinishTimeValues to succeed
		patches.ApplyFunc(UpdateFinishTimeValues, func(id string, finishTime time.Time) error {
			return nil
		})

		// Mock UpdateInspectionResultValues to succeed
		patches.ApplyFunc(UpdateInspectionResultValues, func(id string, inspectionResult string) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateResultTableValues(id, status, finishTime, inspectionResultJSON)
			So(err, ShouldBeNil)
		})

		Convey("When UpdateResultStatus fails", func() {
			patches.ApplyFunc(UpdateResultStatus, func(id string, status string) error {
				return errors.New("mock UpdateResultStatus error")
			})

			err := UpdateResultTableValues(id, status, finishTime, inspectionResultJSON)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateResultStatus error")
		})

		Convey("When UpdateFinishTimeValues fails", func() {
			patches.ApplyFunc(UpdateFinishTimeValues, func(id string, finishTime time.Time) error {
				return errors.New("mock UpdateFinishTimeValues error")
			})

			err := UpdateResultTableValues(id, status, finishTime, inspectionResultJSON)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateFinishTimeValues error")
		})

		Convey("When UpdateInspectionResultValues fails", func() {
			patches.ApplyFunc(UpdateInspectionResultValues, func(id string, inspectionResult string) error {
				return errors.New("mock UpdateInspectionResultValues error")
			})

			err := UpdateResultTableValues(id, status, finishTime, inspectionResultJSON)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateInspectionResultValues error")
		})
	})
}

/* Ended by AICoder, pid:a6274vaf8buca8214d09097c4056a861c5617c80 */

/* Started by AICoder, pid:9e725hfbfd0602214e660919509d3372c5241ff4 */
func TestUpdateExecute(t *testing.T) {
	Convey("Test UpdateExecute", t, func() {
		id := "test-id"
		executeResult := "success"
		executePlanPerson := "John Doe"
		finishTime := time.Now()
		startTime := time.Now().Add(-1 * time.Hour) // 假设开始时间比结束时间早

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock all Update functions to succeed
		patches.ApplyFunc(UpdatePlanExecuteEndTime, func(planId string, executeEndTime time.Time) error {
			return nil
		})

		patches.ApplyFunc(UpdatePlanExecuteStartTime, func(planId string, executeStartTime time.Time) error {
			return nil
		})

		patches.ApplyFunc(UpdatePlanExecuteResult, func(planId string, executeResult string) error {
			return nil
		})

		patches.ApplyFunc(UpdatePlanExecutePlanPerson, func(planId string, executePlanPerson string) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateExecute(id, executeResult, executePlanPerson, finishTime, startTime)
			So(err, ShouldBeNil)
		})

		Convey("When UpdatePlanExecuteEndTime fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteEndTime, func(planId string, executeEndTime time.Time) error {
				return errors.New("mock UpdatePlanExecuteEndTime error")
			})

			err := UpdateExecute(id, executeResult, executePlanPerson, finishTime, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteEndTime error")
		})

		Convey("When UpdatePlanExecuteStartTime fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteStartTime, func(planId string, executeStartTime time.Time) error {
				return errors.New("mock UpdatePlanExecuteStartTime error")
			})

			err := UpdateExecute(id, executeResult, executePlanPerson, finishTime, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteStartTime error")
		})

		Convey("When UpdatePlanExecuteResult fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteResult, func(planId string, executeResult string) error {
				return errors.New("mock UpdatePlanExecuteResult error")
			})

			err := UpdateExecute(id, executeResult, executePlanPerson, finishTime, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteResult error")
		})

		Convey("When UpdatePlanExecutePlanPerson fails", func() {
			patches.ApplyFunc(UpdatePlanExecutePlanPerson, func(planId string, executePlanPerson string) error {
				return errors.New("mock UpdatePlanExecutePlanPerson error")
			})

			err := UpdateExecute(id, executeResult, executePlanPerson, finishTime, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecutePlanPerson error")
		})
	})
}

/* Ended by AICoder, pid:9e725hfbfd0602214e660919509d3372c5241ff4 */

/* Started by AICoder, pid:v9778pb9291f398148430ba0f09b6643fb357875 */
func TestUpdateStopTime(t *testing.T) {
	Convey("Test UpdateStopTime", t, func() {
		id := "test-id"
		finishTime := time.Now()
		startTime := time.Now().Add(-1 * time.Hour) // 假设开始时间比结束时间早

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock UpdateStopFinishTimeValues to succeed
		patches.ApplyFunc(UpdateStopFinishTimeValues, func(id string, finishTime time.Time) error {
			return nil
		})

		// Mock UpdateStopStartTimeValues to succeed
		patches.ApplyFunc(UpdateStopStartTimeValues, func(id string, startTime time.Time) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateStopTime(id, finishTime, startTime)
			So(err, ShouldBeNil)
		})

		Convey("When UpdateStopFinishTimeValues fails", func() {
			patches.ApplyFunc(UpdateStopFinishTimeValues, func(id string, finishTime time.Time) error {
				return errors.New("mock UpdateStopFinishTimeValues error")
			})

			err := UpdateStopTime(id, finishTime, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateStopFinishTimeValues error")
		})

		Convey("When UpdateStopStartTimeValues fails", func() {
			patches.ApplyFunc(UpdateStopStartTimeValues, func(id string, startTime time.Time) error {
				return errors.New("mock UpdateStopStartTimeValues error")
			})

			err := UpdateStopTime(id, finishTime, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateStopStartTimeValues error")
		})
	})
}

/* Ended by AICoder, pid:v9778pb9291f398148430ba0f09b6643fb357875 */

/* Started by AICoder, pid:74be8yccc1ka9bc140480b49d0e40c54e364c4af */
func TestUpdateResultStopStatus(t *testing.T) {
	Convey("Test UpdateResultStopStatus", t, func() {
		id := "test-id"
		status := "completed"
		executeResult := "success"

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock UpdateStopResultStatus to succeed
		patches.ApplyFunc(UpdateStopResultStatus, func(id string, status string) error {
			return nil
		})

		// Mock UpdateStopExecuteResultStatus to succeed
		patches.ApplyFunc(UpdateStopExecuteResultStatus, func(id string, executeResult string) error {
			return nil
		})

		Convey("Successful case", func() {
			inspectResult := map[string]interface{}{"key": "value"}
			err := UpdateResultStopStatus(inspectResult, id, status, executeResult)
			So(err, ShouldBeNil)
		})

		Convey("When UpdateStopResultStatus fails", func() {
			patches.ApplyFunc(UpdateStopResultStatus, func(id string, status string) error {
				return errors.New("mock UpdateStopResultStatus error")
			})

			inspectResult := map[string]interface{}{"key": "value"}
			err := UpdateResultStopStatus(inspectResult, id, status, executeResult)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateStopResultStatus error")
		})

		Convey("When UpdateStopExecuteResultStatus fails", func() {
			patches.ApplyFunc(UpdateStopExecuteResultStatus, func(id string, executeResult string) error {
				return errors.New("mock UpdateStopExecuteResultStatus error")
			})

			inspectResult := map[string]interface{}{"key": "value"}
			err := UpdateResultStopStatus(inspectResult, id, status, executeResult)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdateStopExecuteResultStatus error")
		})

		Convey("When executeResult is 'not implemented'", func() {
			inspectResult := map[string]interface{}{"key": "value"}
			err := UpdateResultStopStatus(inspectResult, id, status, "not implemented")
			So(err, ShouldBeNil) // Should not call UpdateStopExecuteResultStatus
		})
	})
}

/* Ended by AICoder, pid:74be8yccc1ka9bc140480b49d0e40c54e364c4af */

/* Started by AICoder, pid:rf626b99f5me7bb1413509592039284db3e5ea17 */
func TestUpdateExecuteTime(t *testing.T) {
	Convey("Test UpdateExecuteTime", t, func() {
		id := "test-id"
		finishTime := time.Now()
		startTime := time.Now().Add(-1 * time.Hour) // 假设开始时间比结束时间早

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock UpdatePlanExecuteEndTime to succeed
		patches.ApplyFunc(UpdatePlanExecuteEndTime, func(id string, finishTime time.Time) error {
			return nil
		})

		// Mock UpdatePlanExecuteStartTime to succeed
		patches.ApplyFunc(UpdatePlanExecuteStartTime, func(id string, startTime time.Time) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateExecuteTime(id, finishTime, startTime)
			So(err, ShouldBeNil)
		})

		Convey("When UpdatePlanExecuteEndTime fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteEndTime, func(id string, finishTime time.Time) error {
				return errors.New("mock UpdatePlanExecuteEndTime error")
			})

			err := UpdateExecuteTime(id, finishTime, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteEndTime error")
		})

		Convey("When UpdatePlanExecuteStartTime fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteStartTime, func(id string, startTime time.Time) error {
				return errors.New("mock UpdatePlanExecuteStartTime error")
			})

			err := UpdateExecuteTime(id, finishTime, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteStartTime error")
		})
	})
}

/* Ended by AICoder, pid:rf626b99f5me7bb1413509592039284db3e5ea17 */

/* Started by AICoder, pid:8998b51349y6597149430ab0501a3d493465568c */
func TestUpdateExecuteResult(t *testing.T) {
	Convey("Test UpdateExecuteResult", t, func() {
		id := "test-id"
		executeResult := "success"
		executePlanPerson := "John Doe"

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock UpdatePlanExecuteResult to succeed
		patches.ApplyFunc(UpdatePlanExecuteResult, func(id string, executeResult string) error {
			return nil
		})

		// Mock UpdatePlanExecutePlanPerson to succeed
		patches.ApplyFunc(UpdatePlanExecutePlanPerson, func(id string, executePlanPerson string) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateExecuteResult(id, executeResult, executePlanPerson)
			So(err, ShouldBeNil)
		})

		Convey("When UpdatePlanExecuteResult fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteResult, func(id string, executeResult string) error {
				return errors.New("mock UpdatePlanExecuteResult error")
			})

			err := UpdateExecuteResult(id, executeResult, executePlanPerson)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteResult error")
		})

		Convey("When UpdatePlanExecutePlanPerson fails", func() {
			patches.ApplyFunc(UpdatePlanExecutePlanPerson, func(id string, executePlanPerson string) error {
				return errors.New("mock UpdatePlanExecutePlanPerson error")
			})

			err := UpdateExecuteResult(id, executeResult, executePlanPerson)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecutePlanPerson error")
		})
	})
}

/* Ended by AICoder, pid:8998b51349y6597149430ab0501a3d493465568c */

func TestGetResultStatus(t *testing.T) {
	Convey("TestGetResultStatus", t, func() {
		id := "test-id"
		resultId := "test-result-id"
		currentTime := time.Now()

		mockInspectionPlan := map[string]interface{}{
			"id":                  id,
			"name":                "test-plan",
			"status":              "running",
			"executeResult":       "not implemented",
			"createTime":          currentTime,
			"latestModifyTime":    time.Time{},
			"createMode":          1,
			"createStrategy":      1,
			"executeStrategyTime": currentTime,
		}

		mockInspectionResult := map[string]interface{}{
			"id":                resultId,
			"planId":            id,
			"status":            "finished",
			"executeResult":     "success",
			"startTime":         currentTime,
			"finishTime":        currentTime,
			"executePlanPerson": "test-author",
		}

		Convey("successful case", func() {
			// Mock models.QueryResourceById for InspectionPlanTable
			patchQueryPlan := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				if tableName == constant.TABLE_NAME_InspectionPlanTable {
					return mockInspectionPlan, nil
				}
				return mockInspectionResult, nil
			})
			defer patchQueryPlan.Reset()

			status, err := GetResultStatus(id, resultId)
			So(err, ShouldBeNil)
			So(status, ShouldEqual, "finished")
		})

		Convey("error case when QueryResourceById fails for InspectionPlanTable", func() {
			// Mock models.QueryResourceById for InspectionPlanTable
			patchQueryPlan := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				if tableName == constant.TABLE_NAME_InspectionPlanTable {
					return nil, fmt.Errorf("mock QueryResourceById error")
				}
				return mockInspectionResult, nil
			})
			defer patchQueryPlan.Reset()

			status, err := GetResultStatus(id, resultId)
			So(err, ShouldBeNil) 
			So(status, ShouldEqual, "")
		})

		Convey("error case when plan status is not a string", func() {
			invalidPlan := make(map[string]interface{})
			for k, v := range mockInspectionPlan {
				invalidPlan[k] = v
			}
			invalidPlan["status"] = 123 

			// Mock models.QueryResourceById for InspectionPlanTable
			patchQueryPlan := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				if tableName == constant.TABLE_NAME_InspectionPlanTable {
					return invalidPlan, nil
				}
				return mockInspectionResult, nil
			})
			defer patchQueryPlan.Reset()

			status, err := GetResultStatus(id, resultId)
			So(err, ShouldBeNil) 
			So(status, ShouldEqual, "")
		})

		Convey("error case when QueryResourceById fails for InspectionResultTable", func() {
			// Mock models.QueryResourceById
			patchQueryResult := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				if tableName == constant.TABLE_NAME_InspectionPlanTable {
					return mockInspectionPlan, nil
				}
				return nil, fmt.Errorf("mock QueryResourceById error")
			})
			defer patchQueryResult.Reset()

			status, err := GetResultStatus(id, resultId)
			So(err, ShouldBeNil) 
			So(status, ShouldEqual, "")
		})

		Convey("error case when result status is not a string", func() {
			invalidResult := make(map[string]interface{})
			for k, v := range mockInspectionResult {
				invalidResult[k] = v
			}
			invalidResult["status"] = 123 

			// Mock models.QueryResourceById
			patchQueryResult := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				if tableName == constant.TABLE_NAME_InspectionPlanTable {
					return mockInspectionPlan, nil
				}
				return invalidResult, nil
			})
			defer patchQueryResult.Reset()

			status, err := GetResultStatus(id, resultId)
			So(err, ShouldBeNil) 
			So(status, ShouldEqual, "")
		})
	})
}

func TestUpdateExecuteStartTimeAndResult(t *testing.T) {
	Convey("Test UpdateExecuteStartTimeAndResult", t, func() {
		id := "test-id"
		executeResult := "success"
		startTime := time.Now().Add(-1 * time.Hour)

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock functions to succeed
		patches.ApplyFunc(UpdatePlanExecuteStartTime, func(planId string, executeStartTime time.Time) error {
			return nil
		})

		patches.ApplyFunc(UpdatePlanExecuteResult, func(planId string, executeResult string) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateExecuteStartTimeAndResult(id, executeResult, startTime)
			So(err, ShouldBeNil)
		})

		Convey("When UpdatePlanExecuteStartTime fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteStartTime, func(planId string, executeStartTime time.Time) error {
				return errors.New("mock UpdatePlanExecuteStartTime error")
			})

			err := UpdateExecuteStartTimeAndResult(id, executeResult, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteStartTime error")
		})

		Convey("When UpdatePlanExecuteResult fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteResult, func(planId string, executeResult string) error {
				return errors.New("mock UpdatePlanExecuteResult error")
			})

			err := UpdateExecuteStartTimeAndResult(id, executeResult, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteResult error")
		})
	})
}

func TestUpdateExecuteStartTimeAndPlanPerson(t *testing.T) {
	Convey("Test UpdateExecuteStartTimeAndPlanPerson", t, func() {
		id := "test-id"
		executePlanPerson := "John Doe"
		startTime := time.Now().Add(-1 * time.Hour)

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock functions to succeed
		patches.ApplyFunc(UpdatePlanExecuteStartTime, func(planId string, executeStartTime time.Time) error {
			return nil
		})

		patches.ApplyFunc(UpdatePlanExecutePlanPerson, func(planId string, executePlanPerson string) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateExecuteStartTimeAndPlanPerson(id, executePlanPerson, startTime)
			So(err, ShouldBeNil)
		})

		Convey("When UpdatePlanExecuteStartTime fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteStartTime, func(planId string, executeStartTime time.Time) error {
				return errors.New("mock UpdatePlanExecuteStartTime error")
			})

			err := UpdateExecuteStartTimeAndPlanPerson(id, executePlanPerson, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteStartTime error")
		})

		Convey("When UpdatePlanExecutePlanPerson fails", func() {
			patches.ApplyFunc(UpdatePlanExecutePlanPerson, func(planId string, executePlanPerson string) error {
				return errors.New("mock UpdatePlanExecutePlanPerson error")
			})

			err := UpdateExecuteStartTimeAndPlanPerson(id, executePlanPerson, startTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecutePlanPerson error")
		})
	})
}

func TestUpdateExecuteEndTimeAndResult(t *testing.T) {
	Convey("Test UpdateExecuteEndTimeAndResult", t, func() {
		id := "test-id"
		executeResult := "success"
		finishTime := time.Now()

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock functions to succeed
		patches.ApplyFunc(UpdatePlanExecuteEndTime, func(planId string, executeEndTime time.Time) error {
			return nil
		})

		patches.ApplyFunc(UpdatePlanExecuteResult, func(planId string, executeResult string) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateExecuteEndTimeAndResult(id, executeResult, finishTime)
			So(err, ShouldBeNil)
		})

		Convey("When UpdatePlanExecuteEndTime fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteEndTime, func(planId string, executeEndTime time.Time) error {
				return errors.New("mock UpdatePlanExecuteEndTime error")
			})

			err := UpdateExecuteEndTimeAndResult(id, executeResult, finishTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteEndTime error")
		})

		Convey("When UpdatePlanExecuteResult fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteResult, func(planId string, executeResult string) error {
				return errors.New("mock UpdatePlanExecuteResult error")
			})

			err := UpdateExecuteEndTimeAndResult(id, executeResult, finishTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteResult error")
		})
	})
}

func TestUpdateExecuteEndTimeAndPlanPerson(t *testing.T) {
	Convey("Test UpdateExecuteEndTimeAndPlanPerson", t, func() {
		id := "test-id"
		executePlanPerson := "John Doe"
		finishTime := time.Now()

		patches := gomonkey.NewPatches()
		defer patches.Reset()

		// Mock functions to succeed
		patches.ApplyFunc(UpdatePlanExecuteEndTime, func(planId string, executeEndTime time.Time) error {
			return nil
		})

		patches.ApplyFunc(UpdatePlanExecutePlanPerson, func(planId string, executePlanPerson string) error {
			return nil
		})

		Convey("Successful case", func() {
			err := UpdateExecuteEndTimeAndPlanPerson(id, executePlanPerson, finishTime)
			So(err, ShouldBeNil)
		})

		Convey("When UpdatePlanExecuteEndTime fails", func() {
			patches.ApplyFunc(UpdatePlanExecuteEndTime, func(planId string, executeEndTime time.Time) error {
				return errors.New("mock UpdatePlanExecuteEndTime error")
			})

			err := UpdateExecuteEndTimeAndPlanPerson(id, executePlanPerson, finishTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecuteEndTime error")
		})

		Convey("When UpdatePlanExecutePlanPerson fails", func() {
			patches.ApplyFunc(UpdatePlanExecutePlanPerson, func(planId string, executePlanPerson string) error {
				return errors.New("mock UpdatePlanExecutePlanPerson error")
			})

			err := UpdateExecuteEndTimeAndPlanPerson(id, executePlanPerson, finishTime)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock UpdatePlanExecutePlanPerson error")
		})
	})
}

func TestUpdateRunningStatusToDisconnected(t *testing.T) {
	Convey("TestUpdateRunningStatusToDisconnected", t, func() {
		resultId := "test-result-id"

		mockInspectionResult := map[string]interface{}{
			"id": resultId,
			"inspectionResult": `{
				"healthyCheckResult": {"status": "running"},
				"rdmaResult": {"status": "completed"},
				"gpuCheckResult": {"status": "running"},
				"cclResult": {"status": "completed"},
				"modelResult": {"status": "running"}
			}`,
		}

		Convey("successful case", func() {
			// Mock QueryResourceById to return mock data
			patchQuery := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return mockInspectionResult, nil
			})
			defer patchQuery.Reset()

			// Mock UpdateResourceById to succeed
			patchUpdate := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, id string, updateResource map[string]interface{}) bool {
				return true
			})
			defer patchUpdate.Reset()

			err := UpdateRunningStatusToDisconnected(resultId)
			So(err, ShouldBeNil)
		})

		Convey("error case when QueryResourceById fails", func() {
			// Mock QueryResourceById to return error
			patchQuery := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return nil, fmt.Errorf("mock QueryResourceById error")
			})
			defer patchQuery.Reset()

			err := UpdateRunningStatusToDisconnected(resultId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "mock QueryResourceById error")
		})

		Convey("error case when inspection result is nil", func() {
			// Mock QueryResourceById to return nil result
			patchQuery := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return nil, nil
			})
			defer patchQuery.Reset()

			err := UpdateRunningStatusToDisconnected(resultId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "inspection result not found")
		})

		Convey("error case when json unmarshal fails", func() {
			invalidMockResult := make(map[string]interface{})
			for k, v := range mockInspectionResult {
				invalidMockResult[k] = v
			}
			invalidMockResult["inspectionResult"] = "invalid json"

			// Mock QueryResourceById to return invalid data
			patchQuery := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return invalidMockResult, nil
			})
			defer patchQuery.Reset()

			err := UpdateRunningStatusToDisconnected(resultId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "failed to unmarshal inspection result")
		})

		Convey("error case when UpdateResourceById fails", func() {
			// Mock QueryResourceById to return mock data
			patchQuery := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return mockInspectionResult, nil
			})
			defer patchQuery.Reset()

			// Mock UpdateResourceById to fail
			patchUpdate := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, id string, updateResource map[string]interface{}) bool {
				return false
			})
			defer patchUpdate.Reset()

			err := UpdateRunningStatusToDisconnected(resultId)
			So(err, ShouldBeNil) 
		})

		Convey("case when no running status found", func() {
			noRunningMockResult := make(map[string]interface{})
			for k, v := range mockInspectionResult {
				noRunningMockResult[k] = v
			}
			noRunningMockResult["inspectionResult"] = `{
				"healthyCheckResult": {"status": "completed"},
				"rdmaResult": {"status": "completed"},
				"gpuCheckResult": {"status": "completed"},
				"cclResult": {"status": "completed"},
				"modelResult": {"status": "completed"}
			}`

			// Mock QueryResourceById to return mock data
			patchQuery := gomonkey.ApplyFunc(models.QueryResourceById, func(tableName string, id string) (map[string]interface{}, error) {
				return noRunningMockResult, nil
			})
			defer patchQuery.Reset()

			// Mock UpdateResourceById to succeed
			patchUpdate := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, id string, updateResource map[string]interface{}) bool {
				return true
			})
			defer patchUpdate.Reset()

			err := UpdateRunningStatusToDisconnected(resultId)
			So(err, ShouldBeNil)
		})
	})
}
