package controllers

import (
	"context"
	"cwsm/infra/authorization"
	"cwsm/infra/configcenter"
	"errors"
	"testing"
	"time"

	gomonkey "github.com/agiledragon/gomonkey/v2"

	. "github.com/onsi/ginkgo"

	. "github.com/onsi/gomega"
	"github.com/smartystreets/goconvey/convey"
)

/* Started by AICoder, pid:422438baa5xc5a614aa8089ba0235649ac71709e */
var _ = Describe("TestGetVcjobConfig", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetFaultAnalaSwitchValue returns false", func() {
		It("should return faultAnalaSwitch as '0'", func() {
			patcher.ApplyFunc(configcenter.GetFaultAnalaSwitchValue, func() bool {
				return false
			})

			result, err := GetVcjobConfig("cluster123")

			Expect(err).To(BeNil())
			expected := map[string]interface{}{"faultAnalaSwitch": "0"}
			Expect(result).To(Equal(expected))
		})
	})

	Context("When GetFaultAnalaSwitchValue returns true", func() {
		It("should return faultAnalaSwitch as '1'", func() {
			patcher.ApplyFunc(configcenter.GetFaultAnalaSwitchValue, func() bool {
				return true
			})

			result, err := GetVcjobConfig("cluster123")

			Expect(err).To(BeNil())
			expected := map[string]interface{}{"faultAnalaSwitch": "1"}
			Expect(result).To(Equal(expected))
		})
	})
})

/* Ended by AICoder, pid:422438baa5xc5a614aa8089ba0235649ac71709e */

/* Started by AICoder, pid:1b8f3614f027e5414d1a0a96b0d0d07036639dee */
func TestGetVcjobSwitchSynchronization(t *testing.T) {
	convey.Convey("Test GetVcjobSwitchSynchronization", t, func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		convey.Convey("When GetClusterUuidFromPvrmByMap returns valid UUIDs", func() {
			uuidList := []map[string]interface{}{
				{"uuid": "test-uuid-1"},
				{"uuid": "test-uuid-2"},
			}

			patches := gomonkey.NewPatches()
			patches.ApplyFunc(configcenter.GetFaultAnalaSwitchValue, func() bool {
				return true
			})
			patches.ApplyFunc(authorization.GetClusterUuidFromPvrmByMap, func() ([]map[string]interface{}, error) {
				return uuidList, nil
			})
			patches.ApplyFunc(configcenter.PostValueOfFaultanalySwitch, func(uuid string, switchValue bool) error {
				return nil
			})
			defer patches.Reset()

			go GetVcjobSwitchSynchronization(ctx)

			time.Sleep(1 * time.Second)
		})

		convey.Convey("When GetClusterUuidFromPvrmByMap returns an error", func() {
			patches := gomonkey.NewPatches()
			patches.ApplyFunc(configcenter.GetFaultAnalaSwitchValue, func() bool {
				return true
			})
			patches.ApplyFunc(authorization.GetClusterUuidFromPvrmByMap, func() ([]map[string]interface{}, error) {
				return nil, errors.New("failed to get UUIDs")
			})
			defer patches.Reset()

			go GetVcjobSwitchSynchronization(ctx)

			time.Sleep(1 * time.Second)
		})

		convey.Convey("When PostValueOfFaultanalySwitch returns an error", func() {
			uuidList := []map[string]interface{}{
				{"uuid": "test-uuid-1"},
			}

			patches := gomonkey.NewPatches()
			patches.ApplyFunc(configcenter.GetFaultAnalaSwitchValue, func() bool {
				return true
			})
			patches.ApplyFunc(authorization.GetClusterUuidFromPvrmByMap, func() ([]map[string]interface{}, error) {
				return uuidList, nil
			})
			patches.ApplyFunc(configcenter.PostValueOfFaultanalySwitch, func(uuid string, switchValue bool) error {
				return errors.New("comparison error")
			})
			defer patches.Reset()

			go GetVcjobSwitchSynchronization(ctx)

			time.Sleep(1 * time.Second)
		})

		convey.Convey("When the context is canceled", func() {
			go GetVcjobSwitchSynchronization(ctx)

			cancel()
			time.Sleep(1 * time.Second)
		})
	})
}

/* Ended by AICoder, pid:1b8f3614f027e5414d1a0a96b0d0d07036639dee */
