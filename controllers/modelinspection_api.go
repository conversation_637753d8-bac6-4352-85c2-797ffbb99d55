package controllers

import (
	"cwsm/infra/cwsmutils"
	"net/http"
)

type ModelinspectionController struct {
	cwsmutils.Controller
}

/* Started by AICoder, pid:n5f1401fc74daeb14c5d093990ff380abe058543 */
func (e *ModelinspectionController) Get() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetModelinspection(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:n5f1401fc74daeb14c5d093990ff380abe058543 */

/* Started by AICoder, pid:k04d5d2273i3eae14c5e09db70661a0507f5dbe3 */
func (e *ModelinspectionController) GetModelinspectionConfig() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetModelinspectionConfigs(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:k04d5d2273i3eae14c5e09db70661a0507f5dbe3 */

/* Started by AICoder, pid:t719alc6acafc76148fe09f880ca700e13f55398 */
func (e *ModelinspectionController) GetClRdmaInspectionConfig() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetClRdmaInspectionConfigs(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:t719alc6acafc76148fe09f880ca700e13f55398 */

/* Started by AICoder, pid:w583eo866bp018f145790a2bd05f02088495de8e */
func (e *ModelinspectionController) GetNamespace() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetNamespaceConfigs(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:w583eo866bp018f145790a2bd05f02088495de8e */

/* Started by AICoder, pid:ce565ke9f7953e014b390a8ae03a7105a3d509a0 */
func (e *ModelinspectionController) GetDataSets() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetNDataSetsConfigs(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:ce565ke9f7953e014b390a8ae03a7105a3d509a0 */

func (e *ModelinspectionController) GetCclinspectionconfig() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetCclinspectionconfigs(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Started by AICoder, pid:m4328e2b57oc1ed1478d0bd3507c51002fd56c29 */
func (e *ModelinspectionController) Post() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := ActivateModelinspection(clusterId, e.Ctx.Input.RequestBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:m4328e2b57oc1ed1478d0bd3507c51002fd56c29 */

/* Started by AICoder, pid:e28ed94666i6cb214047094ae0994800eff555f0 */
func (e *ModelinspectionController) PostModelinspectionConfig() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := ActivateModelinspectionConfigs(clusterId, e.Ctx.Input.RequestBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:e28ed94666i6cb214047094ae0994800eff555f0 */

/* Started by AICoder, pid:81bd4u7f1em5c6514e520bd1904d87080fc56cdc */
func (e *ModelinspectionController) PostClRdmaInspectionConfig() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := ActivateClRdmaInspectionConfigs(clusterId, e.Ctx.Input.RequestBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:81bd4u7f1em5c6514e520bd1904d87080fc56cdc */

/* Started by AICoder, pid:n100ac940a5fc95144420aa290bec806fc45ed20 */
func (e *ModelinspectionController) PostNamespace() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := ActivateNamespaceConfigs(clusterId, e.Ctx.Input.RequestBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:n100ac940a5fc95144420aa290bec806fc45ed20 */

func (e *ModelinspectionController) PostCclinspectionconfig() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := ActivateCclinspectionconfig(clusterId, e.Ctx.Input.RequestBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Started by AICoder, pid:j4417z132164e5c148d00b02d04ddb0b70367e23 */
func (e *ModelinspectionController) Stop() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	taskId := e.Ctx.Input.Param(":taskId")
	err := StopModelinspection(clusterId, taskId)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

/* Ended by AICoder, pid:j4417z132164e5c148d00b02d04ddb0b70367e23 */

/* Started by AICoder, pid:ibbce863f6m51d414e5b09a910a53101a7065c1b */
func (e *ModelinspectionController) Delete() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	taskId := e.Ctx.Input.Param(":taskId")
	err := DeleteModelinspection(clusterId, taskId)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

/* Ended by AICoder, pid:ibbce863f6m51d414e5b09a910a53101a7065c1b */

/* Started by AICoder, pid:07a9d3231352bf9146bf0aed106a080576f7f8dc */
func (e *ModelinspectionController) actionResponse(err error, sucCodeStatus int, data interface{}, failCodeStatus int) {
	if err != nil {
		e.ApiResponse(failCodeStatus, map[string]string{"message": err.Error()})
		return
	}
	e.ApiResponse(sucCodeStatus, data)
}

/* Ended by AICoder, pid:07a9d3231352bf9146bf0aed106a080576f7f8dc */
