package controllers

import (
	"context"
	"cwsm/infra/authorization"
	"cwsm/infra/configcenter"
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/models"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

/* Started by AICoder, pid:33f5f7744e8a491e9747edffccbd7389 */
func GetNsVcjobEvents(clusterId string, projectName string) (interface{}, error) {
	keywords := wsm.VcjobEventsWsmKeywords{
		ClusterId:   clusterId,
		ProjectName: projectName,
	}

	vcjobevents, err, suc := wsm.VcjobEventsHandler(keywords, nil, "get")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return vcjobevents, nil
}

/* Ended by AICoder, pid:33f5f7744e8a491e9747edffccbd7389 */

/* Started by AICoder, pid:51d1e186349b4aa7a5efb690f840a1ed */
func GetVcjobEvents(clusterId string, projectName string, name string) (interface{}, error) {
	keywords := wsm.VcjobEventsWsmKeywords{
		ClusterId:   clusterId,
		ProjectName: projectName,
		Name:        name,
	}

	vcjobevents, err, suc := wsm.VcjobOneEventsHandler(keywords, nil, "getvcjobevents")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return vcjobevents, nil
}

/* Ended by AICoder, pid:51d1e186349b4aa7a5efb690f840a1ed */

func GetVcjobWithId(vcjobId string) (interface{}, error) {
	res, err := models.QueryResourceByUuid(constant.TABLE_NAME_VCJOBFAULTHISTORY, vcjobId)
	if err != nil {
		return nil, err
	}
	if len(res) != 0 {
		faultInfo := res["faultInfo"]
		faultRes, ok := faultInfo.(string)
		if !ok {
			return nil, fmt.Errorf("the format of faultInfo is invalid")
		}
		if len(faultRes) == 0 {
			return nil, nil
		}
		returnRes := map[string]interface{}{}
		err := json.Unmarshal([]byte(faultRes), &returnRes)
		if err != nil {
			return nil, err
		}
		return returnRes, nil
	}
	return nil, nil
}

/* Started by AICoder, pid:6e115b625d6046c2be17a42b80ed1a13 */
func GetVcjobs(clusterId string, projectName string) (interface{}, error) {
	keywords := wsm.VcjobEventsWsmKeywords{
		ClusterId:   clusterId,
		ProjectName: projectName,
	}

	vcjobevents, err, suc := wsm.VcjobEventsHandler(keywords, nil, "getvcjobs")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return vcjobevents, nil
}

/* Ended by AICoder, pid:6e115b625d6046c2be17a42b80ed1a13 */

/* Started by AICoder, pid:s50ffg13c8m32fd14f4d0b7d70897826d2a3445e */
func GetVcjobConfig(clusterId string) (interface{}, error) {
	var faultAnalaSwitch string
	faultAnalaSwitchValue := configcenter.GetFaultAnalaSwitchValue()

	if !faultAnalaSwitchValue {
		faultAnalaSwitch = "0"
	} else {
		faultAnalaSwitch = "1"
	}

	vcjobcfg := map[string]interface{}{
		"faultAnalaSwitch": faultAnalaSwitch,
	}

	return vcjobcfg, nil
}

/* Ended by AICoder, pid:s50ffg13c8m32fd14f4d0b7d70897826d2a3445e */

/* Started by AICoder, pid:h488fm3960r592f14fbc0ad380ceca39b0e5232e */
func GetVcjobSwitchSynchronization(ctx context.Context) {
	ticker := time.NewTicker(60 * time.Minute)

	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			logger.Infof("GetVcjobSwitchSynchronization stopped")
			return
		case <-ticker.C:

			faultanalaSwitchValue := configcenter.GetFaultAnalaSwitchValue()
			uuidList, err := authorization.GetClusterUuidFromPvrmByMap()
			if err != nil {
				logger.Errorf("Error getting UUID list: %v", err)
				continue
			}
			var wg sync.WaitGroup
			for _, uuidMap := range uuidList {
				if uuid, ok := uuidMap["uuid"].(string); ok {
					wg.Add(1)
					go func(uuid string) {
						defer wg.Done()
						errPostValueOfFaultanalySwitch := configcenter.PostValueOfFaultanalySwitch(uuid, faultanalaSwitchValue)
						if errPostValueOfFaultanalySwitch != nil {
							logger.Errorf("Error comparing status of switch for UUID %s: %v", uuid, errPostValueOfFaultanalySwitch)
						}
					}(uuid)
				}
			}
			wg.Wait()
		}
	}
}

/* Ended by AICoder, pid:h488fm3960r592f14fbc0ad380ceca39b0e5232e */
