package controllers

import (
	"cwsm/infra/wsm"
	"fmt"
)

/* Started by AICoder, pid:de524e8dc94c4305980c5853bf1511a7 */
func GetScclinspection(clusterId string) (interface{}, error) {
	keywords := wsm.ScclinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	scclinspection, err, suc := wsm.ScclinspectionHandler(keywords, nil, "get")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}

	return scclinspection, nil
}

/* Ended by AICoder, pid:de524e8dc94c4305980c5853bf1511a7 */

/* Started by AICoder, pid:2ee10d7fc9c2422ba4ef8f8a7c53450e */
func ActivateScclinspection(clusterId string, reqBody []byte) (interface{}, error) {
	keywords := wsm.ScclinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	scclinspection, err, suc := wsm.ScclinspectionHandler(keywords, reqBody, "post")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}

	return scclinspection, nil
}

/* Ended by AICoder, pid:2ee10d7fc9c2422ba4ef8f8a7c53450e */

/* Started by AICoder, pid:9b6b29a017fc454486c9bb5cc3bc8a74 */
func StopScclinspection(clusterId string) error {
	keywords := wsm.ScclinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	_, err, suc := wsm.ScclinspectionHandler(keywords, nil, "stop")
	if !suc {
		if err == "" {
			return fmt.Errorf("an internal error occurred in the code")
		} else {
			return fmt.Errorf(err)
		}
	}

	return nil
}

/* Ended by AICoder, pid:9b6b29a017fc454486c9bb5cc3bc8a74 */
