//rdma
package controllers

import (
	"cwsm/infra/cwsmutils"
	"cwsm/tools/commontools/logger"
	"net/http"
)

type RdmaController struct {
	cwsmutils.Controller
}

func (e *RdmaController) Get() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetRdma(clusterId)
	logger.Info("get rdma clusterId is :%s,data is %s", clusterId, data)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *RdmaController) GetRdmaTopoCfg() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetRdmaTopo(clusterId)
	logger.Info("get rdma topo config clusterId is :%s,data is %s", clusterId, data)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *RdmaController) Post() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := ActivateRdma(clusterId, e.Ctx.Input.RequestBody)
	logger.Info("post rdma topo config clusterId is :%s,data is %s", clusterId, data)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *RdmaController) Stop() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	logger.Info("stop rdma topo config clusterId is :%s", clusterId)
	err := StopRdma(clusterId)
	e.actionResponse(err, http.StatusOK, nil, http.StatusBadRequest)
}

func (e *RdmaController) actionResponse(err error, sucCodeStatus int, data interface{}, failCodeStatus int) {
	if err != nil {
		e.ApiResponse(failCodeStatus, map[string]string{"message": err.Error()})
		return
	}
	e.ApiResponse(sucCodeStatus, data)
}
