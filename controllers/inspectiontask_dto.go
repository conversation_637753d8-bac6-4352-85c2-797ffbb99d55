package controllers

import (
	"cwsm/infra/wsm"
	"fmt"
)

type InspectiontaskRequestParam struct {
	ClusterId string
}

type InspectiontaskReq interface{}

/* Started by AICoder, pid:d435d5a86492467495b74c9d8d1da05e */
func GetInspectiontask(reqParam InspectiontaskRequestParam) (interface{}, error) {
	keywords := wsm.InspectiontaskWsmKeywords{
		ClusterId: reqParam.ClusterId,
	}
	Inspectiontask, err, suc := wsm.InspectiontaskHandler(keywords, nil, "get")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return Inspectiontask, nil
}

/* Ended by AICoder, pid:d435d5a86492467495b74c9d8d1da05e */

/* Started by AICoder, pid:6af4caaa36d248e3969dcc2aea3e3a61 */
func ActivateInspectiontask(reqParam InspectiontaskRequestParam, reqBody interface{}) (interface{}, error) {
	keywords := wsm.InspectiontaskWsmKeywords{
		ClusterId: reqParam.ClusterId,
	}
	Inspectiontask, err, suc := wsm.InspectiontaskHandler(keywords, reqBody, "post")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return Inspectiontask, nil
}

/* Ended by AICoder, pid:6af4caaa36d248e3969dcc2aea3e3a61 */

/* Started by AICoder, pid:8018ae7c0f654f5db022442f52cd9fa9 */
func DeleteInspectiontask(reqParam InspectiontaskRequestParam) error {
	keywords := wsm.InspectiontaskWsmKeywords{
		ClusterId: reqParam.ClusterId,
	}
	_, err, suc := wsm.InspectiontaskHandler(keywords, nil, "delete")
	if !suc {
		if err == "" {
			return fmt.Errorf("an internal error occurred in the code")
		} else {
			return fmt.Errorf(err)
		}
	}
	return nil
}

/* Ended by AICoder, pid:8018ae7c0f654f5db022442f52cd9fa9 */
