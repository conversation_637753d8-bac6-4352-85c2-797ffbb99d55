package controllers

import (
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/logger"
	"fmt"
)

/* Started by AICoder, pid:54786a6711cd46958c1df6fc373865e3 */
func GetHealth(clusterId string) (interface{}, error) {
	keywords := wsm.HealthWsmKeywords{
		ClusterId: clusterId,
	}

	logger.Infof("get health")

	health, err, suc := wsm.HealthHandler(keywords, nil, "get")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return health, nil
}

/* Ended by AICoder, pid:54786a6711cd46958c1df6fc373865e3 */

/* Started by AICoder, pid:a45ec0a9fc334fd183513cedd7c33d3f */
func GetHealthCfg(clusterId string) (interface{}, error) {
	keywords := wsm.HealthWsmKeywords{
		ClusterId: clusterId,
	}

	logger.Infof("get health cfg")

	health, err, suc := wsm.HealthHandler(keywords, nil, "gethealthcheckcfg")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return health, nil
}

/* Ended by AICoder, pid:a45ec0a9fc334fd183513cedd7c33d3f */

/* Started by AICoder, pid:y4990qc39a45a121491e0970e07d1f16f8d7f621 */
func GetHealthCheckGPUNodesCfg(clusterId string) (interface{}, error) {
	keywords := wsm.HealthWsmKeywords{
		ClusterId: clusterId,
	}

	logger.Infof("get health check gpu nodes cfg")

	health, err, suc := wsm.HealthHandler(keywords, nil, "gethealthcheckgpunodescfg")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return health, nil
}

/* Ended by AICoder, pid:y4990qc39a45a121491e0970e07d1f16f8d7f621 */

/* Started by AICoder, pid:bd50683d312a4eb1acf01df24033abd9 */
func ActivateHealth(clusterId string, reqBody []byte) (interface{}, error) {
	keywords := wsm.HealthWsmKeywords{
		ClusterId: clusterId,
	}

	logger.Infof("activate health")

	health, err, suc := wsm.HealthHandler(keywords, reqBody, "post")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return health, nil
}

/* Ended by AICoder, pid:bd50683d312a4eb1acf01df24033abd9 */
