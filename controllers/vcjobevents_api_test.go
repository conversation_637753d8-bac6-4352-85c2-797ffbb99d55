package controllers

import (
	"cwsm/infra/configcenter"
	"cwsm/infra/cwsmutils"
	"cwsm/models"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	beego "github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:v13e84283498d5814d890900a08c8853fa898b86 */
func GetVcjobBeegoController() *beego.Controller {
	beegoController := &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/tester/vcjob1?vcjobId=1234",
			},
				Form: map[string][]string{}},
			Input: &context.BeegoInput{
				Context: &context.Context{
					Request: &http.Request{URL: &url.URL{
						Host: "inner-router-director:8241",
						Path: "/api/v1.0/cwsm/cluster/cluster123/apts/tester/vcjob1?vcjobId=1234",
					},
						Form: map[string][]string{}},
				},
			},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
	return beegoController
}

var _ = Describe("VcjobEventsController Get", func() {
	var (
		patcher *gomonkey.Patches
		ctrl    *VcjobEventsController
		//rec            *httptest.ResponseRecorder
		cwsmController  *cwsmutils.Controller
		beegoController *beego.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})

		patcher.ApplyMethod(reflect.TypeOf(beegoController), "GetString", func(*beego.Controller, string, ...string) string {
			return "1234"
		})
		//rec = httptest.NewRecorder()
		beegoController := GetVcjobBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &VcjobEventsController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("WhenQueryResourcebyUuid failed", func() {
		It("it should return err", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/tester/vcjob1?vcjobId=1234", clusterId), nil)
			ctrl.Ctx.Request = req
			patcher.ApplyFunc(models.QueryResourceByUuid, func(string, string) (map[string]interface{}, error) {
				return map[string]interface{}{}, fmt.Errorf("query failed")
			})
			ctrl.GetVcjobEvents()
		})
	})

	Context("When QueryResourcebyUuid return empty result", func() {
		It("it should return err", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/tester/vcjob1?vcjobId=1234", clusterId), nil)
			ctrl.Ctx.Request = req
			patcher.ApplyFunc(models.QueryResourceByUuid, func(string, string) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			ctrl.GetVcjobEvents()
		})
	})

	Context("When QueryResourcebyUuid return result, but faultInfo is empty", func() {
		It("it should return err", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/tester/vcjob1?vcjobId=1234", clusterId), nil)
			ctrl.Ctx.Request = req
			patcher.ApplyFunc(models.QueryResourceByUuid, func(string, string) (map[string]interface{}, error) {
				return map[string]interface{}{"uuid": "1234"}, nil
			})
			ctrl.GetVcjobEvents()
		})
	})

	Context("When QueryResourcebyUuid return result, but faultInfo cannot unmarshal to map", func() {
		It("it should return err", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/tester/vcjob1?vcjobId=1234", clusterId), nil)
			ctrl.Ctx.Request = req
			patcher.ApplyFunc(models.QueryResourceByUuid, func(string, string) (map[string]interface{}, error) {
				return map[string]interface{}{"uuid": "1234", "faultInfo": "1"}, nil
			})
			ctrl.GetVcjobEvents()
		})
	})
})

/* Ended by AICoder, pid:v13e84283498d5814d890900a08c8853fa898b86 */

/* Started by AICoder, pid:m6bd6zd23e9382d14eeb09961093f256d3082c04 */
func VcjobPostBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/modelinspection",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController Post", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *VcjobEventsController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := VcjobPostBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &VcjobEventsController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When PostConfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/modelinspection", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(configcenter.ActivateVcjobConfig, func(clusterId string, reqBody []byte) (interface{}, error) {
				return nil, nil
			})

			ctrl.Post()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:m6bd6zd23e9382d14eeb09961093f256d3082c04 */
