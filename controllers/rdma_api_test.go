package controllers

import (
	"bytes"
	"cwsm/infra/cwsmutils"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:x27915bfeb7e8bc14acc086710a56c425ce7aeaf */
var _ = Describe("RdmaController Get", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *RdmaController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()

		beegoController := GetBeegoController()

		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &RdmaController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetRdma returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/rdma/cluster/%s", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")

			patcher.ApplyFunc(GetRdma, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			ctrl.Get()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:x27915bfeb7e8bc14acc086710a56c425ce7aeaf */

/* Started by AICoder, pid:s8e726b4a3x2b3f148570ba2d0914f6d3e04b35f */
var _ = Describe("RdmaController GetRdmaTopoCfg", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *RdmaController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()

		beegoController := GetBeegoController()

		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &RdmaController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetRdmaTopo returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/rdma/topo/cluster/%s", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")

			patcher.ApplyFunc(GetRdmaTopo, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			ctrl.GetRdmaTopoCfg()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})

	Context("When GetRdmaTopo returns data", func() {
		It("should respond with data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/rdma/topo/cluster/%s", clusterId), nil)
			ctrl.Ctx.Request = req

			data := map[string]interface{}{"topology": "mock topology"}
			patcher.ApplyFunc(GetRdmaTopo, func(clusterId string) (interface{}, error) {
				return data, nil
			})

			ctrl.GetRdmaTopoCfg()

			Expect(rec.Code).To(Equal(http.StatusOK))
		})
	})
})

/* Ended by AICoder, pid:s8e726b4a3x2b3f148570ba2d0914f6d3e04b35f */

/* Started by AICoder, pid:ef69d79e8ede7b41475f0a580070c440def8051c */
var _ = Describe("RdmaController Post", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *RdmaController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()

		beegoController := GetBeegoController()

		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &RdmaController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When ActivateRdma returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			reqBody := []byte(`{"param": "value"}`)
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/rdma/cluster/%s", clusterId), bytes.NewReader(reqBody))
			ctrl.Ctx.Request = req
			err := errors.New("activation failed")

			patcher.ApplyFunc(ActivateRdma, func(clusterId string, body []byte) (interface{}, error) {
				return nil, err
			})

			ctrl.Post()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:ef69d79e8ede7b41475f0a580070c440def8051c */

/* Started by AICoder, pid:840c5a9e12fcbc0145a9094bd08e114bc896c722 */
var _ = Describe("RdmaController Stop", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *RdmaController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()

		beegoController := GetBeegoController()

		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &RdmaController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When StopRdma succeeds", func() {
		It("should respond with status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/rdma/cluster/%s/stop", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(StopRdma, func(clusterId string) error {
				return nil
			})

			ctrl.Stop()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:840c5a9e12fcbc0145a9094bd08e114bc896c722 */
