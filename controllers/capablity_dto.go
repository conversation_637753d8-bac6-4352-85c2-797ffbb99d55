package controllers

import (
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/logger"
	"fmt"
)

func GetAccelerator(clusterId string) (interface{}, error) {
	data, suc := wsm.AdrmHandler(clusterId, nil, "get")
	if !suc {
		logger.Error("get information from acceleratordevice failed")
		return nil, fmt.Errorf("get information from acceleratordevice failed")
	}
	return data, nil
}

func GetNodes(clusterId string) (interface{}, error) {
	data, suc := wsm.AdrmHandler(clusterId, nil, "getnode")
	if !suc {
		logger.Error("get node information failed")
		return nil, fmt.Errorf("get node information failed")
	}
	return data, nil
}
