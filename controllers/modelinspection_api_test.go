package controllers

import (
	"cwsm/infra/cwsmutils"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	beego "github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:v13e84283498d5814d890900a08c8853fa898b86 */
func GetBeegoController() *beego.Controller {
	beegoController := &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/modelinspection",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
	return beegoController
}

var _ = Describe("ModelinspectionController Get", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetModelinspection returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/modelinspection", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")
			patcher.ApplyFunc(GetModelinspection, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			ctrl.Get()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:v13e84283498d5814d890900a08c8853fa898b86 */

/* Started by AICoder, pid:v4deemc4a29835514db30a6280fe825f4b48e181 */
func GetModelinspectionConfigBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/modelinspectionconfig",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController GetModelinspectionConfig", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetModelinspectionConfigBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetModelinspectionConfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/modelinspectionconfig", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(GetModelinspectionConfigs, func(clusterId string) (interface{}, error) {
				return nil, nil
			})

			ctrl.GetModelinspectionConfig()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:v4deemc4a29835514db30a6280fe825f4b48e181 */

/* Started by AICoder, pid:f08a2z877877ce314ded0886101597581148975f */
func GetClRdmaInspectionConfigBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/clrdmainspectionconfig",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController GetClRdmaInspectionConfig", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetClRdmaInspectionConfigBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetClRdmaInspectionConfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/clrdmainspectionconfig", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(GetClRdmaInspectionConfigs, func(clusterId string) (interface{}, error) {
				return nil, nil
			})

			ctrl.GetClRdmaInspectionConfig()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:f08a2z877877ce314ded0886101597581148975f */

/* Started by AICoder, pid:gafadz39c8c47cb1438e0813106a1e59b4782095 */
func GetNamespaceBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/namespace",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController GetNamespaceConfig", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetNamespaceBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetNamespaceConfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/namespace", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(GetNamespaceConfigs, func(clusterId string) (interface{}, error) {
				return nil, nil
			})

			ctrl.GetNamespace()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:gafadz39c8c47cb1438e0813106a1e59b4782095 */

/* Started by AICoder, pid:c54515f25210e6e14bc9095300e9285ef65846b3 */
func GetDataSetsBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/datasets",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController GetDataSetsConfig", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetDataSetsBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetDataSetsConfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/datasets", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(GetNDataSetsConfigs, func(clusterId string) (interface{}, error) {
				return nil, nil
			})

			ctrl.GetDataSets()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:c54515f25210e6e14bc9095300e9285ef65846b3 */

/* Started by AICoder, pid:r2c5d91ddca651214e32087980958456b5f83c4c */
func PostBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/modelinspection",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController Post", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := PostBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When PostConfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/modelinspection", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(ActivateModelinspection, func(clusterId string, reqBody []byte) (interface{}, error) {
				return nil, nil
			})

			ctrl.Post()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:r2c5d91ddca651214e32087980958456b5f83c4c */

/* Started by AICoder, pid:m8e8cn0135239d714732081d9094c658a1b868e5 */
func PostModelinspectionConfigBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/modelinspection",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController PostModelinspectionConfig", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := PostModelinspectionConfigBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When PostModelinspectionConfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/modelinspection", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(ActivateModelinspectionConfigs, func(clusterId string, reqBody []byte) (interface{}, error) {
				return nil, nil
			})

			ctrl.PostModelinspectionConfig()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:m8e8cn0135239d714732081d9094c658a1b868e5 */

/* Started by AICoder, pid:rde7at7063m151214b540a2b60fb4351abb8de48 */
func PostClRdmaInspectionConfigBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/clrdmainspectionconfig",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController PostClRdmaInspectionConfig", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := PostClRdmaInspectionConfigBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When PostClRdmaInspectionConfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/clrdmainspectionconfig", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(ActivateClRdmaInspectionConfigs, func(clusterId string, reqBody []byte) (interface{}, error) {
				return nil, nil
			})

			ctrl.PostClRdmaInspectionConfig()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:rde7at7063m151214b540a2b60fb4351abb8de48 */

/* Started by AICoder, pid:1ba6b35d97z7bdb1411e09e21076125b145807d1 */
func PostNamespaceBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/namespace",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController PostNamespaceConfig", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := PostNamespaceBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When PostNamespaceConfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/namespace", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(ActivateNamespaceConfigs, func(clusterId string, reqBody []byte) (interface{}, error) {
				return nil, nil
			})

			ctrl.PostNamespace()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:1ba6b35d97z7bdb1411e09e21076125b145807d1 */

/* Started by AICoder, pid:h37bfs4ab9p03cd149050b06e0451a54e019fbd9 */
func StopBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/modelinspection/stop/task123",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController Stop", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := StopBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When StopConfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			taskId := "task123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/modelinspection/stop/%s", clusterId, taskId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(StopModelinspection, func(clusterId string, taskId string) error {
				return nil
			})

			ctrl.Stop()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:h37bfs4ab9p03cd149050b06e0451a54e019fbd9 */

/* Started by AICoder, pid:y8c78a45a04ab1d14b6b0a88d0eb3e589279ddf6 */
func DeleteBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/modelinspection/delete/task123",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController Delete", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := DeleteBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When DeleteConfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			taskId := "task123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/modelinspection/delete/%s", clusterId, taskId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(DeleteModelinspection, func(clusterId string, taskId string) error {
				return nil
			})

			ctrl.Delete()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:y8c78a45a04ab1d14b6b0a88d0eb3e589279ddf6 */

func GetCclinspectionBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/cclinspectionconfig",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController GetCclinspectionconfig", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetCclinspectionBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetCclinspectionconfigs returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/cclinspectionconfig", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(GetCclinspectionconfigs, func(clusterId string) (interface{}, error) {
				return nil, nil
			})

			ctrl.GetCclinspectionconfig()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

func PostCclinspectionBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/cclinspectionconfig",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

var _ = Describe("ModelinspectionController PostCclinspectionconfig", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ModelinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := PostCclinspectionBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ModelinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When PostCclinspectionconfig returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/cclinspectionconfig", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(ActivateCclinspectionconfig, func(clusterId string, reqBody []byte) (interface{}, error) {
				return nil, nil
			})

			ctrl.PostCclinspectionconfig()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})
