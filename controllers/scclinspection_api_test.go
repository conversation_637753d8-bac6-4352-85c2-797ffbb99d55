package controllers

import (
	"bytes"
	"cwsm/infra/cwsmutils"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:x1c9064e2b097c0149ba0b9c30dcaa4bd284b6cf */
var _ = Describe("ScclinspectionController Get", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ScclinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ScclinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetScclinspection returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")
			patcher.ApplyFunc(GetScclinspection, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			ctrl.Get()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:x1c9064e2b097c0149ba0b9c30dcaa4bd284b6cf */

/* Started by AICoder, pid:z0621q2d97hd8771420f0a4cc0db8c46c6851264 */
var _ = Describe("ScclinspectionController Post", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ScclinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ScclinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When ActivateScclinspection returns valid data", func() {
		It("should respond with valid data and status 200", func() {
			clusterId := "cluster123"
			reqBody := []byte(`{"key": "value"}`)
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection", clusterId), bytes.NewReader(reqBody))
			ctrl.Ctx.Request = req

			expectedData := map[string]interface{}{"status": "activated"}
			patcher.ApplyFunc(ActivateScclinspection, func(clusterId string, body []byte) (interface{}, error) {
				return expectedData, nil
			})

			ctrl.Post()

			Expect(rec.Code).To(Equal(http.StatusOK))
		})
	})
})

/* Ended by AICoder, pid:z0621q2d97hd8771420f0a4cc0db8c46c6851264 */

/* Started by AICoder, pid:jb17ac8713dfb11149870b37b0292c4492d4f21f */
var _ = Describe("ScclinspectionController Stop", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *ScclinspectionController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &ScclinspectionController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When StopScclinspection succeeds", func() {
		It("should respond with status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection/stop", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(StopScclinspection, func(clusterId string) error {
				return nil
			})

			ctrl.Stop()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(BeEmpty())
		})
	})
})

/* Ended by AICoder, pid:jb17ac8713dfb11149870b37b0292c4492d4f21f */
