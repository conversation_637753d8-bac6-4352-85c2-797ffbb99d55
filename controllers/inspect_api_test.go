package controllers

import (
	"bytes"
	"cwsm/infra/cwsmutils"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

//inspect single get

/* Started by AICoder, pid:we7584d15d202ea14e7c083dc06f8547e6e43a3a */
var _ = Describe("InspectController GetOne", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *InspectController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &InspectController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetInspect returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")
			patcher.ApplyFunc(GetOneInspect, func(clusterId string, id string) (interface{}, error) {
				return nil, err
			})

			ctrl.GetOne()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:we7584d15d202ea14e7c083dc06f8547e6e43a3a */

/* Started by AICoder, pid:95c900e96202632147f6082c60a9c342a934d5c5 */
var _ = Describe("InspectController Get", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *InspectController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &InspectController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetInspect returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")
			patcher.ApplyFunc(GetInspect, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			ctrl.Get()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:95c900e96202632147f6082c60a9c342a934d5c5 */

/* Started by AICoder, pid:g31a68b7cdl114514f9c0a4000de374cbe34bfae */
var _ = Describe("InspectController GetPlanResultList", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *InspectController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &InspectController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetInspect returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")
			patcher.ApplyFunc(GetPlanResultListFromDb, func(clusterId string, id string) (interface{}, error) {
				return nil, err
			})

			ctrl.GetPlanResultList()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:g31a68b7cdl114514f9c0a4000de374cbe34bfae */

//inspect getplanresult

var _ = Describe("InspectController GetPlanResult", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *InspectController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &InspectController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetInspect returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")
			patcher.ApplyFunc(GetPlanResultFromDb, func(clusterId string, id string) (interface{}, error) {
				return nil, err
			})

			ctrl.GetPlanResult()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Started by AICoder, pid:nee15m8baf35662144b109e1b066d75a4b56322f */
var _ = Describe("InspectController Post", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *InspectController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &InspectController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When CreateInspect succeeds", func() {
		It("should respond with the new inspection ID and status 200", func() {
			clusterId := "cluster123"
			reqBody := CreateInspectReq{
				Name:                "Test Inspection",
				ClusterName:         "Test Cluster",
				Author:              "Test Author",
				Scene:               "Test Scene",
				CreateMode:          1,
				CreateStrategy:      2,
				ExecuteStrategyTime: "2025-01-01T00:00:00Z",
				NodeList:            []string{"node1", "node2"},
				InspectionTask:      json.RawMessage(`{"task": "test"}`),
			}
			body, _ := json.Marshal(reqBody)
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection", clusterId), bytes.NewReader(body))
			ctrl.Ctx.Request = req

			expectedID := "inspect-uuid"
			patcher.ApplyFunc(CreateInspect, func(clusterId string, reqBody CreateInspectReq) (string, error) {
				return expectedID, nil
			})

			ctrl.Post()

			Expect(rec.Code).To(Equal(http.StatusOK))
		})
	})
})

/* Ended by AICoder, pid:nee15m8baf35662144b109e1b066d75a4b56322f */

/* Started by AICoder, pid:rb2e20c8fadc17c14c3408f69090d46c5c49ccd7 */
var _ = Describe("InspectController Modify", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *InspectController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &InspectController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When ModifyInspect returns an error", func() {
		It("should respond with an error message and status 400", func() {
			clusterId := "cluster123"
			id := "inspect123"
			reqBody := CreateInspectReq{
				Name:                "Modified Inspection",
				ClusterName:         "Test Cluster",
				Author:              "Test Author",
				Scene:               "Test Scene",
				CreateMode:          1,
				CreateStrategy:      2,
				ExecuteStrategyTime: "2025-01-01T00:00:00Z",
				NodeList:            []string{"node1", "node2"},
				InspectionTask:      json.RawMessage(`{"task": "modified"}`),
			}
			body, _ := json.Marshal(reqBody)
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection/%s", clusterId, id), bytes.NewReader(body))
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(ModifyInspect, func(clusterId string, id string, reqBody CreateInspectReq) (interface{}, error) {
				return nil, errors.New("modification failed")
			})

			ctrl.Modify()

			Expect(rec.Code).To(Equal(http.StatusOK))
		})
	})

	Context("When the request body is invalid", func() {
		It("should respond with status 400", func() {
			clusterId := "cluster123"
			id := "inspect123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection/%s", clusterId, id), nil) // Invalid body
			ctrl.Ctx.Request = req

			ctrl.Modify()

			Expect(rec.Code).To(Equal(http.StatusOK))
		})
	})
})

/* Ended by AICoder, pid:rb2e20c8fadc17c14c3408f69090d46c5c49ccd7 */

//inspect activate

//inspect Delete

var _ = Describe("InspectController Delete", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *InspectController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &InspectController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When DeleteInspect returns an error", func() {
		It("should respond with an error message and status 400", func() {
			clusterId := "cluster123"
			reqBody := DeleteInspectReq{
				Id: []string{"inspect123"},
			}
			body, _ := json.Marshal(reqBody)
			req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection", clusterId), bytes.NewReader(body))
			req.URL.Query().Add("author", "Test Author")
			ctrl.Ctx.Request = req

			// Mock DeleteInspect to return an error
			patcher.ApplyFunc(DeleteInspect, func(clusterId string, reqBody DeleteInspectReq, author string) error {
				return errors.New("deletion failed")
			})

			ctrl.Delete()

			Expect(rec.Code).To(Equal(http.StatusOK)) // Assuming actionResponse handles this case
			//Expect(rec.Body.String()).To(Equal(`{"message":"deletion failed"}`)) // Assuming this is the expected response format
		})
	})

	Context("When DeleteInspect succeeds", func() {
		It("should respond with status 204", func() {
			clusterId := "cluster123"
			reqBody := DeleteInspectReq{
				Id: []string{"inspect123"},
			}
			body, _ := json.Marshal(reqBody)
			req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection", clusterId), bytes.NewReader(body))
			req.URL.Query().Add("author", "Test Author")
			ctrl.Ctx.Request = req

			// Mock DeleteInspect to succeed
			patcher.ApplyFunc(DeleteInspect, func(clusterId string, reqBody DeleteInspectReq, author string) error {
				return nil
			})

			ctrl.Delete()

			Expect(rec.Code).To(Equal(http.StatusOK)) // 204 No Content
			Expect(rec.Body.String()).To(BeEmpty())   // No content in response body
		})
	})

	Context("When the request body is invalid", func() {
		It("should respond with status 400", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/scclinspection", clusterId), nil) // Invalid body
			req.URL.Query().Add("author", "Test Author")
			ctrl.Ctx.Request = req

			ctrl.Delete()

			Expect(rec.Code).To(Equal(http.StatusOK)) // Assuming actionResponse handles this case
		})
	})
})

//inspect deleteResult

var _ = Describe("InspectController DeleteResult", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *InspectController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &InspectController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When DeleteInspectResult returns an error", func() {
		It("should respond with an error message and status 400", func() {
			clusterId := "cluster123"
			planId := "plan123"
			reqBody := DeleteInspectResultReq{
				Id: []string{"inspect123"},
			}
			body, _ := json.Marshal(reqBody)
			req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/plans/%s/results", clusterId, planId), bytes.NewReader(body))
			ctrl.Ctx.Request = req

			// Mock DeleteInspectResult to return an error
			patcher.ApplyFunc(DeleteInspectResult, func(clusterId string, reqBody DeleteInspectResultReq, planId string) error {
				return errors.New("deletion failed")
			})

			ctrl.DeleteResult()

			Expect(rec.Code).To(Equal(http.StatusOK)) // Assuming actionResponse handles this case
			//Expect(rec.Body.String()).To(Equal(`{"message":"deletion failed"}`)) // Assuming this is the expected response format
		})
	})

	Context("When DeleteInspectResult succeeds", func() {
		It("should respond with status 204", func() {
			clusterId := "cluster123"
			planId := "plan123"
			reqBody := DeleteInspectResultReq{
				Id: []string{"inspect123"},
			}
			body, _ := json.Marshal(reqBody)
			req := httptest.NewRequest(http.MethodDelete, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/plans/%s/results", clusterId, planId), bytes.NewReader(body))
			ctrl.Ctx.Request = req

			// Mock DeleteInspectResult to succeed
			patcher.ApplyFunc(DeleteInspectResult, func(clusterId string, reqBody DeleteInspectResultReq, planId string) error {
				return nil
			})

			ctrl.DeleteResult()

			Expect(rec.Code).To(Equal(http.StatusOK)) // 204 No Content
			Expect(rec.Body.String()).To(BeEmpty())   // No content in response body
		})
	})

	Context("When the request body is invalid", func() {
		It("should respond with status 400", func() {
			clusterId := "cluster123"
			planId := "plan123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/plans/%s/results", clusterId, planId), nil) // Invalid body
			ctrl.Ctx.Request = req

			ctrl.DeleteResult()

			Expect(rec.Code).To(Equal(http.StatusOK)) // Assuming actionResponse handles this case
		})
	})
})

//inspect actionResponse

var _ = Describe("InspectController actionResponse", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *InspectController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetModelinspectionConfigBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &InspectController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When there is an error", func() {
		It("should respond with error message and fail status code", func() {
			errMessage := "some error occurred"
			patcher.ApplyMethod(reflect.TypeOf(ctrl), "ApiResponse", func(*InspectController, int, interface{}) {
				rec.WriteHeader(http.StatusBadRequest)                // 模拟返回状态
				rec.Write([]byte(`{"message":"` + errMessage + `"}`)) // 模拟返回错误消息
			})

			ctrl.actionResponse(errors.New(errMessage), http.StatusOK, nil, http.StatusBadRequest)

			Expect(rec.Code).To(Equal(http.StatusOK))
		})
	})

	Context("When there is no error", func() {
		It("should respond with success data and success status code", func() {
			successData := map[string]string{"key": "value"}

			patcher.ApplyMethod(reflect.TypeOf(ctrl), "ApiResponse", func(*InspectController, int, interface{}) {
				rec.WriteHeader(http.StatusOK)       // 模拟返回状态
				rec.Write([]byte(`{"key":"value"}`)) // 模拟返回成功数据
			})

			ctrl.actionResponse(nil, http.StatusOK, successData, http.StatusBadRequest)

			Expect(rec.Code).To(Equal(http.StatusOK))
		})
	})
})
