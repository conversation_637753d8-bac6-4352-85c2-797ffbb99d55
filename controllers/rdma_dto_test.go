package controllers

import (
	"cwsm/infra/wsm"
	"errors"
	"fmt"

	gomonkey "github.com/agiledragon/gomonkey/v2"

	. "github.com/onsi/ginkgo"

	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:t04d0i90d6mb291147b70b67107bc47902119d19 */
var _ = Describe("TestGetRdma", func() {
	var (
		reqParam wsm.RdmaWsmKeywords
		patcher  *gomonkey.Patches
	)

	BeforeEach(func() {
		reqParam = wsm.RdmaWsmKeywords{
			ClusterId: "cluster123",
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.RdmaHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := GetRdma(reqParam.ClusterId)

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.RdmaHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"id": 1, "name": "rdma1"}
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := GetRdma(reqParam.ClusterId)

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.RdmaHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := GetRdma(reqParam.ClusterId)

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.RdmaHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := GetRdma(reqParam.ClusterId)

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:t04d0i90d6mb291147b70b67107bc47902119d19 */

/* Started by AICoder, pid:2d315w3874b3d3e1401c0a7df0e63c74da71750b */
var _ = Describe("TestGetRdmaTopo", func() {
	var (
		reqParam wsm.RdmaWsmKeywords
		patcher  *gomonkey.Patches
	)

	BeforeEach(func() {
		reqParam = wsm.RdmaWsmKeywords{
			ClusterId: "cluster123",
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.RdmaHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := GetRdmaTopo(reqParam.ClusterId)

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.RdmaHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"topology": "rdma_topo"}
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := GetRdmaTopo(reqParam.ClusterId)

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.RdmaHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := GetRdmaTopo(reqParam.ClusterId)

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.RdmaHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := GetRdmaTopo(reqParam.ClusterId)

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:2d315w3874b3d3e1401c0a7df0e63c74da71750b */

/* Started by AICoder, pid:l1541f8e8eh65e21485108e3902eaa7ca9d1006e */
var _ = Describe("TestActivateRdma", func() {
	var (
		clusterId string
		reqBody   []byte
		patcher   *gomonkey.Patches
	)

	BeforeEach(func() {
		clusterId = "cluster123"
		reqBody = []byte(`{"key": "value"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.RdmaHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, body []byte, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := ActivateRdma(clusterId, reqBody)

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.RdmaHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"status": "activated"}
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, body []byte, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := ActivateRdma(clusterId, reqBody)

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.RdmaHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, body []byte, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := ActivateRdma(clusterId, reqBody)

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.RdmaHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, body []byte, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := ActivateRdma(clusterId, reqBody)

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:l1541f8e8eh65e21485108e3902eaa7ca9d1006e */

/* Started by AICoder, pid:qbabe4e627k1f77143ba0b7b205b4152d9e24131 */
var _ = Describe("TestStopRdma", func() {
	var (
		clusterId string
		patcher   *gomonkey.Patches
	)

	BeforeEach(func() {
		clusterId = "cluster123"
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.RdmaHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			queryErr := StopRdma(clusterId)

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
		})
	})

	Context("When wsm.RdmaHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			queryErr := StopRdma(clusterId)

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
		})
	})

	Context("When wsm.RdmaHandler returns success", func() {
		It("should return no error", func() {
			patcher.ApplyFunc(wsm.RdmaHandler, func(keywords wsm.RdmaWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			queryErr := StopRdma(clusterId)

			Expect(queryErr).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:qbabe4e627k1f77143ba0b7b205b4152d9e24131 */
