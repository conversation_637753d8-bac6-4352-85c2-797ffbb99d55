package controllers

import (
	"cwsm/infra/cwsmutils"
	"cwsm/models"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"net/http"
)

type DebugController struct {
	cwsmutils.Controller
}

func (d *DebugController) SwitchLogLevel() {
	dstLevel := d.Ctx.Input.Param(":level")
	goodLogLevel := []string{"info", "debug", "warn", "error", "panic", "fetal"}
	if cwsmutils.IsEleInSlice(goodLogLevel, dstLevel) {
		logger.SetLogLevel(dstLevel)
		curLevel := logger.GetLogLevel()
		d.ApiResponse(http.StatusOK, curLevel)
		return
	}
	d.ApiResponse(http.StatusInternalServerError, "Miss level")
}

func (d *DebugController) GetDbInfo() {
	tableName := d.Ctx.Input.Param(":tablename")
	searchRes, err := models.QueryResourceAll(tableName)
	if err != nil {
		d.ApiResponse(http.StatusInternalServerError, err.Error())
		return
	}
	d.ApiResponse(http.StatusOK, searchRes)
}

func (d *DebugController) InsertRow() {
	tableName := d.Ctx.Input.Param(":tablename")
	rawData := make(map[string]interface{})
	if err := json.Unmarshal(d.Ctx.Input.RequestBody, &rawData); err != nil {
		d.ApiResponse(http.StatusInternalServerError, err.Error())
		return
	}
	insertRes := models.InsertNewResource(tableName, rawData)
	if !insertRes {
		d.ApiResponse(http.StatusInternalServerError, "Insert table Error")
		return
	}
	d.ApiResponse(http.StatusOK, "Success")
}

func (d *DebugController) DeleteById() {
	tableName := d.Ctx.Input.Param(":tablename")
	id := d.Ctx.Input.Param(":id")
	res := models.DeleteResourceById(tableName, id)
	if res {
		d.ApiResponse(http.StatusOK, "Success")
		return
	}
	d.ApiResponse(http.StatusInternalServerError, "Delete Error")
}

func (d *DebugController) ChangeRowById() {
	tableName := d.Ctx.Input.Param(":tablename")
	id := d.Ctx.Input.Param(":id")
	updateReq := map[string]interface{}{}
	if err := json.Unmarshal(d.Ctx.Input.RequestBody, &updateReq); err != nil {
		d.ApiResponse(http.StatusInternalServerError, err.Error())
		return
	}
	res := models.UpdateResourceById(tableName, id, updateReq)
	if res {
		d.ApiResponse(http.StatusOK, "Success")
		return
	}
	d.ApiResponse(http.StatusInternalServerError, "Update Error")
}
