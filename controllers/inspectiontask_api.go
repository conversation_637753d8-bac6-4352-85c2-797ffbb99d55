//inspectiontask

package controllers

import (
	"cwsm/infra/cwsmutils"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"net/http"
)

type InspectiontaskController struct {
	cwsmutils.Controller
}

func (e *InspectiontaskController) Get() {
	reqParam := e.getRequestParam("get")
	data, err := GetInspectiontask(reqParam)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *InspectiontaskController) Post() {
	reqParam := e.getRequestParam("post")
	var reqBody InspectiontaskReq
	if !e.getRequestBody("post", &reqBody) {
		return
	}
	data, err := ActivateInspectiontask(reqParam, reqBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *InspectiontaskController) Delete() {
	reqParam := e.getRequestParam("delete")
	err := DeleteInspectiontask(reqParam)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

func (e *InspectiontaskController) getRequestParam(method string) InspectiontaskRequestParam {
	clusterId := e.Ctx.Input.Param(":clusterId")
	logger.Debugf("method:%s input clusterId:%s", method, clusterId)
	return InspectiontaskRequestParam{ClusterId: clusterId}
}

func (e *InspectiontaskController) actionResponse(err error, sucCodeStatus int, data interface{}, failCodeStatus int) {
	if err != nil {
		e.ApiResponse(failCodeStatus, map[string]string{"message": err.Error()})
		return
	}
	e.ApiResponse(sucCodeStatus, data)
}

func (e *InspectiontaskController) getRequestBody(method string, reqBody interface{}) bool {
	var err error
	switch method {
	case "post":
		err = json.Unmarshal(e.Ctx.Input.RequestBody, &reqBody)
	default:
		return false
	}
	if err != nil {
		logger.Errorf("method:%s request body Unmarshal failed", method)
		e.ApiResponse(http.StatusBadRequest, map[string]string{"message": err.Error()})
		return false
	}
	return true
}
