package controllers

import (
	"cwsm/infra/wsm"
	"fmt"
)

type ModelinspectionRequestParam struct {
	ClusterId string
	TaskId    string
}

/* Started by AICoder, pid:t531b2c7a7v571814bfd0bee90489c17870462a7 */
func GetModelinspection(clusterId string) (interface{}, error) {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
	}
	modelinspection, err, suc := wsm.ModelinspectionHandler(keywords, nil, "get")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return modelinspection, nil
}

/* Ended by AICoder, pid:t531b2c7a7v571814bfd0bee90489c17870462a7 */

/* Started by AICoder, pid:68d96d10aad8352141530ae7008982189ea50a0d */
func GetModelinspectionConfigs(clusterId string) (interface{}, error) {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	modelinspectioncfg, err, suc := wsm.ModelinspectionHandler(keywords, nil, "getmodelinspectionconfigs")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return modelinspectioncfg, nil
}

/* Ended by AICoder, pid:68d96d10aad8352141530ae7008982189ea50a0d */

/* Started by AICoder, pid:b5cf5kdd71g11b0148010b07f0fa1519ce954ebb */
func GetClRdmaInspectionConfigs(clusterId string) (interface{}, error) {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	clrdmainspectioncfg, err, suc := wsm.ModelinspectionHandler(keywords, nil, "getclrdmainspectionconfigs")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return clrdmainspectioncfg, nil
}

/* Ended by AICoder, pid:b5cf5kdd71g11b0148010b07f0fa1519ce954ebb */

func GetCclinspectionconfigs(clusterId string) (interface{}, error) {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	cclinspectioncfg, err, suc := wsm.ModelinspectionHandler(keywords, nil, "getcclinspectionconfig")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("failed to get cluster information or timeout")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return cclinspectioncfg, nil
}

/* Started by AICoder, pid:424b9rf5eaoc82114c270b4110125b1d7da5b9d1 */
func GetNamespaceConfigs(clusterId string) (interface{}, error) {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	namespace, err, suc := wsm.ModelinspectionHandler(keywords, nil, "getnamespace")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return namespace, nil
}

/* Ended by AICoder, pid:424b9rf5eaoc82114c270b4110125b1d7da5b9d1 */

/* Started by AICoder, pid:z2023l14ddqf8651443a0989701f601142b53438 */
func GetNDataSetsConfigs(clusterId string) (interface{}, error) {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	namespace, err, suc := wsm.ModelinspectionHandler(keywords, nil, "getdatasets")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return namespace, nil
}

/* Ended by AICoder, pid:z2023l14ddqf8651443a0989701f601142b53438 */

/* Started by AICoder, pid:l6e3cw2f6d737a8142ad08b7f06d1718f7f5b4e8 */
func ActivateModelinspection(clusterId string, reqBody []byte) (interface{}, error) {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	modelinspection, err, suc := wsm.ModelinspectionHandler(keywords, reqBody, "post")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return modelinspection, nil
}

/* Ended by AICoder, pid:l6e3cw2f6d737a8142ad08b7f06d1718f7f5b4e8 */

/* Started by AICoder, pid:u09351483crc3f414877098fd0b91f1a1fa58f9b */
func ActivateModelinspectionConfigs(clusterId string, reqBody []byte) (interface{}, error) {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	modelinspectioncfg, err, suc := wsm.ModelinspectionHandler(keywords, reqBody, "postmodelinspectionconfigs")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return modelinspectioncfg, nil
}

/* Ended by AICoder, pid:u09351483crc3f414877098fd0b91f1a1fa58f9b */

/* Started by AICoder, pid:y18b5ddce7hf36a145b5089ed0e9fe1148154c88 */
func ActivateClRdmaInspectionConfigs(clusterId string, reqBody []byte) (interface{}, error) {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	clrdmainspectioncfg, err, suc := wsm.ModelinspectionHandler(keywords, reqBody, "postclrdmainspectionconfigs")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return clrdmainspectioncfg, nil
}

/* Ended by AICoder, pid:y18b5ddce7hf36a145b5089ed0e9fe1148154c88 */

func ActivateCclinspectionconfig(clusterId string, reqBody []byte) (interface{}, error) {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	cclinspectioncfg, err, suc := wsm.ModelinspectionHandler(keywords, reqBody, "postcclinspectionconfig")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("failed to get cluster information or timeout")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return cclinspectioncfg, nil
}

/* Started by AICoder, pid:v3286s27553e3891470709e000e24310b275d493 */
func ActivateNamespaceConfigs(clusterId string, reqBody []byte) (interface{}, error) {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
	}

	namespace, err, suc := wsm.ModelinspectionHandler(keywords, reqBody, "postnamespace")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return namespace, nil
}

/* Ended by AICoder, pid:v3286s27553e3891470709e000e24310b275d493 */

/* Started by AICoder, pid:w2f07x0df0r0fbc14ab7097e90283e1026b5085f */
func StopModelinspection(clusterId string, taskId string) error {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
		TaskId:    taskId,
	}
	_, err, suc := wsm.ModelinspectionHandler(keywords, nil, "stop")
	if !suc {
		if err == "" {
			return fmt.Errorf("an internal error occurred in the code")
		} else {
			return fmt.Errorf(err)
		}
	}
	return nil
}

/* Ended by AICoder, pid:w2f07x0df0r0fbc14ab7097e90283e1026b5085f */

/* Started by AICoder, pid:v4a29nafc3raaa014545096eb0bf7b11c1c5fa4a */
func DeleteModelinspection(clusterId string, taskId string) error {
	keywords := wsm.ModelinspectionWsmKeywords{
		ClusterId: clusterId,
		TaskId:    taskId,
	}
	_, err, suc := wsm.ModelinspectionHandler(keywords, nil, "delete")
	if !suc {
		if err == "" {
			return fmt.Errorf("an internal error occurred in the code")
		} else {
			return fmt.Errorf(err)
		}
	}
	return nil
}

/* Ended by AICoder, pid:v4a29nafc3raaa014545096eb0bf7b11c1c5fa4a */
