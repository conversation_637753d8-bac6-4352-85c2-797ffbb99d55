package controllers

import (
	"cwsm/infra/configcenter"
	"cwsm/infra/cwsmutils"
	"net/http"
)

type VcjobEventsController struct {
	cwsmutils.Controller
}

func (e *VcjobEventsController) Get() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	projectName := e.Ctx.Input.Param(":projectName")
	data, err := GetNsVcjobEvents(clusterId, projectName)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *VcjobEventsController) GetVcjobEvents() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	projectName := e.Ctx.Input.Param(":projectName")
	name := e.Ctx.Input.Param(":name")
	vcjobId := e.GetString("vcjobId")
	if len(vcjobId) != 0 {
		data, err := GetVcjobWithId(vcjobId)
		e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
		return
	}
	data, err := GetVcjobEvents(clusterId, projectName, name)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *VcjobEventsController) GetVcjobs() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	projectName := e.Ctx.Input.Param(":projectName")
	data, err := GetVcjobs(clusterId, projectName)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *VcjobEventsController) GetVcjobCfg() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetVcjobConfig(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Started by AICoder, pid:o1db3oe9c2bc1c5149950a793095930b47c59e02 */
func (e *VcjobEventsController) Post() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := configcenter.ActivateVcjobConfig(clusterId, e.Ctx.Input.RequestBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:o1db3oe9c2bc1c5149950a793095930b47c59e02 */

func (e *VcjobEventsController) actionResponse(err error, sucCodeStatus int, data interface{}, failCodeStatus int) {
	if err != nil {
		e.ApiResponse(failCodeStatus, map[string]string{"message": err.Error()})
		return
	}
	e.ApiResponse(sucCodeStatus, data)
}
