package controllers

import (
	"cwsm/infra/cwsmutils"
	"net/http"
)

type ScclinspectionController struct {
	cwsmutils.Controller
}

/* Started by AICoder, pid:80287sfe12g2f9d1465008967070860a19556796 */
func (e *ScclinspectionController) Get() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetScclinspection(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:80287sfe12g2f9d1465008967070860a19556796 */

/* Started by AICoder, pid:xa416l463dm40fd143090b70d0177e05b895bd1c */
func (e *ScclinspectionController) Post() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := ActivateScclinspection(clusterId, e.Ctx.Input.RequestBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:xa416l463dm40fd143090b70d0177e05b895bd1c */

/* Started by AICoder, pid:x60e5o0e2fx130614ef4096150bc1109af150234 */
func (e *ScclinspectionController) Stop() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	err := StopScclinspection(clusterId)
	e.actionResponse(err, http.StatusOK, nil, http.StatusBadRequest)
}

/* Ended by AICoder, pid:x60e5o0e2fx130614ef4096150bc1109af150234 */

func (e *ScclinspectionController) actionResponse(err error, sucCodeStatus int, data interface{}, failCodeStatus int) {
	if err != nil {
		e.ApiResponse(failCodeStatus, map[string]string{"message": err.Error()})
		return
	}
	e.ApiResponse(sucCodeStatus, data)
}
