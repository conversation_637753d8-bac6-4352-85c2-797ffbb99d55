package controllers

import (
	"bytes"
	"cwsm/infra/cwsmutils"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:i9ff0j4b3a30ea51408b080bd0f7cd4853e476ae */
var _ = Describe("HealthController Get", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *HealthController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &HealthController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetHealth returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/health", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")
			patcher.ApplyFunc(GetHealth, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			ctrl.Get()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:i9ff0j4b3a30ea51408b080bd0f7cd4853e476ae */

/* Started by AICoder, pid:y9dcdq07cf6f20c14bfc0872a022e24a92041343 */
var _ = Describe("HealthController GetHealthCheckCfg", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *HealthController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &HealthController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetHealthCheckCfg returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/healthcheckcfg", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")
			patcher.ApplyFunc(GetHealthCfg, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			ctrl.GetHealthCheckCfg()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:y9dcdq07cf6f20c14bfc0872a022e24a92041343 */

/* Started by AICoder, pid:t34a1qb34b360811498809cf60933c4a169461ee */
var _ = Describe("HealthController GetHealthCheckGPUNodes", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *HealthController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &HealthController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetHealthCheckGPUNodes returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/healthcheckgpunodes", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")
			patcher.ApplyFunc(GetHealthCheckGPUNodesCfg, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			ctrl.GetHealthCheckGPUNodes()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:t34a1qb34b360811498809cf60933c4a169461ee */

/* Started by AICoder, pid:d1515f0c0754c0b143940aa29000514da5084af3 */
var _ = Describe("HealthController Post", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *HealthController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()

		beegoController := GetBeegoController()

		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &HealthController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When ActivateHealth returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			reqBody := []byte(`{"param": "value"}`)
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/health/cluster/%s", clusterId), bytes.NewReader(reqBody))
			ctrl.Ctx.Request = req
			err := errors.New("activation failed")

			patcher.ApplyFunc(ActivateHealth, func(clusterId string, body []byte) (interface{}, error) {
				return nil, err
			})

			ctrl.Post()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:d1515f0c0754c0b143940aa29000514da5084af3 */
