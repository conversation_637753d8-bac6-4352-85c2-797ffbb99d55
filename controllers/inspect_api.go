package controllers

import (
	"cwsm/infra/cwsmutils"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"net/http"
)

//inspect POST
type InspectController struct {
	cwsmutils.Controller
}

/* Started by AICoder, pid:l9398o3bb4p5b8814d8d0b40706c2f020706644b */
func (e *InspectController) Get() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	logger.Info("Get clusterId:%s", clusterId)
	data, err := GetInspect(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:l9398o3bb4p5b8814d8d0b40706c2f020706644b */

//inspect single get
func (e *InspectController) GetOne() {
	logger.Info("STARTSTARTSTARTGetOne")
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	logger.Info("GetOne clusterId:%s,id:%s", clusterId, id)
	data, err := GetOneInspect(clusterId, id)
	logger.Info("GetOneGetOneGetOne data:%v", data)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

//inspect getplanresultlist
func (e *InspectController) GetPlanResultList() {
	logger.Info("STARTSTARTSTARTGET")
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	logger.Info("GetPlanResultList clusterId:%s,id:%s", clusterId, id)
	data, err := GetPlanResultListFromDb(clusterId, id)
	logger.Info("GetGetGetGetGet data:%v", data)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

//inspect getplanresult
func (e *InspectController) GetPlanResult() {
	logger.Info("STARTSTARTSTART GetPlanResult")
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	resultId := e.Ctx.Input.Param(":resultid")
	logger.Info("GetPlanResultGetPlanResult id:%s,resultId:%s", clusterId, id, resultId)
	data, err := GetPlanResultFromDb(clusterId, id, resultId)
	logger.Info("GetPlanResultGetPlanResult data:%v", data)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Started by AICoder, pid:x0746023a1racfc1441f0b068084ef190151caaf */
func (e *InspectController) Post() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	var reqBody CreateInspectReq
	if !e.getRequestBody("post", &reqBody) {
		logger.Errorf("Post json.Unmarshal error")
		return
	}

	data, err := CreateInspect(clusterId, reqBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:x0746023a1racfc1441f0b068084ef190151caaf */

/* Started by AICoder, pid:w697aa2240b856b14def0aa540e0bf175b128ce0 */
func (e *InspectController) Modify() {
	var reqBody CreateInspectReq
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	if !e.getRequestBody("post", &reqBody) {
		logger.Errorf("Modify json.Unmarshal error")
		return
	}

	data, err := ModifyInspect(clusterId, id, reqBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:w697aa2240b856b14def0aa540e0bf175b128ce0 */

//inspect activate
func (e *InspectController) Activate() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	author := e.Ctx.Input.Query("author")
	err := ActivateInspect(clusterId, id, author)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

//inspect stop
func (e *InspectController) Stop() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	author := e.Ctx.Input.Query("author")
	err := StopInspect(clusterId, id, author)
	e.actionResponse(err, http.StatusOK, nil, http.StatusBadRequest)
}

//inspect Delete
func (e *InspectController) Delete() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	logger.Info("Delete clusterId:%s", clusterId)
	// author := e.Ctx.Input.Query("author")
	var reqBody DeleteInspectReq
	if !e.getRequestBody("delete", &reqBody) {
		return
	}
	//clusterId := e.Ctx.Input.Param(":clusterId")
	author := e.Ctx.Input.Query("author")
	err := DeleteInspect(clusterId, reqBody, author)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

//inspect deleteResult
func (e *InspectController) DeleteResult() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	planId := e.Ctx.Input.Param(":planid")
	logger.Info("DeleteResult clusterId:%s,planId:%s", clusterId, planId)
	var reqBody DeleteInspectResultReq
	if !e.getRequestBody("deleteResult", &reqBody) {
		return
	}
	err := DeleteInspectResult(clusterId, reqBody, planId)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

//inspect POST
func (e *InspectController) getRequestBody(method string, reqBody interface{}) bool {
	var err error
	switch method {
	case "post":
		err = json.Unmarshal(e.Ctx.Input.RequestBody, &reqBody)
	case "delete", "deleteResult":
		err = json.Unmarshal(e.Ctx.Input.RequestBody, &reqBody)
	default:
		return false
	}
	if err != nil {
		logger.Errorf("method:%s request body Unmarshal failed", method)
		e.ApiResponse(http.StatusBadRequest, map[string]string{"message": err.Error()})
		return false
	}
	return true
}

//inspect POST
func (e *InspectController) actionResponse(err error, sucCodeStatus int, data interface{}, failCodeStatus int) {
	if err != nil {
		e.ApiResponse(failCodeStatus, map[string]string{"message": err.Error()})
		return
	}
	e.ApiResponse(sucCodeStatus, data)
}
