package controllers

import (
	"cwsm/infra/cwsmutils"
	"cwsm/tools/commontools/logger"
	"net/http"
)

type HealthController struct {
	cwsmutils.Controller
}

func (e *HealthController) Get() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	logger.Infof("get clusterId:%s", clusterId)
	data, err := GetHealth(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *HealthController) GetHealthCheckCfg() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	logger.Infof("get health check cfg clusterId:%s", clusterId)
	data, err := GetHealthCfg(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Started by AICoder, pid:ibef96b693m6cc0141700b8b605c340869253a82 */
func (e *HealthController) GetHealthCheckGPUNodes() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetHealthCheckGPUNodesCfg(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:ibef96b693m6cc0141700b8b605c340869253a82 */

func (e *HealthController) Post() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	logger.Infof("post clusterId:%s", clusterId)
	data, err := ActivateHealth(clusterId, e.Ctx.Input.RequestBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *HealthController) actionResponse(err error, sucCodeStatus int, data interface{}, failCodeStatus int) {
	if err != nil {
		e.ApiResponse(failCodeStatus, map[string]string{"message": err.Error()})
		return
	}
	e.ApiResponse(sucCodeStatus, data)
}
