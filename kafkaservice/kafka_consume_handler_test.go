package kafkaservice

import (
	"cwsm/infra/configcenter"
	"cwsm/infra/constant"

	"github.com/agiledragon/gomonkey"

	. "github.com/onsi/ginkgo"
)

/* Started by AICoder, pid:67de0wf715d338014d140b11c06e2a7096e4fd0b */
var _ = Describe("TestConsumeTopic", func() {
	var (
		patcher  *gomonkey.Patches
		consumer ConsumerCWSM
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When topic is ConfigCenterRefresh", func() {
		It("should handle the notification correctly when type is startup", func() {
			topic := constant.ConfigCenterRefresh
			value := []byte(`{"type": "startup"}`)
			jobId := "job123"

			patcher.ApplyFunc(configcenter.GetSwitchAndDayAndNumberConfigCenterValue, func() {
				return
			})

			consumer.ConsumeTopic(topic, value, jobId)
		})

		It("should log an error if JSON unmarshal fails", func() {
			topic := constant.ConfigCenterRefresh
			value := []byte(`invalid_json`)
			jobId := "job123"

			consumer.ConsumeTopic(topic, value, jobId)
		})

		It("should call GetNotificationTypeForChangeSwitchDayNumber for refresh type", func() {
			topic := constant.ConfigCenterRefresh
			value := []byte(`{"type": "refresh", "microservices": []}`)
			jobId := "job123"

			patcher.ApplyFunc(configcenter.GetNotificationTypeForChangeSwitchDayNumber, func(notificationType string, notification map[string]interface{}) bool {
				return true
			})

			consumer.ConsumeTopic(topic, value, jobId)
		})

		It("should log an error for unknown notification type", func() {
			topic := constant.ConfigCenterRefresh
			value := []byte(`{"type": "unknown"}`)
			jobId := "job123"

			consumer.ConsumeTopic(topic, value, jobId)
		})

		It("should log 'Unknown notification type' when type is missing", func() {
			topic := constant.ConfigCenterRefresh
			value := []byte(`{}`)
			jobId := "job123"

			consumer.ConsumeTopic(topic, value, jobId)
		})
	})

	Context("When topic is ConfigCenterVcjob", func() {
		It("should log the resource change", func() {
			topic := constant.ConfigCenterResourceChange
			value := []byte(`{"some": "data"}`)
			jobId := "job123"

			consumer.ConsumeTopic(topic, value, jobId)
		})
	})
})

/* Ended by AICoder, pid:67de0wf715d338014d140b11c06e2a7096e4fd0b */

var _ = Describe("TestConsumeTopic", func() {
	Context("When unmarshal json to struct failed", func() {
		It("should return err", func() {
			client := &ConsumerCWSM{}
			client.ConsumeTopic(constant.ConfigCenterResourceChange, []byte("1"), "1")
		})
	})

	Context("When consume message is invalid", func() {
		It("should return err", func() {
			client := &ConsumerCWSM{}
			consumeMessage := []byte(`[{"action":"create","baseMoc":"director.containervcjob","description":"6cae5341-2fbb-4f0d-97c0-d3f8fabe7d50","id":"a17f49b5-cf8d-48c9-9434-8d7843424ca6","kafkaKey":"550e8400-e29b-41d4-a716-************","moc":"director.containervcjob","model":"director.containervcjob","newModel":{"moc":"director.containervcjob","kafkaKey":"550e8400-e29b-41d4-a716-************","namedDn":["director.containervcjob=testmasterpod007"],"displayName":"testmasterpod007","nbiIdDn":["director.containervcjob=a17f49b5-cf8d-48c9-9434-8d7843424ca6"],"description":"","cloudUUID":"39715b14-8aad-43f9-b513-052f5f30f325","dn":["director.containervcjob=a17f49b5-cf8d-48c9-9434-8d7843424ca6"],"nbiId":"a17f49b5-cf8d-48c9-9434-8d7843424ca6","namespaceId":"3090c4d3-56ca-41d1-aa05-c8b08913f4a7","cloudName":"张超环境","name":"testmasterpod007","tenantID":"da956d79-defd-5732-a562-aea21f03ae4a","model":"director.containervcjob","id":"a17f49b5-cf8d-48c9-9434-8d7843424ca6","last_transition_time":"2024-11-30T03:36:30Z","baseMoc":"director.containervcjob","rm:last_modified":1732936244793,"rm:data_owner":"rm:default","status":"running"},"oldModel":null,"transId":"35b411b0-45b3-4cd6-8ad9-4430c1ddf6fe","transactionID":"*************:HeaderTransactionID:7d14a912-6a16-4031-9527-64e71f5eaa21&2024-11-30T11:10:44.784Z&&88ff8ad935&0"}]`)
			client.ConsumeTopic(constant.ConfigCenterResourceChange, consumeMessage, "1")
		})
	})

	Context("When consume message is valid", func() {
		It("should return nil", func() {
			client := &ConsumerCWSM{}
			consumeMessage := []byte(`[{"action":"create","baseMoc":"director.containervcjob","description":"6cae5341-2fbb-4f0d-97c0-d3f8fabe7d50","id":"a17f49b5-cf8d-48c9-9434-8d7843424ca6","kafkaKey":"550e8400-e29b-41d4-a716-************","moc":"director.containervcjob","model":"director.containervcjob","newModel":{"moc":"director.containervcjob","kafkaKey":"550e8400-e29b-41d4-a716-************","namedDn":["director.containervcjob=testmasterpod007"],"displayName":"testmasterpod007","nbiIdDn":["director.containervcjob=a17f49b5-cf8d-48c9-9434-8d7843424ca6"],"description":"","cloudUUID":"39715b14-8aad-43f9-b513-052f5f30f325","dn":["director.containervcjob=a17f49b5-cf8d-48c9-9434-8d7843424ca6"],"nbiId":"a17f49b5-cf8d-48c9-9434-8d7843424ca6","namespaceId":"3090c4d3-56ca-41d1-aa05-c8b08913f4a7","cloudName":"张超环境","name":"testmasterpod007","tenantID":"da956d79-defd-5732-a562-aea21f03ae4a","model":"director.containervcjob","id":"a17f49b5-cf8d-48c9-9434-8d7843424ca6","last_transition_time":"2024-11-30T03:36:30Z","baseMoc":"director.containervcjob","rm:last_modified":1732936244793,"rm:data_owner":"rm:default","namespaceName":"test70b","status":"running"},"oldModel":null,"transId":"35b411b0-45b3-4cd6-8ad9-4430c1ddf6fe","transactionID":"*************:HeaderTransactionID:7d14a912-6a16-4031-9527-64e71f5eaa21&2024-11-30T11:10:44.784Z&&88ff8ad935&0"}]`)
			client.ConsumeTopic(constant.ConfigCenterResourceChange, consumeMessage, "1")
		})
	})
})
