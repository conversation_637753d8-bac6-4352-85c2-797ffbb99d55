// package kafkaservice
package kafkaservice

import (
	"cwsm/infra/configcenter"
	"cwsm/infra/constant"
	"cwsm/models/vcjob_handler"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"os"
)

type ConsumerCWSM struct{}

func (k *ConsumerCWSM) KafkaClientID() string {
	return constant.KafkaClientID
}

func (k *ConsumerCWSM) KafkaTopics() []string {
	return []string{constant.ConfigCenterRefresh, constant.ConfigCenterResourceChange}
}

func (k *ConsumerCWSM) KafkaHost() string {
	ip, port := os.Getenv("OPENPALETTE_KAFKA_ADDRESS"), os.Getenv("OPENPALETTE_KAFKA_PORT")
	if len(ip) != 0 && len(port) != 0 {
		return ip + ":" + port
	}
	return constant.KafkaLocalHostIP + ":" + constant.KafkaLocalHostPort
}

/* Started by AICoder, pid:v2d27i96c6b54c41443e0b301006152581111cd8 */
func (k *ConsumerCWSM) ConsumeTopic(topic string, value []byte, jobId string) {
	if topic == constant.ConfigCenterRefresh {
		var notification map[string]interface{}
		if err := json.Unmarshal(value, &notification); err != nil {
			logger.Error("Failed to unmarshal value: %s", string(value))
			return
		}

		if notificationType, ok := notification["type"].(string); ok {
			isGetNotificationTypeForChangeSwitchDayNumberTrue := configcenter.GetNotificationTypeForChangeSwitchDayNumber(notificationType, notification)
			if !isGetNotificationTypeForChangeSwitchDayNumberTrue {
				logger.Errorf("get notificationtype for change switch day number failed")
			}
		} else {
			logger.Info("Unknown notification type")
		}

	} else if topic == constant.ConfigCenterResourceChange {
		rsc := []*vcjob_handler.ResourceChange{}
		if err := json.Unmarshal(value, &rsc); err != nil {
			logger.Errorf("unmarshal vcjob resource change kafka message failed, topic %s, err %v", topic, err)
			return
		}
		for _, item := range rsc {
			if item.BaseMoc == "director.containervcjob" {
				logger.Infof("%s", value)
				if !item.IsResourceMessageValid() {
					logger.Errorf("the kafka message of containervcjob change is invalid, ignore.")
					continue
				}
				vcItem := item.TranslateToVcJobItem()
				if vcjob_handler.VcjobHandler != nil {
					vcjob_handler.VcjobHandler.AddMessageToQueueWithId(item.Id, vcItem)
				}
			}
		}
	}
}

/* Ended by AICoder, pid:v2d27i96c6b54c41443e0b301006152581111cd8 */
