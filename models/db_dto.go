package models

import (
	"cwsm/infra/constant"
	"strings"
	"time"
)

type WorkspaceCols struct {
	ClusterId         string `json:"clusterId"`
	ProjectId         string `json:"projectId"`
	ProjectName       string `json:"projectName"`
	Name              string `json:"name"`
	Description       string `json:"description"`
	EnvironmentId     string `json:"environmentId"`
	ComputeResourceId string `json:"computeResourceId"`
	DataResourceId    string `json:"dataResourceId"`
	UpdateTime        string `json:"updateTime"`
	Creator           string `json:"creator"`
	ActivateUser      string `json:"activateUser"`
	ActiveTime        string `json:"activeTime"`
	Metadata          string `json:"metadata"`
	Status            string `json:"status"`
	CreateTime        string `json:"createTime"`
	StopTime          string `json:"stopTime"`
	Id                string `json:"id"`
}

type EnvironmentCols struct {
	Id         string `json:"id"`
	Name       string `json:"name"`
	ProjectId  string `json:"projectId"`
	IsShared   bool   `json:"isShared"`
	Tools      string `json:"tools"`
	Creator    string `json:"creator"`
	UpdateTime string `json:"updateTime"`
	Metadata   string `json:"metadata"`
}

type ComputeResourceCols struct {
	Id         string  `json:"id"`
	Name       string  `json:"name"`
	ProjectId  string  `json:"projectId"`
	IsShared   bool    `json:"isShared"`
	CPUCores   float64 `json:"cpuCores"`
	CPUMemory  int     `json:"cpuMemory"`
	GPU        string  `json:"gpu"`
	Creator    string  `json:"creator"`
	UpdateTime string  `json:"updatetime"`
	Metadata   string  `json:"metadata"`
}

type DataResourceCols struct {
	Id         string `json:"id"`
	Name       string `json:"name"`
	ProjectId  string `json:"projectId"`
	IsShared   bool   `json:"isShared"`
	Context    string `json:"context"`
	Metadata   string `json:"metadata"`
	Creator    string `json:"creator"`
	UpdateTime string `json:"updateTime"`
}

type EvaluationPlanCols struct {
	Id            string `json:"id"`
	Name          string `json:"name"`
	ProjectId     string `json:"projectId"`
	ProjectName   string `json:"projectName"`
	Creator       string `json:"creator"`
	Operator      string `json:"operator"`
	ClusterId     string `json:"clusterId"`
	StartTime     string `json:"startTime"`
	EndTime       string `json:"endTime"`
	WorkspaceId   string `json:"workspaceId"`
	WorkspaceName string `json:"workspaceName"`
	TestType      string `json:"testType"`
	TestScene     string `json:"testScene"`
	TestContent   string `json:"testContent"`
	Nodes         string `json:"nodes"`
	Gpus          string `json:"gpus"`
	Status        string `json:"status"`
	Result        string `json:"result"`
}

type StopToWsm struct {
	Action string `json:"action"`
}

type EvaluationPlan struct {
	Action       string             `json:"action"`
	Name         string             `json:"name"`
	ProjectId    string             `json:"projectId"`
	EvaluatePlan EvaluationPlanCols `json:"evaluatePlan"`
}

type EvaluationToWsm struct {
	Action       string             `json:"action"`
	EvaluatePlan EvaluationPlanCols `json:"evaluatePlan"`
}

type EvaluateToWsm struct {
	Id          string `json:"id"`
	Name        string `json:"name"`
	ProjectId   string `json:"projectId"`
	WorkspaceId string `json:"workspaceId"`
	TestType    string `json:"testType"`
	TestScene   string `json:"testScene"`
	TestContent string `json:"testContent"`
}

type EvaluationTaskCols struct {
	Id          string `json:"id"`
	PlanId      string `json:"planId"`
	Name        string `json:"name"`
	ProjectId   string `json:"projectId"`
	ProjectName string `json:"projectName"`
	Operator    string `json:"operator"`
	StartTime   string `json:"startTime"`
	EndTime     string `json:"endTime"`
	WorkspaceId string `json:"workspaceId"`
	TestType    string `json:"testType"`
	TestScene   string `json:"testScene"`
	TestContent string `json:"testContent"`
	Gpus        string `json:"gpus"`
	GpuUuid     string `json:"gpuUuid"`
	Status      string `json:"status"`
	Result      string `json:"result"`
	Count       string `json:"count"`
	Err         string `json:"err"`
	ResultInfo  string `json:"resultInfo"`
	Performance string `json:"performance"`
}

type EvaluationCaseCols struct {
	Id         string `json:"id"`
	Name       string `json:"name"`
	Creator    string `json:"creator"`
	ProjectId  string `json:"projectId"`
	TestType   string `json:"testType"`
	UpdateTime string `json:"updateTime"`
	UsedCount  int    `json:"usedCount"`
	ModelName  string `json:"modelName"`
	ModelPara  string `json:"modelPara"`
}

type PlatPerformanceReqInfoCols struct {
	Id         string `json:"id"`
	TaskId     string `json:"taskId"`
	PlanId     string `json:"planId"`
	ModelName  string `json:"modelName"`
	TestType   string `json:"testType"`
	JobAbility string `json:"jobAbility"`
}

type PlatPerformanceGpuAbilityReqInfoCols struct {
	Id         string `json:"id"`
	TaskId     string `json:"taskId"`
	PlanId     string `json:"planId"`
	ModelName  string `json:"modelName"`
	TestType   string `json:"testType"`
	GpuAbility string `json:"gpuAbility"`
}

type InspectionPlanCols struct {
	Id                  string    `json:"id"`
	ResultId            string    `json:"resultId"`
	PlanResultId        string    `json:"planResultId"`
	Name                string    `json:"name"`
	ClusterName         string    `json:"clusterName"`
	ClusterId           string    `json:"clusterId"`
	Scene               string    `json:"scene"`
	NodeList            string    `json:"nodeList"`
	Status              string    `json:"status"`
	DesignPlanPerson    string    `json:"designPlanPerson"`
	ModifyPlanPerson    string    `json:"modifyPlanPerson"`
	CreateTime          time.Time `json:"createTime"`
	LatestModifyTime    time.Time `json:"latestModifyTime"`
	CreateMode          int       `json:"createMode"`
	CreateStrategy      int       `json:"createStrategy"`
	ExecuteStrategyTime time.Time `json:"executeStrategyTime"`
	InspectTaskName     string    `json:"inspectTaskName"`
	InspectionTask      string    `json:"inspectionTask"`
}

type InspectionResultCols struct {
	Id                string    `json:"id"`
	PlanId            string    `json:"planId"`
	ResultId          string    `json:"resultId"`
	ExecuteResult     string    `json:"executeResult"`
	Status            string    `json:"status"`
	StartTime         time.Time `json:"startTime"`
	FinishTime        time.Time `json:"finishTime"`
	ExecutePlanPerson string    `json:"executePlanPerson"`
	CompletedCount    int       `json:"completedCount"`
	RemainingTime     int       `json:"remainingTime"`
	InspectionResult  string    `json:"inspectionResult"`
}

const (
	createVcJobFaultHistoryTable = "create table if not exists VcjobFaultHistory(" +
		"uuid varchar(128) PRIMARY KEY UNIQUE NOT NULL," +
		"name varchar(128)," +
		"status varchar(128)," +
		"tenant_name varchar(128)," +
		"cloud_uuid varchar(128)," +
		"cloud_name varchar(128)," +
		"cluster_id varchar(128)," +
		"lastTransitionTime varchar(128)," +
		"faultInfo varchar(65535)" +
		");"

	createInspectionPlanTable = "create table if not exists InspectionPlanTable(" +
		"id varchar(128) PRIMARY KEY UNIQUE NOT NULL," +
		"resultId varchar(128) NOT NULL," +
		"planResultId varchar(128) NOT NULL," +
		"name varchar(128) NOT NULL," +
		"clusterName varchar(128) NOT NULL," +
		"clusterId varchar(128) NOT NULL," +
		"scene varchar(128) NOT NULL," +
		"nodeList varchar(128) NOT NULL," +
		"status varchar(128) NOT NULL," +
		"designPlanPerson varchar(128) NOT NULL," +
		"modifyPlanPerson varchar(128) NOT NULL," +
		"createTime timestamp NOT NULL," +
		"latestModifyTime timestamp NOT NULL," +
		"createMode bigint NOT NULL," +
		"createStrategy bigint NOT NULL," +
		"executeStrategyTime timestamp NOT NULL," +
		"inspectTaskName varchar(128) NOT NULL," +
		"inspectionTask varchar(65535) NOT NULL" +
		");"

	createInspectionResultTable = "create table if not exists InspectionResultTable(" +
		"id varchar(128) PRIMARY KEY UNIQUE," +
		"planId varchar(128)," +
		"resultId varchar(128)," +
		"executeResult varchar(128) ," +
		"status varchar(128) ," +
		"startTime timestamp ," +
		"finishTime timestamp ," +
		"executePlanPerson varchar(128) ," +
		"completedCount bigint ," +
		"remainingTime bigint ," +
		"inspectionResult varchar(65535) " +
		");"
    createFaultInfoTable = "create table if not exists FaultInfo(" +
		"vcjobId varchar(128)," +
		"id varchar(128) PRIMARY KEY UNIQUE NOT NULL," +
		"faultCode bigint," +
		"faultInfo varchar(65535)" +
		");"
)

func CreateDbTable() error {
	InitTables()
	return nil
}

func getThreeElementColumType(tablename string) []string {
	switch tablename {
	case constant.TABLE_NAME_VCJOBFAULTHISTORY:
		return []string{"uuid", "name", "status", "cloud_uuid", "cluster_id", "cloud_name", "tenant_name", "faultInfo", "lastTransitionTime"}
	case constant.TABLE_NAME_InspectionPlanTable:
		return []string{"id", "name", "clusterName", "clusterId", "resultId", "scene", "nodeList", "status", "designPlanPerson", "modifyPlanPerson", "createTime", "latestModifyTime", "createMode", "createStrategy", "executeStrategyTime", "inspectTaskName", "inspectionTask", "planResultId"}
	case constant.TABLE_NAME_InspectionResultTable:
		return []string{"id", "planId", "executeResult", "status", "startTime", "finishTime", "executePlanPerson", "completedCount", "remainingTime", "inspectionResult", "resultId"}
	case constant.TABLE_NAME_FAULTINFO:
		return []string{"vcjobId", "id", "faultCode", "faultInfo"}
	default:
		return []string{}
	}
}

func LowerKeyToNormalKey(columType []string, srcMapList []map[string]interface{}) []map[string]interface{} {
	outputList := []map[string]interface{}{}
	for _, srcMap := range srcMapList {
		tmpMap := map[string]interface{}{}
		for _, key := range columType {
			tmpMap[key] = srcMap[strings.ToLower(key)]
		}
		outputList = append(outputList, tmpMap)
	}
	return outputList
}
