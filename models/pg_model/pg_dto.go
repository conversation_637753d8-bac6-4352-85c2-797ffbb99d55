package pg_model

import (
	"errors"

	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
)

func NewPgRow(tableCol []string, srcData map[string]interface{}) (*dbcore.Row, error) {
	var row []interface{}
	for _, key := range tableCol {
		if val, ok := srcData[key]; ok {
			row = append(row, val)
		} else {
			err := errors.New("key Error:" + key)
			return nil, err
		}

	}
	return &dbcore.Row{Values: row}, nil

}
