package pg_model

import (
	"cwsm/tools/commontools/logger"
	"errors"
	"os"
	"reflect"

	dbcore "cwsm/tools/commontools/db/dbutil/db-core"

	"cwsm/tools/commontools/db/dbutil"
)

type Pg_table_operator struct {
	client *dbutil.DBClient
}

func (p *Pg_table_operator) GetClient() *dbutil.DBClient {
	return p.client
}

func (p *Pg_table_operator) CreateDb() {
	p.client = dbutil.GetDbClient()
	if p.client == nil {
		logger.Errorf("DBClient is nil")
		os.Exit(1)
	}
}

func (p *Pg_table_operator) CreateTable(createSql string) error {
	if !p.client.Exec(createSql) {
		return errors.New("exec create Table sql failed:" + createSql)
	}
	return nil
}

func (p *Pg_table_operator) InsertOneRow(tableName string, columType []string, insertRow map[string]interface{}) bool {
	rowinfo, err := NewPgRow(columType, insertRow)
	if err != nil {
		logger.Error(err.Error())
		return false
	}
	res := p.client.Insert(tableName, columType, rowinfo)
	return res
}

func (p *Pg_table_operator) UpdateOneRow(tableName string, colsToChange map[string]interface{}, conditionCol string, conditionValue string) bool {
	updateCon := dbcore.NewCondition(conditionCol, dbcore.Operator.EQ, conditionValue)
	res := p.client.Update(tableName, colsToChange, updateCon)
	return res
}

func (p *Pg_table_operator) DeleteOneRow(tableName string, colName string, colValue string) bool {
	delCon := dbcore.NewCondition(colName, dbcore.Operator.EQ, colValue)
	res := p.client.Delete(tableName, delCon)
	return res
}

func (p *Pg_table_operator) QueryPgByCondition(tableName string, conditionMap map[string]interface{}) ([]map[string]interface{}, error) {
	keys := reflect.ValueOf(conditionMap).MapKeys()
	conlist := []*dbcore.Condition{}
	for _, key := range keys {
		strkey := key.Interface().(string)
		value := conditionMap[strkey]
		conlist = append(conlist, dbcore.NewCondition(strkey, dbcore.Operator.EQ, value))
	}
	queryCon := conlist[0]
	for i := 1; i < len(conlist); i++ {
		queryCon.And(conlist[i])
	}
	rows, err := p.client.Query(tableName, queryCon)
	if err != nil {
		return nil, err
	}
	return rows, nil
}

func (p *Pg_table_operator) QueryPgAll(tableName string) ([]map[string]interface{}, error) {
	rows, err := p.client.Query(tableName, nil)
	if err != nil {
		return nil, err
	}
	return rows, nil
}
