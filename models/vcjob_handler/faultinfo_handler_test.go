package vcjob_handler

import (
	"cwsm/infra/authorization"
	"cwsm/models"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"testing"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	"github.com/google/uuid"
	. "github.com/smartystreets/goconvey/convey"
)

/* Started by AICoder, pid:me58d6b537cd0ea140750a61404cde6e7eb28264 */
func TestParseRdmaWarningInfo(t *testing.T) {
	Convey("Given a FaultInfoHandler and VcjobCloudClusterInfo", t, func() {
		handler := &FaultInfoHandler{}
		vcjobCloudClusterInfo := &VcjobCloudClusterInfo{
			UUID:        "uuid1",
			Name:        "name1",
			TenantName:  "tenant1",
			ClusterName: "cluster1",
			ClusterId:   "clusterId1",
			CloudId:     "cloudId1",
			CloudName:   "cloudName1",
		}

		<PERSON><PERSON>("Case 1: Normal scenario with multiple reasons", func() {
			faultInfoWsm := FaultInfoWsm{
				Name:      "job1",
				NameSpace: "namespace1",
				UID:       "uid1",
				RdmaWarningInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-01T00:00:00Z", NodeName: "node1", Object: "object1", Reason: "RDMADeviceDropped", Message: "message1"},
					{TimeStamp: "2023-01-02T00:00:00Z", NodeName: "node2", Object: "object2", Reason: "reason2", Message: "message2"},
				},
			}
			faultCodeMap := make(map[int64]*FaultInfo)
			handler.parseRdmaWarningInfo(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)

			So(len(faultCodeMap), ShouldEqual, 1)
			So(faultCodeMap, ShouldContainKey, int64(33012001))
		})

		Convey("Case 2: Adding more warnings to an existing fault code", func() {
			faultInfoWsm := FaultInfoWsm{
				Name:      "job1",
				NameSpace: "namespace1",
				UID:       "uid1",
				RdmaWarningInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-01T00:00:00Z", NodeName: "node1", Object: "object1", Reason: "RDMADeviceDropped", Message: "message1"},
					{TimeStamp: "2023-01-03T00:00:00Z", NodeName: "node3", Object: "object3", Reason: "RDMADeviceDropped", Message: "message3"},
				},
			}
			faultCodeMap := make(map[int64]*FaultInfo)
			handler.parseRdmaWarningInfo(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)

			So(len(faultCodeMap[int64(33012001)].RootCauseObject), ShouldEqual, 2)
		})

		Convey("Case 3: Unknown reason should be skipped", func() {
			faultInfoWsm := FaultInfoWsm{
				Name:      "job1",
				NameSpace: "namespace1",
				UID:       "uid1",
				RdmaWarningInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-04T00:00:00Z", NodeName: "node4", Object: "object4", Reason: "unknown_reason", Message: "message4"},
				},
			}
			faultCodeMap := make(map[int64]*FaultInfo)
			handler.parseRdmaWarningInfo(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)

			So(len(faultCodeMap), ShouldEqual, 0)
		})
	})
}

/* Ended by AICoder, pid:me58d6b537cd0ea140750a61404cde6e7eb28264 */

/* Started by AICoder, pid:ddffaw31d35b84414bea081700b07a5d24415680 */
func TestParseRdmaWarningInfoNew(t *testing.T) {
	Convey("Given a set of RDMA warning information and VcjobCloudClusterInfo", t, func() {
		faultInfoWsm := &FaultInfoWsm{
			Name: "test-job",
			UID:  "uid123",
			RdmaWarningInfo: []ResourceEventInfo{
				{Reason: "RDMADeviceDropped", NodeId: "1", NodeName: "node1", TimeStamp: "2023-10-01T00:00:00Z", Message: "message1"},
				{Reason: "RDMADeviceDropped", NodeId: "", NodeName: "node2", TimeStamp: "2023-10-01T00:01:00Z", Message: "message2"},
				{Reason: "RDMADeviceDropped", NodeId: "1", NodeName: "node1", TimeStamp: "2023-10-01T00:00:00Z", Message: "message2"},
			},
		}
		vcjobCloudClusterInfo := &VcjobCloudClusterInfo{
			CloudId:   "cloud1",
			CloudName: "cloudName1",
			ClusterId: "cluster1",
		}

		Convey("When parseRdmaWarningInfoNew is called with multiple warnings for the same reason", func() {
			ft := &FaultInfoHandler{}
			faultCodeMap := make(map[int64]*FaultInfo)
			ft.parseRdmaWarningInfoNew(faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)

			Convey("Then the faultCodeMap should be populated correctly", func() {
				So(len(faultCodeMap), ShouldEqual, 1)
				faultInfo, ok := faultCodeMap[33012001]
				So(ok, ShouldBeTrue)
				So(faultInfo.FaultCode, ShouldEqual, int64(33012001))
				So(faultInfo.EnvId, ShouldEqual, "cloud1")
				So(faultInfo.EnvName, ShouldEqual, "cloudName1")
				So(faultInfo.DescriptionZH, ShouldContainSubstring, RDMADeviceDroped_Desc_zh)
				So(faultInfo.DescriptionEN, ShouldContainSubstring, RDMADeviceDroped_Desc_en)
				So(len(faultInfo.RootCauseObject), ShouldEqual, 2)
				So(len(faultInfo.RootCauseDetail), ShouldEqual, 2)
				So(len(faultInfo.AssociatedObjects), ShouldEqual, 1)
			})
		})

		Convey("When the reason does not map to a known fault code", func() {
			faultInfoWsm.RdmaWarningInfo[0].Reason = "unknown-reason"
			faultInfoWsm.RdmaWarningInfo[1].Reason = "unknown-reason"
			faultInfoWsm.RdmaWarningInfo[2].Reason = "unknown-reason" // 添加这一行

			faultCodeMap := make(map[int64]*FaultInfo)
			ft := &FaultInfoHandler{}
			ft.parseRdmaWarningInfoNew(faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)
			Convey("Then the faultCodeMap should be empty", func() {
				So(len(faultCodeMap), ShouldEqual, 0)
			})
		})
	})
}

/* Ended by AICoder, pid:ddffaw31d35b84414bea081700b07a5d24415680 */

/* Started by AICoder, pid:fba14fcf8bbc2c514b0d0932700e0c430f97d8ea */
func TestProcessRdmaWarnings(t *testing.T) {
	Convey("Given a set of RDMA warning information", t, func() {
		warnings := []ResourceEventInfo{
			{NodeId: "node1", NodeName: "nodeA", TimeStamp: "2023-10-01T00:00:00Z", Message: "Message1", Reason: "RDMADeviceDropped"},
			{NodeId: "node2", NodeName: "nodeB", TimeStamp: "2023-10-01T00:01:00Z", Message: "Message2", Reason: "RDMADeviceDropped"},
			{NodeId: "node1", NodeName: "nodeA", TimeStamp: "2023-10-01T00:02:00Z", Message: "Message3", Reason: "RDMADeviceDropped"},
			{NodeId: "node3", NodeName: "nodeC", TimeStamp: "2023-10-01T00:03:00Z", Message: "Message4", Reason: "reason2"},
		}

		faultInfoWsm := &FaultInfoWsm{RdmaWarningInfo: warnings}

		Convey("When ProcessRdmaWarnings is called", func() {
			rdmaObjectDetail := ProcessRdmaWarnings(faultInfoWsm)

			Convey("Then the result should contain the correct details for each reason and node", func() {
				So(len(rdmaObjectDetail), ShouldEqual, 2) // Two reasons: RDMADeviceDropped and reason2

				// Check details for RDMADeviceDropped
				reason1Details := rdmaObjectDetail["RDMADeviceDropped"]
				So(len(reason1Details), ShouldEqual, 2) // Two nodes: nodeA and nodeB

				nodeADetails := reason1Details["nodeA"]
				So(nodeADetails.Detail, ShouldContainSubstring, "Message1")
				So(nodeADetails.Detail, ShouldContainSubstring, "Message3")

				nodeBDetails := reason1Details["nodeB"]
				So(nodeBDetails.Detail, ShouldContainSubstring, "Message2")

				// Check details for reason2
				reason2Details := rdmaObjectDetail["reason2"]
				So(len(reason2Details), ShouldEqual, 1) // One node: nodeC

				nodeCDetails := reason2Details["nodeC"]
				So(nodeCDetails.Detail, ShouldContainSubstring, "Message4")
			})
		})

		Convey("When there are no warnings", func() {
			faultInfoWsmEmpty := &FaultInfoWsm{RdmaWarningInfo: []ResourceEventInfo{}}
			rdmaObjectDetailEmpty := ProcessRdmaWarnings(faultInfoWsmEmpty)

			Convey("Then the result should be an empty map", func() {
				So(rdmaObjectDetailEmpty, ShouldBeEmpty)
			})
		})
	})
}

/* Ended by AICoder, pid:fba14fcf8bbc2c514b0d0932700e0c430f97d8ea */

/* Started by AICoder, pid:edf0cp33c7lf4ae14f0c0bf840f2a66f3aa27bce */
func TestParseGpuTrainInfo(t *testing.T) {
	Convey("Given a FaultInfoHandler and VcjobCloudClusterInfo", t, func() {
		handler := &FaultInfoHandler{}
		vcjobCloudClusterInfo := &VcjobCloudClusterInfo{
			CloudId:   "cloudId1",
			CloudName: "cloudName1",
		}

		Convey("Case 1: Normal scenario with multiple reasons", func() {
			faultInfoWsm := FaultInfoWsm{
				Name: "job1",
				UID:  "uid1",
				GpuTrainInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-01T00:00:00Z", NodeName: "node1", Object: "object1", Reason: "11:0xA4033002 2-bit ECC error", Message: "message1"},
					{TimeStamp: "2023-01-02T00:00:00Z", NodeName: "node2", Object: "object2", Reason: "5:0xA4031001 COMMAND PROCESSOR engine hang", Message: "message2"},
				},
			}
			faultCodeMap := make(map[int64]*FaultInfo)
			handler.parseGpuTrainInfo(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)

			So(len(faultCodeMap), ShouldEqual, 2)
			So(faultCodeMap, ShouldContainKey, int64(31017001))
			So(faultCodeMap, ShouldContainKey, int64(31017003))
		})

		Convey("Case 2: Adding more GPU train info to an existing fault code", func() {
			faultInfoWsm := FaultInfoWsm{
				Name: "job1",
				UID:  "uid1",
				GpuTrainInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-01T00:00:00Z", NodeName: "node1", Object: "object1", Reason: "GpuPciLost", Message: "message1"},
					{TimeStamp: "2023-01-03T00:00:00Z", NodeName: "node3", Object: "object3", Reason: "GpuPciLost", Message: "message3"},
				},
			}
			faultCodeMap := make(map[int64]*FaultInfo)
			handler.parseGpuTrainInfo(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)

			So(len(faultCodeMap[int64(31017002)].RootCauseObject), ShouldEqual, 2)
			So(len(faultCodeMap[int64(31017002)].RootCauseDetail), ShouldEqual, 2)
		})

		Convey("Case 3: Unknown reason should be skipped", func() {
			faultInfoWsm := FaultInfoWsm{
				Name: "job1",
				UID:  "uid1",
				GpuTrainInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-04T00:00:00Z", NodeName: "node4", Object: "object4", Reason: "unknown_reason", Message: "message4"},
				},
			}
			faultCodeMap := make(map[int64]*FaultInfo)
			handler.parseGpuTrainInfo(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)

			So(len(faultCodeMap), ShouldEqual, 0)
		})
	})
}

/* Ended by AICoder, pid:edf0cp33c7lf4ae14f0c0bf840f2a66f3aa27bce */

/* Started by AICoder, pid:vc868m0512p6005143bc084c503b0857e8c73a7e */
func TestParseJobWarningInfo(t *testing.T) {
	Convey("Given a FaultInfoHandler and VcjobCloudClusterInfo", t, func() {
		handler := &FaultInfoHandler{}
		vcjobCloudClusterInfo := &VcjobCloudClusterInfo{
			CloudId:   "cloudId1",
			CloudName: "cloudName1",
		}

		Convey("Case 1: Normal scenario with single reasons", func() {
			faultInfoWsm := FaultInfoWsm{
				Name: "job1",
				UID:  "uid1",
				JobWarningInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-01T00:00:00Z", NodeName: "", Object: "job1", Reason: "Insufficient cluster resources", Message: "message1"},
				},
			}
			faultCodeMap := make(map[int64]*FaultInfo)

			handler.parseJobWarningInfo(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)

			So(len(faultCodeMap), ShouldEqual, 1)
			So(faultCodeMap, ShouldContainKey, int64(40003001))
		})

		Convey("Case 2: Normal scenario with multiple reasons", func() {
			faultInfoWsm := FaultInfoWsm{
				Name: "job1",
				UID:  "uid1",
				JobWarningInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-01T00:00:00Z", NodeName: "", Object: "job1", Reason: "Insufficient cluster resources", Message: "message1"},
					{TimeStamp: "2023-01-02T00:00:00Z", NodeName: "", Object: "job1", Reason: "Blueprint and node labels do not match", Message: "message2"},
				},
			}
			faultCodeMap := make(map[int64]*FaultInfo)

			handler.parseJobWarningInfo(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)
			So(len(faultCodeMap), ShouldEqual, 2)
			So(faultCodeMap, ShouldContainKey, int64(40003001))
			So(faultCodeMap, ShouldContainKey, int64(40003002))
		})

		Convey("Case 3: Unknown reason should be skipped", func() {
			faultInfoWsm := FaultInfoWsm{
				Name: "job1",
				UID:  "uid1",
				JobWarningInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-04T00:00:00Z", NodeName: "", Object: "job1", Reason: "unknown_reason", Message: "message4"},
				},
			}
			faultCodeMap := make(map[int64]*FaultInfo)

			handler.parseJobWarningInfo(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)

			So(len(faultCodeMap), ShouldEqual, 0)
		})
	})
}

/* Ended by AICoder, pid:vc868m0512p6005143bc084c503b0857e8c73a7e */

/* Started by AICoder, pid:o242cycf2f11d2014d5d0b8e3097414224020fea */
func TestParseNewFaultInfo(t *testing.T) {
	Convey("Given a FaultInfoHandler and VcjobCloudClusterInfo", t, func() {
		handler := &FaultInfoHandler{}
		vcjobCloudClusterInfo := &VcjobCloudClusterInfo{
			CloudId:   "cloudId1",
			CloudName: "cloudName1",
			UUID:      "uid1",
		}

		Convey("Case 1: Normal scenario with multiple reasons", func() {
			faultInfoWsm := FaultInfoWsm{
				Name: "job1",
				UID:  "uid1",
				GpuTrainInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-01T00:00:00Z", NodeName: "", Object: "object1", Reason: "11:0xA4033002 2-bit ECC error", Message: "message1"},
					{TimeStamp: "2023-01-02T00:00:00Z", NodeName: "", Object: "object2", Reason: "5:0xA4031001 COMMAND PROCESSOR engine hang", Message: "message2"},
				},
				JobWarningInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-03T00:00:00Z", NodeName: "", Object: "object3", Reason: "Insufficient cluster resources", Message: "message3"},
				},
				RdmaWarningInfo: []ResourceEventInfo{
					{TimeStamp: "2023-01-04T00:00:00Z", NodeName: "", Object: "object4", Reason: "RDMADeviceDropped", Message: "message4"},
				},
			}
			faultInfoJSON, _ := json.Marshal(faultInfoWsm)
			faultInfos, _ := handler.ParseNewFaultInfo(string(faultInfoJSON), vcjobCloudClusterInfo)

			So(len(faultInfos), ShouldEqual, 4) // Assuming each reason maps to a unique fault code
		})

		Convey("Case 2: Empty JSON should return nil", func() {
			faultInfoJSON := `{ "GpuTrainInfo": [], "JobWarningInfo": [], "RdmaWarningInfo": [] }`
			faultInfos, _ := handler.ParseNewFaultInfo(faultInfoJSON, vcjobCloudClusterInfo)
			So(faultInfos, ShouldBeEmpty)
		})

		Convey("Case 3: Invalid JSON should return nil", func() {
			faultInfoJSON := `{"GpuTrainInfo": [`
			faultInfos, _ := handler.ParseNewFaultInfo(faultInfoJSON, vcjobCloudClusterInfo)
			So(faultInfos, ShouldBeNil)
		})
	})
}

/* Ended by AICoder, pid:o242cycf2f11d2014d5d0b8e3097414224020fea */

/* Started by AICoder, pid:wcceby55a0cc16214bb80a06d0066940dd46f270 */
func TestCompareRootCauseObjects(t *testing.T) {
	Convey("Given two sets of RootCauseObjects", t, func() {

		Convey("Case 1: Identical sets should return true", func() {
			oldObj := []*RootCauseObject{
				{Id: "id1"},
				{Id: "id2"},
			}
			newObj := []*RootCauseObject{
				{Id: "id2"},
				{Id: "id1"},
			}
			result := compareRootCauseObjects(oldObj, newObj)
			So(result, ShouldBeTrue)
		})

		Convey("Case 2: Different sets should return false", func() {
			oldObj := []*RootCauseObject{
				{Id: "id1"},
				{Id: "id2"},
			}
			newObj := []*RootCauseObject{
				{Id: "id3"},
				{Id: "id4"},
			}
			result := compareRootCauseObjects(oldObj, newObj)
			So(result, ShouldBeFalse)
		})

		Convey("Case 3: One empty set should return false", func() {
			oldObj := []*RootCauseObject{}
			newObj := []*RootCauseObject{
				{Id: "id1"},
			}
			result := compareRootCauseObjects(oldObj, newObj)
			So(result, ShouldBeFalse)
		})

		Convey("Case 4: Both empty sets should return true", func() {
			oldObj := []*RootCauseObject{}
			newObj := []*RootCauseObject{}
			result := compareRootCauseObjects(oldObj, newObj)
			So(result, ShouldBeTrue)
		})
	})
}

/* Ended by AICoder, pid:wcceby55a0cc16214bb80a06d0066940dd46f270 */

/* Started by AICoder, pid:oe479te344wa6d5145f00ac020c7aa5876077250 */
func TestCompareRootCauseDetail(t *testing.T) {
	Convey("Given two sets of RootCauseDetails", t, func() {

		Convey("Case 1: Identical sets should return true", func() {
			oldObj := []*RootCauseDetail{
				{RcObjectId: "id1", Data: []string{"data1"}},
				{RcObjectId: "id2", Data: []string{"data2"}},
			}
			newObj := []*RootCauseDetail{
				{RcObjectId: "id1", Data: []string{"data1"}},
				{RcObjectId: "id2", Data: []string{"data2"}},
			}
			result := compareRootCauseDetail(oldObj, newObj)
			So(result, ShouldBeTrue)
		})

		Convey("Case 2: Different data in one object should return false", func() {
			oldObj := []*RootCauseDetail{
				{RcObjectId: "id1", Data: []string{"data1"}},
			}
			newObj := []*RootCauseDetail{
				{RcObjectId: "id1", Data: []string{"data2"}},
			}
			result := compareRootCauseDetail(oldObj, newObj)
			So(result, ShouldBeFalse)
		})

		Convey("Case 3: One missing object should return false", func() {
			oldObj := []*RootCauseDetail{
				{RcObjectId: "id1", Data: []string{"data1"}},
			}
			newObj := []*RootCauseDetail{
				{RcObjectId: "id2", Data: []string{"data2"}},
			}
			result := compareRootCauseDetail(oldObj, newObj)
			So(result, ShouldBeFalse)
		})

		Convey("Case 4: Empty sets should return true", func() {
			oldObj := []*RootCauseDetail{}
			newObj := []*RootCauseDetail{}
			result := compareRootCauseDetail(oldObj, newObj)
			So(result, ShouldBeTrue)
		})

		Convey("Case 5: Data has different length should return false", func() {
			oldObj := []*RootCauseDetail{
				{RcObjectId: "id1", Data: []string{"data1", "data2"}},
			}
			newObj := []*RootCauseDetail{
				{RcObjectId: "id1", Data: []string{"data1"}},
			}
			result := compareRootCauseDetail(oldObj, newObj)
			So(result, ShouldBeFalse)
		})
	})
}

/* Ended by AICoder, pid:oe479te344wa6d5145f00ac020c7aa5876077250 */

/* Started by AICoder, pid:04924d4a9cc86eb14d6e0891e0fbee7071314036 */
func TestIsFaultInfoChanged(t *testing.T) {
	Convey("Given two FaultInfo objects", t, func() {

		Convey("Case 1: Identical FaultInfo should return false", func() {
			oldFi := &FaultInfo{
				FaultCode:       2,
				RootCauseObject: []*RootCauseObject{{Id: "id1"}},
				RootCauseDetail: []*RootCauseDetail{{RcObjectId: "id1", Data: []string{"data1"}}},
			}
			newFi := &FaultInfo{
				FaultCode:       2,
				RootCauseObject: []*RootCauseObject{{Id: "id1"}},
				RootCauseDetail: []*RootCauseDetail{{RcObjectId: "id1", Data: []string{"data1"}}},
			}
			result := isFaultInfoChanged(oldFi, newFi)
			So(result, ShouldBeFalse)
		})

		Convey("Case 2: Different RootCauseObjects should return true", func() {
			oldFi := &FaultInfo{
				FaultCode:       2,
				RootCauseObject: []*RootCauseObject{{Id: "id1"}},
			}
			newFi := &FaultInfo{
				FaultCode:       2,
				RootCauseObject: []*RootCauseObject{{Id: "id2"}},
			}
			result := isFaultInfoChanged(oldFi, newFi)
			So(result, ShouldBeTrue)
		})

		Convey("Case 3: Different RootCauseDetails should return true", func() {
			oldFi := &FaultInfo{
				FaultCode:       2,
				RootCauseDetail: []*RootCauseDetail{{RcObjectId: "id1", Data: []string{"data1"}}},
			}
			newFi := &FaultInfo{
				FaultCode:       2,
				RootCauseDetail: []*RootCauseDetail{{RcObjectId: "id1", Data: []string{"data2"}}},
			}
			result := isFaultInfoChanged(oldFi, newFi)
			So(result, ShouldBeTrue)
		})

		Convey("Case 4: RootCauseDetails and RootCauseObjects are nil should return false", func() {
			oldFi := &FaultInfo{
				FaultCode: 1,
			}
			newFi := &FaultInfo{
				FaultCode: 1,
			}
			result := isFaultInfoChanged(oldFi, newFi)
			So(result, ShouldBeFalse)
		})

		Convey("Case 5: FaultCode marked as fixed should return false", func() {
			oldFi := &FaultInfo{
				FaultCode:       31017001,
				RootCauseObject: []*RootCauseObject{{Id: "id1"}},
				RootCauseDetail: []*RootCauseDetail{{RcObjectId: "id1", Data: []string{"data1"}}},
			}
			newFi := &FaultInfo{
				FaultCode:       31017001,
				RootCauseObject: []*RootCauseObject{{Id: "id1"}},
				RootCauseDetail: []*RootCauseDetail{{RcObjectId: "id1", Data: []string{"data1"}}},
			}
			result := isFaultInfoChanged(oldFi, newFi)
			So(result, ShouldBeFalse)
		})
	})
}

/* Ended by AICoder, pid:04924d4a9cc86eb14d6e0891e0fbee7071314036 */

/* Started by AICoder, pid:nd18eqd07c30e0d14b360b2440c29b579b019cbb */
func TestGetChangedFaultInfo(t *testing.T) {
	Convey("Given two sets of FaultInfo", t, func() {

		Convey("Case 1: Identical sets should return empty result", func() {
			oldObj := []*FaultInfo{
				{FaultCode: 1, FaultId: "fault1"},
			}
			newObj := []*FaultInfo{
				{FaultCode: 1, FaultId: "fault1"},
			}
			result := (&FaultInfoHandler{}).GetChangedFaultInfo(oldObj, newObj, "")
			So(result, ShouldBeEmpty)
		})

		Convey("Case 2: Different sets should return changed items due to RootCauseObject change", func() {
			oldObj := []*FaultInfo{
				{FaultCode: 1, FaultId: "", RootCauseObject: []*RootCauseObject{{Id: "id1"}}},
			}
			newObj := []*FaultInfo{
				{FaultCode: 1, FaultId: "", RootCauseObject: []*RootCauseObject{{Id: "id2"}}}, // Changed RootCauseObject
			}
			result := (&FaultInfoHandler{}).GetChangedFaultInfo(oldObj, newObj, "")
			So(len(result), ShouldEqual, 1)
			So(result[0].FaultCode, ShouldEqual, int64(1))
		})

		Convey("Case 3: New items should be added to the result", func() {
			oldObj := []*FaultInfo{
				{FaultCode: 1, FaultId: "fault1"},
			}
			newObj := []*FaultInfo{
				{FaultCode: 1, FaultId: "fault1"},
				{FaultCode: 2, FaultId: "fault2"}, // New item
			}
			result := (&FaultInfoHandler{}).GetChangedFaultInfo(oldObj, newObj, "")
			So(len(result), ShouldEqual, 1)
			So(result[0].FaultCode, ShouldEqual, int64(2))
			So(result[0].FaultId, ShouldEqual, "fault2")
		})

		Convey("Case 4: Cleared items should have ClearedTime set", func() {
			oldObj := []*FaultInfo{
				{FaultCode: 1, FaultId: "fault1"},
			}
			newObj := []*FaultInfo{}
			result := (&FaultInfoHandler{}).GetChangedFaultInfo(oldObj, newObj, "")
			So(len(result), ShouldEqual, 1)
			So(result[0].ClearedTime, ShouldNotBeEmpty)
			result = (&FaultInfoHandler{}).GetChangedFaultInfo(oldObj, newObj, "2025-03-10T06:43:03Z")
			So(len(result), ShouldEqual, 1)
			So(result[0].ClearedTime, ShouldNotBeEmpty)
		})
	})
}

/* Ended by AICoder, pid:nd18eqd07c30e0d14b360b2440c29b579b019cbb */

/* Started by AICoder, pid:2a1e4uadbfy9cc214fcc082cd0ff3b6a887494a2 */
func TestGetVcjobFaultInfoFromDB(t *testing.T) {
	Convey("Given a Vcjob ID", t, func() {

		Convey("Case 1: Valid data should return correct FaultInfo", func() {
			patch := gomonkey.ApplyFunc(models.QueryResourceByCondition, func(tableName string, condition map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{
					{
						"faultCode": 3.3012001e+07,
						"faultInfo": `{"fault_id":"0e7ff592-f59e-11ef-b04b-5eb727681b13","fault_code":33012001,"raised_time":"2025-02-28T06:34:26.867Z","cleared_time":"","reported_time":"2025-02-28T06:34:26.867Z","description_zh":"任务op-ai-testjob-0发生RDMA设备掉卡。","description_en":"Vcjob op-ai-testjob-0 encountered RDMA Device Removed From the Node.","env_id":"5fe8d778-3dfe-4bd6-ab44-3ec09fc653de","env_name":"c1_cim2","root_cause_objects":[{"env_id":"5fe8d778-3dfe-4bd6-ab44-3ec09fc653de","id":"9f094da7-d93e-4526-bc0a-43a5dec63295:0000:9d:00.3","name":"id mlx5_27, pci 0000:9d:00.3","moc":"director.containernoderdmainterface"}],"root_cause_detail":[{"rc_object_id":"faffe7c6-0527-4fed-b8e2-3d073c481c28","type":"others","data":["timeStamp:2025-02-28T14:24:05+08:00,node:cluster-cim2-minion-1-0,message:pf id mlx5_6, pf pci 0000:9d:00.0\ndevice 0000:9d:00.0 not found in node cluster-cim2-minion-1-0"]}],"associated_objects":[{"env_id":"5fe8d778-3dfe-4bd6-ab44-3ec09fc653de","id":"6fd58c52-4e4f-4f82-bf79-22b3a9a878be","name":"op-ai-testjob-0","moc":"director.containervcjob"}],"need_report_to_north":false}`,
						"id":        "0e7ff592-f59e-11ef-b04b-5eb727681b13",
						"vcjobId":   "6fd58c52-4e4f-4f82-bf79-22b3a9a878be",
					},
				}, nil
			})
			defer patch.Reset()

			faultInfos, err := (&FaultInfoHandler{}).GetVcjobFaultInfoFromDB("6fd58c52-4e4f-4f82-bf79-22b3a9a878be")
			So(err, ShouldBeNil)
			So(len(faultInfos), ShouldEqual, 1)
			So(faultInfos[0].FaultCode, ShouldEqual, int64(33012001))
		})

		Convey("Case 2: Invalid JSON should be skipped", func() {
			patch := gomonkey.ApplyFunc(models.QueryResourceByCondition, func(tableName string, condition map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{
					{
						"faultInfo": `invalid json`,
					},
				}, nil
			})
			defer patch.Reset()

			faultInfos, err := (&FaultInfoHandler{}).GetVcjobFaultInfoFromDB("invalid-json")
			So(err, ShouldBeNil)
			So(len(faultInfos), ShouldEqual, 0)
		})

		Convey("Case 3: Non-string faultInfo should be skipped", func() {
			patch := gomonkey.ApplyFunc(models.QueryResourceByCondition, func(tableName string, condition map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{
					{
						"faultInfo": 123,
					},
				}, nil
			})
			defer patch.Reset()

			faultInfos, err := (&FaultInfoHandler{}).GetVcjobFaultInfoFromDB("non-string-faultInfo")
			So(err, ShouldBeNil)
			So(len(faultInfos), ShouldEqual, 0)
		})

		Convey("Case 4: Database query error should return error", func() {
			patch := gomonkey.ApplyFunc(models.QueryResourceByCondition, func(tableName string, condition map[string]interface{}) ([]map[string]interface{}, error) {
				return nil, errors.New("query error")
			})
			defer patch.Reset()

			faultInfos, err := (&FaultInfoHandler{}).GetVcjobFaultInfoFromDB("query-error")
			So(err, ShouldNotBeNil)
			So(faultInfos, ShouldBeNil)
		})
	})
}

/* Ended by AICoder, pid:2a1e4uadbfy9cc214fcc082cd0ff3b6a887494a2 */

/* Started by AICoder, pid:1922308582j0b65145e00b2020fc255ad6691b36 */
func TestTranslateStructToMap(t *testing.T) {
	Convey("Given a FaultItem", t, func() {

		Convey("Case 1: Valid struct should be converted to map without error", func() {
			faultItem := &FaultItem{
				VcjobId:   "vcjob1",
				Id:        "id1",
				FaultCode: 1001,
				FaultInfo: "info1",
			}

			result, err := (&FaultInfoHandler{}).translateStructToMap(faultItem)
			So(err, ShouldBeNil)
			So(result["vcjobId"], ShouldEqual, "vcjob1")
			So(result["id"], ShouldEqual, "id1")
			So(result["faultCode"], ShouldEqual, float64(1001)) // JSON numbers are floats
			So(result["faultInfo"], ShouldEqual, "info1")
		})

		Convey("Case 2: Marshal error should return an error", func() {
			patcher := gomonkey.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
				return nil, errors.New("marshal error")
			})
			defer patcher.Reset()

			faultItem := &FaultItem{
				VcjobId:   "vcjob1",
				Id:        "id1",
				FaultCode: 1001,
				FaultInfo: "info1",
			}

			result, err := (&FaultInfoHandler{}).translateStructToMap(faultItem)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "marshal error")
			So(result, ShouldBeNil)
		})

		Convey("Case 3: Unmarshal error should return an error", func() {
			// Create a valid JSON string that cannot be unmarshalled into the expected structure
			patcher := gomonkey.ApplyFunc(json.Unmarshal, func(data []byte, v interface{}) error {
				return errors.New("unmarshal error")
			})
			defer patcher.Reset()

			faultItem := &FaultItem{
				VcjobId:   "vcjob1",
				Id:        "id1",
				FaultCode: 1001,
				FaultInfo: "info1",
			}

			result, err := (&FaultInfoHandler{}).translateStructToMap(faultItem)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "unmarshal error")
			So(result, ShouldBeNil)
		})
	})
}

/* Ended by AICoder, pid:1922308582j0b65145e00b2020fc255ad6691b36 */

/* Started by AICoder, pid:cd785ddc3bx61701428c0a10c001385aba571941 */
func TestInsertFaultInfoToDB(t *testing.T) {
	Convey("Given a FaultItem to insert into the database", t, func() {

		Convey("Case 1: Valid FaultItem should be inserted without error", func() {
			faultItem := &FaultItem{
				VcjobId:   "vcjob1",
				Id:        "id1",
				FaultCode: 1001,
				FaultInfo: "info1",
			}

			patch := gomonkey.ApplyFunc(models.InsertNewResource, func(tableName string, newResource map[string]interface{}) bool {
				return true
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).InsertFaultInfoToDB(faultItem)
			So(err, ShouldBeNil)
		})

		Convey("Case 2: TranslateStructToMap error should return an error", func() {
			faultItem := &FaultItem{
				VcjobId:   "vcjob1",
				Id:        "id1",
				FaultCode: 1001,
				FaultInfo: "info1",
			}

			patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&FaultInfoHandler{}), "translateStructToMap", func(_ *FaultInfoHandler, _ *FaultItem) (map[string]interface{}, error) {
				return nil, errors.New("translateStructToMap error")
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).InsertFaultInfoToDB(faultItem)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "translateStructToMap error")
		})

		Convey("Case 3: InsertNewResource fail should return an error", func() {
			faultItem := &FaultItem{
				VcjobId:   "vcjob1",
				Id:        "id1",
				FaultCode: 1001,
				FaultInfo: "info1",
			}

			patch := gomonkey.ApplyFunc(models.InsertNewResource, func(tableName string, newResource map[string]interface{}) bool {
				return false
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).InsertFaultInfoToDB(faultItem)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, fmt.Sprintf("insert faultinfo to db failed id %s", faultItem.Id))
		})
	})
}

/* Ended by AICoder, pid:cd785ddc3bx61701428c0a10c001385aba571941 */

/* Started by AICoder, pid:p53bcmd465yde1f142510a93d0ef975817c7f5eb */
func TestUpdateFaultInfoToDB(t *testing.T) {
	Convey("Given a FaultItem to update in the database", t, func() {

		Convey("Case 1: Valid FaultItem should be updated without error", func() {
			faultItem := &FaultItem{
				VcjobId:   "vcjob1",
				Id:        "id1",
				FaultCode: 1001,
				FaultInfo: "info1",
			}

			patch := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
				return true
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).UpdateFaultInfoToDB(faultItem)
			So(err, ShouldBeNil)
		})

		Convey("Case 2: TranslateStructToMap error should return an error", func() {
			faultItem := &FaultItem{
				VcjobId:   "vcjob1",
				Id:        "id1",
				FaultCode: 1001,
				FaultInfo: "info1",
			}

			patch := gomonkey.ApplyPrivateMethod(reflect.TypeOf(&FaultInfoHandler{}), "translateStructToMap", func(_ *FaultInfoHandler, _ *FaultItem) (map[string]interface{}, error) {
				return nil, errors.New("translateStructToMap error")
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).UpdateFaultInfoToDB(faultItem)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "translateStructToMap error")
		})

		Convey("Case 3: UpdateResourceById fail should return an error", func() {
			faultItem := &FaultItem{
				VcjobId:   "vcjob1",
				Id:        "id1",
				FaultCode: 1001,
				FaultInfo: "info1",
			}

			patch := gomonkey.ApplyFunc(models.UpdateResourceById, func(tableName string, idValue string, updateResource map[string]interface{}) bool {
				return false
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).UpdateFaultInfoToDB(faultItem)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, fmt.Sprintf("insert faultinfo to db failed id %s", faultItem.Id))
		})
	})
}

/* Ended by AICoder, pid:p53bcmd465yde1f142510a93d0ef975817c7f5eb */

/* Started by AICoder, pid:g40dd7c6b2zb69f14c9c0a04f0115025aa29a456 */
func TestDeleteFaultInfoFromDB(t *testing.T) {
	Convey("Given a FaultId to delete from the database", t, func() {

		Convey("Case 1: Valid FaultId should be deleted without error", func() {
			faultId := "id1"

			patch := gomonkey.ApplyFunc(models.DeleteResourceById, func(tableName string, idValue string) bool {
				return true
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).DeleteFaultInfoFromDB(faultId)
			So(err, ShouldBeNil)
		})

		Convey("Case 2: DeleteResourceById fail should return an error", func() {
			faultId := "id1"

			patch := gomonkey.ApplyFunc(models.DeleteResourceById, func(tableName string, idValue string) bool {
				return false
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).DeleteFaultInfoFromDB(faultId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, fmt.Sprintf("delete faultinfo from db failed id %s", faultId))
		})
	})
}

/* Ended by AICoder, pid:g40dd7c6b2zb69f14c9c0a04f0115025aa29a456 */
/* Started by AICoder, pid:gf8baaf8b1xd5f41417a094280ddc75d5bf42066 */
func TestPostFaultInfoToFaultMgt(t *testing.T) {
	Convey("Given a FaultInfo to post to fault management", t, func() {

		Convey("Case 1: Valid FaultInfo should be posted without error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
				FaultId:   "fault1",
			}
			vcjobId := "vcjob1"

			patch := gomonkey.ApplyFunc(authorization.PostVcjobFaultInfo, func(request []byte, vcjobId string) error {
				return nil
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).PostFaultInfoToFaultMgt(faultInfo, vcjobId)
			So(err, ShouldBeNil)
		})

		Convey("Case 2: Marshal error should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
				FaultId:   "fault1",
			}
			vcjobId := "vcjob1"

			patcher := gomonkey.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
				return nil, errors.New("marshal error")
			})
			defer patcher.Reset()

			err := (&FaultInfoHandler{}).PostFaultInfoToFaultMgt(faultInfo, vcjobId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "marshal error")
		})

		Convey("Case 3: PostVcjobFaultInfo failure should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
				FaultId:   "fault1",
			}
			vcjobId := "vcjob1"

			patch := gomonkey.ApplyFunc(authorization.PostVcjobFaultInfo, func(request []byte, vcjobId string) error {
				return errors.New("post failed")
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).PostFaultInfoToFaultMgt(faultInfo, vcjobId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "post failed")
		})
	})
}

/* Ended by AICoder, pid:gf8baaf8b1xd5f41417a094280ddc75d5bf42066 */

/* Started by AICoder, pid:y5d24a0b0869b2314a470bf780c7765d5b54af7c */
func TestPutFaultInfoToFaultMgt(t *testing.T) {
	Convey("Given a FaultInfo to put to fault management", t, func() {

		Convey("Case 1: Valid FaultInfo should be put without error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
				FaultId:   "fault1",
			}
			vcjobId := "vcjob1"

			patch := gomonkey.ApplyFunc(authorization.PutVcjobFaultInfo, func(request []byte, vcjobId string) error {
				return nil
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).PutFaultInfoToFaultMgt(faultInfo, vcjobId)
			So(err, ShouldBeNil)
		})

		Convey("Case 2: Marshal error should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
				FaultId:   "fault1",
			}
			vcjobId := "vcjob1"

			patcher := gomonkey.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
				return nil, errors.New("marshal error")
			})
			defer patcher.Reset()

			err := (&FaultInfoHandler{}).PutFaultInfoToFaultMgt(faultInfo, vcjobId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "marshal error")
		})

		Convey("Case 3: PutVcjobFaultInfo failure should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
				FaultId:   "fault1",
			}
			vcjobId := "vcjob1"

			patch := gomonkey.ApplyFunc(authorization.PutVcjobFaultInfo, func(request []byte, vcjobId string) error {
				return errors.New("put failed")
			})
			defer patch.Reset()

			err := (&FaultInfoHandler{}).PutFaultInfoToFaultMgt(faultInfo, vcjobId)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "put failed")
		})
	})
}

/* Ended by AICoder, pid:y5d24a0b0869b2314a470bf780c7765d5b54af7c */
/* Started by AICoder, pid:e49e7oa014kd31a145760adc0135da136c862ab3 */
func TestFaultInfoCreateHandle(t *testing.T) {
	Convey("Given a VcjobId and FaultInfo to create", t, func() {

		Convey("Case 1: Valid input should succeed", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
			}
			vcjobId := "vcjob1"

			patchPost := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "PostFaultInfoToFaultMgt", func(_ *FaultInfoHandler, faultInfo *FaultInfo, vcjobId string) error {
				return nil
			})
			patchInsert := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "InsertFaultInfoToDB", func(_ *FaultInfoHandler, faultItem *FaultItem) error {
				return nil
			})
			defer patchPost.Reset()
			defer patchInsert.Reset()

			patcherUUID := gomonkey.ApplyFunc(uuid.NewUUID, func() (uuid.UUID, error) {
				return uuid.Parse("0e7ff592-f59e-11ef-b04b-5eb727681b13")
			})
			defer patcherUUID.Reset()

			err := (&FaultInfoHandler{}).FaultInfoCreateHandle(vcjobId, faultInfo)
			So(err, ShouldBeNil)
		})

		Convey("Case 2: UUID generation failure should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
			}
			vcjobId := "vcjob1"

			patcherUUID := gomonkey.ApplyFunc(uuid.NewUUID, func() (uuid.UUID, error) {
				return uuid.UUID{}, errors.New("uuid error")
			})
			defer patcherUUID.Reset()

			err := (&FaultInfoHandler{}).FaultInfoCreateHandle(vcjobId, faultInfo)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "uuid error")
		})

		Convey("Case 3: PostFaultInfoToFaultMgt failure should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
			}
			vcjobId := "vcjob1"

			patchPost := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "PostFaultInfoToFaultMgt", func(_ *FaultInfoHandler, faultInfo *FaultInfo, vcjobId string) error {
				return errors.New("post error")
			})
			defer patchPost.Reset()

			patcherUUID := gomonkey.ApplyFunc(uuid.NewUUID, func() (uuid.UUID, error) {
				return uuid.Parse("0e7ff592-f59e-11ef-b04b-5eb727681b13")
			})
			defer patcherUUID.Reset()

			err := (&FaultInfoHandler{}).FaultInfoCreateHandle(vcjobId, faultInfo)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "post error")
		})

		Convey("Case 4: JSON Marshal failure should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
			}
			vcjobId := "vcjob1"

			patchPost := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "PostFaultInfoToFaultMgt", func(_ *FaultInfoHandler, faultInfo *FaultInfo, vcjobId string) error {
				return nil
			})
			defer patchPost.Reset()

			patcherMarshal := gomonkey.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
				return nil, errors.New("marshal error")
			})
			defer patcherMarshal.Reset()

			patcherUUID := gomonkey.ApplyFunc(uuid.NewUUID, func() (uuid.UUID, error) {
				return uuid.Parse("0e7ff592-f59e-11ef-b04b-5eb727681b13")
			})
			defer patcherUUID.Reset()

			err := (&FaultInfoHandler{}).FaultInfoCreateHandle(vcjobId, faultInfo)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "marshal error")
		})

		Convey("Case 5: InsertFaultInfoToDB failure should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
			}
			vcjobId := "vcjob1"

			patchPost := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "PostFaultInfoToFaultMgt", func(_ *FaultInfoHandler, faultInfo *FaultInfo, vcjobId string) error {
				return nil
			})
			patchInsert := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "InsertFaultInfoToDB", func(_ *FaultInfoHandler, faultItem *FaultItem) error {
				return errors.New("insert error")
			})
			defer patchPost.Reset()
			defer patchInsert.Reset()

			patcherUUID := gomonkey.ApplyFunc(uuid.NewUUID, func() (uuid.UUID, error) {
				return uuid.Parse("0e7ff592-f59e-11ef-b04b-5eb727681b13")
			})
			defer patcherUUID.Reset()

			err := (&FaultInfoHandler{}).FaultInfoCreateHandle(vcjobId, faultInfo)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "insert error")
		})
	})
}

/* Ended by AICoder, pid:e49e7oa014kd31a145760adc0135da136c862ab3 */
/* Started by AICoder, pid:l0d72e62b0u39de1495b0a5c81261d3b0c708883 */
func TestFaultInfoUpdateHandle(t *testing.T) {
	Convey("Given a VcjobId and FaultInfo to update", t, func() {

		Convey("Case 1: Valid input should succeed", func() {
			faultInfo := &FaultInfo{
				FaultCode:   1001,
				FaultId:     "fault1",
				ClearedTime: "",
			}
			vcjobId := "vcjob1"

			patchPut := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "PutFaultInfoToFaultMgt", func(_ *FaultInfoHandler, faultInfo *FaultInfo, vcjobId string) error {
				return nil
			})
			patchUpdate := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "UpdateFaultInfoToDB", func(_ *FaultInfoHandler, faultItem *FaultItem) error {
				return nil
			})
			defer patchPut.Reset()
			defer patchUpdate.Reset()

			err := (&FaultInfoHandler{}).FaultInfoUpdateHandle(vcjobId, faultInfo)
			So(err, ShouldBeNil)
		})

		Convey("Case 2: PutFaultInfoToFaultMgt failure should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode:   1001,
				FaultId:     "fault1",
				ClearedTime: "",
			}
			vcjobId := "vcjob1"

			patchPut := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "PutFaultInfoToFaultMgt", func(_ *FaultInfoHandler, faultInfo *FaultInfo, vcjobId string) error {
				return errors.New("put error")
			})
			defer patchPut.Reset()

			err := (&FaultInfoHandler{}).FaultInfoUpdateHandle(vcjobId, faultInfo)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "put error")
		})

		Convey("Case 3: JSON Marshal failure should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode:   1001,
				FaultId:     "fault1",
				ClearedTime: "",
			}
			vcjobId := "vcjob1"

			patchPut := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "PutFaultInfoToFaultMgt", func(_ *FaultInfoHandler, faultInfo *FaultInfo, vcjobId string) error {
				return nil
			})
			patcherMarshal := gomonkey.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
				return nil, errors.New("marshal error")
			})
			defer patchPut.Reset()
			defer patcherMarshal.Reset()

			err := (&FaultInfoHandler{}).FaultInfoUpdateHandle(vcjobId, faultInfo)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "marshal error")
		})

		Convey("Case 4: UpdateFaultInfoToDB failure should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode:   1001,
				FaultId:     "fault1",
				ClearedTime: "",
			}
			vcjobId := "vcjob1"

			patchPut := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "PutFaultInfoToFaultMgt", func(_ *FaultInfoHandler, faultInfo *FaultInfo, vcjobId string) error {
				return nil
			})
			patchUpdate := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "UpdateFaultInfoToDB", func(_ *FaultInfoHandler, faultItem *FaultItem) error {
				return errors.New("update error")
			})
			defer patchPut.Reset()
			defer patchUpdate.Reset()

			err := (&FaultInfoHandler{}).FaultInfoUpdateHandle(vcjobId, faultInfo)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "update error")
		})

		Convey("Case 5: ClearedTime set should call DeleteFaultInfoFromDB", func() {
			faultInfo := &FaultInfo{
				FaultCode:   1001,
				FaultId:     "fault1",
				ClearedTime: "2023-01-01T00:00:00Z",
			}
			vcjobId := "vcjob1"

			patchPut := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "PutFaultInfoToFaultMgt", func(_ *FaultInfoHandler, faultInfo *FaultInfo, vcjobId string) error {
				return nil
			})
			patchDelete := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "DeleteFaultInfoFromDB", func(_ *FaultInfoHandler, faultId string) error {
				return nil
			})
			defer patchPut.Reset()
			defer patchDelete.Reset()

			err := (&FaultInfoHandler{}).FaultInfoUpdateHandle(vcjobId, faultInfo)
			So(err, ShouldBeNil)
		})

		Convey("Case 6: DeleteFaultInfoFromDB failure should return an error", func() {
			faultInfo := &FaultInfo{
				FaultCode:   1001,
				FaultId:     "fault1",
				ClearedTime: "2023-01-01T00:00:00Z",
			}
			vcjobId := "vcjob1"

			patchPut := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "PutFaultInfoToFaultMgt", func(_ *FaultInfoHandler, faultInfo *FaultInfo, vcjobId string) error {
				return nil
			})
			patchDelete := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "DeleteFaultInfoFromDB", func(_ *FaultInfoHandler, faultId string) error {
				return errors.New("delete error")
			})
			defer patchPut.Reset()
			defer patchDelete.Reset()

			err := (&FaultInfoHandler{}).FaultInfoUpdateHandle(vcjobId, faultInfo)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "delete error")
		})
	})
}

/* Ended by AICoder, pid:l0d72e62b0u39de1495b0a5c81261d3b0c708883 */

/* Started by AICoder, pid:e1d8312f08j4c5c1465c0ac240dafc7f64929f57 */
func TestFaultInfoChangeHandle(t *testing.T) {
	Convey("Given a VcjobId and FaultInfo to change", t, func() {

		Convey("Case 1: New FaultInfo should call FaultInfoCreateHandle", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
				FaultId:   "",
			}
			vcjobId := "vcjob1"

			patchCreate := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "FaultInfoCreateHandle", func(_ *FaultInfoHandler, vcjobId string, faultInfo *FaultInfo) error {
				return nil
			})
			defer patchCreate.Reset()

			ft := &FaultInfoHandler{}
			ft.FaultInfoChangelHandle(vcjobId, faultInfo)
			So(true, ShouldBeTrue) // If we reach this point, the function executed without error
		})

		Convey("Case 2: Existing FaultInfo should call FaultInfoUpdateHandle", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
				FaultId:   "fault1",
			}
			vcjobId := "vcjob1"

			patchUpdate := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "FaultInfoUpdateHandle", func(_ *FaultInfoHandler, vcjobId string, faultInfo *FaultInfo) error {
				return nil
			})
			defer patchUpdate.Reset()

			ft := &FaultInfoHandler{}
			ft.FaultInfoChangelHandle(vcjobId, faultInfo)
			So(true, ShouldBeTrue) // If we reach this point, the function executed without error
		})

		Convey("Case 3: FaultInfoCreateHandle failure should log an error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
				FaultId:   "",
			}
			vcjobId := "vcjob1"

			patchCreate := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "FaultInfoCreateHandle", func(_ *FaultInfoHandler, vcjobId string, faultInfo *FaultInfo) error {
				return errors.New("create error")
			})
			defer patchCreate.Reset()

			ft := &FaultInfoHandler{}
			ft.FaultInfoChangelHandle(vcjobId, faultInfo)
			So(true, ShouldBeTrue) // If we reach this point, the function logged an error as expected
		})

		Convey("Case 4: FaultInfoUpdateHandle failure should log an error", func() {
			faultInfo := &FaultInfo{
				FaultCode: 1001,
				FaultId:   "fault1",
			}
			vcjobId := "vcjob1"

			patchUpdate := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "FaultInfoUpdateHandle", func(_ *FaultInfoHandler, vcjobId string, faultInfo *FaultInfo) error {
				return errors.New("update error")
			})
			defer patchUpdate.Reset()

			ft := &FaultInfoHandler{}
			ft.FaultInfoChangelHandle(vcjobId, faultInfo)
			So(true, ShouldBeTrue) // If we reach this point, the function logged an error as expected
		})
	})
}

/* Ended by AICoder, pid:e1d8312f08j4c5c1465c0ac240dafc7f64929f57 */

/* Started by AICoder, pid:c09dar0b3aud15f1483c085fa0f7514537982991 */
func TestFaultInfoHandle(t *testing.T) {
	Convey("Given a faultInfo string and VcjobCloudClusterInfo", t, func() {

		vcjobCloudClusterInfo := &VcjobCloudClusterInfo{
			UUID: "test-uuid",
		}
		faultInfo := "{}" // 假设的故障信息字符串

		Convey("Case 1: GetVcjobFaultInfoFromDB failure should log an error", func() {
			patchGet := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "GetVcjobFaultInfoFromDB", func(_ *FaultInfoHandler, vcjobId string) ([]*FaultInfo, error) {
				return nil, errors.New("get vcjob faultinfo from db failed")
			})
			defer patchGet.Reset()

			ft := &FaultInfoHandler{}
			ft.FaultInfoHandle(faultInfo, vcjobCloudClusterInfo)
			// Since the function logs the error and returns early, we just need to ensure it doesn't panic.
			So(true, ShouldBeTrue) // If we reach this point, the function executed without panicking
		})

		Convey("Case 2: Valid input should process changed fault info", func() {
			oldFaultInfo := []*FaultInfo{{FaultId: "fault1"}}
			newFaultInfo := []*FaultInfo{{FaultId: "fault2"}}

			patchGet := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "GetVcjobFaultInfoFromDB", func(_ *FaultInfoHandler, vcjobId string) ([]*FaultInfo, error) {
				return oldFaultInfo, nil
			})
			patchParse := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "ParseNewFaultInfo", func(_ *FaultInfoHandler, faultInfo string, vcjobCloudClusterInfo *VcjobCloudClusterInfo) ([]*FaultInfo, string) {
				return newFaultInfo, "2025-03-10T06:43:03.000Z"
			})
			patchChange := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "GetChangedFaultInfo", func(_ *FaultInfoHandler, oldFaultInfo, newFaultInfo []*FaultInfo) []*FaultInfo {
				return newFaultInfo
			})
			patchChangeHandle := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "FaultInfoChangelHandle", func(_ *FaultInfoHandler, vcjobId string, faultInfo *FaultInfo) {
				// Do nothing, just simulate the method call
			})
			defer patchGet.Reset()
			defer patchParse.Reset()
			defer patchChange.Reset()
			defer patchChangeHandle.Reset()

			ft := &FaultInfoHandler{}
			ft.FaultInfoHandle(faultInfo, vcjobCloudClusterInfo)
			// If we reach this point, the function executed without error
			So(true, ShouldBeTrue)
		})
	})
}

/* Ended by AICoder, pid:c09dar0b3aud15f1483c085fa0f7514537982991 */
