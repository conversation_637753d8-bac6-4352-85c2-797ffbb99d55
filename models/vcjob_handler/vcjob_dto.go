package vcjob_handler

type ResourceChange struct {
	Action   string         `json:"action,omitempty"`
	BaseMoc  string         `json:"baseMoc,omitempty"`
	Id       string         `json:"id,omitempty"`
	NewModel *ResourceModel `json:"newModel,omitempty"`
	OldModel *ResourceModel `json:"oldModel,omitempty"`
}

type ResourceModel struct {
	LastTransitionTime string `json:"last_transition_time,omitempty"`
	CloudUuid          string `json:"cloudUUID,omitempty"`
	CloudName          string `json:"cloudName,omitempty"`
	Name               string `json:"name,omitempty"`
	Status             string `json:"status,omitempty"`
	NamespaceName      string `json:"namespaceName,omitempty"`
	ClusterId          string `json:"clusterId,omitempty"`
}

type VcJobList struct {
	Vcjobs []*vcjobItem `json:"vcjobs"`
}

type vcjobItem struct {
	FaultInfo          string `json:"faultInfo"`
	Uuid               string `json:"uuid"`
	Name               string `json:"name"`
	CloudUuid          string `json:"cloud_uuid"`
	CloudName          string `json:"cloud_name"`
	TenantName         string `json:"tenant_name"`
	Status             string `json:"status"`
	LastTransitionTime string `json:"lastTransitionTime"`
	Action             string `json:"-"`
	Clusterid          string `json:"cluster_id"`
}

func (rc *ResourceChange) IsResourceMessageValid() bool {
	if rc.Action == "delete" {
		return len(rc.Id) != 0 && len(rc.OldModel.CloudUuid) != 0 &&
			len(rc.OldModel.NamespaceName) != 0 &&
			len(rc.OldModel.Name) != 0 && len(rc.OldModel.CloudName) != 0
	}
	return len(rc.Id) != 0 && len(rc.NewModel.CloudUuid) != 0 &&
		len(rc.NewModel.NamespaceName) != 0 &&
		len(rc.NewModel.Name) != 0 && len(rc.NewModel.CloudName) != 0
}

func (rc *ResourceChange) TranslateToVcJobItem() *vcjobItem {
	vc := &vcjobItem{}
	vc.Action = rc.Action
	vc.Uuid = rc.Id
	if (rc.Action == "create" || rc.Action == "update") && rc.NewModel != nil {
		vc.Name = rc.NewModel.Name
		vc.CloudUuid = rc.NewModel.CloudUuid
		vc.Status = rc.NewModel.Status
		vc.LastTransitionTime = rc.NewModel.LastTransitionTime
		vc.CloudName = rc.NewModel.CloudName
		vc.TenantName = rc.NewModel.NamespaceName
		vc.Clusterid = rc.NewModel.ClusterId
	}
	if rc.Action == "delete" && rc.OldModel != nil {
		vc.Name = rc.OldModel.Name
		vc.CloudUuid = rc.OldModel.CloudUuid
		vc.Status = "deleted"
		vc.LastTransitionTime = rc.OldModel.LastTransitionTime
		vc.CloudName = rc.OldModel.CloudName
		vc.TenantName = rc.OldModel.NamespaceName
		vc.Clusterid = rc.OldModel.ClusterId
	}
	return vc
}
