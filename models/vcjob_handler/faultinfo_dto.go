package vcjob_handler

/* Started by AICoder, pid:508f1qa3e0md06b148c108bce0e71630efc94b27 */
const (
	Layout                    = "2006-01-02T15:04:05.999Z"
	ECC_ERROR_Desc_zh         = "GPU内存2比特ECC错误，原因为GPU的DDR或者HBM内存颗粒部分损坏，数据读写不正确。"
	ECC_ERROR_Desc_en         = "2-bit ECC GPU memory errors due to partially damaged DDR/HBM memory chips on a single device, resulting in data corruption during read/write."
	GpuPciLost_Desc_zh        = "GPU设备掉卡。"
	GpuPciLost_Desc_en        = "GPU Device Removed From the Node."
	ProcessEngineHang_Desc_zh = "GPU COMMAND PROCESSOR 引擎 hang。"
	ProcessEngineHang_Desc_en = "GPU COMMAND PROCESSOR engine hang."
	ComputeEngineHang_Desc_zh = "GPU计算引擎hang，原因为计算核内同步指令有bug或者计算核代码逻辑错误。"
	ComputeEngineHang_Desc_en = "GPU compute engine hang，There is a bug in the compute core synchronization instructions or a logical error in the compute core code."
	DMAEngineHang_Desc_zh     = "GPU DMA 引擎hang，原因可能为执行命令发生互锁或者等待超时。"
	DMAEngineHang_Desc_en     = "GPU DMA engine hang,There may be a deadlock in the device executing commands, or the conditions for the device to wait have not been met."

	SMMUFault_Desc_zh                  = "GPU系统内存管理单元故障。"
	SMMUFault_Desc_en                  = "GPU System Memory Management Unit fault."
	VideoEngineHang_Desc_zh            = "GPU video 引擎 hang。"
	VideoEngineHang_Desc_en            = "GPU video engine hang."
	BusError_Desc_zh                   = "GPU总线错误。"
	BusError_Desc_en                   = "GPU Bus Error."
	HeartBeatLost_Desc_zh              = "GPU设备与host心跳丢失故障。"
	HeartBeatLost_Desc_en              = "GPU device lost heartbeat with host."
	KernelOOM_Desc_zh                  = "GPU节点操作系统内核OOM故障，可能导致系统内存分配失败。"
	KernelOOM_Desc_en                  = "GPU node operating system kernel OOM failure, Maybe system memory allocation failed."
	DeviceBoot_Desc_zh                 = "GPU设备加载驱动失败。"
	DeviceBoot_Desc_en                 = "GPU device driver failed to load."
	KernelPanic_Desc_zh                = "操作系统内核panic，可能原因如下: 1. 内核出现oops（空指针引用.除0等）。 2. 地址异常、访问非法地址等。 3. CPU停止响应。"
	KernelPanic_Desc_en                = "operating system kernel panic, possible reasons are as follows:1.Kernel oops (null pointer dereference, division by zero, etc.).2.Address exceptions, illegal address access, etc. 3.CPU stops responding."
	FirmwareFailure_Desc_zh            = "GPU固件异常故障。"
	FirmwareFailure_Desc_en            = "GPU firmware anomaly failure."
	GpuResidual_Desc_zh                = "GPU占用残留导致训练失败。"
	GpuResidual_Desc_en                = "residual GPU usage causing training failure."
	PCleAERError_Desc_zh               = "GPU PCIe AER故障。"
	PCleAERError_Desc_en               = "GPU PCIe AER failure."
	GPUUnvailable_Desc_zh              = "GPU驱动掉卡。"
	GPUUnvailable_Desc_en              = "GPU unavailable failure."
	RDMADeviceDroped_Desc_zh           = "RDMA设备掉卡。"
	RDMADeviceDroped_Desc_en           = "RDMA Device Removed From the Node."
	InsufficientClusterRes_Desc_zh     = "集群资源不足的故障，原因为集群资源配额小于蓝图请求的资源。"
	InsufficientClusterRes_Desc_en     = "insufficient cluster resources."
	AffinityIssue_Desc_zh              = "节点亲和性标签问题的故障，原因为蓝图中申请资源的标签和集群的标签不一致。"
	AffinityIssue_Desc_en              = "node affinity label issue."
	GPUResourceInsufficient_Desc_zh    = "GPU资源不足。"
	GPUResourceInsufficient_Desc_en    = "insufficient GPU resources."
	RDMAResourceInsufficient_Desc_zh   = "RDMA资源不足。"
	RDMAResourceInsufficient_Desc_en   = "insufficient RDMA resources."
	CPUResoureceInsufficient_Desc_zh   = "CPU资源不足。"
	CPUResoureceInsufficient_Desc_en   = "insufficient CPU resources."
	MemoryResourceInsufficient_Desc_zh = "内存资源不足。"
	MemoryResourceInsufficient_Desc_en = "insufficient Memory resources."
	GpuMemoryUnitInvalid_Desc_zh       = "GPU设备CR内存单位错误。"
	GpuMemoryUnitInvalid_Desc_en       = "incorrect CR(GPU device Custom Resource) memory unit."
	GpuCRCountAbnormal_Desc_zh         = "GPU设备CR数量异常。"
	GpuCRCountAbnormal_Desc_en         = "abnormal CR(GPU device Custom Resource) count."

	PodFailedRdmaAlreadyAllocated_Desc_zh = "Pod在节点启动失败-RDMA已分配。"
	PodFailedRdmaAlreadyAllocated_Desc_en = "pod failed to start on node - RDMA already allocated."
	GpuUuuidDuplicate_Desc_zh             = "GPU UUID 重复。"
	GpuUuuidDuplicate_Desc_en             = "duplicate GPU UUID."
	GpuDriverFailure_Desc_zh              = "GPU驱动失败。"
	GpuDriverFailure_Desc_en              = "GPU driver failure."
	GpuTopoError_Desc_zh                  = "GPU拓扑错误。"
	GpuTopoError_Desc_en                  = "GPU topology error."
)

/* Ended by AICoder, pid:508f1qa3e0md06b148c108bce0e71630efc94b27 */

type FaultItem struct {
	VcjobId   string `json:"vcjobId"`
	Id        string `json:"id"`
	FaultCode int64  `json:"faultCode"`
	FaultInfo string `json:"faultInfo"`
}

type FaultInfo struct {
	FaultId           string              `json:"fault_id"`
	FaultCode         int64               `json:"fault_code"`
	RaisedTime        string              `json:"raised_time"`
	ClearedTime       string              `json:"cleared_time"`
	ReportedTime      string              `json:"reported_time"`
	DescriptionZH     string              `json:"description_zh"`
	DescriptionEN     string              `json:"description_en"`
	EnvId             string              `json:"env_id"`
	EnvName           string              `json:"env_name"`
	RootCauseObject   []*RootCauseObject  `json:"root_cause_objects"`
	RootCauseDetail   []*RootCauseDetail  `json:"root_cause_detail"`
	AssociatedObjects []*AssociatedObject `json:"associated_objects"`
	NeedReportToNorth bool                `json:"need_report_to_north"`
}

type VcjobCloudClusterInfo struct {
	UUID        string `json:"uuid"`
	Name        string `json:"name"`
	TenantName  string `json:"tenantName"`
	ClusterName string `json:"clusterName"`
	ClusterId   string `json:"clusterId"`
	CloudId     string `json:"cloudId"`
	CloudName   string `json:"cloudName"`
}

type RootCauseObject struct {
	EnvId string `json:"env_id"`
	Id    string `json:"id"`
	Name  string `json:"name"`
	Moc   string `json:"moc"`
}

type RootCauseDetail struct {
	RcObjectId string   `json:"rc_object_id"`
	Type       string   `json:"type"`
	Data       []string `json:"data"`
}

type AssociatedObject struct {
	EnvId string `json:"env_id"`
	Id    string `json:"id"`
	Name  string `json:"name"`
	Moc   string `json:"moc"`
}

type FaultInfoWsm struct {
	Name         string              `json:"name"`
	NameSpace    string              `json:"namespace"`
	UID          string              `json:"uid,omitempty"`
	ReportTime   string              `json:"reportTime,omitempty"`
	GpuTrainInfo []ResourceEventInfo `json:"gpuTrainInfo,omitempty"`
	// GpuTrainInfo []ResourceEventInfo `json:"gpuWarningInfo,omitempty"`
	JobWarningInfo  []ResourceEventInfo `json:"jobWarningInfo,omitempty"`
	RdmaWarningInfo []ResourceEventInfo `json:"rdmaDeviceWarningInfo,omitempty"`
}

type ResourceEventInfo struct {
	TimeStamp  string `json:"timeStamp"`
	JobName    string `json:"jobName,omitempty"`
	ObjectType string `json:"objectType"`
	Object     string `json:"object"`
	ObjectId   string `json:"objectId"`
	NodeName   string `json:"nodeName"`
	NodeId     string `json:"nodeId,omitempty"`
	Reason     string `json:"reason"`
	Message    string `json:"message"`
	EventName  string `json:"eventName,omitempty"`
	Suggestion string `json:"suggestion,omitempty"`
}
type FaultCodeInfo struct {
	FaultCode     int64  `json:"faultCode"`
	DescriptionZH string `json:"description_zh"`
	DescriptionEN string `json:"description_en"`
	RootCauseMoc  string `json:"route_cause_moc"`
}

/* Started by AICoder, pid:j8a9etb5c7k2d3714b150934008c802a08b44c29 */
var ReasonToFaultCode = map[string]*FaultCodeInfo{
	"11:0xA4033002 2-bit ECC error":              {31017001, ECC_ERROR_Desc_zh, ECC_ERROR_Desc_en, ""},
	"GpuPciLost":                                 {31017002, GpuPciLost_Desc_zh, GpuPciLost_Desc_en, ""},
	"5:0xA4031001 COMMAND PROCESSOR engine hang": {31017003, ProcessEngineHang_Desc_zh, ProcessEngineHang_Desc_en, ""},
	"7:0xA4031003 compute engine hang":           {31017004, ComputeEngineHang_Desc_zh, ComputeEngineHang_Desc_en, ""},
	"6:0xA4031002 DMA engine hang":               {31017005, DMAEngineHang_Desc_zh, DMAEngineHang_Desc_en, ""},
	"8:0xA4031004 video engine hang":             {31017006, VideoEngineHang_Desc_zh, VideoEngineHang_Desc_en, ""},
	"17:0xA4031005 bus error":                    {31017007, BusError_Desc_zh, BusError_Desc_en, ""},
	"16:0xA4041001 heartbeat error":              {31017008, HeartBeatLost_Desc_zh, HeartBeatLost_Desc_en, ""},
	"3:0x62021001 operating system kernel OOM":   {31017009, KernelOOM_Desc_zh, KernelOOM_Desc_en, ""},
	"4:0xA4030001 device boot failure":           {31017010, DeviceBoot_Desc_zh, DeviceBoot_Desc_en, ""},
	"2:0x64020001 operating system kernel panic": {31017011, KernelPanic_Desc_zh, KernelPanic_Desc_en, ""},
	"15:0xA4040001 firmware error":               {31017012, FirmwareFailure_Desc_zh, FirmwareFailure_Desc_en, ""},
	"GpuResidual":                                {31017013, GpuResidual_Desc_zh, GpuResidual_Desc_en, ""},
	"13:0xA4034001 AER error":                    {31017014, PCleAERError_Desc_zh, PCleAERError_Desc_en, ""},
	"Unavailable":                                {31017015, GPUUnvailable_Desc_zh, GPUUnvailable_Desc_en, ""},
	"9:0xA3032001 SMMU fault":                    {31017016, SMMUFault_Desc_zh, SMMUFault_Desc_en, ""},
	"RDMADeviceDropped":                          {33012001, RDMADeviceDroped_Desc_zh, RDMADeviceDroped_Desc_en, ""},
	// "RDMA device status unhealthy":               {33012002, "RDMA设备状态不健康", "RDMA device status unhealthy", ""},
	"Insufficient cluster resources":         {40003001, InsufficientClusterRes_Desc_zh, InsufficientClusterRes_Desc_en, ""},
	"Blueprint and node labels do not match": {40003002, AffinityIssue_Desc_zh, AffinityIssue_Desc_en, ""}, // 修改并取消注释
	// "Insufficient GPU Resources":             {40003003, GPUResourceInsufficient_Desc_zh, GPUResourceInsufficient_Desc_en, ""},
	// "Insufficient RDMA Resources":            {40003004, RDMAResourceInsufficient_Desc_zh, RDMAResourceInsufficient_Desc_en, ""},
	// "Insufficient CPU Resources":             {40003005, CPUResoureceInsufficient_Desc_zh, CPUResoureceInsufficient_Desc_en, ""},
	// "Insufficient Memory Resources":          {40003006, MemoryResourceInsufficient_Desc_zh, MemoryResourceInsufficient_Desc_en, ""},
	// "Gpu memory unit invalid":                {40003007, GpuMemoryUnitInvalid_Desc_zh, GpuMemoryUnitInvalid_Desc_en, ""},
	// "Gpu CR num invalid":                     {40003008, GpuCRCountAbnormal_Desc_zh, GpuCRCountAbnormal_Desc_en, ""},
	// "RDMANetwork vf is already allocated":    {40003009, PodFailedRdmaAlreadyAllocated_Desc_zh, PodFailedRdmaAlreadyAllocated_Desc_en, ""},
	// "GpuUuidDuplicate":                       {40003010, GpuUuuidDuplicate_Desc_zh, GpuUuuidDuplicate_Desc_en, ""},
	// "Gpu driver not ready":                   {40003011, GpuDriverFailure_Desc_zh, GpuDriverFailure_Desc_en, ""},
	// "Gpu topo invalid":                       {40003012, GpuTopoError_Desc_zh, GpuTopoError_Desc_en, ""},
}

/* Ended by AICoder, pid:j8a9etb5c7k2d3714b150934008c802a08b44c29 */
var ReasonFixed = map[int64]struct{}{
	31017001: {}, //"11:0xA4033002 2-bit ECC error"
	31017002: {}, //"GpuPciLost"
	31017003: {}, //"5:0xA4031001 COMMAND PROCESSOR engine hang"
	31017004: {}, //"7:0xA4031003 compute engine hang"
	31017005: {}, //"6:0xA4031002 DMA engine hang"
	31017006: {}, //"8:0xA4031004 video engine hang"
	31017007: {}, //"17:0xA4031005 bus error"
	31017008: {}, //"16:0xA4041001 heartbeat error"
	31017009: {}, //"3:0x62021001 operating system kernel OOM"
	31017010: {}, //"4:0xA4030001 device boot failure"
	31017011: {}, //"2:0x64020001 operating system kernel panic"
	31017012: {}, //"15:0xA4040001 firmware error"
	31017014: {}, //"13:0xA4034001 AER error"
	31017016: {}, //"9:0xA3032001 SMMU fault"
}

type RdmaDetailInfo struct {
	NodeId     string
	NodeName   string
	Detail     string
	RaisedTime string
}
