package vcjob_handler

import (
	"context"
	"cwsm/infra/authorization"
	"cwsm/infra/configcenter"
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/models"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

var VcjobHandler *vcjobHandler
var vcjobStatusNeedDemacration []string = []string{"pending", "restarting", "running"}
var vcjobStatusCouldNeedDemacration []string = []string{"terminating", "aborting", "aborted", "terminated", "failed"}

type vcjobHandler struct {
	VcJobMessageQueue            map[string]*Queue
	PeriodicFaultDemacrationList []map[string]interface{}
	lock                         sync.Mutex
	stopChan                     chan struct{}
}

/* Started by AICoder, pid:zd24bcb63b34bb0141410aa3d35038528784b1b9 */
func NewVcjobHandler(vcjob *VcJobList) *vcjobHandler {
	if vcjob == nil {
		return &vcjobHandler{}
	}
	vcjob_handler := &vcjobHandler{
		stopChan:          make(chan struct{}),
		VcJobMessageQueue: make(map[string]*Queue, 0),
	}
	for _, vcjobItem := range vcjob.Vcjobs {
		logger.Info("init queue uuid %s", vcjobItem.Uuid)
		vcjobItem.Action = "create"
		vcjob_handler.AddMessageToQueueWithId(vcjobItem.Uuid, vcjobItem)
	}
	return vcjob_handler
}

func InitVcjobFromPvrm() error {
	vcjobInfo, err := authorization.GetVcjobInfoFromPvrm()
	if err != nil {
		return err
	}
	vcjobInst := &VcJobList{}
	if err := json.Unmarshal(vcjobInfo, vcjobInst); err != nil {
		return err
	}
	VcjobHandler = NewVcjobHandler(vcjobInst)
	return nil
}

func (vc *vcjobHandler) AddMessageToQueueWithId(uuid string, item *vcjobItem) {
	logger.Info("add uuid to queue, %s", uuid)
	vc.lock.Lock()
	defer vc.lock.Unlock()
	if len(uuid) != 0 {
		if _, ok := vc.VcJobMessageQueue[uuid]; !ok {
			logger.Info("add vcjob message to new uuid queue, %s", uuid)
			vc.VcJobMessageQueue[uuid] = &Queue{}
			vc.VcJobMessageQueue[uuid].Enqueue(item)
			go vc.ConsumeVcJobChangeMessage(uuid, vc.VcJobMessageQueue[uuid])
			return
		}
		logger.Info("add vcjob message to old uuid queue, %s", uuid)
		vc.VcJobMessageQueue[uuid].Enqueue(item)
	}
}

func (vc *vcjobHandler) DeleteQueueWithId(uuid string) {
	logger.Info("delete queue with, %s", uuid)
	vc.lock.Lock()
	defer vc.lock.Unlock()
	delete(vc.VcJobMessageQueue, uuid)
}

func (vc *vcjobHandler) HandlerVcJobWithDB(k string, value *vcjobItem) error {
	if value.Action == "delete" {
		if !models.DeleteResourceByCol(constant.TABLE_NAME_FAULTINFO, "vcjobId", k) {
			logger.Info("delete vcjob %s faultinfo from db failed", k)
		}
		if !configcenter.GetRetentionSwitchValue() {
			logger.Info("the RetentionSwitchValue is false, do not keep history vcjob, delete")
			if !models.DeleteResourceByUuid(constant.TABLE_NAME_VCJOBFAULTHISTORY, k) {
				return fmt.Errorf("delete vcjob %s from db failed", k)
			}
			return nil
		}
		logger.Info("the RetentionSwitchValue is true, keep history vcjob, update")
	}
	if err := vc.UpdateVcjobToDB(k, value); err != nil {
		return err
	}
	return nil
}

func (vc *vcjobHandler) ConsumeVcJobChangeMessage(key string, q *Queue) {
	for {
		select {
		case <-vc.stopChan:
			logger.Info("receive the ctx to stop consume vcjob message")
			return
		default:
			if q.Length() == 0 {
				time.Sleep(2 * time.Second)
				continue
			}
			value := q.GetFirstValue()
			logger.Infof("consume vcjob change message, uuid: %s", key)
			if err := vc.HandlerVcJobWithDB(key, value); err != nil {
				logger.Errorf("%v", err)
				continue
			}
			q.Dequeue()
			if value.Action == "delete" {
				logger.Infof("the vcjob %s queue receive delete message, stop consume", key)
				vc.DeleteQueueWithId(key)
				return
			}
			time.Sleep(2 * time.Second)
		}
	}

}

func (vc *vcjobHandler) UpdateVcjobToDB(key string, item *vcjobItem) error {
	if item == nil {
		return fmt.Errorf("update vcjob to database failed, because update item is nil")
	}
	res, err := models.QueryResourceByUuid(constant.TABLE_NAME_VCJOBFAULTHISTORY, key)
	if err != nil {
		return fmt.Errorf("query vcjob %s to database by uuid failed", key)
	}
	updateMap, err := vc.translateStructToMap(item)
	if err != nil {
		return err
	}
	if len(res) == 0 {
		if item.Action == "create" {
			return vc.InsertVcjobInfoToDB(key, updateMap)
		}
		logger.Info("the vcjob has been deleted before update, ignore")
		return nil
	}
	return vc.UpdateVcjobInfoToDB(key, updateMap)
}

func (vc *vcjobHandler) InsertVcjobInfoToDB(key string, updateMap map[string]interface{}) error {
	if !models.InsertNewResource(constant.TABLE_NAME_VCJOBFAULTHISTORY, updateMap) {
		return fmt.Errorf("no vcjob with uuid %s in db, but insert vcjob to database by uuid failed", key)
	}
	return nil
}

func (vc *vcjobHandler) UpdateVcjobInfoToDB(key string, updateMap map[string]interface{}) error {
	cleanUpdateMap(updateMap)
	if !models.UpdateResourceByUuid(constant.TABLE_NAME_VCJOBFAULTHISTORY, key, updateMap) {
		return fmt.Errorf("update vcjob %s to database by uuid failed", key)
	}
	return nil
}

func cleanUpdateMap(updateMap map[string]interface{}) {
	for key, v := range updateMap {
		if key == "uuid" {
			delete(updateMap, key)
			continue
		}
		if strVal, ok := v.(string); ok && strVal == "" {
			delete(updateMap, key)
		}
	}
}

func (vc *vcjobHandler) GetPeriodicFaultDemacration(ctx context.Context) {
	vc.doFaultDemacration()
	timeDuration := 10 * time.Second
	timer := time.NewTimer(timeDuration)
	defer timer.Stop()

	for {
		select {
		case <-ctx.Done():
			logger.Info("receive sys ctx to stop periodic fault demacration")
			return
		case <-timer.C:
			vc.doFaultDemacration()
		}
		timer.Reset(timeDuration)
	}
}

func (vc *vcjobHandler) doFaultDemacration() {
	if configcenter.GetFaultAnalaSwitchValue() {
		logger.Info("the faultanalaSwitchValue is true, begin to start periodic fault demacration")
		res, err := models.QueryResourceAll(constant.TABLE_NAME_VCJOBFAULTHISTORY)
		if err != nil {
			logger.Error("During periodic fault demacration, query vcjob from db failed")
			return
		}
		vc.getVcjobNeedDemacration(res)
		vc.GetFaultDemacrationResult()
	} else {
		logger.Info("the faultanalaSwitchValue is false, do not need periodic fault demacration")
	}
}

func (vc *vcjobHandler) GetFaultDemacrationResult() {
	var wg sync.WaitGroup
	var clusterInfo = make(map[string]authorization.OpenpaletteInfo, 0)
	for _, inst := range vc.PeriodicFaultDemacrationList {
		vcInst, err := vc.translateMapToVcjobStruct(inst)
		if err != nil {
			logger.Errorf("%v", err)
			continue
		}
		if _, ok := clusterInfo[vcInst.Clusterid]; !ok {
			info, err := wsm.GetOpenpalette(vcInst.Clusterid)
			if err != nil {
				logger.Errorf("GetFaultDemacrationResult get openpalette info error %v", err)
				continue
			}
			clusterInfo[vcInst.Clusterid] = info
		}
		wg.Add(1)
		go func(vcItem *vcjobItem, info authorization.OpenpaletteInfo) {
			defer wg.Done()
			faultRes, err := vc.getFaultDemacrationResultWithOpInfo(vcItem, info)
			if err != nil {
				logger.Errorf("%v", err)
				if strings.Contains(err.Error(), "not found") {
					vcInst.Status = "deleted"
				} else {
					return
				}
			}
			vcInst.FaultInfo = string(faultRes)
			faultInfoHandler := &FaultInfoHandler{}
			logger.Info("began to get faultInfo %v", vcInst.FaultInfo)
			vcjobCloudClusterInfo := &VcjobCloudClusterInfo{UUID: vcInst.Uuid, Name: vcInst.Name, TenantName: vcInst.TenantName,
				ClusterId: vcInst.Clusterid, ClusterName: info.ClusterName, CloudId: vcInst.CloudUuid, CloudName: vcInst.CloudName}
			go faultInfoHandler.FaultInfoHandle(vcInst.FaultInfo, vcjobCloudClusterInfo)
			vc.AddMessageToQueueWithId(vcInst.Uuid, vcInst)
		}(vcInst, clusterInfo[vcInst.Clusterid])
	}
	wg.Wait()
}

func (vc *vcjobHandler) getFaultDemacrationResultWithOpInfo(vcItem *vcjobItem, info authorization.OpenpaletteInfo) ([]byte, error) {
	header := restful.DefaultHeaders()
	if len(info.Token) > 0 {
		header["X-Auth-Token"] = info.Token
	}
	url := info.MsbURL + "/opapi/wsm/v1/apts/vcjobevents/" + vcItem.TenantName + "/" + vcItem.Name
	body, _, _, err := restful.GetMethod(url, header, info.SSL)
	if err != nil {
		return nil, err
	}
	logger.Info("get vcjob %s, name %s demacration success", vcItem.Uuid, vcItem.Name)
	return body, nil
}

func (vc *vcjobHandler) translateMapToVcjobStruct(item map[string]interface{}) (*vcjobItem, error) {
	jsonData, err := json.Marshal(item)
	if err != nil {
		return nil, fmt.Errorf("error marshalling map: %v", err)
	}

	var vcjob = &vcjobItem{}
	err = json.Unmarshal(jsonData, vcjob)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling JSON: %v", err)
	}
	return vcjob, nil
}

func (vc *vcjobHandler) getVcjobNeedDemacration(src []map[string]interface{}) {
	res := make([]map[string]interface{}, 0)
	for _, vcjobMap := range src {
		state, ok := vcjobMap["status"].(string)
		if !ok {
			continue
		}
		if isContains(state, vcjobStatusNeedDemacration) {
			res = append(res, vcjobMap)
			continue
		}
		if isContains(state, vcjobStatusCouldNeedDemacration) {
			info, ok := vcjobMap["faultInfo"].(string)
			if !ok || len(info) == 0 {
				res = append(res, vcjobMap)
				continue
			}
		}
	}
	vc.PeriodicFaultDemacrationList = res
}

func (vc *vcjobHandler) translateStructToMap(item interface{}) (map[string]interface{}, error) {
	jsonBytes, err := json.Marshal(item)
	if err != nil {
		return map[string]interface{}{}, err
	}
	var updataMap map[string]interface{}
	err = json.Unmarshal(jsonBytes, &updataMap)
	if err != nil {
		return map[string]interface{}{}, err
	}
	return updataMap, nil
}

func isContains(src string, srcStr []string) bool {
	for _, s := range srcStr {
		if s == src {
			return true
		}
	}
	return false
}

/* Ended by AICoder, pid:zd24bcb63b34bb0141410aa3d35038528784b1b9 */
