package vcjob_handler

import (
	"cwsm/infra/authorization"
	"cwsm/infra/constant"
	"cwsm/models"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
)

type FaultInfoHandler struct {
}

/* Started by AICoder, pid:n36feodc6ct491f1405f081860b6fb2256c3a54e */
func (ft *FaultInfoHandler) ParseNewFaultInfo(faultInfo string, vcjobCloudClusterInfo *VcjobCloudClusterInfo) ([]*FaultInfo, string) {
	var faultInfoWsm FaultInfoWsm
	err := json.Unmarshal([]byte(faultInfo), &faultInfoWsm)
	if err != nil {
		logger.Info("unmarshal faultInfo of apts error: %v", err)
		return nil, ""
	}
	if faultInfoWsm.UID != vcjobCloudClusterInfo.UUID {
		logger.Info("already deleted vcjob : %v,curID %s ", vcjobCloudClusterInfo.UUID, faultInfoWsm.UID)
		return nil, ""
	}

	faultCodeMap := make(map[int64]*FaultInfo)

	ft.parseGpuTrainInfo(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)
	ft.parseJobWarningInfo(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)
	ft.parseRdmaWarningInfoNew(&faultInfoWsm, vcjobCloudClusterInfo, faultCodeMap)

	var faultInfos []*FaultInfo
	for _, info := range faultCodeMap {
		faultInfos = append(faultInfos, info)
	}
	return faultInfos, convertTimestampToUTC(faultInfoWsm.ReportTime)
}

/* Ended by AICoder, pid:n36feodc6ct491f1405f081860b6fb2256c3a54e */

/* Started by AICoder, pid:72ac2626be8cd9e146ba093be03dff0872d88f3d */
func NewRootCauseObject(envId, id, name, moc string) *RootCauseObject {
	return &RootCauseObject{
		EnvId: envId,
		Id:    id,
		Name:  name,
		Moc:   moc,
	}
}

/* Ended by AICoder, pid:72ac2626be8cd9e146ba093be03dff0872d88f3d */

/* Started by AICoder, pid:hd555yf7aeq562114c2b0bf12097e10993c7965e */
func NewRootCauseDetail(id, typ, data string) *RootCauseDetail {
	return &RootCauseDetail{
		RcObjectId: id,
		Type:       typ,
		Data:       []string{data},
	}
}

/* Ended by AICoder, pid:hd555yf7aeq562114c2b0bf12097e10993c7965e */

/* Started by AICoder, pid:3e69by5df724d231436f0b64605daa0f7648d1f3 */
func NewAssociatedObject(envId, id, name, moc string) *AssociatedObject {
	return &AssociatedObject{
		EnvId: envId,
		Id:    id,
		Name:  name,
		Moc:   moc,
	}
}

/* Ended by AICoder, pid:3e69by5df724d231436f0b64605daa0f7648d1f3 */

/* Started by AICoder, pid:4f248l3531ub673146e108ced09b0135e0d87fbc */
func (ft *FaultInfoHandler) parseGpuTrainInfo(faultInfoWsm *FaultInfoWsm, vcjobCloudClusterInfo *VcjobCloudClusterInfo, faultCodeMap map[int64]*FaultInfo) {
	for _, gpuInfo := range faultInfoWsm.GpuTrainInfo {
		faultCodeInfo, ok := ReasonToFaultCode[gpuInfo.Reason]
		if !ok {
			logger.Info("reason %s does not map to a known fault code", gpuInfo.Reason)
			continue // Skip if reason does not map to a known fault code
		}
		objectId := gpuInfo.ObjectId
		if objectId == "" {
			objectId = gpuInfo.Object
		}
		faultCode := faultCodeInfo.FaultCode
		rootCauseObject := NewRootCauseObject(vcjobCloudClusterInfo.CloudId, gpuInfo.ObjectId, gpuInfo.Object, "director.containergpu")
		rootCauseDetail := NewRootCauseDetail(objectId, "others",
			fmt.Sprintf("timeStamp:%s,node:%s,message:%s", gpuInfo.TimeStamp, gpuInfo.NodeName, gpuInfo.Message))
		if _, exists := faultCodeMap[faultCode]; !exists {
			faultCodeMap[faultCode] = &FaultInfo{
				FaultCode:       faultCodeInfo.FaultCode,
				EnvId:           vcjobCloudClusterInfo.CloudId,
				EnvName:         vcjobCloudClusterInfo.CloudName,
				RaisedTime:      convertTimestampToUTC(gpuInfo.TimeStamp),
				ReportedTime:    convertTimestampToUTC(faultInfoWsm.ReportTime),
				DescriptionZH:   fmt.Sprintf("任务%s发生%s", faultInfoWsm.Name, faultCodeInfo.DescriptionZH),
				DescriptionEN:   fmt.Sprintf("Vcjob %s encountered %s", faultInfoWsm.Name, faultCodeInfo.DescriptionEN),
				RootCauseObject: []*RootCauseObject{rootCauseObject},
				RootCauseDetail: []*RootCauseDetail{rootCauseDetail},
				AssociatedObjects: []*AssociatedObject{
					NewAssociatedObject(vcjobCloudClusterInfo.CloudId, faultInfoWsm.UID, faultInfoWsm.Name, "director.containervcjob")},
			}
		} else {
			faultCodeMap[faultCode].RootCauseObject = append(faultCodeMap[faultCode].RootCauseObject, rootCauseObject)
			faultCodeMap[faultCode].RootCauseDetail = append(faultCodeMap[faultCode].RootCauseDetail, rootCauseDetail)
		}
	}
}

/* Ended by AICoder, pid:4f248l3531ub673146e108ced09b0135e0d87fbc */

/* Started by AICoder, pid:087f662cf1ab1ad1452f0ac350db8d291355af5f */
func (ft *FaultInfoHandler) parseJobWarningInfo(faultInfoWsm *FaultInfoWsm, vcjobCloudClusterInfo *VcjobCloudClusterInfo, faultCodeMap map[int64]*FaultInfo) {
	for _, jobWarnInfo := range faultInfoWsm.JobWarningInfo {
		faultCodeInfo, ok := ReasonToFaultCode[jobWarnInfo.Reason]
		if !ok {
			logger.Info("reason %s does not map to a known fault code", jobWarnInfo.Reason)
			continue // Skip if reason does not map to a known fault code
		}
		faultCode := faultCodeInfo.FaultCode
		rootCauseObject := NewRootCauseObject(vcjobCloudClusterInfo.CloudId, vcjobCloudClusterInfo.ClusterId, vcjobCloudClusterInfo.ClusterName, "director.containercluster")
		rootCauseDetail := NewRootCauseDetail(vcjobCloudClusterInfo.ClusterId, "others",
			fmt.Sprintf("timeStamp:%s,message:%s", jobWarnInfo.TimeStamp, jobWarnInfo.Message))

		faultCodeMap[faultCode] = &FaultInfo{
			FaultCode:       faultCode,
			EnvId:           vcjobCloudClusterInfo.CloudId,
			EnvName:         vcjobCloudClusterInfo.CloudName,
			RaisedTime:      convertTimestampToUTC(jobWarnInfo.TimeStamp),
			ReportedTime:    convertTimestampToUTC(faultInfoWsm.ReportTime),
			DescriptionZH:   fmt.Sprintf("任务%s发生%s", faultInfoWsm.Name, faultCodeInfo.DescriptionZH),
			DescriptionEN:   fmt.Sprintf("Vcjob %s encountered %s", faultInfoWsm.Name, faultCodeInfo.DescriptionEN),
			RootCauseObject: []*RootCauseObject{rootCauseObject},
			RootCauseDetail: []*RootCauseDetail{rootCauseDetail},
			AssociatedObjects: []*AssociatedObject{
				NewAssociatedObject(vcjobCloudClusterInfo.CloudId, faultInfoWsm.UID, faultInfoWsm.Name, "director.containervcjob")},
		}
	}
}

/* Ended by AICoder, pid:087f662cf1ab1ad1452f0ac350db8d291355af5f */
// func convertTimestampToUTC(timestamp string) string {
// 	// 解析时间戳字符串
// 	t, err := time.Parse(time.RFC3339, timestamp)
// 	if err != nil {
// 		return ""
// 	}

// 	// 转换为UTC时间
// 	utcTime := t.UTC()

// 	// 格式化为字符串
// 	return utcTime.Format(Layout)
// }

func convertTimestampToUTC(timestamp string) string {
	// 解析时间戳字符串
	t, err := time.Parse(time.RFC3339, timestamp)
	if err != nil {
		return ""
	}

	// 转换为UTC时间
	utcTime := t.UTC()

	// 先格式化为基本格式
	baseFormat := utcTime.Format("2006-01-02T15:04:05Z")

	// 在Z前插入.000
	return baseFormat[:19] + ".000" + baseFormat[19:]
}

/* Started by AICoder, pid:yae2d1aa1f59a81141d20b02f0bf252e5728bad8 */
func (ft *FaultInfoHandler) parseRdmaWarningInfo(faultInfoWsm *FaultInfoWsm, vcjobCloudClusterInfo *VcjobCloudClusterInfo, faultCodeMap map[int64]*FaultInfo) {
	for _, rdmaInfo := range faultInfoWsm.RdmaWarningInfo {
		faultCodeInfo, ok := ReasonToFaultCode[rdmaInfo.Reason]
		if !ok {
			logger.Info("reason %s does not map to a known fault code", rdmaInfo.Reason)
			continue // Skip if reason does not map to a known fault code
		}
		faultCode := faultCodeInfo.FaultCode
		rootCauseObject := NewRootCauseObject(vcjobCloudClusterInfo.CloudId, rdmaInfo.NodeId, rdmaInfo.NodeName, "director.containernode")
		rootCauseDetail := NewRootCauseDetail(rdmaInfo.NodeId, "others",
			fmt.Sprintf("timeStamp:%s,node:%s,message:%s", rdmaInfo.TimeStamp, rdmaInfo.NodeName, rdmaInfo.Message))
		if _, exists := faultCodeMap[faultCode]; !exists {
			faultCodeMap[faultCode] = &FaultInfo{
				FaultCode:       faultCode,
				EnvId:           vcjobCloudClusterInfo.CloudId,
				EnvName:         vcjobCloudClusterInfo.CloudName,
				RaisedTime:      convertTimestampToUTC(rdmaInfo.TimeStamp),
				ReportedTime:    convertTimestampToUTC(faultInfoWsm.ReportTime),
				DescriptionZH:   fmt.Sprintf("任务%s发生%s", faultInfoWsm.Name, faultCodeInfo.DescriptionZH),
				DescriptionEN:   fmt.Sprintf("Vcjob %s encountered %s", faultInfoWsm.Name, faultCodeInfo.DescriptionEN),
				RootCauseObject: []*RootCauseObject{rootCauseObject},
				RootCauseDetail: []*RootCauseDetail{rootCauseDetail},
				AssociatedObjects: []*AssociatedObject{
					NewAssociatedObject(vcjobCloudClusterInfo.CloudId, faultInfoWsm.UID, faultInfoWsm.Name, "director.containervcjob")}}
		} else {
			faultCodeMap[faultCode].RootCauseObject = append(faultCodeMap[faultCode].RootCauseObject, rootCauseObject)
			faultCodeMap[faultCode].RootCauseDetail = append(faultCodeMap[faultCode].RootCauseDetail, rootCauseDetail)
		}
	}
}

/* Ended by AICoder, pid:yae2d1aa1f59a81141d20b02f0bf252e5728bad8 */

/* Started by AICoder, pid:h40a0o80b1k643a1476a08cbe001b62336b87353 */
func (ft *FaultInfoHandler) parseRdmaWarningInfoNew(faultInfoWsm *FaultInfoWsm, vcjobCloudClusterInfo *VcjobCloudClusterInfo, faultCodeMap map[int64]*FaultInfo) {
	// Call the new function to process RDMA warnings
	rdmaObjectDetail := ProcessRdmaWarnings(faultInfoWsm)

	for faultReason, rdmaInfos := range rdmaObjectDetail {
		faultCodeInfo, ok := ReasonToFaultCode[faultReason]
		if !ok {
			logger.Info("reason %s does not map to a known fault code", faultReason)
			continue // Skip if reason does not map to a known fault code
		}
		faultCode := faultCodeInfo.FaultCode
		for _, rdmaInfo := range rdmaInfos {
			objectId := rdmaInfo.NodeId
			if objectId == "" {
				objectId = rdmaInfo.NodeName
			}
			rootCauseObject := NewRootCauseObject(vcjobCloudClusterInfo.CloudId, rdmaInfo.NodeId, rdmaInfo.NodeName, "director.containernode")
			rootCauseDetail := NewRootCauseDetail(objectId, "others", rdmaInfo.Detail)
			if _, exists := faultCodeMap[faultCode]; !exists {
				faultCodeMap[faultCode] = &FaultInfo{
					FaultCode:       faultCode,
					EnvId:           vcjobCloudClusterInfo.CloudId,
					EnvName:         vcjobCloudClusterInfo.CloudName,
					RaisedTime:      convertTimestampToUTC(rdmaInfo.RaisedTime),
					ReportedTime:    convertTimestampToUTC(faultInfoWsm.ReportTime),
					DescriptionZH:   fmt.Sprintf("任务%s发生%s", faultInfoWsm.Name, faultCodeInfo.DescriptionZH),
					DescriptionEN:   fmt.Sprintf("Vcjob %s encountered %s", faultInfoWsm.Name, faultCodeInfo.DescriptionEN),
					RootCauseObject: []*RootCauseObject{rootCauseObject},
					RootCauseDetail: []*RootCauseDetail{rootCauseDetail},
					AssociatedObjects: []*AssociatedObject{
						NewAssociatedObject(vcjobCloudClusterInfo.CloudId, faultInfoWsm.UID, faultInfoWsm.Name, "director.containervcjob")},
				}
			} else {
				faultCodeMap[faultCode].RootCauseObject = append(faultCodeMap[faultCode].RootCauseObject, rootCauseObject)
				faultCodeMap[faultCode].RootCauseDetail = append(faultCodeMap[faultCode].RootCauseDetail, rootCauseDetail)
			}
		}
	}
}

/* Ended by AICoder, pid:h40a0o80b1k643a1476a08cbe001b62336b87353 */

/* Started by AICoder, pid:24a88ae05462517142d80a38e04ef92423c405de */
func ProcessRdmaWarnings(faultInfoWsm *FaultInfoWsm) map[string]map[string]*RdmaDetailInfo {
	rdmaObjectDetail := make(map[string]map[string]*RdmaDetailInfo)

	for _, rdmaInfo := range faultInfoWsm.RdmaWarningInfo {
		faultReason := rdmaInfo.Reason
		if _, ok := rdmaObjectDetail[faultReason]; !ok {
			rdmaObjectDetail[faultReason] = make(map[string]*RdmaDetailInfo)
		}
		if detailInfo, ok := rdmaObjectDetail[faultReason][rdmaInfo.NodeName]; !ok || detailInfo == nil {
			rdmaDetailInfo := &RdmaDetailInfo{
				NodeId:     rdmaInfo.NodeId,
				NodeName:   rdmaInfo.NodeName,
				RaisedTime: rdmaInfo.TimeStamp,
				Detail:     fmt.Sprintf("timeStamp:%s,node:%s,message:%s \n", rdmaInfo.TimeStamp, rdmaInfo.NodeName, rdmaInfo.Message),
			}
			rdmaObjectDetail[faultReason][rdmaInfo.NodeName] = rdmaDetailInfo
		} else {
			detail := detailInfo.Detail
			detail += fmt.Sprintf("timeStamp:%s,node:%s,message:%s\n", rdmaInfo.TimeStamp, rdmaInfo.NodeName, rdmaInfo.Message)
			rdmaObjectDetail[faultReason][rdmaInfo.NodeName].Detail = detail
		}
	}

	return rdmaObjectDetail
}

/* Ended by AICoder, pid:24a88ae05462517142d80a38e04ef92423c405de */

func getUTCTime() string {
	localTime := time.Now()
	utcTIme := localTime.UTC()
	return utcTIme.Format(Layout)
}

/* Started by AICoder, pid:8e831m6441p600e148120a65008cf84a6af24b70 */
func (ft *FaultInfoHandler) GetChangedFaultInfo(faultInfoOld []*FaultInfo, faultInfoNew []*FaultInfo, reportTime string) []*FaultInfo {
	result := make([]*FaultInfo, 0, len(faultInfoOld)+len(faultInfoNew))

	// Map for quick lookup
	oldMap := make(map[int64]*FaultInfo, len(faultInfoOld))
	newMap := make(map[int64]*FaultInfo, len(faultInfoNew))

	for _, fi := range faultInfoOld {
		oldMap[fi.FaultCode] = fi
	}
	for _, fi := range faultInfoNew {
		newMap[fi.FaultCode] = fi
	}

	utcTime := getUTCTime()

	// Check for items in old
	for id, oldFi := range oldMap {
		if newFi, exists := newMap[id]; !exists {
			setFaultInfoClearTime(oldFi, reportTime, utcTime)
			result = append(result, oldFi)
		} else {
			if isFaultInfoChanged(oldFi, newFi) {
				// setFaultInfoRaisedTime(newFi, utcTime)
				// setFaultInfoReportTime(newFi, utcTime)
				newFi.RaisedTime = oldFi.RaisedTime
				newFi.ReportedTime = oldFi.ReportedTime
				newFi.FaultId = oldFi.FaultId
				result = append(result, newFi)
			}
		}
	}

	// Check for items in new but not in old
	for id, newFi := range newMap {
		if _, exists := oldMap[id]; !exists {
			setFaultInfoRaisedTime(newFi, utcTime)
			setFaultInfoReportTime(newFi, utcTime)
			result = append(result, newFi)
		}
	}

	return result
}

/* Ended by AICoder, pid:8e831m6441p600e148120a65008cf84a6af24b70 */

func isFaultInfoChanged(oldFi, newFi *FaultInfo) bool {
	if !compareRootCauseObjects(oldFi.RootCauseObject, newFi.RootCauseObject) {
		return true
	}
	if _, exist := ReasonFixed[oldFi.FaultCode]; exist {
		return false
	}
	return !compareRootCauseDetail(oldFi.RootCauseDetail, newFi.RootCauseDetail)
}

/* Started by AICoder, pid:t9b73bc4437191314d9a09eba0e5952da1543f3e */

func setFaultInfoRaisedTime(faultInfo *FaultInfo, raiseTime string) {
	if faultInfo.RaisedTime == "" {
		faultInfo.RaisedTime = raiseTime
	}
}

func setFaultInfoReportTime(faultInfo *FaultInfo, reportTime string) {
	if faultInfo.ReportedTime == "" {
		faultInfo.ReportedTime = reportTime
	}
}

func setFaultInfoClearTime(faultInfo *FaultInfo, reportTime string, clearTime string) {
	if reportTime == "" {
		faultInfo.ClearedTime = clearTime
	} else {
		faultInfo.ClearedTime = reportTime
	}
}

// compareRootCauseObjects compares two slices of RootCauseObject based on their Ids.
func compareRootCauseObjects(oldObj, newObj []*RootCauseObject) bool {
	oldMap := make(map[string]struct{}, len(oldObj))
	newMap := make(map[string]struct{}, len(newObj))

	for _, obj := range oldObj {
		oldMap[obj.Id] = struct{}{}
	}
	for _, obj := range newObj {
		newMap[obj.Id] = struct{}{}
	}

	if len(oldMap) != len(newMap) {
		return false
	}

	for id := range oldMap {
		if _, exists := newMap[id]; !exists {
			return false
		}
	}

	return true
}

/* Ended by AICoder, pid:t9b73bc4437191314d9a09eba0e5952da1543f3e */

func compareRootCauseDetail(oldObj, newObj []*RootCauseDetail) bool {
	oldMap := make(map[string][]string)
	for _, old := range oldObj {
		oldMap[old.RcObjectId] = old.Data
	}

	for _, new := range newObj {
		if oldData, exists := oldMap[new.RcObjectId]; !exists {
			return false
		} else {
			if !equalSlices(oldData, new.Data) {
				return false
			}

		}
	}
	return true
}

func equalSlices(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}

func (ft *FaultInfoHandler) GetVcjobFaultInfoFromDB(vcjobId string) ([]*FaultInfo, error) {
	queryCondition := map[string]interface{}{"vcjobId": vcjobId}
	faultInfos, err := models.QueryResourceByCondition(constant.TABLE_NAME_FAULTINFO, queryCondition)
	if err != nil {
		return nil, err
	}
	result := make([]*FaultInfo, 0, len(faultInfos))
	for _, faultItem := range faultInfos {
		if faultInfoValue, ok := faultItem["faultInfo"]; ok {
			var faultInfo *FaultInfo = &FaultInfo{}
			value, ok := faultInfoValue.(string)
			if !ok {
				continue
			}
			err := json.Unmarshal([]byte(value), faultInfo)
			if err != nil {
				continue
			}
			result = append(result, faultInfo)
		}
	}
	return result, nil
}

func (ft *FaultInfoHandler) InsertFaultInfoToDB(faultItem *FaultItem) error {
	faultItemValue, err := ft.translateStructToMap(faultItem)
	if err != nil {
		return err
	}
	logger.Info("insert faultItemValue %v", faultItemValue)
	if !models.InsertNewResource(constant.TABLE_NAME_FAULTINFO, faultItemValue) {
		return fmt.Errorf("insert faultinfo to db failed id %s", faultItem.Id)
	}
	return nil
}

func (ft *FaultInfoHandler) UpdateFaultInfoToDB(faultItem *FaultItem) error {
	faultItemValue, err := ft.translateStructToMap(faultItem)
	if err != nil {
		return err
	}
	logger.Info("update faultItemValue %v", faultItemValue)
	if !models.UpdateResourceById(constant.TABLE_NAME_FAULTINFO, faultItem.Id, faultItemValue) {
		return fmt.Errorf("insert faultinfo to db failed id %s", faultItem.Id)
	}

	return nil
}

func (ft *FaultInfoHandler) translateStructToMap(faultItem *FaultItem) (map[string]interface{}, error) {
	item, err := json.Marshal(faultItem)
	if err != nil {
		return nil, err
	}
	var faultItemValue map[string]interface{}
	err = json.Unmarshal(item, &faultItemValue)
	if err != nil {
		return nil, err
	}
	return faultItemValue, nil
}

func (ft *FaultInfoHandler) DeleteFaultInfoFromDB(faultId string) error {
	logger.Info("delete faultItemValue %v", faultId)
	if !models.DeleteResourceById(constant.TABLE_NAME_FAULTINFO, faultId) {
		return fmt.Errorf("delete faultinfo from db failed id %s", faultId)
	}
	return nil
}

func (ft *FaultInfoHandler) FaultInfoHandle(faultInfo string, vcjobCloudClusterInfo *VcjobCloudClusterInfo) {
	faultInfoOld, err := ft.GetVcjobFaultInfoFromDB(vcjobCloudClusterInfo.UUID)
	if err != nil {
		logger.Error("get faultinfo from db failed id %s", vcjobCloudClusterInfo.UUID)
		return
	}
	faultInfoNew, reportTime := ft.ParseNewFaultInfo(faultInfo, vcjobCloudClusterInfo)
	changedFaultInfo := ft.GetChangedFaultInfo(faultInfoOld, faultInfoNew, reportTime)

	for _, faultInfo := range changedFaultInfo {
		go ft.FaultInfoChangelHandle(vcjobCloudClusterInfo.UUID, faultInfo)
	}
}

func (ft *FaultInfoHandler) FaultInfoChangelHandle(vcjobId string, faultInfo *FaultInfo) {
	if faultInfo.FaultId == "" {
		err := ft.FaultInfoCreateHandle(vcjobId, faultInfo)
		if err != nil {
			logger.Info("FaultInfoCreateHandle failed err: %v", err)
		}
	} else {
		err := ft.FaultInfoUpdateHandle(vcjobId, faultInfo)
		if err != nil {
			logger.Info("FaultInfoUpdateHandle failed err: %v", err)
		}
	}
}

func (ft *FaultInfoHandler) FaultInfoCreateHandle(vcjobId string, faultInfo *FaultInfo) error {
	id, err := uuid.NewUUID()
	if err != nil {
		return err
	}
	faultInfo.FaultId = id.String()
	err = ft.PostFaultInfoToFaultMgt(faultInfo, vcjobId)
	if err != nil {
		return err
	}
	faultInfoValue, err := json.Marshal(faultInfo)
	if err != nil {
		return err
	}
	faultItem := &FaultItem{
		Id:        faultInfo.FaultId,
		FaultInfo: string(faultInfoValue),
		FaultCode: faultInfo.FaultCode,
		VcjobId:   vcjobId,
	}
	return ft.InsertFaultInfoToDB(faultItem)
}

func (ft *FaultInfoHandler) FaultInfoUpdateHandle(vcjobId string, faultInfo *FaultInfo) error {
	err := ft.PutFaultInfoToFaultMgt(faultInfo, vcjobId)
	if err != nil {
		return err
	}
	faultInfoValue, err := json.Marshal(faultInfo)
	if err != nil {
		return err
	}
	faultItem := &FaultItem{
		Id:        faultInfo.FaultId,
		FaultInfo: string(faultInfoValue),
		FaultCode: faultInfo.FaultCode,
		VcjobId:   vcjobId,
	}
	if faultInfo.ClearedTime != "" {
		err = ft.DeleteFaultInfoFromDB(faultInfo.FaultId)
	} else {
		err = ft.UpdateFaultInfoToDB(faultItem)
	}
	return err
}

func (ft *FaultInfoHandler) PostFaultInfoToFaultMgt(faultInfo *FaultInfo, vcjobId string) error {
	request, err := json.Marshal(faultInfo)
	if err != nil {
		return err
	}
	logger.Info("PostFaultInfoToFaultMgt %v", string(request))
	return authorization.PostVcjobFaultInfo(request, vcjobId)
	// return nil
}

func (ft *FaultInfoHandler) PutFaultInfoToFaultMgt(faultInfo *FaultInfo, vcjobId string) error {
	request, err := json.Marshal(faultInfo)
	if err != nil {
		return err
	}
	logger.Info("PutFaultInfoToFaultMgt %v", string(request))
	return authorization.PutVcjobFaultInfo(request, vcjobId)
	// return nil
}
