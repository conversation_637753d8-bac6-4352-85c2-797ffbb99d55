package vcjob_handler

import "sync"

/* Started by AICoder, pid:80f22q5569ka1d1143110b5b80a7ec550fa2bab2 */
// 使用切片实现的简单队列
type Queue struct {
	items []*vcjobItem
	lock  sync.Mutex
}

// 入队
func (q *Queue) Enqueue(value *vcjobItem) {
	q.lock.Lock()
	defer q.lock.Unlock()
	q.items = append(q.items, value)
}

// 出队
func (q *Queue) Dequeue() (*vcjobItem, bool) {
	q.lock.Lock()
	defer q.lock.Unlock()
	if len(q.items) == 0 {
		return nil, false
	}
	value := (q.items)[0]
	q.items = (q.items)[1:]
	return value, true
}

// 检查队列是否为空
func (q *Queue) Length() int {
	q.lock.Lock()
	defer q.lock.Unlock()
	return len(q.items)
}

func (q *Queue) GetFirstValue() (value *vcjobItem) {
	q.lock.Lock()
	defer q.lock.Unlock()
	if len(q.items) == 0 {
		return nil
	}
	return (q.items)[0]
}

/* Ended by AICoder, pid:80f22q5569ka1d1143110b5b80a7ec550fa2bab2 */
