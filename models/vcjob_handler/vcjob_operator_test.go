package vcjob_handler

import (
	"context"
	"reflect"
	"testing"
	"time"

	"cwsm/infra/authorization"
	"cwsm/infra/configcenter"
	"cwsm/models"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
)

func TestInitHistoryVcjobHandler(t *testing.T) {
	<PERSON>vey("TestInitHistoryVcjobHandler", t, func() {
		Convey(" when get vcjob from pvrm failed", func() {
			VcjobHandler = &vcjobHandler{}
			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()
			go InitHistoryVcjobHandler(ctx)
			time.Sleep(1 * time.Second)
		})

		<PERSON>vey("Init vcjobHandler success", func() {
			vcjobInfo := []byte(`{
				"vcjobs": [{
				"uuid": "e446ba28-152b-4abd-bda2-56ffebcb242a",
				"name": "job-dh-0",
				"status": "running",
				"label": null,
				"created_at": "2024-11-01T06:05:27Z",
				"namespace_id": "",
				"dc_id": "8b3b363b-6d9b-42cd-a021-8848c9b866f8",
				"dc_name": "DC1",
				"cloud_uuid": "a2ba43f9-aafb-44c0-9419-e15dd1812901",
				"cloud_name": "GPU-no-operating",
				"tenant_id": "237c1579-5e53-5624-a32d-23f6e5d5630b",
				"tenant_name": "default",
				"queue": "default",
				"priority_class_name": "high-priority",
				"min_available": 1,
				"min_success": -1,
				"pm_info": null
				},
				{
					"uuid": "a446ba28-152b-4abd-bda2-56ffebcb242a",
					"name": "job-dh-0",
					"status": "running",
					"label": null,
					"created_at": "2024-11-01T06:05:27Z",
					"namespace_id": "",
					"dc_id": "8b3b363b-6d9b-42cd-a021-8848c9b866f8",
					"dc_name": "DC1",
					"cloud_uuid": "a2ba43f9-aafb-44c0-9419-e15dd1812901",
					"cloud_name": "GPU-no-operating",
					"tenant_id": "237c1579-5e53-5624-a32d-23f6e5d5630b",
					"tenant_name": "default",
					"queue": "default",
					"priority_class_name": "high-priority",
					"min_available": 1,
					"min_success": -1,
					"pm_info": null
					}]}`)

			patch := gomonkey.ApplyFunc(authorization.GetVcjobInfoFromPvrm, func() ([]byte, error) {
				return vcjobInfo, nil
			})
			defer patch.Reset()
			patch1 := gomonkey.ApplyMethodSeq(reflect.TypeOf(VcjobHandler), "HandlerVcJobWithDB", []gomonkey.OutputCell{
				{Values: gomonkey.Params{nil}},
				{Values: gomonkey.Params{nil}},
				{Values: gomonkey.Params{nil}},
				{Values: gomonkey.Params{nil}},
			})
			defer patch1.Reset()
			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()
			go InitHistoryVcjobHandler(ctx)
			time.Sleep(5 * time.Second)

			item1 := &vcjobItem{
				Uuid: "e446ba28-152b-4abd-bda2-56ffebcb242a",
				Name: "vcjob1",
			}
			item2 := &vcjobItem{
				Uuid: "a446ba28-152b-4abd-bda2-56ffebcb242a",
				Name: "vcjob1",
			}
			VcjobHandler.AddMessageToQueueWithId("e446ba28-152b-4abd-bda2-56ffebcb242a", item1)
			VcjobHandler.AddMessageToQueueWithId("a446ba28-152b-4abd-bda2-56ffebcb242a", item2)
			time.Sleep(2 * time.Second)
		})
	})
}

/* Started by AICoder, pid:w882327e15fe3371401d0867209b755e315843bb */
func TestAgingVcJobTableWithConfig(t *testing.T) {
    Convey("Test AgingVcJobTableWithNumber", t, func() {
        patch := gomonkey.ApplyFunc(models.QueryResourceAll, func(string) ([]map[string]interface{}, error) {
            return []map[string]interface{}{
                {
                    "uuid":               "e446ba28-152b-4abd-bda2-56ffebcb242a",
                    "name":               "vcjob1",
                    "status":             "deleted",
                    "lastTransitionTime": "1234",
                },
                {
                    "uuid":               "e446ba28-152b-4abd-bda2-56ffebcb242b",
                    "name":               "vcjob2",
                    "status":             "deleted",
                    "faultInfo":          "",
                    "lastTransitionTime": "2024-11-01T06:05:27Z",
                },
                {
                    "uuid":               "e446ba28-152b-4abd-bda2-56ffebcb242c",
                    "name":               "vcjob3",
                    "status":             "deleted",
                    "faultInfo":          "",
                    "lastTransitionTime": "2024-11-02T06:05:27Z",
                },
            }, nil
        })
        defer patch.Reset()

        patchDay := gomonkey.ApplyFunc(configcenter.GetRetentionDayValue, func() int {
            return 1
        })

        patchNumber := gomonkey.ApplyFunc(configcenter.GetRetentionNumberValue, func() int {
            return 1
        })

        defer patchDay.Reset()
        defer patchNumber.Reset()

        // Mock DeleteResourceByUuid to return true for successful deletion
        patch1 := gomonkey.ApplyFuncSeq(models.DeleteResourceByUuid, []gomonkey.OutputCell{
            {Values: gomonkey.Params{true}},
            {Values: gomonkey.Params{true}},
            {Values: gomonkey.Params{true}},
        })
        defer patch1.Reset()

        // Mock DeleteResourceByCol with mixed results (true, true, false)
        patch2 := gomonkey.ApplyFuncSeq(models.DeleteResourceByCol, []gomonkey.OutputCell{
            {Values: gomonkey.Params{true}},
            {Values: gomonkey.Params{true}},
            {Values: gomonkey.Params{false}},
        })
        defer patch2.Reset()

        AgingVcJobTableWithConfig()
    })
}
/* Ended by AICoder, pid:w882327e15fe3371401d0867209b755e315843bb */