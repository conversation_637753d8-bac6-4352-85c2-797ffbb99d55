package vcjob_handler

import (
	"context"
	"cwsm/infra/configcenter"
	"cwsm/infra/constant"
	"cwsm/models"
	"cwsm/tools/commontools/logger"
	"sort"
	"time"
)

/* Started by AICoder, pid:21329y0dedkab361487c08ebf12f9f016994960f */
func InitHistoryVcjobHandler(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			if Vc<PERSON>b<PERSON>and<PERSON> != nil {
				VcjobHandler.stopChan <- struct{}{}
			}
			return

		default:
			logger.Info("Init vcjob from pvrm and create vcjob_handler")
			err := InitVcjobFromPvrm()
			if err != nil {
				logger.Errorf("init vcjob from pvrm failed, retry")
				time.Sleep(1 * time.Second)
				continue
			}
			//go VcjobHandler.ListenVcJobMap(ctx)
			goto outerLoop
		}
	}
outerLoop:
	if Vc<PERSON>bHandler != nil {
		logger.Info("start vcjob periodic fault demacration and db vcjob retention gorouting...")
		go VcjobHandler.GetPeriodicFaultDemacration(ctx)
		go StartVcjobTimeRetention(ctx)
	}
}

func StartVcjobTimeRetention(ctx context.Context) {
	logger.Info("start vcjob time retention gorouting...")
	timer := time.NewTicker(300 * time.Second)
	defer timer.Stop()

	for {
		select {
		case <-ctx.Done():
			logger.Infof("receive ctx to stop vcjob time retention")
			return
		case <-timer.C:
			AgingVcJobTableWithConfig()
		}
	}
}

func AgingVcJobTableWithConfig() {
	ageVcJobList := make([]map[string]interface{}, 0)
	res, err := models.QueryResourceAll(constant.TABLE_NAME_VCJOBFAULTHISTORY)
	if err != nil {
		logger.Error("query all vcjobs from database failed")
		return
	}
	for _, vcjobMap := range res {
		state, ok := vcjobMap["status"].(string)
		if !ok {
			continue
		}
		if state == "deleted" {
			ageVcJobList = append(ageVcJobList, vcjobMap)
		}
	}

	sort.Slice(ageVcJobList, func(i, j int) bool {
		ti, err := time.Parse(time.RFC3339, ageVcJobList[i]["lastTransitionTime"].(string))
		if err != nil {
			return false
		}
		tj, err := time.Parse(time.RFC3339, ageVcJobList[j]["lastTransitionTime"].(string))
		if err != nil {
			return false
		}
		return ti.Before(tj)
	})

	ageNum := len(ageVcJobList) - configcenter.GetRetentionNumberValue()
	deleteVcJobList := make([]map[string]interface{}, 0, len(ageVcJobList))
	for index, vcjob := range ageVcJobList {
		if index < ageNum {
			deleteVcJobList = append(deleteVcJobList, vcjob)
		} else {
			dt, err := time.Parse(time.RFC3339, vcjob["lastTransitionTime"].(string))
			if err != nil {
				logger.Errorf("err: %v", err)
				continue
			}
			if dt.Add(time.Duration(configcenter.GetRetentionDayValue()) * 24 * time.Hour).Before(time.Now().UTC()) {
				deleteVcJobList = append(deleteVcJobList, vcjob)
				continue
			}
			break
		}

	}
	for _, deleteVcjob := range deleteVcJobList {
		uuid, ok := deleteVcjob["uuid"].(string)
		if !ok {
			continue
		}
		if !models.DeleteResourceByCol(constant.TABLE_NAME_FAULTINFO, "vcjobId", uuid) {
			logger.Info("delete vcjob %s faultinfo from db failed", uuid)
		}
		if !models.DeleteResourceByUuid(constant.TABLE_NAME_VCJOBFAULTHISTORY, uuid) {
			logger.Errorf("Aging Vcjob DataBase with num failed, delete vcjob %s failed", uuid)
		}
	}
}

/* Ended by AICoder, pid:21329y0dedkab361487c08ebf12f9f016994960f */
