package vcjob_handler

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestResourceChangeStruct(t *testing.T) {
	Convey("Test ResourceChangeStruct", t, func() {
		rsc := &ResourceChange{
			Action:  "delete",
			BaseMoc: "director.containervcjob",
			Id:      "vcjob123",
			NewModel: &ResourceModel{
				LastTransitionTime: "1",
				CloudName:          "1",
				CloudUuid:          "1",
				Name:               "vcjob",
				Status:             "pending",
				NamespaceName:      "tester",
			},
			OldModel: &ResourceModel{
				LastTransitionTime: "1",
				CloudName:          "1",
				CloudUuid:          "1",
				Name:               "vcjob",
				Status:             "pending",
				NamespaceName:      "tester",
			},
		}
		<PERSON>vey("Test resouce change struct is valid when action is delete", func() {
			res := rsc.IsResourceMessageValid()
			So(res, ShouldBeTrue)
			vc := rsc.TranslateToVcJobItem()
			So(vc.CloudName, ShouldEqual, "1")
			So(vc.Status, ShouldEqual, "deleted")
		})
		<PERSON>vey("Test resouce change struct is invalid when action is update", func() {
			rsc.Action = "update"
			res := rsc.IsResourceMessageValid()
			So(res, ShouldBeTrue)
			vc := rsc.TranslateToVcJobItem()
			So(vc.CloudName, ShouldEqual, "1")
		})
	})
}
