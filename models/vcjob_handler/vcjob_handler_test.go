package vcjob_handler

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"cwsm/infra/authorization"
	"cwsm/infra/configcenter"
	"cwsm/infra/wsm"
	"cwsm/models"

	"zte.com.cn/cms/crmX/commontools-base/restful"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
)

// func TestVcjobHandler(t *testing.T) {
// 	Convey("Test vcjob Handler", t, func() {
// 		vcjobInfo := []byte(`{
// 			"vcjobs": [{
// 			"uuid": "e446ba28-152b-4abd-bda2-56ffebcb242a",
// 			"name": "job-dh-0",
// 			"status": "running",
// 			"label": null,
// 			"created_at": "2024-11-01T06:05:27Z",
// 			"namespace_id": "",
// 			"dc_id": "8b3b363b-6d9b-42cd-a021-8848c9b866f8",
// 			"dc_name": "DC1",
// 			"cloud_uuid": "a2ba43f9-aafb-44c0-9419-e15dd1812901",
// 			"cloud_name": "GPU-no-operating",
// 			"tenant_id": "237c1579-5e53-5624-a32d-23f6e5d5630b",
// 			"tenant_name": "default",
// 			"queue": "default",
// 			"priority_class_name": "high-priority",
// 			"min_available": 1,
// 			"min_success": -1,
// 			"pm_info": null
// 			},{
// 				"uuid": "a446ba28-152b-4abd-bda2-56ffebcb242a",
// 				"name": "job-dh-0",
// 				"status": "running",
// 				"label": null,
// 				"created_at": "2024-11-01T06:05:27Z",
// 				"namespace_id": "",
// 				"dc_id": "8b3b363b-6d9b-42cd-a021-8848c9b866f8",
// 				"dc_name": "DC1",
// 				"cloud_uuid": "a2ba43f9-aafb-44c0-9419-e15dd1812901",
// 				"cloud_name": "GPU-no-operating",
// 				"tenant_id": "237c1579-5e53-5624-a32d-23f6e5d5630b",
// 				"tenant_name": "default",
// 				"queue": "default",
// 				"priority_class_name": "high-priority",
// 				"min_available": 1,
// 				"min_success": -1,
// 				"pm_info": null
// 				}]}`)
// 		err := InitVcjobFromPvrm()
// 		So(err, ShouldBeError)

// 		patch := gomonkey.ApplyFunc(authorization.GetVcjobInfoFromPvrm, func() ([]byte, error) {
// 			return vcjobInfo, nil
// 		})
// 		defer patch.Reset()
// 		err = InitVcjobFromPvrm()
// 		So(err, ShouldBeNil)
// 		So(len(VcjobHandler.VcJobMessageQueue), ShouldEqual, 2)
// 	})
// }

func TestNewVcjobHandler(t *testing.T) {
	Convey("Test NewVcjobHandler", t, func() {
		Convey("item is nil", func() {
			vc := NewVcjobHandler(nil)
			So(len(vc.VcJobMessageQueue), ShouldEqual, 0)
		})
	})
}

func TestUpdateVcjobToDB(t *testing.T) {
	Convey("Test UpdateVcjobToDB", t, func() {
		Convey("when item is nil, it should return err", func() {
			VcjobHandler1 := &vcjobHandler{}
			err := VcjobHandler1.UpdateVcjobToDB("123", nil)
			So(err, ShouldBeError)
		})

		Convey("when item is non-nil, but query resource from db failed, it should return err", func() {
			patch1 := gomonkey.ApplyFunc(models.QueryResourceByUuid, func(string, string) (map[string]interface{}, error) {
				return map[string]interface{}{}, fmt.Errorf("err")
			})
			defer patch1.Reset()
			VcjobHandler1 := &vcjobHandler{}
			item := &vcjobItem{
				Uuid: "12345",
				Name: "vcjob1",
			}
			err := VcjobHandler1.UpdateVcjobToDB(item.Uuid, item)
			So(err, ShouldBeError)
		})

		Convey("when item is non-nil, but the faultdemacration vcjob is not in db", func() {
			patch1 := gomonkey.ApplyFunc(models.QueryResourceByUuid, func(string, string) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			defer patch1.Reset()

			patch2 := gomonkey.ApplyFunc(models.InsertNewResource, func(string, map[string]interface{}) bool {
				return false
			})
			defer patch2.Reset()
			VcjobHandler1 := &vcjobHandler{}
			item := &vcjobItem{
				Uuid:      "12345",
				Name:      "vcjob1",
				FaultInfo: "1234",
			}
			err := VcjobHandler1.UpdateVcjobToDB(item.Uuid, item)
			So(err, ShouldBeNil)
		})

		Convey("when item is non-nil, create vcjob in db", func() {
			patch1 := gomonkey.ApplyFunc(models.QueryResourceByUuid, func(string, string) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			defer patch1.Reset()

			patch2 := gomonkey.ApplyFunc(models.InsertNewResource, func(string, map[string]interface{}) bool {
				return true
			})
			defer patch2.Reset()
			VcjobHandler1 := &vcjobHandler{}
			item := &vcjobItem{
				Uuid:      "12345",
				Name:      "vcjob1",
				FaultInfo: "1234",
				Action:    "create",
			}
			err := VcjobHandler1.UpdateVcjobToDB(item.Uuid, item)
			So(err, ShouldBeNil)
		})

		Convey("when item is non-nil, but insert to db successfully", func() {
			patch1 := gomonkey.ApplyFunc(models.QueryResourceByUuid, func(string, string) (map[string]interface{}, error) {
				return map[string]interface{}{}, nil
			})
			defer patch1.Reset()

			patch2 := gomonkey.ApplyFunc(models.InsertNewResource, func(string, map[string]interface{}) bool {
				return true
			})
			defer patch2.Reset()
			VcjobHandler1 := &vcjobHandler{}
			item := &vcjobItem{
				Uuid: "12345",
				Name: "vcjob1",
			}
			err := VcjobHandler1.UpdateVcjobToDB(item.Uuid, item)
			So(err, ShouldBeNil)
		})

		Convey("when item is non-nil, but update resource from db failed, it should return err", func() {
			patch1 := gomonkey.ApplyFunc(models.QueryResourceByUuid, func(string, string) (map[string]interface{}, error) {
				return map[string]interface{}{
					"uuid": "123455",
				}, nil
			})
			defer patch1.Reset()

			patch2 := gomonkey.ApplyFunc(models.UpdateResourceByUuid, func(string, string, map[string]interface{}) bool {
				return false
			})
			defer patch2.Reset()
			VcjobHandler1 := &vcjobHandler{}
			item := &vcjobItem{
				Uuid: "12345",
				Name: "vcjob1",
			}
			err := VcjobHandler1.UpdateVcjobToDB(item.Uuid, item)
			So(err, ShouldBeError)
		})

		Convey("when item is non-nil, but update resource successfully", func() {
			patch1 := gomonkey.ApplyFunc(models.QueryResourceByUuid, func(string, string) (map[string]interface{}, error) {
				return map[string]interface{}{
					"uuid": "123455",
				}, nil
			})
			defer patch1.Reset()

			patch2 := gomonkey.ApplyFunc(models.UpdateResourceByUuid, func(string, string, map[string]interface{}) bool {
				return true
			})
			defer patch2.Reset()
			VcjobHandler1 := &vcjobHandler{}
			item := &vcjobItem{
				Uuid: "12345",
				Name: "vcjob1",
			}
			err := VcjobHandler1.UpdateVcjobToDB(item.Uuid, item)
			So(err, ShouldBeNil)
		})
	})
}

func TestGetPeriodicFaultDemacration(t *testing.T) {
	Convey("Test GetPeriodicFaultDemacration", t, func() {
		Convey("when item is nil, it should return err", func() {
			VcjobHandler1 := &vcjobHandler{
				VcJobMessageQueue: make(map[string]*Queue, 0),
			}
			patch := gomonkey.ApplyFunc(models.QueryResourceAll, func(string) ([]map[string]interface{}, error) {
				return []map[string]interface{}{
					{"uuid": "123", "status": "pending"}, {"uuid": "1234", "status": "aborted", "faultInfo": ""},
				}, nil
			})
			defer patch.Reset()
			patch1 := gomonkey.ApplyFuncSeq(wsm.GetOpenpaletteByCloudUuid, []gomonkey.OutputCell{
				{Values: gomonkey.Params{authorization.OpenpaletteInfo{Token: "1"}, nil}},
				{Values: gomonkey.Params{authorization.OpenpaletteInfo{Token: "2"}, nil}},
			})
			defer patch1.Reset()

			patch2 := gomonkey.ApplyFuncSeq(restful.GetMethod, []gomonkey.OutputCell{
				{Values: gomonkey.Params{[]byte("123"), nil, 200, nil}},
				{Values: gomonkey.Params{[]byte("123"), nil, 200, nil}},
			})
			defer patch2.Reset()
			patch3 := gomonkey.ApplyFunc(configcenter.GetFaultAnalaSwitchValue, func() bool {
				return true
			})
			defer patch3.Reset()
			patch4 := gomonkey.ApplyPrivateMethod(VcjobHandler1, "translateMapToVcjobStruct", func(*vcjobHandler, map[string]interface{}) (*vcjobItem, error) {
				return nil, fmt.Errorf("error")
			})
			defer patch4.Reset()
			patch5 := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "GetVcjobFaultInfoFromDB", func(_ *FaultInfoHandler, vcjobId string) ([]*FaultInfo, error) {
				return nil, fmt.Errorf("error")
			})
			defer patch5.Reset()
			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()
			go VcjobHandler1.GetPeriodicFaultDemacration(ctx)
			time.Sleep(15 * time.Second)
		})
	})
}

/* Started by AICoder, pid:1d7e8ye281zd7a01442f091c70999d7a3e019d1b */
func TestGetPeriodicFaultDemacrationSuccess(t *testing.T) {
	Convey("Test GetPeriodicFaultDemacration", t, func() {
		Convey("when item is nil, it should return err", func() {
			VcjobHandler1 := &vcjobHandler{
				VcJobMessageQueue: make(map[string]*Queue, 0),
			}
			patch := gomonkey.ApplyFuncSeq(models.QueryResourceAll, []gomonkey.OutputCell{
				{Values: gomonkey.Params{[]map[string]interface{}{{"uuid": "123", "status": "pending"}, {"uuid": "1234", "status": "aborted", "faultInfo": ""}}, nil}},
				{Values: gomonkey.Params{[]map[string]interface{}{{"uuid": "123", "status": "pending"}, {"uuid": "1234", "status": "aborted", "faultInfo": ""}}, nil}},
			})
			defer patch.Reset()

			patches := gomonkey.ApplyFuncSeq(models.QueryResourceByUuid, []gomonkey.OutputCell{
				{Values: gomonkey.Params{map[string]interface{}{"uuid": "123", "status": "pending"}, nil}},
				{Values: gomonkey.Params{map[string]interface{}{"uuid": "123", "status": "pending"}, nil}},
				{Values: gomonkey.Params{map[string]interface{}{"uuid": "123", "status": "pending"}, nil}},
				{Values: gomonkey.Params{map[string]interface{}{"uuid": "123", "status": "pending"}, nil}},
			})
			defer patches.Reset()

			patchese := gomonkey.ApplyFuncSeq(models.UpdateResourceByUuid, []gomonkey.OutputCell{
				{Values: gomonkey.Params{true}},
				{Values: gomonkey.Params{true}},
				{Values: gomonkey.Params{true}},
				{Values: gomonkey.Params{true}},
			})
			defer patchese.Reset()
			patch1 := gomonkey.ApplyFuncSeq(wsm.GetOpenpalette, []gomonkey.OutputCell{
				{Values: gomonkey.Params{authorization.OpenpaletteInfo{Token: "1"}, nil}},
				{Values: gomonkey.Params{authorization.OpenpaletteInfo{Token: "2"}, nil}},
				{Values: gomonkey.Params{authorization.OpenpaletteInfo{Token: "3"}, nil}},
				{Values: gomonkey.Params{authorization.OpenpaletteInfo{Token: "4"}, nil}},
			})
			defer patch1.Reset()

			patch2 := gomonkey.ApplyFuncSeq(restful.GetMethod, []gomonkey.OutputCell{
				{Values: gomonkey.Params{[]byte("123"), nil, 200, nil}},
				{Values: gomonkey.Params{[]byte("123"), nil, 200, nil}},
				{Values: gomonkey.Params{[]byte("123"), nil, 200, nil}},
				{Values: gomonkey.Params{[]byte("123"), nil, 200, nil}},
			})
			defer patch2.Reset()
			patch3 := gomonkey.ApplyFunc(configcenter.GetFaultAnalaSwitchValue, func() bool {
				return true
			})
			defer patch3.Reset()
			patch5 := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "GetVcjobFaultInfoFromDB", func(_ *FaultInfoHandler, vcjobId string) ([]*FaultInfo, error) {
				return nil, fmt.Errorf("error")
			})
			defer patch5.Reset()
			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()
			go VcjobHandler1.GetPeriodicFaultDemacration(ctx)
			time.Sleep(15 * time.Second)
		})
	})
}

/* Ended by AICoder, pid:1d7e8ye281zd7a01442f091c70999d7a3e019d1b */

func TestGetFaultDemacrationResultFail(t *testing.T) {
	Convey("Test GetPeriodicFaultDemacration Result", t, func() {
		Convey("when return not found err, it should return err", func() {
			VcjobHandler1 := &vcjobHandler{
				VcJobMessageQueue: make(map[string]*Queue, 0),
				PeriodicFaultDemacrationList: []map[string]interface{}{
					{"uuid": "123"}},
			}
			patch1 := gomonkey.ApplyFunc(wsm.GetOpenpalette, func(string) (authorization.OpenpaletteInfo, error) {
				return authorization.OpenpaletteInfo{Token: "1"}, nil
			})
			defer patch1.Reset()
			patch2 := gomonkey.ApplyFunc(restful.GetMethod, func(string, map[string]string, *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 400, fmt.Errorf("vcjob not found")
			})
			defer patch2.Reset()

			patch3 := gomonkey.ApplyMethod(reflect.TypeOf(VcjobHandler1), "AddMessageToQueueWithId", func(*vcjobHandler, string, *vcjobItem) {
			})
			defer patch3.Reset()
			patch5 := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "GetVcjobFaultInfoFromDB", func(_ *FaultInfoHandler, vcjobId string) ([]*FaultInfo, error) {
				return nil, fmt.Errorf("error")
			})
			defer patch5.Reset()
			go VcjobHandler1.GetFaultDemacrationResult()
			time.Sleep(5 * time.Second)
		})

		Convey("when return other err", func() {
			VcjobHandler1 := &vcjobHandler{
				VcJobMessageQueue: make(map[string]*Queue, 0),
				PeriodicFaultDemacrationList: []map[string]interface{}{
					{"uuid": "123"}},
			}
			patch1 := gomonkey.ApplyFunc(wsm.GetOpenpalette, func(string) (authorization.OpenpaletteInfo, error) {
				return authorization.OpenpaletteInfo{Token: "1"}, nil
			})
			defer patch1.Reset()
			patch2 := gomonkey.ApplyFunc(restful.GetMethod, func(string, map[string]string, *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 400, fmt.Errorf("vcjob not fou")
			})
			defer patch2.Reset()
			patch5 := gomonkey.ApplyMethod(reflect.TypeOf(&FaultInfoHandler{}), "GetVcjobFaultInfoFromDB", func(_ *FaultInfoHandler, vcjobId string) ([]*FaultInfo, error) {
				return nil, fmt.Errorf("error")
			})
			defer patch5.Reset()
			go VcjobHandler1.GetFaultDemacrationResult()
			time.Sleep(5 * time.Second)
		})
	})
}

func TestHandlerVcJobWithDB(t *testing.T) {
	Convey("Test HandlerVcJobWithDB", t, func() {
		Convey("when item action is delete", func() {
			VcjobHandler1 := &vcjobHandler{
				VcJobMessageQueue: make(map[string]*Queue, 0),
			}
			patch := gomonkey.ApplyFunc(models.DeleteResourceByUuid, func(string, string) bool {
				return true
			})
			defer patch.Reset()
			patch1 := gomonkey.ApplyFunc(models.DeleteResourceByCol, func(tableName string, ColName string, colValue string) bool {
				return true
			})
			defer patch1.Reset()

			patch3 := gomonkey.ApplyFunc(configcenter.GetRetentionSwitchValue, func() bool {
				return false
			})
			defer patch3.Reset()
			vcjobItem := &vcjobItem{Action: "delete"}
			VcjobHandler1.HandlerVcJobWithDB("123", vcjobItem)
		})

		Convey("when item action is delete, but switch is false", func() {
			VcjobHandler1 := &vcjobHandler{
				VcJobMessageQueue: make(map[string]*Queue, 0),
			}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(VcjobHandler1), "UpdateVcjobToDB", func(*vcjobHandler, string, *vcjobItem) error {
				return nil
			})
			defer patch.Reset()
			patch1 := gomonkey.ApplyFunc(models.DeleteResourceByCol, func(tableName string, ColName string, colValue string) bool {
				return true
			})
			defer patch1.Reset()
			patch3 := gomonkey.ApplyFunc(configcenter.GetRetentionSwitchValue, func() bool {
				return true
			})
			defer patch3.Reset()
			vcjobItem := &vcjobItem{Action: "delete"}
			VcjobHandler1.HandlerVcJobWithDB("123", vcjobItem)
		})
	})
}

func TestConsumeVcJobChangeMessage(t *testing.T) {
	Convey("Test ConsumeVcJobChangeMessage", t, func() {
		Convey("when item action is delete", func() {
			VcjobHandler1 := &vcjobHandler{
				VcJobMessageQueue: make(map[string]*Queue, 0),
			}
			vcjobItem1 := &vcjobItem{Action: "delete"}
			VcjobHandler1.AddMessageToQueueWithId("123", vcjobItem1)
			patch1 := gomonkey.ApplyMethod(reflect.TypeOf(VcjobHandler1), "HandlerVcJobWithDB", func(*vcjobHandler, string, *vcjobItem) error {
				return nil
			})
			defer patch1.Reset()
			time.Sleep(2 * time.Second)
		})

		Convey("when receive stopchan", func() {
			VcjobHandler1 := &vcjobHandler{
				VcJobMessageQueue: make(map[string]*Queue, 0),
				stopChan:          make(chan struct{}, 1),
			}
			VcjobHandler1.stopChan <- struct{}{}
			vcjobItem1 := &vcjobItem{Action: "delete"}
			VcjobHandler1.AddMessageToQueueWithId("123", vcjobItem1)
			patch1 := gomonkey.ApplyMethod(reflect.TypeOf(VcjobHandler1), "HandlerVcJobWithDB", func(*vcjobHandler, string, *vcjobItem) error {
				return nil
			})
			defer patch1.Reset()
			time.Sleep(2 * time.Second)
		})
	})
}
