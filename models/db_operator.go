package models

import (
	"cwsm/models/pg_model"
	"cwsm/tools/commontools/logger"
)

var pgdb = &pg_model.Pg_table_operator{}

func InitTables() {
	pgdb.CreateDb()
	createSql := []string{createVcJobFaultHistoryTable, createInspectionPlanTable, createInspectionResultTable,createFaultInfoTable}
	if pgdb.GetClient() == nil {
		logger.Errorf("PGDB Client is nil")
		return
	}
	for _, sql := range createSql {
		if err := pgdb.CreateTable(sql); err != nil {
			logger.Errorf("Create Table failed: " + err.Error())
		}
	}
}

func QueryResourceAll(tableName string) ([]map[string]interface{}, error) {
	queryres, queryerr := pgdb.QueryPgAll(tableName)
	if queryerr != nil {
		logger.Errorf("PGDB Query All Failed: " + queryerr.Error())
		return nil, queryerr
	}
	output := LowerKeyToNormalKey(getThreeElementColumType(tableName), queryres)
	return output, nil
}

func QueryResourceByCondition(tableName string, queryCondition map[string]interface{}) ([]map[string]interface{}, error) {
	queryres, queryerr := pgdb.QueryPgByCondition(tableName, queryCondition)
	if queryerr != nil {
		logger.Errorf("PGDB Query Failed: " + queryerr.Error())
		return nil, queryerr
	}
	if len(queryres) == 0 {
		logger.Info("PGDB Query Result is none %s query %v result is none", tableName, queryCondition)
		return nil, nil
	}
	output := LowerKeyToNormalKey(getThreeElementColumType(tableName), queryres)
	return output, nil
}

func QueryResourceById(tableName string, idValue string) (map[string]interface{}, error) {
	queryCondition := map[string]interface{}{"id": idValue}
	queryres, queryerr := pgdb.QueryPgByCondition(tableName, queryCondition)
	if queryerr != nil {
		logger.Errorf("PGDB Query Failed" + queryerr.Error())
		return nil, queryerr
	}
	if len(queryres) == 0 {
		logger.Info("PGDB Query Result is none %s query %s result is none", tableName, idValue)
		return nil, nil
	}
	output := LowerKeyToNormalKey(getThreeElementColumType(tableName), queryres)
	return output[0], nil
}

func QueryResourceByResultId(tableName string, idValue string) (map[string]interface{}, error) {
	queryCondition := map[string]interface{}{"resultId": idValue}
	queryres, queryerr := pgdb.QueryPgByCondition(tableName, queryCondition)
	if queryerr != nil {
		logger.Errorf("PGDB Query Failed" + queryerr.Error())
		return nil, queryerr
	}

	if len(queryres) == 0 {
		logger.Info("PGDB Query Result is none %s query %s result is none", tableName, idValue)
		return nil, nil
	}
	output := LowerKeyToNormalKey(getThreeElementColumType(tableName), queryres)
	return output[0], nil
}

func QueryResourceByTime(tableName string, startTime string) (map[string]interface{}, error) {
	queryCondition := map[string]interface{}{"startTime": startTime}
	queryres, queryerr := pgdb.QueryPgByCondition(tableName, queryCondition)
	if queryerr != nil {
		logger.Errorf("PGDB Query Failed" + queryerr.Error())
		return nil, queryerr
	}

	if len(queryres) == 0 {
		logger.Info("PGDB Query Result is none %s query %s result is none", tableName, startTime)
		return nil, nil
	}
	output := LowerKeyToNormalKey(getThreeElementColumType(tableName), queryres)
	return output[0], nil
}

func InsertNewResource(tableName string, newResource map[string]interface{}) bool {
	if !pgdb.InsertOneRow(tableName, getThreeElementColumType(tableName), newResource) {
		logger.Errorf("PGDB Insert %s Row Failed", tableName)
		return false
	}
	return true
}

func UpdateResourceById(tableName string, idValue string, updateResource map[string]interface{}) bool {
	if !pgdb.UpdateOneRow(tableName, updateResource, "id", idValue) {
		logger.Errorf("PGDB Update row Failed, id = " + idValue)
		return false
	}
	return true
}

func UpdateResourceByResultId(tableName string, idValue string, updateResource map[string]interface{}) bool {
	if !pgdb.UpdateOneRow(tableName, updateResource, "resultId", idValue) {
		logger.Errorf("PGDB Update row Failed, resultId = " + idValue)
		return false
	}
	return true
}

func UpdateResourceByName(tableName string, nameValue string, updateResource map[string]interface{}) bool {
	if !pgdb.UpdateOneRow(tableName, updateResource, "name", nameValue) {
		logger.Errorf("PGDB Update row Failed, name = " + nameValue)
		return false
	}
	return true
}

func DeleteResourceById(tableName string, idValue string) bool {
	if !pgdb.DeleteOneRow(tableName, "id", idValue) {
		logger.Errorf("PGDB Delete row Failed id = " + idValue)
		return false
	}
	return true
}

func DeleteResourceByUuid(tableName string, idValue string) bool {
	if !pgdb.DeleteOneRow(tableName, "uuid", idValue) {
		logger.Errorf("PGDB Delete row Failed uuid = " + idValue)
		return false
	}
	return true
}

func DeleteResourceByCol(tableName string,ColName string, colValue string) bool {
	if !pgdb.DeleteOneRow(tableName,ColName, colValue) {
		logger.Errorf("PGDB Delete row Failed %s = %s" ,ColName, colValue)
		return false
	}
	return true
}

func UpdateResourceByUuid(tableName string, idValue string, updateResource map[string]interface{}) bool {
	if !pgdb.UpdateOneRow(tableName, updateResource, "uuid", idValue) {
		logger.Errorf("PGDB Update row Failed, uuid = " + idValue)
		return false
	}
	return true
}

func QueryResourceByUuid(tableName string, idValue string) (map[string]interface{}, error) {
	queryCondition := map[string]interface{}{"uuid": idValue}
	queryres, queryerr := pgdb.QueryPgByCondition(tableName, queryCondition)
	if queryerr != nil {
		logger.Errorf("PGDB Query Failed" + queryerr.Error())
		return nil, queryerr
	}
	if len(queryres) == 0 {
		logger.Errorf("PGDB Query Result is none")
		return nil, nil
	}
	output := LowerKeyToNormalKey(getThreeElementColumType(tableName), queryres)
	return output[0], nil
}
