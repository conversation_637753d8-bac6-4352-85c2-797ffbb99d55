package models

import (
	"cwsm/models/pg_model"
	"fmt"
	"reflect"
	"testing"

	"cwsm/infra/constant"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
)

func TestInitTables(t *testing.T) {
	Convey("TestInitTables", t, func() {
		Convey("init tables failed", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "CreateDb", func(*pg_model.Pg_table_operator) {
			})
			defer patch.Reset()
			// patch2 := gomonkey.ApplyMethodSeq(reflect.TypeOf(pgmodel), "CreateTable", []gomonkey.OutputCell{
			// 	{Values: gomonkey.Params{nil}},
			// 	{Values: gomonkey.Params{nil}},
			// 	{Values: gomonkey.Params{nil}},
			// })
			// defer patch2.Reset()
			InitTables()
		})
	})
}

func TestDeleteResourceByUuid(t *testing.T) {
	Convey("TestDeleteResourceByUuid", t, func() {
		Convey("delete failed, return err", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "DeleteOneRow", func(*pg_model.Pg_table_operator, string, string, string) bool {
				return false
			})
			defer patch.Reset()
			res := DeleteResourceByUuid("vcjobtable", "123")
			So(res, ShouldBeFalse)
		})
		Convey("delete successfully, return true", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "DeleteOneRow", func(*pg_model.Pg_table_operator, string, string, string) bool {
				return true
			})
			defer patch.Reset()
			res := DeleteResourceByUuid("vcjobtable", "123")
			So(res, ShouldBeTrue)
		})
	})
}

func TestUpdateResourceByUuid(t *testing.T) {
	Convey("TestUpdateResourceByUuid", t, func() {
		Convey("update failed, return err", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "UpdateOneRow", func(*pg_model.Pg_table_operator, string, map[string]interface{}, string, string) bool {
				return false
			})
			defer patch.Reset()
			res := UpdateResourceByUuid("vcjobtable", "123", map[string]interface{}{})
			So(res, ShouldBeFalse)
		})
		Convey("update successfully, return true", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "UpdateOneRow", func(*pg_model.Pg_table_operator, string, map[string]interface{}, string, string) bool {
				return true
			})
			defer patch.Reset()
			res := UpdateResourceByUuid("vcjobtable", "123", map[string]interface{}{})
			So(res, ShouldBeTrue)
		})
	})
}

func TestQueryResourceByUuid(t *testing.T) {
	Convey("TestQueryResourceByUuid", t, func() {
		Convey("query failed, return err", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{}, fmt.Errorf("err")
			})
			defer patch.Reset()
			_, err := QueryResourceByUuid("vcjobtable", "123")
			So(err, ShouldBeError)
		})
		Convey("query res is nil, return nil", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{}, nil
			})
			defer patch.Reset()
			_, err := QueryResourceByUuid("vcjobtable", "123")
			So(err, ShouldBeNil)
		})

		Convey("query res is non-nil, return nil", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{{"123": "123"}}, nil
			})
			defer patch.Reset()
			_, err := QueryResourceByUuid("vcjobtable", "123")
			So(err, ShouldBeNil)
		})
	})
}

func TestGetThreeElementColumType(t *testing.T) {
	Convey("TestGetThreeElementColumType", t, func() {
		getThreeElementColumType(constant.TABLE_NAME_VCJOBFAULTHISTORY)
		getThreeElementColumType(constant.TABLE_NAME_InspectionPlanTable)
		getThreeElementColumType(constant.TABLE_NAME_InspectionResultTable)
	})
}

func TestQueryResourceByCondition(t *testing.T) {
	Convey("TestQueryResourceByCondition", t, func() {
		Convey("query failed, return err", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return nil, fmt.Errorf("err")
			})
			defer patch.Reset()

			_, err := QueryResourceByCondition("vcjobtable", map[string]interface{}{"uuid": "123"})
			So(err, ShouldBeError)
		})

		Convey("query result is nil, return nil", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{}, nil
			})
			defer patch.Reset()

			result, err := QueryResourceByCondition("vcjobtable", map[string]interface{}{"uuid": "123"})
			So(err, ShouldBeNil)
			So(result, ShouldBeNil)
		})

		Convey("query result is non-nil, return result", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			mockResult := []map[string]interface{}{
				{"id": "123", "name": "test"},
			}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return mockResult, nil
			})
			defer patch.Reset()

			_, err := QueryResourceByCondition("vcjobtable", map[string]interface{}{"uuid": "123"})
			So(err, ShouldBeNil)
		})
	})
}

func TestQueryResourceById(t *testing.T) {
	Convey("TestQueryResourceById", t, func() {
		Convey("query failed, return err", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return nil, fmt.Errorf("err")
			})
			defer patch.Reset()

			_, err := QueryResourceById("vcjobtable", "123")
			So(err, ShouldBeError)
		})

		Convey("query result is nil, return nil", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{}, nil
			})
			defer patch.Reset()

			result, err := QueryResourceById("vcjobtable", "123")
			So(err, ShouldBeNil)
			So(result, ShouldBeNil)
		})

		Convey("query result is non-nil, return result", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			mockResult := []map[string]interface{}{
				{"id": "123", "name": "test"},
			}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return mockResult, nil
			})
			defer patch.Reset()

			_, err := QueryResourceById("vcjobtable", "123")
			So(err, ShouldBeNil)
		})
	})
}

func TestQueryResourceByResultId(t *testing.T) {
	Convey("TestQueryResourceByResultId", t, func() {
		Convey("query failed, return err", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return nil, fmt.Errorf("err")
			})
			defer patch.Reset()

			_, err := QueryResourceByResultId("vcjobtable", "result-123")
			So(err, ShouldBeError)
		})

		Convey("query result is nil, return nil", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{}, nil
			})
			defer patch.Reset()

			result, err := QueryResourceByResultId("vcjobtable", "result-123")
			So(err, ShouldBeNil)
			So(result, ShouldBeNil)
		})

		Convey("query result is non-nil, return result", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			mockResult := []map[string]interface{}{
				{"resultId": "result-123", "name": "test"},
			}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return mockResult, nil
			})
			defer patch.Reset()

			_, err := QueryResourceByResultId("vcjobtable", "result-123")
			So(err, ShouldBeNil)
		})
	})
}

func TestQueryResourceByTime(t *testing.T) {
	Convey("TestQueryResourceByTime", t, func() {
		Convey("query failed, return err", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return nil, fmt.Errorf("err")
			})
			defer patch.Reset()

			_, err := QueryResourceByTime("vcjobtable", "2023-01-01T00:00:00Z")
			So(err, ShouldBeError)
		})

		Convey("query result is nil, return nil", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return []map[string]interface{}{}, nil
			})
			defer patch.Reset()

			result, err := QueryResourceByTime("vcjobtable", "2023-01-01T00:00:00Z")
			So(err, ShouldBeNil)
			So(result, ShouldBeNil)
		})

		Convey("query result is non-nil, return result", func() {
			pgmodel := &pg_model.Pg_table_operator{}
			mockResult := []map[string]interface{}{
				{"startTime": "2023-01-01T00:00:00Z", "name": "test"},
			}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "QueryPgByCondition", func(*pg_model.Pg_table_operator, string, map[string]interface{}) ([]map[string]interface{}, error) {
				return mockResult, nil
			})
			defer patch.Reset()

			_, err := QueryResourceByTime("vcjobtable", "2023-01-01T00:00:00Z")
			So(err, ShouldBeNil)
		})
	})
}

func TestUpdateResourceByResultId(t *testing.T) {
	Convey("TestUpdateResourceByResultId", t, func() {
		Convey("update failed, return false", func() {
			// 模拟 UpdateOneRow 方法返回 false
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "UpdateOneRow", func(*pg_model.Pg_table_operator, string, map[string]interface{}, string, string) bool {
				return false
			})
			defer patch.Reset()

			result := UpdateResourceByResultId("vcjobtable", "result-123", map[string]interface{}{"name": "test"})
			So(result, ShouldBeFalse)
		})

		Convey("update successful, return true", func() {
			// 模拟 UpdateOneRow 方法返回 true
			pgmodel := &pg_model.Pg_table_operator{}
			patch := gomonkey.ApplyMethod(reflect.TypeOf(pgmodel), "UpdateOneRow", func(*pg_model.Pg_table_operator, string, map[string]interface{}, string, string) bool {
				return true
			})
			defer patch.Reset()

			result := UpdateResourceByResultId("vcjobtable", "result-123", map[string]interface{}{"name": "test"})
			So(result, ShouldBeTrue)
		})
	})
}

/* Started by AICoder, pid:nabc9xf1ff1184f14dc40b9b0097e1268f351ac5 */

func TestDeleteResourceByCol(t *testing.T) {
	Convey("Given a table name, column name, and column value", t, func() {

		Convey("Case 1: Successful deletion should return true", func() {
			patch := gomonkey.ApplyMethod(reflect.TypeOf(&pg_model.Pg_table_operator{}), "DeleteOneRow", func(_ *pg_model.Pg_table_operator, tableName string, colName string, colValue string) bool {
				return true
			})
			defer patch.Reset()

			result := DeleteResourceByCol("test_table", "test_col", "test_value")
			So(result, ShouldBeTrue)
		})

		Convey("Case 2: Failed deletion should return false", func() {
			patch := gomonkey.ApplyMethod(reflect.TypeOf(&pg_model.Pg_table_operator{}), "DeleteOneRow", func(_ *pg_model.Pg_table_operator, tableName string, colName string, colValue string) bool {
				return false
			})
			defer patch.Reset()

			result := DeleteResourceByCol("test_table", "test_col", "test_value")
			So(result, ShouldBeFalse)
		})
	})
}

/* Ended by AICoder, pid:nabc9xf1ff1184f14dc40b9b0097e1268f351ac5 */
