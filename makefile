export PATH := $(TOOLS):$(PATH)
export GO111MODULE := auto
export GOSUMDB := off

# 省略自命令时执行help自命令
.PHONY: default
default: help

.PHONY: all
all: build ci

.PHONY: builds
build:
	@echo "[START] go install..."
	cd $(shell pwd) && go mod tidy && go install -v
	@echo "[END] go install."
	
# 代码根目录名
override CODE_DIR = cwsm


# 流水线ut verify 变量
# 代码质量检查工具　govet　入参
GOVET_SWITCH = on
GOVET_EXCLUDE_PATH = conf tests tools

# 代码规范检查工具　golint　入参
GOLINT_SWITCH = on
GOLINT_EXCLUDE_PATH = conf tests tools

# 函数重复度检查工具　dupl　入参
DUPL_SWITCH = on

# 函数长度检查工具　flen　入参
FLEN_SWITCH = on
FLEN_EXCLUDE_PATH = conf tests tools
FLEN_ARGS = $(CODE_DIR)

# 函数圈复杂度检查工具　gocyclo　入参
GOCYCLO_SWITCH = on
GOCYCLO_EXCLUDE_PATH = conf tests tools
GOCYCLO_ARGS = $(CODE_DIR)

# 函数深度检查工具　godepth　入参
GODEPTH_SWITCH = on
GODEPTH_EXCLUDE_PATH = conf tests tools

# 代码安全检查工具　gosec　入参
GOSEC_SWITCH = off
GOSEC_EXCLUD_PATH = conf tests tools

# 单元测试　gocoverng　入参
UT_EXCLUDED_PATH=$(shell pwd)/menu $(shell pwd)/kafkaservice $(shell pwd)/tools
UT_COV_THS = 85
FT_PATH = $(shell pwd)/tests/testcase
# UT_ARGS = -timeout=30m -type=merge -ftpath=$(FT_PATH) $(shell pwd) $(UT_EXCLUDED_PATH)
UT_ARGS = -timeout=30m -type=merge $(shell pwd) $(UT_EXCLUDED_PATH)
# 代码行覆盖率检查工具　goinccover　入参
#INC_UT_COV_THS = 85
#INC_COV_ARGS = -timeout=30m -type=ft
# INC_COV_EXCLUDE = $(shell pwd)/mocker $(shell pwd)/ft ${work_path}/vendor

# INC_LINE_ARGS = -type=ut -scenario=line -isinc=true -timeout=30m -ftpath=$(FT_PATH) $(shell pwd)
INC_LINE_ARGS = -type=ut -scenario=line -isinc=true -timeout=30m $(shell pwd)
# 需要排除的路径，可选。多个以空格分隔
#INC_LINE_EXCLUDE = $(shell pwd)/menu $(shell pwd)/kafkaservice $(shell pwd)/tools  

INC_BRANCH_ARGS = -type=ut -scenario=branch -isinc=true -timeout=30m $(shell pwd)
# INC_BRANCH_ARGS = -type=ut -scenario=branch -isinc=true -timeout=30m -ftpath=$(FT_PATH) $(shell pwd)
#INC_BRANCH_EXCLUDE = $(shell pwd)/menu $(shell pwd)/kafkaservice $(shell pwd)/tools

FULL_LINE_ARGS = -type=ut -scenario=line -timeout=30m $(shell pwd)
# FULL_LINE_ARGS = -type=ut -scenario=line -timeout=30m -ftpath=$(FT_PATH) $(shell pwd)
#FULL_LINE_EXCLUDE = $(shell pwd)/menu $(shell pwd)/kafkaservice $(shell pwd)/tools 

FULL_BRANCH_ARGS = -type=ut -scenario=branch -timeout=30m $(shell pwd)
# FULL_BRANCH_ARGS = -type=ut -scenario=branch -timeout=30m -ftpath=$(FT_PATH) $(shell pwd)
#FULL_BRANCH_EXCLUDE = $(shell pwd)/menu $(shell pwd)/kafkaservice $(shell pwd)/tools


# 引入公共　makefile
include $(TOOLS)/makefile
