package migration

func transVersion2Data(version string) string {
	if data, ok := VersionDateMap[version]; ok {
		return data
	}
	return version
}

type Migrations []MigrationIF

func (m Migrations) Len() int {
	return len(m)
}

func (m Migrations) Swap(i, j int) {
	m[i], m[j] = m[j], m[i]
}

func (m Migrations) Less(i, j int) bool {
	t1 := transVersion2Data(m[i].GetDataVersion())
	t2 := transVersion2Data(m[j].GetDataVersion())
	return t1 < t2
}
