package migration

import (
	"errors"
	"cwsm/tools/commontools/logger"
)

var (
	MigrationManagerList = &MigrationManager{}
)

type MigrationProcessIF interface {
	InitMigrationManager()
	CreateBaseLine()
}

func StartMigration(migrationProcessIF MigrationProcessIF) error {
	migrationProcessIF.InitMigrationManager()
	migrationProcessIF.CreateBaseLine()
	if !MigrationManagerList.DoMigrations() {
		return errors.New("migration failed")
	}
	logger.Infof("migration process success")
	return nil
}
