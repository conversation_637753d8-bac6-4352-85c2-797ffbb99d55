package migration

import (
	"errors"
	"reflect"
	"sort"
	"cwsm/tools/commontools/db/dbutil"
	"cwsm/tools/commontools/logger"
)

type MigrationManager struct {
	client     *dbutil.DBClient
	migrations []MigrationIF
}

func (m *MigrationManager) setMigMgrDbClient() bool {
	m.client = dbutil.GetDbClient()
	if m.client == nil {
		logger.Errorf("setMigMgrDbClient is nil")
		return false
	}

	return true
}

func (m *MigrationManager) getTableDataVersion() (string, error) {
	rows, err := m.client.Query(PgTableNameDataVersion, nil)
	if err != nil {
		return "", err
	}
	for _, row := range rows {
		if version, ok := row[PgColDataVersion]; ok {
			return m.parseVersion(version), nil
		}
	}
	return "", errors.New("can not found base data version from db")
}

func (m *MigrationManager) parseVersion(version interface{}) string {
	strVersion := ""
	if _, ok := version.(string); ok {
		strVersion = version.(string)
	}
	if _, ok := version.([]uint8); ok {
		var newBytes []byte
		tmpValue := version.([]uint8)
		for key := range tmpValue {
			newBytes = append(newBytes, byte(tmpValue[key]))
		}
		strVersion = string(newBytes)
	}

	return strVersion
}

func (m *MigrationManager) UpdateDataVersion(version string) bool {
	data := map[string]interface{}{
		PgColDataVersion: version,
	}
	return m.client.Update(PgTableNameDataVersion, data, nil)
}

func (m *MigrationManager) AddMigration(mgr MigrationIF) *MigrationManager {
	m.migrations = append(m.migrations, mgr)
	return m
}

func (m *MigrationManager) sortMigrations() ([]MigrationIF, error) {
	var needDo []MigrationIF
	currVersion, err := m.getTableDataVersion()
	if err != nil {
		return needDo, err
	}

	logger.Infof("current data version is: ", currVersion)

	for _, value := range m.migrations {
		if transVersion2Data(currVersion) < transVersion2Data(value.GetDataVersion()) {
			needDo = append(needDo, value)
		}
	}
	sort.Sort(Migrations(needDo))
	return needDo, nil
}

func (m *MigrationManager) DoMigrations() bool {
	if !m.setMigMgrDbClient() {
		return false
	}

	needDo, err := m.sortMigrations()
	if err != nil {
		logger.Errorf("sort need migrations failed")
		return false
	}
	for key := range needDo {
		if ok := needDo[key].Up() && m.UpdateDataVersion(needDo[key].GetDataVersion()); !ok {
			logger.Errorf("migration failed: ", reflect.TypeOf(needDo[key]), "version ", needDo[key].GetDataVersion())
			return false
		}
	}
	return true
}
