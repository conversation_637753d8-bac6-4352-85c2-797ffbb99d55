package opeanapi

import (
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/httpservice"
	"encoding/json"
	"fmt"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

func GetOpapiVersion(baseurl string, project string, headers map[string]string, auth *restful.SSLAuth) string {
	url := baseurl + fmt.Sprintf(OpapiVersionFmt, project)
	bytes, _, _, err := httpservice.Get(url, headers, auth)
	if err != nil {
		logger.Errorf("GetOpapiVersion failed: %v", err)
		return ""
	}

	response := struct {
		Version string `json:"PaaS_version"`
	}{}
	if err = json.Unmarshal(bytes, &response); err != nil {
		logger.Errorf("GetOpapiVersion: json.Unmarshal failed: %v", err)
	}
	return response.Version
}
