package microservice

import (
	"bytes"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/infa/util/nettool"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/httpservice"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

func GetMicroService(serviceName string) (*MsbMicroServiceInfoDto, error) {
	url := GetMicroServicesFromMsbURL("v1", serviceName, "v1")
	logger.Infof("GetMicroService url: %s", url)
	body, _, _, err := restful.GetMethod(url, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("GetMicroService Query Micro Service:%s From Msb failed:%s", serviceName, err)
		return nil, err
	}

	var msbRes MsbMicroServiceInfoDto
	if err = json.Unmarshal(body, &msbRes); err != nil {
		logger.Errorf("GetMicroService Query Micro Services From Msb serviceName:%s, body:%s "+
			"json.Unmarshal failed:%s", serviceName, string(body), err)
		return nil, err
	}
	return &msbRes, nil
}

func GetMicroServiceDetail(serviceName string) (*MicroServiceDetails, error) {
	url := GetMicroServicesFromMsbURL("v1", serviceName, "v1")
	logger.Infof("[GetMicroServiceDetail]: url: %s", url)
	body, _, _, err := restful.GetMethod(url, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetMicroServiceDetail]: Query Micro Service Detail:%s From Msb failed:%s", serviceName, err)
		return nil, err
	}

	microServiceDetails := &MicroServiceDetails{}
	if err = json.Unmarshal(body, microServiceDetails); err != nil {
		logger.Errorf("GetMicroServiceDetail Query Micro Services DetailFrom Msb serviceName:%s, body:%s "+
			"json.Unmarshal failed:%s", serviceName, string(body), err)
		return nil, err
	}
	return microServiceDetails, nil
}

func AddMsbPort(portReq *MsbPortRouterReqDto) error {
	msbIP := nettool.IpFormatter(MsbIP4Openpalett)
	url := "http://" + msbIP + ":" + MsbPort4Openpalett +
		"/api/microservices/" + "v1" + "/services"
	_, _, _, err := httpservice.Post(url+"?createOrUpdate=false", httpservice.DefaultHeaders(), portReq, nil)
	if err != nil {
		logger.Errorf("AddMsbPort Post failed:", err.Error())
		return errors.New("AddMsbPort post failed")
	}
	return nil
}

func GetMsbPorts(namespace string) ([]*MsbPortRouterReqDto, error) {
	msbIP := nettool.IpFormatter(MsbIP4Openpalett)
	url := "http://" + msbIP + ":" +
		MsbPort4Openpalett + "/api/microservices/" + "v1" +
		"/services?namespace=" + namespace

	res, _, _, err := httpservice.Get(url, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("GetMsbPorts failed:", err.Error())
		return nil, errors.New("GetMsbPorts failed" + err.Error())
	}
	var msbPorts []*MsbPortRouterReqDto
	err = json.Unmarshal(res, &msbPorts)
	if err != nil {
		logger.Errorf("GetMsbPorts json.Unmarshal failed:", err.Error())
		return nil, errors.New("GetMsbPorts json.Unmarshal error," + err.Error())
	}

	return msbPorts, nil
}

func DelMsbPort(msb_port int) error {
	msbIP := nettool.IpFormatter(MsbIP4Openpalett)
	url := "http://" + msbIP + ":" + MsbPort4Openpalett +
		"/api/microservices/" + "v1" + "/services" + "/cloud" +
		strconv.Itoa(msb_port) + "/version/null"
	_, _, _, err := httpservice.Delete(url, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("DelMsbPort Delete failed:", err.Error())
		return errors.New("DelMsbPort Delete failed")
	}
	return nil
}

func GetMicroServicesFromMsbURL(apiVersion, serviceName, srvVersion string) string {
	if apiVersion == "" {
		apiVersion = "v1"
	}
	var urlBuffer bytes.Buffer
	var err error
	if _, err = urlBuffer.WriteString(globalcv.HTTPPrefix); err != nil {
		logger.Errorf("getMicroServicesFromMsbURL write HTTP prefix error:", err.Error())
	}
	msbIP := nettool.IpFormatter(MsbIP4Openpalett)
	if _, err = urlBuffer.WriteString(msbIP + ":" + MsbPort4Openpalett +
		"/api/microservices/" + apiVersion + "/services"); err != nil {
		logger.Errorf("getMicroServicesFromMsbURL write msb error:", err.Error())
	}

	if serviceName != "" {
		if _, err = urlBuffer.WriteString("/" + serviceName); err != nil {
			logger.Errorf("getMicroServicesFromMsbURL write service name error:", err.Error())
		}
	}

	if srvVersion != "" {
		if _, err = urlBuffer.WriteString("/version/" + srvVersion); err != nil {
			logger.Errorf("getMicroServicesFromMsbURL write version error:", err.Error())
		}
	}

	if _, err = urlBuffer.WriteString("?namespace=" + NameSpace4Openpalett); err != nil {
		logger.Errorf("getMicroServicesFromMsbURL write env variable error:", err.Error())
	}

	return urlBuffer.String()
}

func GetPublishUrl(serviceName, ns, urlpath string) (string, error) {
	msbIP := nettool.IpFormatter(MsbIP4Openpalett)
	url := fmt.Sprintf("http://%s:%s/api/microservices/v1/services/%s/version/v1/allpublishaddress?namespace=%s",
		msbIP, MsbPort4Openpalett, serviceName, ns)
	body, _, _, err := restful.GetMethod(url, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetMicroServiceDetail]: Query Micro Service Detail:%s From Msb failed:%s", serviceName, err)
		return "", err
	}
	var publishUrlInfos []PublishUrlInfo
	err = json.Unmarshal(body, &publishUrlInfos)
	if err != nil {
		logger.Errorf("[GetPublishUrl] Unmarshal fail")
		return "", err
	}
	for _, p := range publishUrlInfos {
		if p.PublishProtocol == "http" {
			return "http://" + p.IP + ":" + p.Port + p.PublishURL + urlpath, nil
		}
	}
	logger.Errorf("[GetPublishUrl] no found publish url:" + string(body))
	return "", fmt.Errorf("get publish url fail")
}
