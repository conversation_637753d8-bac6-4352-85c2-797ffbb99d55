package microservice

import "strconv"

var (
	MsbIP4Openpalett     = ""
	MsbPort4Openpalett   = ""
	NameSpace4Openpalett = ""
	MsbIP                = ""
	MsbInnerPort         = ""
)

type MsbMicroServiceInfoDto struct {
	ServiceName string                       `json:"serviceName"`
	Version     string                       `json:"version"`
	URL         string                       `json:"url"`
	Protocol    string                       `json:"protocol"`
	NameSpace   string                       `json:"namespace"`
	Path        string                       `json:"path"`
	Nodes       []MsbMicroServiceNodeInfoDto `json:"nodes"`
}

type MsbMicroServiceNodeInfoDto struct {
	IP         string `json:"ip"`
	IPV4       string `json:"ip_v4"`
	IPV6       string `json:"ip_v6"`
	Port       string `json:"port"`
	NodeID     string `json:"nodeId"`
	Status     string `json:"status"`
	Appversion string `json:"appversion"`
}

type MsbPortRouterReqDto struct {
	ServiceName          string        `json:"serviceName"`
	URL                  string        `json:"url"`
	Version              string        `json:"version"`
	Nodes                []NodeDto     `json:"nodes"`
	Protocol             string        `json:"protocol"`
	PublishPort          string        `json:"publish_port"`
	EnableSSL            bool          `json:"enable_ssl"`
	VisualRange          string        `json:"visualRange"`
	LbPolicy             string        `json:"lb_policy"`
	Publish_iplist       string        `json:"publish_iplist"`
	Namespace            string        `json:"namespace"`
	Network_plane_type   string        `json:"network_plane_type"`
	Host                 string        `json:"host"`
	Subdomain            string        `json:"subdomain"`
	Path                 string        `json:"path"`
	Enable_tls           bool          `json:"enable_tls"`
	Enable_http2         bool          `json:"enable_http2"`
	Enable_client_verify bool          `json:"enable_client_verify"`
	Enable_refer_match   string        `json:"enable_refer_match"`
	Labels               []string      `json:"labels"`
	Metadata             []KeyValue    `json:"metadata"`
	Scope                string        `json:"scope"`
	Rate_limiting        Rate_limiting `json:"rate_limiting"`
	Proxy_rule           Proxy_rule    `json:"proxy_rule"`
}

type KeyValue struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type Rate_limiting struct {
	Limit_req Limit_req `json:"limit_req"`
}

type Limit_req struct {
	Rate  string `json:"rate"`
	Burst string `json:"burst"`
}

type Proxy_rule struct {
	Http_proxy   Http_proxy   `json:"http_proxy"`
	Stream_proxy Stream_proxy `json:"stream_proxy"`
}

type Http_proxy struct {
	Send_timeout string `json:"send_timeout"`
	Read_timeout string `json:"read_timeout"`
}

type Stream_proxy struct {
	Proxy_timeout   string `json:"proxy_timeout"`
	Proxy_responses string `json:"proxy_responses"`
}

type NodeDto struct {
	Port           string `json:"port"`
	IP             string `json:"ip"`
	TTL            string `json:"ttl"`
	LbServerParams string `json:"lb_server_params"`
	CheckType      string `json:"checkType"`
	CheckInterval  string `json:"checkInterval"`
	CheckTimeOut   string `json:"checkTimeOut"`
	CheckURL       string `json:"checkUrl"`
	Ha_role        string `json:"ha_role"`
	Appversion     string `json:"appversion"`
}

func NewNode(port string, ip string) NodeDto {
	return NodeDto{
		Port: port,
		IP:   ip,
	}
}

func NewMsbPortRouter(public_port int, port string, ip string, isSsl bool) MsbPortRouterReqDto {
	return MsbPortRouterReqDto{
		ServiceName:          "cloud" + strconv.Itoa(public_port),
		URL:                  "/",
		Version:              "",
		Nodes:                []NodeDto{NewNode(port, ip)},
		Protocol:             "REST",
		PublishPort:          strconv.Itoa(public_port),
		EnableSSL:            isSsl,
		VisualRange:          "0",
		LbPolicy:             "ip_hash",
		Labels:               []string{},
		Metadata:             []KeyValue{},
		Path:                 "/ROOT_PATH",
		Enable_tls:           false,
		Enable_http2:         false,
		Enable_client_verify: false,
	}
}

type MicroServiceDetails struct {
	ServiceName   string              `json:"serviceName"`
	URL           string              `json:"url"`
	Version       string              `json:"version"`
	Protocol      string              `json:"protocol"`
	InterfaceType string              `json:"type"`
	Status        string              `json:"status"`
	Nodes         []ServerNodeDetails `json:"nodes"`
}

type ServerNodeDetails struct {
	Port       string `json:"port"`
	Ip         string `json:"ip"`
	Ttl        int    `json:"ttl"`
	Expiration string `json:"expiration"`
	CreatedAt  string `json:"created_at"`
	UpdatedAt  string `json:"updated_at"`
}

type PublishUrlInfo struct {
	IP                 string `json:"ip"`
	Port               string `json:"port"`
	PublishURL         string `json:"publish_url"`
	VisualRange        string `json:"visualRange"`
	PublishProtocol    string `json:"publish_protocol"`
	EnableClientVerify bool   `json:"enable_client_verify"`
	EnableHTTP2        bool   `json:"enable_http2"`
	EnableTLS          bool   `json:"enable_tls"`
}
