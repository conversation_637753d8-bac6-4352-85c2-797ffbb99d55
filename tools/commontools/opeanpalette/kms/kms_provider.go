package kms

import (
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/infa/util/nettool"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/httpservice"
	"cwsm/tools/commontools/service/msbservice"
	"cwsm/tools/commontools/storage/rediz/storage/secret_key_redis"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"
)

var (
	MsbIp     = os.Getenv(msbservice.OpenpaletteMSBIP)
	MsbPort   = os.Getenv(msbservice.OpenpaletteMSBPort)
	Namespace = os.Getenv(msbservice.OpenpaletteNameSpace)
)

func CLOUDNATIVE() bool {
	_, exist := os.LookupEnv("caas_type")
	return exist
}

func PutSecretKeyToCache(keyID string) (*secret_key_redis.SecretKeyDetail, error) {
	keyID = strings.TrimSpace(keyID)
	if len(keyID) == 0 {
		logger.Errorf("PutSecretKeyToCache empty secret key id")
		return nil, errors.New("empty secret key id")
	}
	cacheKey := Read(keyID)
	if cacheKey != nil {
		return cacheKey, nil
	}
	detail, err := GetSecretKeyDetail(keyID)
	if err != nil || detail == nil {
		logger.Errorf("PutSecretKeyToCache GetSecretKeyDetail failed: %v", err)
		return nil, err
	}
	Push(detail)
	return detail, nil
}

func GetSecretKeyDetail(keyID string) (*secret_key_redis.SecretKeyDetail, error) {
	url := ""
	if !CLOUDNATIVE() {
		node, err := GetOpProxyNode()
		if err != nil {
			logger.Errorf("PutSecretKeyToCache GetOpProxyNode failed: %v", err)
			return nil, err
		}
		url = fmt.Sprintf(globalcv.DetailURLFmt, nettool.IpFormatter(node.IP), node.Port, Namespace, keyID)
	} else {
		url = fmt.Sprintf(globalcv.DetailURLFmtCloudNative, Namespace, keyID)
	}
	bytes, _, code, err := httpservice.Get(url, httpservice.DefaultHeaders(), nil)
	logger.Infof("GetSecretKeyDetail restclient.Get url: %s, response code: %d", url, code)
	if err != nil {
		logger.Errorf("GetSecretKeyDetail:restclient.Get failed: %v", err)
		return nil, fmt.Errorf("GetSecretKeyDetail:restclient.Get failed: %v", err)
	}
	rsp := &SecretKeyRSP{}
	if err = json.Unmarshal(bytes, rsp); err != nil {
		logger.Errorf("GetSecretKeyDetail:json.Unmarshal response failed: %v", err)
		return nil, fmt.Errorf("GetSecretKeyDetail:json.Unmarshal response failed: %v", err)
	}
	param := &SecretKeyParam{}
	if err = json.Unmarshal([]byte(rsp.KeyParam), param); err != nil {
		logger.Errorf("GetSecretKeyDetail:json.Unmarshal key param failed: %v", err)
		return nil, fmt.Errorf("GetSecretKeyDetail:json.Unmarshal key param failed: %v", err)
	}
	return rsp.ToSecretKeyDetail(param.Nonce), nil
}

func GetOpProxyNode() (*OpProxyNode, error) {
	url := fmt.Sprintf("http://%s:%s/api/microservices/v1/services/op-proxy?namespace=%s",
		nettool.IpFormatter(MsbIp), MsbPort, Namespace)
	bytes, _, code, err := httpservice.Get(url, httpservice.DefaultHeaders(), nil)
	logger.Infof("GetOpProxyNode restclient.Get url: %s, response code: %v", url, code)
	if err != nil {
		logger.Errorf("GetOpProxyNode:restclient.Get failed: %v", err)
		return nil, err
	}
	var ns []OpProxyNodes
	if err = json.Unmarshal(bytes, &ns); err != nil {
		logger.Errorf("GetOpProxyNode:json.Unmarshal node failed: %v", err)
		return nil, err
	}
	if len(ns) == 0 || len(ns[0].Nodes) == 0 {
		logger.Errorf("GetOpProxyNode empty proxy nodes")
		return nil, errors.New("GetOpProxyNode empty proxy nodes")
	}
	return &ns[0].Nodes[0], nil
}
