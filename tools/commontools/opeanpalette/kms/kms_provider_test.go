package kms

import (
	"cwsm/tools/commontools/service/httpservice"
	"encoding/json"
	"errors"
	"net/http"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("GetSecretKeyDetail", func() {
	var (
		patches *gomonkey.Patches
		keyID   string
	)

	BeforeEach(func() {
		patches = gomonkey.NewPatches()
		keyID = "test-key-id"
	})

	AfterEach(func() {
		patches.Reset()
	})

	Context("When not in cloud native mode", func() {

		It("should return an error when GetOpProxyNode fails", func() {
			patches.ApplyFunc(GetOpProxyNode, func() (*OpProxyNode, error) {
				return nil, errors.New("failed to get op proxy node")
			})

			detail, err := GetSecretKeyDetail(keyID)
			Expect(err).To(MatchError("failed to get op proxy node"))
			Expect(detail).To(BeNil())
		})

		It("should return an error when HTTP request fails", func() {
			patches.ApplyFunc(GetOpProxyNode, func() (*OpProxyNode, error) {
				return &OpProxyNode{
					IP:   "127.0.0.1",
					Port: "8080",
				}, nil
			})

			patches.ApplyFunc(httpservice.Get, func(url string, headers map[string]string, body interface{}) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusInternalServerError, errors.New("http error")
			})

			detail, err := GetSecretKeyDetail(keyID)
			Expect(err).To(MatchError("GetSecretKeyDetail:restclient.Get failed: http error"))
			Expect(detail).To(BeNil())
		})

		It("should return an error when JSON unmarshal fails", func() {
			patches.ApplyFunc(GetOpProxyNode, func() (*OpProxyNode, error) {
				return &OpProxyNode{
					IP:   "127.0.0.1",
					Port: "8080",
				}, nil
			})

			patches.ApplyFunc(httpservice.Get, func(url string, headers map[string]string, body interface{}) ([]byte, map[string][]string, int, error) {
				return []byte("invalid json"), nil, http.StatusOK, nil
			})

			detail, err := GetSecretKeyDetail(keyID)
			Expect(err).To(MatchError("GetSecretKeyDetail:json.Unmarshal response failed: invalid character 'i' looking for beginning of value"))
			Expect(detail).To(BeNil())
		})

		It("should return an error when JSON unmarshal for KeyParam fails", func() {
			patches.ApplyFunc(GetOpProxyNode, func() (*OpProxyNode, error) {
				return &OpProxyNode{
					IP:   "127.0.0.1",
					Port: "8080",
				}, nil
			})

			patches.ApplyFunc(httpservice.Get, func(url string, headers map[string]string, body interface{}) ([]byte, map[string][]string, int, error) {
				response := SecretKeyRSP{
					KeyType:      "test-type",
					KeyID:        keyID,
					KeyValue:     "test-value",
					KeyAlgorithm: "AES",
					KeyParam:     `{"invalid-json"`,
				}
				bytes, _ := json.Marshal(response)
				return bytes, nil, http.StatusOK, nil
			})

			detail, err := GetSecretKeyDetail(keyID)
			Expect(err).To(MatchError("GetSecretKeyDetail:json.Unmarshal key param failed: unexpected end of JSON input"))
			Expect(detail).To(BeNil())
		})
	})
})
