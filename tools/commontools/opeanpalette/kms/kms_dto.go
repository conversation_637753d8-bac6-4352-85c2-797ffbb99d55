package kms

import (
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz/storage/secret_key_redis"
)

var (
	service = "cloudfuze"
)

func InitService(new_service string) {
	service = new_service
	Clear()
}

func Push(key *secret_key_redis.SecretKeyDetail) {
	if key == nil {
		return
	}
	err := secret_key_redis.SetCache(service, key)
	if err != nil {
		logger.Erro<PERSON>("SecretKeyCache Push key %s SetCache failed: %v", key.ID, err)
	}
	skds, err := secret_key_redis.GetAllCaches(service)
	if err != nil {
		logger.Errorf("SecretKeyCache Push key %s GetAllCaches failed: %v", key.ID, err)
	}
	logger.Infof("SecretKeyCache Push key %s, all caches: %s", key.ID, util.ToJSONStr(skds))
}

func Read(keyID string) *secret_key_redis.SecretKeyDetail {
	skc, err := secret_key_redis.GetCache(service, keyID)
	if err != nil {
		logger.Errorf("SecretKeyCache Read key %s failed: %v", keyID, err)
		return nil
	}
	return skc
}

func Del(keyID string) {
	secret_key_redis.DelCache(service, keyID)
	skds, err := secret_key_redis.GetAllCaches(service)
	if err != nil {
		logger.Errorf("SecretKeyCache Del key %s GetAllCaches failed: %v", keyID, err)
	}
	logger.Infof("SecretKeyCache Del key %s, all caches: %s", keyID, util.ToJSONStr(skds))
}

func Clear() {
	secret_key_redis.ClearCache(service)
	secret_key_redis.DelLatest(service)
}

func SetLatestKey(key *secret_key_redis.SecretKeyDetail) {
	if key == nil {
		return
	}
	err := secret_key_redis.SetLatest(service, key)
	if err != nil {
		logger.Errorf("SetLatestKey key:%s SetLatest failed: %v", key.ID, err)
	}
	logger.Infof("SecretKeyCache SetLatestKey key %s", key.ID)
}

func LatestKey() *secret_key_redis.SecretKeyDetail {
	sdk, err := secret_key_redis.GetLatest(service)
	if err != nil {
		logger.Errorf("LatestKey GetLatest failed: %v", err)
		return nil
	}
	return sdk
}

func GetPrintCache() string {
	skds, err := secret_key_redis.GetAllCaches(service)
	if err != nil {
		logger.Errorf("SecretKeyCache GetPrintCache GetAllCaches failed: %v", err)
	}
	return util.ToJSONStr(util.ToJSONStr(skds))
}

type SecretKeyRSP struct {
	KeyType      string `json:"secretKeyType"`
	KeyID        string `json:"secretKeyId"`
	KeyValue     string `json:"secretKey"`
	KeyAlgorithm string `json:"secretKeyAlgorithm"`
	KeyParam     string `json:"secretKeyParam"`
}

func (skr *SecretKeyRSP) ToSecretKeyDetail(nonce string) *secret_key_redis.SecretKeyDetail {
	return &secret_key_redis.SecretKeyDetail{
		Type:      skr.KeyType,
		ID:        skr.KeyID,
		Key:       skr.KeyValue,
		Algorithm: skr.KeyAlgorithm,
		Nonce:     nonce,
	}
}

type SecretKeyParam struct {
	Nonce string `json:"nonce"`
}

type OpProxyNodes struct {
	Nodes []OpProxyNode `json:"nodes"`
}

type OpProxyNode struct {
	IP   string `json:"ip"`
	Port string `json:"port"`
}

type SecretKeyTypeREQ struct {
	MsName           string `json:"msName"`
	MsVersion        string `json:"msVersion"`
	Type             string `json:"secretKeyType"`
	Algorithm        string `json:"secretKeyAlgorithm"`
	Param            string `json:"secretKeyParam,omitempty"`
	KeyNameI18n      string `json:"secretKeyNameI18n"`
	KeyDescI18n      string `json:"secretKeyDescI18n"`
	DestroyNotifyURL string `json:"destroyNotifyUrl"`
}

type SecretKeyTypeBasic struct {
	ID   string `json:"secretKeyId"`
	Type string `json:"secretKeyType"`
}

type SecretKeyRevokeREQ struct {
	ID      string `json:"secretKeyId"`
	Type    string `json:"secretKeyType"`
	Result  string `json:"result"`
	Message string `json:"message,omitempty"`
}
