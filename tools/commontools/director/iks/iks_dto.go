package iks

// auth 接口
const (
	AuthPrefix        = "/api/v1.0/auth"
	AuthRsaPrivatekey = AuthPrefix + "/inner/rsa_private_key"
	AuthRsaPublickey  = AuthPrefix + "/rsa_public_key"
	IksPrefix         = "/api/v1.0/iks"
	IksRsaDecrypt     = IksPrefix + "/decrypt"
)

const (
	LowVerSecretKeyID = "2b10dde4-8891-11eb-8dcd-0242ac130003"
)

type RSAPrivateKey struct {
	PrivateKeyPkcs1 string `json:"key_pkcs1_pem"`
	PrivateKeyPkcs8 string `json:"key_pkcs8"`
}

type RSAPublicKey struct {
	Key            string `json:"key"`
	KeyX509Encoded string `json:"key_x509_encoded"`
}

type RsaDecrypt struct {
	Data string `json:"data"`
}
type RsaDecryptDecode struct {
	Decrypt_data []byte `json:"decrypt_data"`
}
