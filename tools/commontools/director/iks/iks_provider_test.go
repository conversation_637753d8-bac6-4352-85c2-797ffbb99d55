package iks

import (
	"cwsm/tools/commontools/service/httpservice"
	"encoding/json"
	"errors"
	"net/http"

	"zte.com.cn/cms/crmX/commontools-base/restful"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:d6627k271a953b4141e608038069697881b96492 */
var _ = Describe("PostRsaDecrypt function", func() {
	var testData *RsaDecrypt
	var patcher *gomonkey.Patches

	BeforeEach(func() {
		testData = &RsaDecrypt{Data: "test data"}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("when marshaling data succeeds", func() {
		It("should return decoded data on successful post", func() {
			// Mock successful response
			response := RsaDecryptDecode{Decrypt_data: []byte("decoded data")}
			respData, _ := json.Marshal(response)

			patcher.ApplyFunc(httpservice.Post, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return respData, nil, http.StatusOK, nil
			})

			result, err := PostRsaDecrypt(testData)
			Expect(err).To(BeNil())
			Expect(result).To(Equal([]byte("decoded data")))
		})

		It("should return an error if post request fails", func() {
			// Mock the HTTP service to simulate a failed post request
			patcher.ApplyFunc(httpservice.Post, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusInternalServerError, errors.New("request failed")
			})

			result, err := PostRsaDecrypt(testData)
			Expect(err).ToNot(BeNil())
			Expect(result).To(BeNil())
			Expect(err.Error()).To(ContainSubstring("post request failed: request failed"))
		})

		It("should return an error if response status code is not OK", func() {
			// Mock the HTTP service to simulate a non-OK response
			patcher.ApplyFunc(httpservice.Post, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusBadRequest, nil
			})

			result, err := PostRsaDecrypt(testData)
			Expect(err).ToNot(BeNil())
			Expect(result).To(BeNil())
			Expect(err.Error()).To(ContainSubstring("post request failed with status code: 400"))
		})

		It("should return an error if unmarshaling response fails", func() {
			// Mock the HTTP service to simulate a successful response but with invalid JSON
			patcher.ApplyFunc(httpservice.Post, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte("invalid json"), nil, http.StatusOK, nil
			})

			result, err := PostRsaDecrypt(testData)
			Expect(err).ToNot(BeNil())
			Expect(result).To(BeNil())
			Expect(err.Error()).To(ContainSubstring("post request failed: invalid character"))
		})
	})

	Context("when marshaling data fails", func() {
		It("should return an error if data cannot be marshaled", func() {
			// Mock json.Marshal to return an error
			patcher.ApplyFunc(json.Marshal, func(v interface{}) ([]byte, error) {
				return nil, errors.New("marshal error")
			})

			result, err := PostRsaDecrypt(testData)
			Expect(err).ToNot(BeNil())
			Expect(result).To(BeNil())
			Expect(err.Error()).To(ContainSubstring("failed to marshal request data: marshal error"))
		})
	})
})

/* Ended by AICoder, pid:d6627k271a953b4141e608038069697881b96492 */
