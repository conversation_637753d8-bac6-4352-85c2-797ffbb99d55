package iks

import (
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/httpservice"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

func GetEncryptedPrivateKey() (*RSAPrivateKey, error) {
	body, _, _, err := httpservice.Get(AuthRsaPrivatekey, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("get rsa privateKey from auth failed," + err.Error())
	}

	keys := &RSAPrivateKey{}
	err = json.Unmarshal(body, keys)
	if err != nil {
		return nil, errors.New("get rsa privateKey from auth json.Unmarshal error," + err.Error())
	}
	return keys, nil
}

func GetEncryptedPublicKey() (*RSAPublicKey, error) {
	body, _, _, err := httpservice.Get(AuthRsaPublickey, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("get rsa publicKey from auth failed," + err.<PERSON>rror())
	}

	keys := &RSAPublicKey{}
	err = json.Unmarshal(body, keys)
	if err != nil {
		return nil, errors.New("get rsa publicKey from auth json.Unmarshal error," + err.Error())
	}

	return keys, nil
}

/* Started by AICoder, pid:b0de877b2fv022a146e80a28e0cf4b155c29390d */
func PostRsaDecrypt(requestData *RsaDecrypt) ([]byte, error) {
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, errors.New("failed to marshal request data: " + err.Error())
	}
	resp, _, rspCode, err := httpservice.Post(IksRsaDecrypt, restful.DefaultHeaders(), jsonData, nil)
	if err != nil {
		return nil, errors.New("post request failed: " + err.Error())
	}
	if rspCode != http.StatusOK {
		return nil, fmt.Errorf("post request failed with status code: %d", rspCode)
	}
	var responseData RsaDecryptDecode
	if err = json.Unmarshal(resp, &responseData); err != nil {
		logger.Errorf("get openpalette auth from keystone json.Unmarshal failed: %v", err)
		return nil, errors.New("post request failed: " + err.Error())
	}
	return responseData.Decrypt_data, nil
}

/* Ended by AICoder, pid:b0de877b2fv022a146e80a28e0cf4b155c29390d */
