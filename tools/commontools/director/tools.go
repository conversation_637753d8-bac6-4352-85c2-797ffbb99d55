package director

import (
	"encoding/json"
	"strings"
)

func ParseErrorResponse(err error) string {
	if err == nil {
		return ""
	}
	dirErr := struct {
		Error string `json:"error"`
	}{}
	if e := json.Unmarshal([]byte(err.<PERSON>rror()), &dirErr); e != nil {
		return err.<PERSON>rror()
	}
	return dirErr.Error
}

func ParseMessageResponse(err error) string {
	if err == nil {
		return ""
	}
	dirErr := struct {
		Message string `json:"message"`
	}{}
	if e := json.Unmarshal([]byte(err.Error()), &dirErr); e != nil {
		return err.Error()
	}
	return dirErr.Message
}

func ParseQueryParams(params map[string]string) string {
	if len(params) == 0 {
		return ""
	}
	var values []string
	for k, v := range params {
		values = append(values, k + "=" + v)
	}
	return "?" + strings.Join(values, "&")
}
