package swr

import (
	"cwsm/tools/commontools/authentication"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

type SwrInfo struct {
	Url      string        `json:"url"`
	UserRole string        `json:"userRole"`
	EnvType  string        `json:"envType"`
	TokenReq SwrLogginInfo `json:"tokenReq"`
}

type SwrLogginInfo struct {
	UserName string           `json:"userName"`
	UserType string           `json:"userType"`
	Password string           `json:"password"`
	Domain   *string          `json:"domain,omitempty"`
	Project  *string          `json:"project,omitempty"`
	SslAuth  *restful.SSLAuth `json:"sslAuth,omitempty"`
}

func (swrLoggingInfo *SwrLogginInfo) ToOsClient() *authentication.OsClient {
	osClient := &authentication.OsClient{}
	osClient.UserName = swrLoggingInfo.UserName
	osClient.Password = swrLoggingInfo.Password
	osClient.TenantName = *swrLoggingInfo.Project
	osClient.Scope = *swrLoggingInfo.Domain
	osClient.SslAuthentication = swrLoggingInfo.SslAuth
	return osClient
}
