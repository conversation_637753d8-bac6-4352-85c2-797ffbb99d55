package pvrm

import (
	"cwsm/tools/commontools/director"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/httpservice"
	"encoding/json"
	"fmt"
	"strings"
)

func GetContainerDetail(uuid string) *ContainerDetail {
	body, _, _, err := httpservice.Get(PvrmContainerDetail+uuid, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetContainerDetail] Failed to get container cloud detail from pvrm, uuid:%s err:%s", uuid, err)
		return nil
	}
	containerDetail := &ContainerDetail{}
	err = json.Unmarshal(body, containerDetail)
	if err != nil {
		logger.Errorf("PvrmProvider GetContainerDetail json.Unmarshal failed, uuid:%s err:%s", uuid, err)
		return nil
	}
	return containerDetail
}

func GetContainerEnvs() *CloudQueryRSP {
	body, _, _, err := httpservice.Get(PvrmContainerEnvs+"?includeKO=true", httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetContainerEnvs] Failed to get container cloudQueryRSP detail from pvrm, err:%s", err)
		return nil
	}
	logger.Infof("PvrmProvider GetContainerEnvs GET res body:%s", string(body))
	cloudQueryRSP := &CloudQueryRSP{}
	err = json.Unmarshal(body, cloudQueryRSP)
	if err != nil {
		logger.Errorf("PvrmProvider GetContainerEnvs json.Unmarshal failed, err:%s", err)
		return nil
	}
	return cloudQueryRSP
}

func GetInnerContainerEnvs() (*CCloudQueryRSP, error) {
	body, _, _, err := httpservice.Get(PvrmPrefix+"/inner/containerclouds?original=true", httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetInnerContainerEnvs] failed to get container clouds from pvrm, err:%s", err)
		return nil, err
	}
	cloudQueryRSP := &CCloudQueryRSP{}
	err = json.Unmarshal(body, cloudQueryRSP)
	if err != nil {
		logger.Errorf("PvrmProvider GetContainerEnvs json.Unmarshal failed, err:%s", err)
		return nil, err
	}
	logger.Infof("[GetInnerContainerEnvs] size: %d", len(cloudQueryRSP.Containerclouds))
	return cloudQueryRSP, nil
}

func GetContainerEnvsUnderDc(dcId string) (*CloudQueryRSP, error) {
	if len(dcId) == 0 {
		return GetContainerEnvs(), nil
	}
	body, _, _, err := httpservice.Get(PvrmContainerEnvs+"?includeKO=true&dcId="+dcId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetContainerEnvs] Failed to get container cloudQueryRSP detail from pvrm, err:%s", err.Error())
		return nil, fmt.Errorf("get container clouds from pvrm, failed: %v", err.Error())
	}
	logger.Infof("PvrmProvider GetContainerEnvs GET res body:%s", string(body))
	cloudQueryRSP := &CloudQueryRSP{}
	err = json.Unmarshal(body, cloudQueryRSP)
	if err != nil {
		logger.Errorf("PvrmProvider GetContainerEnvs json.Unmarshal failed, err:%s", err)
		return nil, fmt.Errorf("get container clouds json unmarshal error: %v", err.Error())
	}
	return cloudQueryRSP, nil
}

func GetContainerNodesById(envId, token string) (*ContainerNodes, error) {
	header := httpservice.DefaultHeaders()
	if len(token) > 0 {
		header[globalcv.HeaderXAuthToken] = token
	}

	body, _, _, err := httpservice.Get(PvrmPrefix+"/containernodes?containeruuid="+envId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetContainerNodesById] failed to get container nodes from pvrm, err: %v", err.Error())
		return nil, err
	}

	nodesInfo := &ContainerNodes{}
	err = json.Unmarshal(body, nodesInfo)
	if err != nil {
		logger.Errorf("[GetContainerNodesById] json.Unmarshal failed, err:%s", err)
		return nil, err
	}
	logger.Infof("[GetContainerNodesById] size: %d", len(nodesInfo.Nodes))

	return nodesInfo, nil
}

func GetContainerTenantsById(envId, token string) (*TenantsInfo, error) {
	header := httpservice.DefaultHeaders()
	if len(token) > 0 {
		header[globalcv.HeaderXAuthToken] = token
	}

	body, _, _, err := httpservice.Get(PvrmContainerTenants+envId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetContainerTenantsById] failed to get container tenants from pvrm, err: %v", err.Error())
		return nil, err
	}

	tenantsInfo := &TenantsInfo{}
	err = json.Unmarshal(body, tenantsInfo)
	if err != nil {
		logger.Errorf("[GetContainerTenantsById] json.Unmarshal failed, err: %v", err.Error())
		return nil, err
	}

	return tenantsInfo, nil
}

func GetContainerTenantById(envId, tenantId, token string) (*TenantInfo, error) {
	header := httpservice.DefaultHeaders()
	if len(token) > 0 {
		header[globalcv.HeaderXAuthToken] = token
	}

	body, _, _, err := httpservice.Get(fmt.Sprintf(PvrmContainerTenant, envId, tenantId), httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetContainerTenantById] failed to get container tenant: %s from pvrm, err: %v", tenantId, err.Error())
		return nil, err
	}

	tenantInfo := &TenantInfo{}
	err = json.Unmarshal(body, tenantInfo)
	if err != nil {
		logger.Errorf("[GetContainerTenantById] json.Unmarshal failed, err: %v", err.Error())
		return nil, err
	}

	return tenantInfo, nil
}

func NotifyCreateEnv(request interface{}) error {
	logger.Infof("[NotifyCreateEnv] pvrm body:%s", strings.ReplaceAll(util.ToStr(request), "assword", ""))
	_, _, _, err := httpservice.Post(PvrmContainerEnvs+"?notify=true", httpservice.DefaultHeaders(), request, nil)
	if err != nil {
		logger.Errorf("[NotifyCreateEnv] POST failed, cloud: %s, error: %v",
			strings.ReplaceAll(util.ToStr(request), "assword", ""), err)
		return fmt.Errorf(director.ParseErrorResponse(err))
	}
	return nil
}

func DeleteTenantById(envId, tenantId string) error {
	logger.Infof("[DeleteTenantById] pvrm id:%s", envId)
	_, _, _, err := httpservice.Delete(fmt.Sprintf(PvrmDeleteTenant, envId, tenantId), httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[DeleteTenantById] pvrm id:%s, error: %v", envId, err)
		return fmt.Errorf(director.ParseErrorResponse(err))
	}

	return nil
}

func NotifyDeleteEnv(envId string) error {
	logger.Infof("[NotifyDeleteEnv] pvrm id:%s", envId)
	_, _, _, err := httpservice.Delete(fmt.Sprintf(urlEnvsDelNotify, envId),
		httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[NotifyDeleteEnv] pvrm id:%s, error: %v", envId, err)
		return fmt.Errorf(director.ParseErrorResponse(err))
	}
	return nil
}

func GetContainerResources(token string) *ContainerCloudsResource {
	header := httpservice.DefaultHeaders()
	if len(token) > 0 {
		header[globalcv.HeaderAccessToken] = token
	}
	body, _, _, err := httpservice.Get(PvrmContainerResources, header, nil)
	if err != nil {
		logger.Errorf("[GetContainerResouces failed to from pvrm, err:%s", err)
		return nil
	}
	logger.Debugf("PvrmProvider GetContainerResouces get res body:%s", string(body))
	ccr := &ContainerCloudsResource{}
	err = json.Unmarshal(body, ccr)
	if err != nil {
		logger.Errorf("PvrmProvider GetContainerResouces json.Unmarshal failed, err:%s", err)
		return nil
	}
	return ccr
}
