package pvrm

import (
	"cwsm/tools/commontools/director/swr"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

// pvrm接口
const (
	PvrmPrefix             = "/api/v1.0/pvrm"
	PvrmContainerDetail    = PvrmPrefix + "/inner/containerclouds/"
	PvrmContainerEnvs      = PvrmPrefix + "/containerclouds"
	PvrmContainerTenants   = PvrmPrefix + "/containertenants/"
	PvrmContainerTenant    = PvrmContainerTenants + "%s/tenants/%s"
	urlEnvsDelNotify       = PvrmPrefix + "/containerclouds/%s?notify=true"
	PvrmDeleteTenant       = PvrmPrefix + "/containertenants/%s/tenants/%s"
	PvrmContainerResources = PvrmPrefix + "/container_resources"
)

type ContainerDetail struct {
	Area              string           `json:"area,omitempty"`
	Name              string           `json:"name"`
	Uuid              string           `json:"uuid"`
	UserName          string           `json:"userName"`
	EnvType           string           `json:"envType"`
	Password          string           `json:"password"`
	Domain            string           `json:"domain"`
	DefaultTenant     string           `json:"defaultTenant"`
	Uri               string           `json:"uri"`
	KeystoneVersion   string           `json:"keystoneVersion"`
	SslAuthentication *restful.SSLAuth `json:"sslAuthentication"`
}

type CloudQueryRSP struct {
	Containerclouds []ContainerDetail `json:"containerclouds"`
}

func (containerDetail *ContainerDetail) ToSwrInfo(reqDataUserRole string) *swr.SwrInfo {
	return &swr.SwrInfo{
		Url:      containerDetail.Uri,
		UserRole: reqDataUserRole,
		EnvType:  containerDetail.EnvType,
		TokenReq: swr.SwrLogginInfo{
			UserName: containerDetail.UserName,
			UserType: "local",
			Password: containerDetail.Password,
			Domain:   &containerDetail.Domain,
			Project:  &containerDetail.DefaultTenant,
			SslAuth:  containerDetail.SslAuthentication}}
}

type CCloudQueryRSP struct {
	Containerclouds []*CCloudDetail `json:"containerclouds"`
}

type CCloudDetail struct {
	DefaultTenant   string                 `json:"defaultTenant" pgdb:"default_tenant"`
	URI             string                 `json:"uri" pgdb:"uri"`
	EnvType         string                 `json:"envType" pgdb:"env_type"`
	Provider        string                 `json:"provider" pgdb:"provider"`
	Status          string                 `json:"status" pgdb:"status"`
	CreatedAt       string                 `json:"created_at,omitempty" pgdb:"created_at,omitempty"`
	UpdatedAt       string                 `json:"updated_at,omitempty" pgdb:"updated_at,omitempty"`
	UUID            string                 `json:"uuid" pgdb:"uuid"`
	Name            string                 `json:"name" pgdb:"name"`
	UserName        string                 `json:"userName" pgdb:"user_name"`
	DcID            string                 `json:"dcId" pgdb:"dc_id"`
	ResourceType    string                 `json:"ResourceType" pgdb:"resource_type"`
	DcName          string                 `json:"dcName" pgdb:"dc_name"`
	RelatedEnvID    string                 `json:"relatedEnvId" pgdb:"related_env_id"`
	RelatedEnvName  string                 `json:"relatedEnvName" pgdb:"related_env_name"`
	Area            string                 `json:"area" pgdb:"area"`
	Description     string                 `json:"description" pgdb:"description"`
	Meta            map[string]interface{} `json:"meta" pgdb:"meta"`
	Labels          []Label                `json:"labels" pgdb:"labels"`
	Domain          string                 `json:"domain" pgdb:"domain"`
	KeystoneVersion string                 `json:"keystoneVersion" pgdb:"keystone_version"`
	Links           []*Link                `json:"links" pgdb:"links"`
	SSLAuth         restful.SSLAuth        `json:"sslAuthentication,omitempty" pgdb:"ssl_auth,omitempty"`
	VimConfig       VimConfig              `json:"vimIdInfo,omitempty" pgdb:"vim_config,omitempty"`
	VimVersion      VimVersion             `json:"versionNoInfo,omitempty" pgdb:"vim_version,omitempty"`
	OpapiVersion    string                 `json:"paasVersion,omitempty" pgdb:"paas_version,omitempty"`
	Password        string                 `json:"password,omitempty"`
	DcLocation      string                 `json:"dcLocation,omitempty"`
	DcRegion        string                 `json:"dcRegion,omitempty"`
	AppCount        int                    `json:"appCount"`
	ClusterCount    int                    `json:"clusterCount"`
	NodeCount       int                    `json:"nodeCount"`
	TenantCount     int                    `json:"tenantCount"`
	PodCount        int                    `json:"podCount"`
	ServiceCount    int                    `json:"serviceCount"`
	KmsKeyID        string                 `json:"kmsKeyID,omitempty"`
	MsbInfo         MsbPortDao             `json:"msbInfo,omitempty"`
}

func (env *CCloudDetail) FindAuthUrl() string {
	for _, l := range env.Links {
		if l.Rel == "self" {
			return l.URI
		}
	}
	return ""
}

type Link struct {
	Rel string `json:"rel"`
	URI string `json:"uri"`
}

type Label struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type VimConfig struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Location    string `json:"location"`
	Ip          string `json:"ip"`
	Description string `json:"description"`
	Integrator  string `json:"integrator"`
	NorthType   string `json:"northType"`
	PimId       string `json:"pimId"`
}

type VimVersion struct {
	BigVersionNo   string `json:"bigVersionNo"`
	VersionNo      string `json:"versionNo"`
	ShortVersionNo string `json:"shortVersionNo"`
	TcfScenario    string `json:"tcfScenario"`
	ServiceSet     string `json:"serviceSet"`
	ProductName    string `json:"productName"`
}

type MsbPortDao struct {
	CreatedAt   string `json:"created_at,omitempty" pgdb:"created_at,nonempty"`
	UpdatedAt   string `json:"updated_at,omitempty" pgdb:"updated_at,nonempty"`
	ContainerIP string `json:"container_ip" pgdb:"cloud_ip"`
	UUID        string `json:"uuid" pgdb:"uuid"`
	MsbPort     int    `json:"msb_port" pgdb:"msb_port"`
	CloudUUID   string `json:"containerUuid" pgdb:"cloud_uuid"`
}

type ContainerNodes struct {
	Nodes []*NodeDto `json:"nodes"`
}

type NodeDto struct {
	*NodeCommon
	VdcID   string `json:"vdcId"`
	VdcName string `json:"vdcName"`
}

type NodeCommon struct {
	CreatedAt     string            `json:"created_at"`
	UpdatedAt     string            `json:"updated_at"`
	UUID          string            `json:"uuid"`
	Name          string            `json:"name"`
	Used          string            `json:"used"`
	Status        string            `json:"status"`
	DcID          string            `json:"dcId"`
	CloudUUID     string            `json:"containerUuid"`
	CloudName     string            `json:"containerName"`
	ClusterUUID   string            `json:"clusterUuid"`
	ClusterName   string            `json:"clusterName"`
	DcName        string            `json:"dcName"`
	TenantID      string            `json:"tenantId"`
	IP            *IpCommon         `json:"ip"`
	Roles         []*string         `json:"roles"`
	Belongs       map[string]string `json:"belongs"`
	Labels        map[string]string `json:"nodeLabels"`
	ResourceStats *ResourceStats    `json:"resource_stats"`
}

type ResourceStats struct {
	CPU         *RStats `json:"cpu"`
	Memory      *RStats `json:"memory"`
	FileSystem  *RStats `json:"file_system"`
	CollectTime string  `json:"collect_time"`
}

type RStats struct {
	Allocatable float32 `json:"allocatable"`
	Capacity    float32 `json:"capacity"`
	Request     float32 `json:"request"`
	Limit       float32 `json:"limit"`
	Available   float32 `json:"available"`
}

type IpCommon struct {
	NetAPI string `json:"net_api"`
	NetMgt string `json:"net_mgt"`
}

type TenantsInfo struct {
	Tenants []*TenantInfo `json:"tenants"`
}

type TenantInfo struct {
	ID                  string `json:"id"`
	Name                string `json:"name"`
	Description         string `json:"description"`
	Self                string `json:"self"`
	Status              string `json:"status"`
	Message             string `json:"message"`
	CreatedBy           string `json:"createdBy"`
	CreatedAt           string `json:"createdAt"`
	Owner               string `json:"owner"`
	UpdatedAt           int64  `json:"UpdateAt"`
	MemberCount         int    `json:"memberCount"`
	ResourceType        string `json:"resourceType"`
	PrivateQuotaStatus  string `json:"privateQuotaStatus"`
	SystemPrefabricated bool   `json:"systemPrefabricated"`
	HTTPDisable         bool   `json:"httpDisable"`
	AdminEnterProject   bool   `json:"adminEnterProject"`
	NeedServiceAccount  bool   `json:"needServiceAccount"`
}

type ContainerCloudsResource struct {
	Clusters    int                     `json:"clusters"`
	AbClusters  int                     `json:"abnormalClusters"`
	Nodes       int                     `json:"nodes"`
	AbNodes     int                     `json:"abnormalNodes"`
	Apps        int                     `json:"apps"`
	AbApps      int                     `json:"abnormalApps"`
	Containers  int                     `json:"containers"`
	Tenants     int                     `json:"tenants"`
	AbTenants   int                     `json:"abnormalTenants"`
	Pods        int                     `json:"pods"`
	AbPods      int                     `json:"abnormalPods"`
	Services    int                     `json:"services"`
	EnvResource []*ContainerEnvResource `json:"envResource"`
	DcResource  []*ContainerDcResource  `json:"dcResource"`
}

type ContainerEnvResource struct {
	CloudId    string `json:"cloudId"`
	DcId       string `json:"dcId"`
	Clusters   int    `json:"clusters"`
	AbClusters int    `json:"abnormalClusters"`
	Nodes      int    `json:"nodes"`
	AbNodes    int    `json:"abnormalNodes"`
	Apps       int    `json:"apps"`
	AbApps     int    `json:"abnormalApps"`
	Tenants    int    `json:"tenants"`
	AbTenants  int    `json:"abnormalTenants"`
	Pods       int    `json:"pods"`
	AbPods     int    `json:"abnormalPods"`
	Services   int    `json:"services"`
}

type ContainerDcResource struct {
	DcId       string `json:"dcId,omitempty"`
	Clusters   int    `json:"clusters"`
	AbClusters int    `json:"abnormalClusters"`
	Nodes      int    `json:"nodes"`
	AbNodes    int    `json:"abnormalNodes"`
	Apps       int    `json:"apps"`
	AbApps     int    `json:"abnormalApps"`
	Tenants    int    `json:"tenants"`
	AbTenants  int    `json:"abnormalTenants"`
	Pods       int    `json:"pods"`
	AbPods     int    `json:"abnormalPods"`
	Services   int    `json:"services"`
}
