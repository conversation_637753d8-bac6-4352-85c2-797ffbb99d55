package cloudfuze

import (
	"cwsm/tools/commontools/commondao/common_cloud_env"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

const (
	CloudfuzePrefix          = "/api/v1.0/cloudfuze"
	CloudfuzeEnvs            = CloudfuzePrefix + "/cloudenvs"
	CloudfuzeMgmtConfig      = CloudfuzePrefix + "/cmp/mgmtconfig/"
	CloudfuzeDataCenterBasic = CloudfuzePrefix + "/dcsIdName"
	CloudfuzeFmtMsbByEnv     = CloudfuzePrefix + "/msbInfos/%s"
	EmptyPlaceholder         = ""
)

type NetCloudRSP struct {
	NetClouds []NetCloudDetail `json:"netclouds"`
}

type NetCloudDetail struct {
	Name              string           `json:"name"`
	Uuid              string           `json:"uuid"`
	UserName          string           `json:"userName"`
	EnvType           string           `json:"envType"`
	Password          string           `json:"password"`
	Domain            string           `json:"domain"`
	DefaultTenant     string           `json:"defaultTenant"`
	Uri               string           `json:"uri"`
	KeystoneVersion   string           `json:"keystoneVersion"`
	SslAuthentication *restful.SSLAuth `json:"sslAuthentication"`
}

type CloudDetailList struct {
	CloudEnvs []*CloudInfo `json:"cloudenvs"`
}

type CloudDetail struct {
	CloudEnv CloudInfo `json:"cloudenv"`
}

type CloudInfo struct {
	common_cloud_env.CloudCommon
	DcName string `json:"dcname"`
}

type MgmtConfig struct {
	MgmtId            string           `json:"MGMTID"`
	SslAuthentication *restful.SSLAuth `json:"sslAuthentication"`
}

type DcBasicInfoList struct {
	Dcs []*DataCenterBasicInfo `json:"dcs"`
}

type DataCenterBasicInfo struct {
	DcID          string          `json:"id,omitempty"`
	DcName        string          `json:"name"`
	Description   string          `json:"description"`
	Location      string          `json:"location"`
	Region        string          `json:"region"`
	CloudType     string          `json:"cloudType"`
	DcGroup       string          `json:"dcGroup"`
	Areas         []string        `json:"areas"`
	OwnerId       string          `json:"ownerId"`
	CreateAt      string          `json:"create_at"`
	UpdateAt      string          `json:"update_at"`
	SyncFrom      string          `json:"sync_from"`
	DcGroupIdName *CloudEnvIdName `json:"dcGroupInfo"` // temporarily apply the obj CloudEnvIdName as the obj DcGroupIdName;
}

type DcBasicInfoWithEnvs struct {
	*DataCenterBasicInfo
	CloudEnvs []*CloudEnvIdName `json:"cloudEnvs,omitempty"`
}

type CloudEnvIdName struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type DcGroupCommonInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	DisplayName string `json:"displayName,omitempty"`
	Description string `json:"description"`
}

type DcGroupRes struct {
	DcGroupCommonInfo
	CreateAt              string   `json:"create_at"`
	UpdateAt              string   `json:"update_at"`
	DirectorDomainTypeIDs []string `json:"director.domaintypeids"`
}

type DataCenterDao struct {
	DcCommon
	DcGroup  string   `json:"dcGroup" pgdb:"dc_group"`
	Areas    []string `json:"areas" pgdb:"areas"`
	OwnerId  string   `json:"ownerId" pgdb:"owner_id"`
	CreateAt string   `json:"create_at" pgdb:"create_at"`
	UpdateAt string   `json:"update_at" pgdb:"update_at"`
	SyncFrom string   `json:"sync_from" pgdb:"sync_from"`
}

type DcCommon struct {
	DcID        string `json:"id,omitempty" pgdb:"id"`
	DcName      string `json:"name" pgdb:"name"`
	Description string `json:"description" pgdb:"description"`
	Location    string `json:"location" pgdb:"location"`
	Region      string `json:"region" pgdb:"region"`
	CloudType   string `json:"cloudType" pgdb:"cloud_type"`
}

type MsbPortDao struct {
	CreatedAt string `json:"created_at,omitempty" pgdb:"created_at,nonempty"`
	UpdatedAt string `json:"updated_at,omitempty" pgdb:"updated_at,nonempty"`
	CloudIP   string `json:"cloud_ip" pgdb:"cloud_ip"`
	ID        string `json:"id" pgdb:"id"`
	MsbPort   int    `json:"msb_port" pgdb:"msb_port"`
	CloudID   string `json:"cloud_id" pgdb:"cloud_id"`
}
