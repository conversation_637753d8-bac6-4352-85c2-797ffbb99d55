package cloudfuze

import (
	"cwsm/tools/commontools/cloudtoken"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/httpservice"
	"encoding/json"
	"fmt"
)

func SyncEnvs2Cloudfuze(request interface{}, microserver string) bool {
	_, _, statue, err := httpservice.Post(CloudfuzePrefix+"/cloudenvs/syncfromold?microserver="+microserver,
		httpservice.DefaultHeaders(), request, nil)
	if err != nil {
		logger.Errorf("SyncEnvs2Cloudfuze failed, request:%s, err:%v",
			util.ToJSONStr(request), err)
		return false
	}
	return statue == 200
}

func GetNetCloudEnvs() *NetCloudRSP {
	body, _, _, err := httpservice.Get(CloudfuzeEnvs, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetNetCloudEnvs] Failed to get net clouds detail from cloudfuze, err:%v", err.Error())
		return nil
	}

	cloudfuzeRSP := &NetCloudRSP{}
	err = json.Unmarshal(body, cloudfuzeRSP)
	if err != nil {
		logger.Errorf("Get network env, json.Unmarshal failed, err:%s", err)
		return nil
	}
	return cloudfuzeRSP
}

func GetCloudById(cloudId string) (*CloudDetail, error) {
	body, _, _, err := httpservice.Get(CloudfuzeEnvs+"/"+cloudId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetContainerCloudById] failed to get container pool from cloudfuze, err: %v", err.Error())
		return nil, err
	}

	cloudDetail := &CloudDetail{}
	err = json.Unmarshal(body, cloudDetail)
	if err != nil {
		logger.Errorf("[GetContainerCloudById] failed to unmarshal json body: %v", err.Error())
		return nil, err
	}
	return cloudDetail, nil
}

func GetCloudEnvs(token, dcId string, envTypes ...string) (*CloudDetailList, error) {
	header := httpservice.DefaultHeaders()
	if len(token) > 0 {
		header[globalcv.HeaderAccessToken] = token
	}

	envs := &CloudDetailList{}
	if len(envTypes) == 0 {
		url := CloudfuzeEnvs
		if len(dcId) > 0 {
			url = fmt.Sprintf("%s?dcId=%s", url, dcId)
		}
		body, _, _, err := httpservice.Get(url, header, nil)
		if err != nil {
			logger.Errorf("[GetCloudEnvs] failed to get cloud envs from cloudfuze, err: %v", err.Error())
			return nil, err
		}
		err = json.Unmarshal(body, envs)
		if err != nil {
			logger.Errorf("[GetCloudEnvs] json unmarshal error: %v", err.Error())
			return nil, err
		}
	}

	for _, envType := range envTypes {
		url := fmt.Sprintf("%s?envType=%s", CloudfuzeEnvs, envType)
		if len(dcId) > 0 {
			url = fmt.Sprintf("%s&dcId=%s", url, dcId)
		}
		body, _, _, err := httpservice.Get(url, header, nil)
		if err != nil {
			logger.Errorf("[GetCloudEnvs] failed to get cloud envs from cloudfuze, err: %v", err.Error())
			return nil, err
		}
		envsTmp := &CloudDetailList{}
		err = json.Unmarshal(body, envsTmp)
		if err != nil {
			logger.Errorf("[GetCloudEnvs - envType = %s] json unmarshal error: %v", envType, err.Error())
			return nil, err
		}
		envs.CloudEnvs = append(envs.CloudEnvs, envsTmp.CloudEnvs...)
	}

	if len(envs.CloudEnvs) == 0 {
		envs.CloudEnvs = []*CloudInfo{}
	}

	return envs, nil
}

func GetTokenBodyByEnvId(etv *cloudtoken.EtvKey) ([]byte, error) {
	body, _, _, err := httpservice.Post(CloudfuzeEnvs+"/"+etv.EnvId+"/tokens", httpservice.DefaultHeaders(), etv.ToTokenQuery(), nil)
	if err != nil {
		logger.Errorf("[GetTokenBodyByEnvId] failed: %v", err.Error())
		return nil, err
	}
	return body, nil
}

func GetTokenBodyByEnvIdAndTenantId(etv *cloudtoken.EtvKey) ([]byte, error) {
	body, _, _, err := httpservice.Post(CloudfuzeEnvs+"/"+etv.EnvId+"/tenants/"+etv.TenantId+"/tokens", httpservice.DefaultHeaders(), etv.ToTokenQuery(), nil)
	if err != nil {
		logger.Errorf("[GetTokenBodyByEnvIdAndTenantId] failed: %v", err.Error())
		return nil, err
	}
	return body, nil
}

func GetTokenBodyByEnvIdAndVdcId(etv *cloudtoken.EtvKey) ([]byte, error) {
	body, _, _, err := httpservice.Post(CloudfuzeEnvs+"/"+etv.EnvId+"/vdcs/"+etv.VdcId+"/tokens", httpservice.DefaultHeaders(), etv.ToTokenQuery(), nil)
	if err != nil {
		logger.Errorf("[GetTokenBodyByEnvIdAndVdcId] failed: %v", err.Error())
		return nil, err
	}
	return body, nil
}

func GetPublicKey4U31Impl(sourceURL string) (string, error) {
	body, _, _, err := httpservice.Get(sourceURL, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetPublicKey4U31Impl] failed: %v", err.Error())
		return EmptyPlaceholder, err
	}

	return string(body), nil
}

func GetMgmtConfigById(mgmtId string) (*MgmtConfig, error) {
	body, _, _, err := httpservice.Get(CloudfuzeMgmtConfig+mgmtId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetMgmtConfigById] failed to get mgmt config from cloudfuze, err: %v", err.Error())
		return nil, err
	}

	config := &MgmtConfig{}
	err = json.Unmarshal(body, config)
	if err != nil {
		logger.Errorf("[GetMgmtConfigById] failed to unmarshal json body: %v", err.Error())
		return nil, err
	}

	return config, nil
}

func GetDcBasicInfoList(token string, cloudType ...string) (*DcBasicInfoList, error) {
	header := httpservice.DefaultHeaders()
	if len(token) > 0 {
		header[globalcv.HeaderAccessToken] = token
	}

	url := CloudfuzeDataCenterBasic
	if len(cloudType) > 0 {
		url = url + "?cloudType=" + cloudType[0]
	}

	body, _, _, err := httpservice.Get(url, header, nil)
	if err != nil {
		logger.Errorf("[GetDcBasicInfoList] failed to get Dcs info from cloudfuze, err: %v", err.Error())
		return nil, err
	}

	dcsInfo := &DcBasicInfoList{}
	err = json.Unmarshal(body, dcsInfo)
	if err != nil {
		logger.Errorf("[GetDcBasicInfoList] failed to unmarshal json body: %v", err.Error())
		return nil, err
	}

	return dcsInfo, nil
}

func GetDcBasicInfo(dcId, token string) (*DcBasicInfoWithEnvs, error) {
	header := httpservice.DefaultHeaders()
	if len(token) > 0 {
		header[globalcv.HeaderAccessToken] = token
	}

	body, _, _, err := httpservice.Get(CloudfuzeDataCenterBasic+"/"+dcId, header, nil)
	if err != nil {
		logger.Errorf("[GetDcBasicInfo] failed to get Dc info from cloudfuze, err: %v", err.Error())
		return nil, err
	}

	dcInfo := &DcBasicInfoWithEnvs{}
	err = json.Unmarshal(body, dcInfo)
	if err != nil {
		logger.Errorf("[GetDcBasicInfo] failed to unmarshal json body: %v", err.Error())
		return nil, err
	}

	return dcInfo, nil
}

func GetMsbInfoByEnv(envId, token string) (*MsbPortDao, error) {
	header := httpservice.DefaultHeaders()
	if len(token) > 0 {
		header[globalcv.HeaderAccessToken] = token
	}
	body, _, _, err := httpservice.Get(fmt.Sprintf(CloudfuzeFmtMsbByEnv, envId), header, nil)
	if err != nil {
		logger.Errorf("[GetMsbInfoByEnv] envId:%s get cloudfuze failed: %v", envId, err.Error())
		return nil, err
	}
	msb_port := &MsbPortDao{}
	err = json.Unmarshal(body, msb_port)
	if err != nil {
		logger.Errorf("[GetMsbInfoByEnv] envId:%s unmarshal json body failed: %v", err.Error())
		return nil, err
	}
	return msb_port, nil
}
