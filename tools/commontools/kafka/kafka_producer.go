package kafka

import (
	"github.com/Shopify/sarama"
	"hash"
	"strings"
	"cwsm/tools/commontools/logger"
)

type KafkaProducer struct {
	producer    sarama.AsyncProducer
	kafkaBroker string //kafka地址
	valid       bool   //是否连接成功
}

// director只有一个kafka，所以只需要实例化一个即可
var kafkaProducerInst *KafkaProducer = nil

func GetKafkaProducer() *KafkaProducer {
	return kafkaProducerInst
}

func InitKafkaProducerClient() {
	kafkaProducerInst = &KafkaProducer{
		kafkaBroker: GetKafkaHost(),
		valid:       false,
	}
	kafkaProducerInst.initKafkaProducer()
}

func (client *KafkaProducer) initKafkaProducer() {
	//init config
	config := sarama.NewConfig()
	config.ClientID = GetKafkaClientID()
	config.Producer.Return.Successes = false
	config.Producer.Return.Errors = false
	config.Producer.Partitioner = sarama.NewCustomHashPartitioner(NewMurmur2)
	config.Version = sarama.V0_10_0_1
	//new producer，考虑每5s重试连接kafka消耗性能，未作重试机制，若需要可加入
	producer, err := sarama.NewAsyncProducer(strings.Split(client.kafkaBroker, ","), config)
	if err != nil {
		logger.Errorf("new async producer failed, err : ", err)
		return
	}
	client.producer = producer
	client.valid = true
	logger.Infof("Start KafkaProducer end, host: %s, client id: %s", client.kafkaBroker, GetKafkaClientID())
}

func (client *KafkaProducer) SendKafkaMsg(topic string, value []byte) {
	logger.Debug("SendKafkaMsg topic: %s, value: %s.", topic, string(value))
	if client == nil || client.valid == false {
		logger.Errorf("No Kafka producer!")
		return
	}

	client.producer.Input() <- &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.ByteEncoder(value),
	}
	logger.Infof("SendKafkaMsg topic %s success!", topic)
}

func (client *KafkaProducer) SendKafkaMsgWithKey(topic string, value []byte, key string) bool {
	logger.Debug("SendKafkaMsgWithKey topic: %s, value: %s, key: %s.", topic, string(value), key)
	if client.valid == false {
		logger.Errorf("No Kafka producer!")
		return false
	}

	client.producer.Input() <- &sarama.ProducerMessage{
		Topic: topic,
		Key:   sarama.StringEncoder(key),
		Value: sarama.ByteEncoder(value),
	}
	logger.Infof("SendKafkaMsgWithKey topic %s success!", topic)
	return true
}

func (client *KafkaProducer) AsyncClose() {
	if client.producer != nil {
		client.producer.AsyncClose()
	}
}

func NewMurmur2() hash.Hash32 {
	var s Murmur2 = 0
	return &s
}

type Murmur2 uint32

func (s *Murmur2) Reset()         { *s = 0 }
func (s *Murmur2) Sum32() uint32  { return uint32(*s) }
func (s *Murmur2) Size() int      { return 4 }
func (s *Murmur2) BlockSize() int { return 1 }
func (s *Murmur2) Sum(in []byte) []byte {
	v := uint32(*s)
	return append(in, byte(v>>24), byte(v>>16), byte(v>>8), byte(v))
}

func (s *Murmur2) Write(data []byte) (int, error) {
	*s = Murmur2(murmur2(data))
	return len(data), nil
}

func murmur2(data []byte) uint32 {
	length := len(data)
	const (
		seed uint32 = 0x9747b28c
		// 'm' and 'r' are mixing constants generated offline.
		// They're not really 'magic', they just happen to work well.
		m = 0x5bd1e995
		r = 24
	)

	// Initialize the hash to a random value
	h := seed ^ uint32(length)
	length4 := length / 4

	for i := 0; i < length4; i++ {
		i4 := i * 4
		k := (uint32(data[i4+0]) & 0xff) + ((uint32(data[i4+1]) & 0xff) << 8) + ((uint32(data[i4+2]) & 0xff) << 16) + ((uint32(data[i4+3]) & 0xff) << 24)
		k *= m
		k ^= k >> r
		k *= m
		h *= m
		h ^= k
	}

	// Handle the last few bytes of the input array
	extra := length % 4
	if extra >= 3 {
		h ^= (uint32(data[(length & ^3)+2]) & 0xff) << 16
	}
	if extra >= 2 {
		h ^= (uint32(data[(length & ^3)+1]) & 0xff) << 8
	}
	if extra >= 1 {
		h ^= uint32(data[length & ^3]) & 0xff
		h *= m
	}

	h ^= h >> 13
	h *= m
	h ^= h >> 15

	return h
}
