package kafka

const ConsumeJobNum = 15

type SecretKeyChangeMsg struct {
	KeyID   string `json:"secretKeyId"`
	KeyType string `json:"secretKeyType"`
}

type clientConfig struct {
	id     string
	host   string
	topics []string
}

var kafkaClientConfig = clientConfig{}

func SetKafkaClientConfig(host, id string, topics []string) {
	kafkaClientConfig.host = host
	kafkaClientConfig.id = id
	kafkaClientConfig.topics = topics
}

func GetKafkaHost() string {
	return kafkaClientConfig.host
}

func GetKafkaTopics() []string {
	return kafkaClientConfig.topics
}

func GetKafkaClientID() string {
	return kafkaClientConfig.id
}
