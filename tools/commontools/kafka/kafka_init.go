package kafka

import (
	"cwsm/tools/commontools/logger"
)

// kafka的配置信息
func InitKafka(k KafkaConsumerIF) {
	SetKafkaClientConfig(k.KafkaHost(), k.<PERSON><PERSON>lientID(), k.<PERSON><PERSON>())
	logger.Infof("kafka host: %s, client id: %s, topics: %v", GetKafkaHost(), GetKafkaClientID(), GetKafkaTopics())
	StartKafkaConsumer(k)
	StartKafkaProducer()
}

func NewKafkaProducer(host string) *KafkaProducer {
	producer := &KafkaProducer{kafkaBroker: host, valid: false}
	producer.initKafkaProducer()
	return producer
}
