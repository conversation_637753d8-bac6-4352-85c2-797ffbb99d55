package kafka

import (
	"strconv"
	"sync"
)

type KafkaConsumerIF interface {
	ConsumeTopic(topic string, value []byte, jobId string)
	KafkaHost() string
	KafkaTopics() []string
	KafkaClientID() string
}

func StartKafkaConsumer(k KafkaConsumerIF) {
	KafkaConsumerJobs = make(chan <PERSON>onsumerMsg, ConsumeJobNum)
	ConfigConsumerGroup()
	go ConnectConsumerGroup()
	go StartConsuming(k)
}

func StartConsuming(k KafkaConsumerIF) {
	var wg sync.WaitGroup
	for i := 0; i < ConsumeJobNum; i++ {
		wg.Add(1)
		jobId := "kafka-consumer-goroutine-num:" + strconv.Itoa(i)
		go consumeSingleMsg(k, &wg, jobId)
	}
	wg.Wait()
	close(KafkaConsumerJobs)
}

func consumeSingleMsg(k KafkaConsumerIF, wg *sync.WaitGroup, jobId string) {
	defer wg.Done()
	for cm := range KafkaConsumerJobs {
		k.<PERSON>op<PERSON>(cm.Topic, cm.Value, jobId)
	}
}
