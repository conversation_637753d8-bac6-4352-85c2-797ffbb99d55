package kafka

import (
	"context"
	"github.com/Shopify/sarama"
	"strings"
	"sync"
	"time"
	"cwsm/tools/commontools/logger"
)

type KafkaConsumerMsg struct {
	Topic string
	Value []byte
}

type KafkaConsumer struct {
	ready chan bool
}

func (consumer *KafkaConsumer) Setup(sarama.ConsumerGroupSession) error {
	close(consumer.ready)
	return nil
}

func (consumer *KafkaConsumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

func (consumer *KafkaConsumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for msg := range claim.Messages() {
		logger.Infof("kafka consumed begin,key = %s,topic = %s, Partition = %d, Offset = %d", string(msg.Key), msg.Topic, msg.Partition, msg.Offset)
		session.MarkMessage(msg, "")
		msgElem := KafkaConsumerMsg{msg.Topic, msg.Value}
		KafkaConsumerJobs <- msgElem
	}

	return nil
}

// --------------------------------------------------------------------

const groupId = "director-pvrm-group-1"

var (
	KafkaConsumerJobs chan KafkaConsumerMsg
	consumerGroup     sarama.ConsumerGroup
	config            *sarama.Config
	kafkaAddrs        []string
	kafkaConnectCount = 1
)

func ConfigConsumerGroup() {
	version, err := sarama.ParseKafkaVersion("2.1.1")
	if err != nil {
		logger.Warnf("parsing Kafka version error:", err.Error())
	}
	config = sarama.NewConfig()
	config.Version = version
	kafkaAddrs = strings.Split(GetKafkaHost(), ",")
	if len(kafkaAddrs) < 1 {
		logger.Warnf("No Kafka adress found!")
		return
	}
	consumerGroup, err = sarama.NewConsumerGroup(kafkaAddrs, groupId, config)
	if err != nil {
		for {
			logger.Warnf("creating consumer group client error: ", err.Error())
			time.Sleep(10 * time.Second)
			if consumerGroup, err = sarama.NewConsumerGroup(kafkaAddrs, groupId, config); err == nil {
				break
			}
		}
	}
}

func ConnectConsumerGroup() {
	consumer := KafkaConsumer{ready: make(chan bool)}
	ctx, cancel := context.WithCancel(context.Background())
	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		var err error
		defer wg.Done()
		for {
			if consumerGroup == nil {
				consumerGroup, err = sarama.NewConsumerGroup(kafkaAddrs, groupId, config)
				if err != nil {
					logger.Warnf("reconnect consumer group client error: ", err.Error())
					time.Sleep(10 * time.Second)
					consumerGroup = nil
					continue
				}
				logger.Debugf("connect consumer success")
				kafkaConnectCount = 1
			}
			if err := consumerGroup.Consume(ctx, GetKafkaTopics(), &consumer); err != nil {
				logger.Debugf("Error from consumer: ", err.Error())
				logger.Debugf("kafka client info: ", consumerGroup)
				if kafkaConnectCount >= 6 {
					err = consumerGroup.Close()
					consumerGroup = nil
					continue
				}
				time.Sleep(10 * time.Second)
				kafkaConnectCount++

			} else {
				kafkaConnectCount = 1
			}
			if ctx.Err() != nil {
				cancel()
				return
			}
			consumer.ready = make(chan bool)
		}
	}()

	<-consumer.ready // Await till the consumer has been set up
	logger.Infof("pvrm Kafka consumer is running...")

	select {
	case <-ctx.Done():
		logger.Warnf("terminating: context cancelled")
	}
	cancel()
	wg.Wait()
	if err := consumerGroup.Close(); err != nil {
		logger.Warnf("Error closing client:", err.Error())
	}
}
