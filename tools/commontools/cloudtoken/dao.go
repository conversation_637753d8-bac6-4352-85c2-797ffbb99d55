package cloudtoken

import (
	"fmt"
	"time"
)

const (
	daoKeyFmt = "|%s|-|%s|-|%s|-|%s|-|%s|-|%s|-|%s|-|%s|"
)

var (
	MaxAllowedFailureTimes = 12
	MaxAllowedIdleDuration = 12 * time.Hour
)

func Key(cloudType, url, user, project, domain, userID, projectID, domainID string) string {
	return fmt.Sprintf(daoKeyFmt, cloudType, url, user, project, domain, userID, projectID, domainID)
}

type Dao struct {
	Key           string              `json:"key"`
	Request       *UniversalREQ       `json:"request"`
	Token         map[string]string   `json:"token"`
	Catalog       map[string]*Catalog `json:"catalog,omitempty"`
	ExpiresAt     string              `json:"expires_at"`
	IssuedAt      string              `json:"issued_at"`
	LastVisitedAt string              `json:"last_visited_at"`
	FailureTimes  int                 `json:"failure_times"`
	TenantId      string              `json:"tenantId"`
	UserId        string              `json:"userId"`
}

func (dao *Dao) Expired() bool {
	switch dao.Request.Type {
	case AuthTypeKeystoneV2, AuthTypeKeystoneV3:
		t, _ := time.Parse(time.RFC3339, dao.ExpiresAt)
		if t.Add(Interval).After(time.Now().UTC()) {
			return false
		}
	default:
	}
	return true
}

func (dao *Dao) Redundant() bool {
	if dao.FailureTimes > MaxAllowedFailureTimes {
		return true
	}
	return false
}

func (dao *Dao) Idle() bool {
	t, _ := time.Parse(time.RFC3339, dao.LastVisitedAt)
	if t.Add(MaxAllowedIdleDuration).Before(time.Now().UTC()) {
		return true
	}
	return false
}

func (dao *Dao) Hibernate() *Dao {
	dao.Token = nil
	dao.Catalog = nil
	dao.IssuedAt = ""
	dao.ExpiresAt = ""
	return dao
}

func (dao *Dao) Hibernated() bool {
	return dao.Token == nil && dao.Catalog == nil
}

type Catalog struct {
	ID        string      `json:"id,omitempty"`
	Name      string      `json:"name"`
	Type      string      `json:"type"`
	Endpoints []*Endpoint `json:"endpoints"`
}

func (c *Catalog) FindPublicEndpoint() *Endpoint {
	for _, endpoint := range c.Endpoints{
		if endpoint.Interface == "public" {
			return endpoint
		}
	}
	return nil
}

type Endpoint struct {
	ID        string `json:"id,omitempty"`
	URL       string `json:"url"`
	Region    string `json:"region"`
	RegionID  string `json:"region_id,omitempty"`
	Interface string `json:"interface"`
}
