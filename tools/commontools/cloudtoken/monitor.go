package cloudtoken

import (
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
	"time"
)

var Interval = time.Second * 220

func MonitorTokensTicker(ticker *time.Ticker) bool {
	defer func() {
		if p := recover(); p != nil {
			logger.Errorf("MonitorTokensTicker panic!!!!!:%v", p)
		}
	}()
	logger.Infof("[MonitorTokensTicker] started")
	daos := []*Dao{}
	if err := rediz.HGetAllInstances(storage.RedisKeyToken, &daos); err != nil {
		logger.Errorf("[MonitorTokensTicker] get all tokens from redis error: %v", err)
		return false
	}
	deletings := map[string]struct{}{}
	numHibernated := 0
	for _, dao := range daos {
		if dao.Hibernated() {
			numHibernated++
			continue
		}

		key := dao.Request.Key()
		if dao.Idle() {
			if err := rediz.HSet(storage.RedisKeyToken, key, dao.Hibernate()); err != nil {
				logger.Errorf("[MonitorTokensTicker] hibernate '%s' failure: %v", key, err)
			} else {
				logger.Infof("[MonitorTokensTicker] '%s' is going to hibernate", key)
			}
			continue
		}
		expiresAt, _ := time.Parse(time.RFC3339, dao.ExpiresAt)
		if dao.Expired() || time.Now().UTC().Add(Interval).After(expiresAt) {
			logger.Infof("[MonitorTokensTicker] '%s' expired", key)
			if err := Create(dao.Request, true); err != nil {
				logger.Errorf("[MonitorTokensTicker] Create failed, key: '%s', error: %v", key, err)
				dao.FailureTimes++
				if err1 := rediz.HSet(storage.RedisKeyToken, key, dao); err1 != nil {
					logger.Errorf("[MonitorTokensTicker] key '%s' set FailureTimes:%d failure: %v",
						key, dao.FailureTimes, err1)
				}
			}
		}
		if dao.Redundant() {
			deletings[key] = struct{}{}
			logger.Infof("[MonitorTokensTicker] '%s' is redundant and will be deleted", key)
		}
	}
	for key := range deletings {
		Delete(key)
	}
	logger.Infof("[MonitorTokensTicker] finished, number of hibernated tokens: %d, total: %d", numHibernated, len(daos))
	return true
}
