package cloudtoken

import (
	"cwsm/tools/commontools/commondao/common_cloud_env"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

/* Started by AICoder, pid:739fbe049e35487f99ca98a00278e63d */
func Create(unir *UniversalREQ, force bool) error {
	var lastedVisitedAt string
	dao, err := getTokenFromRedis(unir.Key())
	if err != nil {
		return err
	}
	if dao != nil && !dao.Hibernated() && !force {
		return nil
	}
	lastedVisitedAt = dao.LastVisitedAt

	if isUserOrPasswordEmpty(unir) {
		return fmt.Errorf("user or pwd is empty")
	}

	request, err := unir.ToCloudTokenREQ()
	if err != nil {
		return err
	}

	url := buildURL(unir.URL, request.TokenApi())
	bs, headers, err := PostTokenProvider(url, unir.Type, request, unir.SSLAuth)
	if err != nil {
		return err
	}

	response, token, err := unmarshalResponseAndGetToken(unir.Type, bs, headers)
	if err != nil {
		return err
	}

	dao = response.ToDao(unir, token, lastedVisitedAt)
	if err = setTokenInRedis(unir.Key(), dao); err != nil {
		return err
	}
	return nil
}

func getTokenFromRedis(key string) (*Dao, error) {
	dao := &Dao{}
	err := rediz.HGetInstance(storage.RedisKeyToken, key, dao)
	if err == nil {
		return dao, nil
	}
	logger.Debugf("[Create] get token %s from redis error: %v", key, err)
	return nil, nil
}

func isUserOrPasswordEmpty(unir *UniversalREQ) bool {
	return (unir.UserID == "" && unir.User == "") || (unir.CipherCode == "" && unir.Password == "")
}

func unmarshalResponseAndGetToken(urlType AuthType, bs []byte, headers http.Header) (CloudTokenRSP, string, error) {
	var response CloudTokenRSP
	var token string
	switch urlType {
	case AuthTypeNetInsight:
		response = &NetInsightRSP{}
	case AuthTypeOpenpalette:
		response = &OpenPaletteRSP{}
	case AuthTypeKeystoneV2:
		response = &KeystoneV2RSP{}
	case AuthTypeKeystoneV3:
		response = &KeystoneV3RSP{}
		for k, vs := range headers {
			if strings.EqualFold(k, "x-subject-token") {
				for _, v := range vs {
					token = v
					break
				}
			}
		}
	default:
		return nil, "", fmt.Errorf("unsupported cloud type %s", urlType)
	}
	if err := json.Unmarshal(bs, response); err != nil {
		logger.Errorf("[Create] json.Unmarshal error: %v", err)
		return nil, "", fmt.Errorf("failed to deserilize response: %v", err)
	}
	return response, token, nil
}

func setTokenInRedis(key string, dao *Dao) error {
	err := rediz.HSet(storage.RedisKeyToken, key, dao)
	if err != nil {
		logger.Errorf("[Create] rediz.HSet error: %v", err)
		return fmt.Errorf("store token error: %v", err)
	}
	return nil
}

func buildURL(baseURL, tokenAPI string) string {
	return fmt.Sprintf("%s%s", baseURL, tokenAPI)
}

/* Ended by AICoder, pid:739fbe049e35487f99ca98a00278e63d */

func Delete(key string) {
	logger.Infof("[cloudtoken] Delete key: %s", key)
	_ = rediz.HDel(storage.RedisKeyToken, key)
}

func ReadOnly(key string, catalogs, interfaces []string) (*UniversalRSP, error) {
	dao, err := visit(key)
	if err != nil {
		logger.Errorf("[ReadOnly] key %s from redis error: %v", key, err)
		return nil, err
	}
	if dao.Idle() || dao.Hibernated() {
		logger.Errorf("[ReadOnly] key %s Idle or Hibernated, lastVisitedAt:%s", key, dao.LastVisitedAt)
		return nil, fmt.Errorf("[ReadOnly] key %s Idle or Hibernated, lastVisitedAt:%s", key, dao.LastVisitedAt)
	}
	return getUniversalRSPFromDaoWithFilter(dao, key, catalogs, interfaces)
}

func Load(key string, catalogs, interfaces []string, refresh bool) (*UniversalRSP, error) {
	dao, err := visit(key)
	if err != nil {
		logger.Debugf("[Load] key %s from redis error: %v", key, err)
		return nil, err
	}
	expiresAt, _ := time.Parse(time.RFC3339, dao.ExpiresAt)
	if dao.Idle() || refresh || dao.Hibernated() || dao.Expired() || time.Now().UTC().Add(Interval).After(expiresAt) {
		if err := update(dao); err != nil {
			logger.Debugf("[Load] update key %s error: %v", key, err)
			return nil, fmt.Errorf("refresh token error: %v", err)
		}
	}
	if err := rediz.HSet(storage.RedisKeyToken, key, dao); err != nil {
		logger.Errorf("[Load] redis HSet key %s error: %v", key, err)
		return nil, fmt.Errorf("update token to redis error: %v", err)
	}
	return getUniversalRSPFromDaoWithFilter(dao, key, catalogs, interfaces)
}

func visit(key string) (*Dao, error) {
	dao := &Dao{}
	if err := rediz.HGetInstance(storage.RedisKeyToken, key, dao); err != nil {
		logger.Debugf("[visit] key %s from redis error: %v", key, err)
		return nil, fmt.Errorf("load token from redis error: %v", err)
	}
	dao.LastVisitedAt = time.Now().UTC().Format(time.RFC3339)
	return dao, nil
}

/* Started by AICoder, pid:d4fbced0716a4367b95fe104d3f79d73 */
func getUniversalRSPFromDaoWithFilter(dao *Dao, key string, catalogs, interfaces []string) (*UniversalRSP, error) {
	rsp := &UniversalRSP{Key: key, Token: dao.Token, Catalog: []*Catalog{}, IssuedAt: dao.IssuedAt,
		ExpiresAt: dao.ExpiresAt, SSLAuth: dao.Request.SSLAuth, Request: dao.Request, TenantId: dao.TenantId,
		UserId: dao.UserId}
	intfs := map[string]struct{}{}
	for _, f := range interfaces {
		intfs[strings.ToLower(f)] = struct{}{}
	}

	switch len(catalogs) {
	case 0:
		// No catalogs provided, do nothing
	case 1:
		catName := catalogs[0]
		if strings.EqualFold(catName, "all") {
			processAllCatalogs(dao, rsp, intfs)
		} else if strings.EqualFold(catName, common_cloud_env.CATALOG_OPENPALETTE) {
			processOpenPaletteCatalogs(dao, rsp, intfs)
		} else {
			processSpecificCatalog(dao, rsp, catName, intfs)
		}
	default:
		processMultipleCatalogs(dao, rsp, catalogs, intfs)
	}
	return rsp, nil
}

func processAllCatalogs(dao *Dao, rsp *UniversalRSP, intfs map[string]struct{}) {
	for _, cat := range dao.Catalog {
		rsp.Catalog = append(rsp.Catalog, filterEndpoints(cat, intfs))
	}
}

func processOpenPaletteCatalogs(dao *Dao, rsp *UniversalRSP, intfs map[string]struct{}) {
	if cat, ok := dao.Catalog[common_cloud_env.NO_AUTH_CATALOG]; ok {
		rsp.Catalog = append(rsp.Catalog, filterEndpoints(cat, intfs))
	}
	if cat, ok := dao.Catalog[common_cloud_env.NEED_AUTH_CATALOG]; ok {
		rsp.Catalog = append(rsp.Catalog, filterEndpoints(cat, intfs))
	}
}

func processSpecificCatalog(dao *Dao, rsp *UniversalRSP, catName string, intfs map[string]struct{}) {
	if cat, ok := extractCatalog(dao.Catalog, catName); ok {
		rsp.Catalog = []*Catalog{filterEndpoints(cat, intfs)}
	}
}

func processMultipleCatalogs(dao *Dao, rsp *UniversalRSP, catalogs []string, intfs map[string]struct{}) {
	for _, c := range catalogs {
		if strings.EqualFold(c, common_cloud_env.CATALOG_OPENPALETTE) {
			processOpenPaletteCatalogs(dao, rsp, intfs)
		} else {
			processSpecificCatalog(dao, rsp, c, intfs)
		}
	}
}

/* Ended by AICoder, pid:d4fbced0716a4367b95fe104d3f79d73 */

func extractCatalog(redisCatalog map[string]*Catalog, key string) (*Catalog, bool) {
	cat, ok := redisCatalog[key]
	switch key {
	case globalcv.GlanceName:
		if !ok {
			if cat, ok = redisCatalog[globalcv.ImageName]; ok {
				cat.Name = globalcv.GlanceName
			}
		}
	}
	return cat, ok
}

func ParseOpenpalette(cata *Catalog, ssl *restful.SSLAuth) bool {
	return ((ssl == nil || ssl.Method != "two-way") &&
		strings.ToLower(cata.Name) == common_cloud_env.CATALOG_OPENPALETTE &&
		strings.ToLower(cata.Type) == common_cloud_env.NO_AUTH_TYPE) ||
		(ssl != nil && ssl.Method == "two-way" &&
			strings.ToLower(cata.Name) == common_cloud_env.CATALOG_OPENPALETTE &&
			strings.ToLower(cata.Type) == common_cloud_env.NEED_AUTH_TYPE)
}

func ParseErrMsg(authType AuthType, res string) error {
	msg, bs := "", []byte(res)
	switch authType {
	case AuthTypeKeystoneV2, AuthTypeKeystoneV3:
		errMsg := &struct {
			Error struct {
				Title   string `json:"title"`
				Message string `json:"message"`
			} `json:"error"`
		}{}
		if err := json.Unmarshal(bs, errMsg); err != nil {
			logger.Debugf("[parseErrMsg] json.Unmarshal failed, error: %v, response body: %s", err, res)
			return fmt.Errorf(res)
		}
		msg = fmt.Sprintf("%s: %s", errMsg.Error.Title, errMsg.Error.Message)
	case AuthTypeOpenpalette:
		errMsg := &struct {
			Message string `json:"message"`
		}{}
		if err := json.Unmarshal(bs, errMsg); err != nil {
			logger.Debugf("[parseErrMsg] json.Unmarshal failed, error: %v, response body: %s", err, res)
			return fmt.Errorf(res)
		}
		msg = errMsg.Message
	case AuthTypeNetInsight:
		msg = string(bs)
	}
	if msg == "" {
		msg = res
	}
	return fmt.Errorf(msg)
}

func update(dao *Dao) error {
	if err := Create(dao.Request, true); err != nil {
		logger.Errorf("[update] Create key %s error: %v", dao.Request.Key(), err)
		return fmt.Errorf("create token error: %v", err)
	}
	newDao := &Dao{}
	_ = rediz.HGetInstance(storage.RedisKeyToken, dao.Request.Key(), newDao)
	dao.Token = newDao.Token
	dao.Catalog = newDao.Catalog
	dao.IssuedAt = newDao.IssuedAt
	dao.ExpiresAt = newDao.ExpiresAt
	return nil
}

func filterEndpoints(catalog *Catalog, interfaces map[string]struct{}) (cat *Catalog) {
	cat = &Catalog{ID: catalog.ID, Name: catalog.Name, Type: catalog.Type, Endpoints: []*Endpoint{}}
	if catalog.Name == common_cloud_env.NO_AUTH_CATALOG || catalog.Name == common_cloud_env.NEED_AUTH_CATALOG {
		cat.Name = common_cloud_env.CATALOG_OPENPALETTE
	}
	if _, ok := interfaces["all"]; ok {
		for _, e := range catalog.Endpoints {
			ept := &Endpoint{ID: e.ID, URL: e.URL, Region: e.Region, RegionID: e.RegionID, Interface: e.Interface}
			cat.Endpoints = append(cat.Endpoints, ept)
		}
		return
	}
	for _, e := range catalog.Endpoints {
		if _, ok := interfaces[strings.ToLower(e.Interface)]; ok {
			ept := &Endpoint{ID: e.ID, URL: e.URL, Region: e.Region, RegionID: e.RegionID, Interface: e.Interface}
			cat.Endpoints = append(cat.Endpoints, ept)
		}
	}
	return
}
