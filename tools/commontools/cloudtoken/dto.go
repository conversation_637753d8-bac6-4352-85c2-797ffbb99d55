package cloudtoken

import (
	"cwsm/tools/commontools/commondao/common_cloud_env"
	"cwsm/tools/commontools/logger"
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

type CloudTokenREQ interface {
	DecodePassword() error
	TokenApi() string
}

type CloudTokenRSP interface {
	ToDao(request *UniversalREQ, token, lastVisitedAt string) *Dao
}

type UniversalREQ struct {
	URL       string           `json:"url"`
	Type      AuthType         `json:"type"`
	User      string           `json:"user"`
	Domain    string           `json:"domain"`
	Project   string           `json:"project"`
	Password  string           `json:"password,omitempty"`
	UserID    string           `json:"userId"`
	DomainID  string           `json:"domainId"`
	ProjectID string           `json:"projectId"`
	SSLAuth   *restful.SSLAuth `json:"sslAuth"`

	CipherCode string `json:"504143c6e2a98f3b84f1541705d2b665"` // 'cipher_code' encoded by MD5
}

func (ur *UniversalREQ) ToCloudTokenREQ() (CloudTokenREQ, error) {
	if ur.CipherCode == "" {
		ur.CipherCode = ur.Password
	}
	ur.Password = ""

	var request CloudTokenREQ
	switch ur.Type {
	case AuthTypeNetInsight:
		request = &NetInsightREQ{UserName: ur.User, Password: ur.CipherCode}
	case AuthTypeOpenpalette:
		request = &OpenPaletteREQ{UserName: ur.User, Password: ur.CipherCode, UserType: "local"}
	case AuthTypeKeystoneV2:
		credentials := &credentials{UserName: ur.User, Password: ur.CipherCode}
		auth := &v2Auth{TenantName: ur.Project, PasswordCredentials: credentials}
		request = &KeystoneV2REQ{Auth: auth}
	case AuthTypeKeystoneV3:
		dn := &domain{ID: ur.DomainID, Name: ur.Domain}
		user := &v3User{Domain: dn, ID: ur.UserID, Name: ur.User, Password: ur.CipherCode}
		scope := &scope{Project: &v3Project{Domain: dn, ID: ur.ProjectID, Name: ur.Project}}
		identity := &identity{Password: &password{User: user}, Methods: []string{"password"}}
		request = &KeystoneV3REQ{Auth: &v3Auth{Scope: scope, Identity: identity}}
	default:
		return nil, fmt.Errorf("unknown auth type %s", ur.Type)
	}
	if err := request.DecodePassword(); err != nil {
		return nil, fmt.Errorf("failed to decode password: %v", err)
	}
	return request, nil
}

func (ur *UniversalREQ) Key() string {
	return Key(string(ur.Type), ur.URL, ur.User, ur.Project, ur.Domain, ur.UserID, ur.ProjectID, ur.DomainID)
}

type UniversalRSP struct {
	Key       string            `json:"key"`
	Token     map[string]string `json:"token"`
	Catalog   []*Catalog        `json:"catalog"`
	SSLAuth   *restful.SSLAuth  `json:"sslAuth"`
	IssuedAt  string            `json:"issuedAt"`
	ExpiresAt string            `json:"expiresAt"`
	Request   *UniversalREQ     `json:"request"`
	TenantId  string            `json:"tenantId"`
	UserId    string            `json:"userId"`
}

func (ur *UniversalRSP) GetToken() string {
	if ur.Token["X-Auth-Token"] == "" {
		return ur.Token["Access-Token"]
	}
	return ur.Token["X-Auth-Token"]
}

type UniversalQueryParam struct {
	Type       string   `json:"type"`
	URL        string   `json:"url"`
	User       string   `json:"user"`
	Project    string   `json:"project"`
	Domain     string   `json:"domain"`
	Interfaces []string `json:"interfaces"`
	Catalog    []string `json:"catalog"`

	UserID    string `json:"userId"`
	ProjectID string `json:"projectId"`
	DomainID  string `json:"domainId"`
}

func (uqr *UniversalQueryParam) Key() string {
	return Key(uqr.Type, uqr.URL, uqr.User, uqr.Project, uqr.Domain, uqr.UserID, uqr.ProjectID, uqr.DomainID)
}

type KeystoneV2RSP struct {
	Access struct {
		User           *v2User      `json:"user"`
		Token          *v2Token     `json:"token"`
		ServiceCatalog []*v2Catalog `json:"serviceCatalog"`
	} `json:"access"`
}

func (k2p *KeystoneV2RSP) ToDao(request *UniversalREQ, _, lastVisitedAt string) *Dao {
	if lastVisitedAt == "" {
		lastVisitedAt = time.Now().UTC().Format(time.RFC3339)
	}
	dao := &Dao{
		Key:           request.Key(),
		Request:       request,
		Token:         map[string]string{"X-Auth-Token": k2p.Access.Token.ID},
		Catalog:       map[string]*Catalog{},
		ExpiresAt:     k2p.Access.Token.Expires,
		IssuedAt:      k2p.Access.Token.IssuedAt,
		LastVisitedAt: lastVisitedAt,
	}
	for _, cata := range k2p.Access.ServiceCatalog {
		if len(cata.Endpoints) == 0 {
			continue
		}
		ep := cata.Endpoints[0]
		endpoints := make([]*Endpoint, 3)
		endpoints[0] = &Endpoint{ID: ep.ID, URL: strings.TrimRight(ep.AdminURL, "/"), Region: ep.Region, Interface: "admin"}
		endpoints[1] = &Endpoint{ID: ep.ID, URL: strings.TrimRight(ep.PublicURL, "/"), Region: ep.Region, Interface: "public"}
		endpoints[2] = &Endpoint{ID: ep.ID, URL: strings.TrimRight(ep.InternalURL, "/"), Region: ep.Region, Interface: "internal"}
		dao.Catalog[cata.Name] = &Catalog{Name: cata.Name, Type: cata.Type, Endpoints: endpoints}
	}
	dao.IssuedAt, dao.ExpiresAt = adjustTimes(k2p.Access.Token.IssuedAt, k2p.Access.Token.Expires)
	dao.TenantId = k2p.Access.Token.Tenant.ID
	dao.UserId = k2p.Access.User.ID
	return dao
}

type KeystoneV2REQ struct {
	Auth *v2Auth `json:"auth"`
}

func (k2q *KeystoneV2REQ) DecodePassword() error {
	if k2q.Auth != nil && k2q.Auth.PasswordCredentials != nil {
		bs, err := base64.StdEncoding.DecodeString(k2q.Auth.PasswordCredentials.Password)
		if err != nil {
			return err
		}
		k2q.Auth.PasswordCredentials.Password = string(bs)
		return nil
	}
	return fmt.Errorf("password is null")
}

func (k2q *KeystoneV2REQ) TokenApi() string {
	return "/v2.0/tokens"
}

type KeystoneV3RSP struct {
	Token *v3Token `json:"token"`
}

func (k3p *KeystoneV3RSP) ToDao(request *UniversalREQ, token, lastVisitedAt string) *Dao {
	if lastVisitedAt == "" {
		lastVisitedAt = time.Now().UTC().Format(time.RFC3339)
	}
	dao := &Dao{
		Key:           request.Key(),
		Request:       request,
		Token:         map[string]string{"X-Auth-Token": token},
		Catalog:       map[string]*Catalog{},
		LastVisitedAt: lastVisitedAt,
	}
	for _, cata := range k3p.Token.Catalog {
		points := make([]*Endpoint, len(cata.Endpoints))
		for i, ep := range cata.Endpoints {
			points[i] = &Endpoint{ID: ep.ID, URL: strings.TrimRight(ep.URL, "/"), Region: ep.Region,
				RegionID: ep.RegionID, Interface: ep.Interface}
		}
		if cata.Name == common_cloud_env.CATALOG_OPENPALETTE {
			cata.Name = cata.Name + "_" + cata.Type
		}
		dao.Catalog[cata.Name] = &Catalog{ID: cata.ID, Name: cata.Name, Type: cata.Type, Endpoints: points}
	}
	dao.IssuedAt, dao.ExpiresAt = adjustTimes(k3p.Token.IssuedAt, k3p.Token.ExpiresAt)
	dao.TenantId = k3p.Token.Project.ID
	dao.UserId = k3p.Token.User.ID
	return dao
}

func adjustTimes(issuedAt, expiresAt string) (string, string) {
	it, err := parseTime(issuedAt)
	if err != nil {
		logger.Errorf("cloudtoken parse issued_at %s error: %v", issuedAt, err)
		return issuedAt, expiresAt
	}
	et, err := parseTime(expiresAt)
	if err != nil {
		logger.Errorf("cloudtoken parse expires_at %s error: %v", expiresAt, err)
		return issuedAt, expiresAt
	}
	now, d := time.Now().UTC(), et.Sub(it)-time.Second*10
	return now.Format(time.RFC3339), now.Add(d).Format(time.RFC3339)
}

func parseTime(input string) (time.Time, error) {
	output, err := time.Parse(time.RFC3339, input)
	if err != nil {
		logger.Errorf("cloudtoken parseTime %s error: %v", input, err)
		if input[len(input)-1] != 'Z' {
			if output, err = time.Parse(time.RFC3339, input+"Z"); err != nil {
				logger.Errorf("cloudtoken parseTime %s error: %v", input+"Z", err)
			}
		}
	}
	return output, err
}

type KeystoneV3REQ struct {
	Auth *v3Auth `json:"auth"`
}

func (k3q *KeystoneV3REQ) BuildKeystoneV3REQ(u, p, domainName, project string) *KeystoneV3REQ {
	return &KeystoneV3REQ{Auth: &v3Auth{
		Scope: &scope{Project: &v3Project{Domain: &domain{Name: domainName}, Name: project}},
		Identity: &identity{
			Password: &password{User: &v3User{
				Domain:   &domain{Name: domainName},
				Name:     u,
				Password: p,
			}},
			Methods: []string{"password"},
		},
	}}
}
func (k3q *KeystoneV3REQ) DecodePassword() error {
	if k3q.Auth != nil && k3q.Auth.Identity != nil && k3q.Auth.Identity.Password != nil && k3q.Auth.Identity.Password.User != nil {
		bs, err := base64.StdEncoding.DecodeString(k3q.Auth.Identity.Password.User.Password)
		if err != nil {
			return err
		}
		k3q.Auth.Identity.Password.User.Password = string(bs)
		return nil
	}
	return fmt.Errorf("password is null")
}

func (k3q *KeystoneV3REQ) TokenApi() string {
	return "/v3/auth/tokens"
}

type OpenPaletteRSP struct {
	AccessToken string  `json:"access_token"`
	User        *opUser `json:"user"`
}

func (opp *OpenPaletteRSP) ToDao(request *UniversalREQ, _, lastVisitedAt string) *Dao {
	now := time.Now().UTC()
	if lastVisitedAt == "" {
		lastVisitedAt = now.Format(time.RFC3339)
	}
	return &Dao{
		Key:           request.Key(),
		Request:       request,
		Token:         map[string]string{"Access-Token": opp.AccessToken, "Authorization": "Bearer " + opp.AccessToken},
		IssuedAt:      now.Format(time.RFC3339),
		ExpiresAt:     now.Add(4 * time.Minute).Format(time.RFC3339),
		LastVisitedAt: lastVisitedAt,
	}
}

type OpenPaletteREQ struct {
	UserName string `json:"userName"`
	Password string `json:"password"`
	UserType string `json:"userType"`
}

func (opq *OpenPaletteREQ) DecodePassword() error {
	return nil
}

func (opq *OpenPaletteREQ) TokenApi() string {
	return "/opapi/authen/v1/admin-tokens"
}

type NetInsightREQ struct {
	UserName string `json:"Username"`
	Password string `json:"Password"`
}

func (nq *NetInsightREQ) DecodePassword() error {
	return nil
}

func (nq *NetInsightREQ) TokenApi() string {
	return "/netInsight/api/login"
}

type NetInsightRSP struct {
	AccessToken string `json:"Access-Token"`
}

func (np *NetInsightRSP) ToDao(_ *UniversalREQ, _, _ string) *Dao {
	return &Dao{
		Token: map[string]string{"Access-Token": np.AccessToken},
	}
}

type AuthType string

const (
	AuthTypeOpenpalette = common_cloud_env.AuthTypeOpenpalette
	AuthTypeKeystoneV2  = common_cloud_env.AuthTypeKeystoneV2
	AuthTypeKeystoneV3  = common_cloud_env.AuthTypeKeystoneV3
	Keystone            = common_cloud_env.Keystone
	AuthTypeNetInsight  = common_cloud_env.AuthTypeNetInsight
)

type v2Token struct {
	Expires     string   `json:"expires"`
	RequestType string   `json:"request_type"`
	AuditIDs    []string `json:"audit_ids"`
	IssuedAt    string   `json:"issued_at"`
	ID          string   `json:"id"`
	Tenant      *struct {
		Description string `json:"description"`
		Enabled     bool   `json:"enabled"`
		ID          string `json:"id"`
		Name        string `json:"name"`
	} `json:"tenant"`
}

type v2Catalog struct {
	Endpoints []*v2Endpoint `json:"endpoints"`
	Type      string        `json:"type"`
	Name      string        `json:"name"`

	//EndpointsLinks []string `json:"endpoints_links"`
}

type v2Endpoint struct {
	AdminURL    string `json:"adminURL"`
	Region      string `json:"region"`
	ID          string `json:"id"`
	InternalURL string `json:"internalURL"`
	PublicURL   string `json:"publicURL"`
}

type v2User struct {
	UserName   string   `json:"username"`
	RolesLinks []string `json:"roles_links"`
	ID         string   `json:"id"`
	Name       string   `json:"name"`
	Roles      []*role  `json:"roles"`
}

type v2Auth struct {
	TenantName          string       `json:"tenantName"`
	PasswordCredentials *credentials `json:"passwordCredentials"`
}

type credentials struct {
	UserName string `json:"username"`
	Password string `json:"password"`
}

type v3Token struct {
	Methods         []string     `json:"methods"`
	User            *v3User      `json:"user"`
	AuditIDs        []string     `json:"audit_ids"`
	ExpiresAt       string       `json:"expires_at"`
	ParentExpiresAt string       `json:"parent_expires_at"`
	IssuedAt        string       `json:"issued_at"`
	Project         *v3Project   `json:"project"`
	IsDomain        bool         `json:"is_domain"`
	Roles           []*role      `json:"roles"`
	Catalog         []*v3Catalog `json:"catalog"`
}

type v3User struct {
	Domain            *domain `json:"domain"`
	ID                string  `json:"id,omitempty"`
	Name              string  `json:"name,omitempty"`
	Password          string  `json:"password,omitempty"`
	PasswordExpiresAt *string `json:"password_expires_at,omitempty"`
}

type v3Project struct {
	Domain *domain `json:"domain"`
	ID     string  `json:"id,omitempty"`
	Name   string  `json:"name,omitempty"`
}

type v3Auth struct {
	Scope    *scope    `json:"scope"`
	Identity *identity `json:"identity"`
}

type v3Catalog struct {
	Endpoints []*v3Endpoint `json:"endpoints"`
	ID        string        `json:"id"`
	Type      string        `json:"type"`
	Name      string        `json:"name"`
}

type v3Endpoint struct {
	ID        string `json:"id"`
	Interface string `json:"interface"`
	RegionID  string `json:"region_id"`
	URL       string `json:"url"`
	Region    string `json:"region"`
}

type domain struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type role struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type scope struct {
	Project *v3Project `json:"project"`
}

type identity struct {
	Password *password `json:"password"`
	Methods  []string  `json:"methods"`
}

type password struct {
	User *v3User `json:"user"`
}

type opUser struct {
	ID            string `json:"id"`
	Name          string `json:"userName"`
	Status        string `json:"status"`
	RegType       string `json:"regType"`
	RegTime       string `json:"regTime"`
	LastLoginTime string `json:"lastLoginTime"`
	UnfreezeTime  string `json:"unfreezeTime"`
}

type TokenQueryParam struct {
	Interfaces []string `json:"interfaces"`
	Catalogs   []string `json:"catalog"`
	Refresh    *bool    `json:"refresh"`
	EndPoint   string   `json:"endPoint"`
}

func (request *TokenQueryParam) FillTokenQueryParam() {
	if request.EndPoint == "" {
		request.EndPoint = common_cloud_env.ENDPOINT_PUBLIC_AUTH
	}

	if request.Refresh == nil {
		truth := false
		request.Refresh = &truth
	}

	if request.Catalogs == nil {
		request.Catalogs = []string{"all"}
	}

	if request.Interfaces == nil {
		request.Interfaces = []string{"all"}
	}
}

type EtvKey struct {
	EnvId        string
	TenantId     string
	VdcId        string
	EndpointName string
	Refresh      bool
	Catalogs     []string
	Interfaces   []string
}

func (etvKey *EtvKey) ToTokenQuery() *TokenQueryParam {
	return &TokenQueryParam{Catalogs: etvKey.Catalogs, Interfaces: etvKey.Interfaces, Refresh: &etvKey.Refresh, EndPoint: etvKey.EndpointName}
}

type AccessInfo struct {
	Token   string           `json:"token"`
	Url     string           `json:"url"`
	SSlAuth *restful.SSLAuth `json:"sslAuthentication"`
}

func (af *AccessInfo) ParseTenantId() string {
	idx := strings.LastIndex(af.Url, "/") + 1
	if idx >= len(af.Url) {
		return ""
	}
	return af.Url[idx:]
}
