package cloudtoken

import (
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/httpservice"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

func PostTokenProvider(url string, urlType AuthType, request CloudTokenREQ, sslAuth *restful.SSLAuth) ([]byte, map[string][]string, error) {
	bs, headers, _, err := httpservice.Post(url, httpservice.DefaultHeaders(), request, sslAuth)
	if err != nil {
		logger.Debugf("PostTokenProvider url:%s error: %v", url, err)
		return nil, nil, ParseErrMsg(urlType, err.Error())
	}
	return bs, headers, nil
}
