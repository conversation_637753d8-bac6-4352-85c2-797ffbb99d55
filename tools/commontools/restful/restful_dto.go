package restful

import (
	"fmt"
	"strings"
)

type SSLAuth struct {
	Method      SSLMethod   `json:"method"`
	SSLProtocol SSLProtocol `json:"sslProtocol"`
	// Example: /usr/share/ssl/director-ca/35c
	RootCert string `json:"rootCert"`
	// Example: /usr/share/ssl/director-south-server/director-server
	ClientCert string `json:"clientCert"`
	// Example: /usr/share/ssl/director-south-server/director-server.key
	ClientKey string `json:"clientKey"`
}

type SSLAuthReq struct {
	EndpointName string `json:"endpointName"`
	*SSLAuth
}

type SSLMethod string

type SSLProtocol string

const (
	SSLMethodOneWay SSLMethod = "one-way"
	SSLMethodTwoWay SSLMethod = "two-way"
	SSLMethodNoAuth SSLMethod = "no-auth"

	SSLProtocolSSL30 SSLProtocol = "SSLv3"
	SSLProtocolTLS10 SSLProtocol = "TLSv1"
	SSLProtocolTLS11 SSLProtocol = "TLSv1.1"
	SSLProtocolTLS12 SSLProtocol = "TLSv1.2"
	SSLProtocolTLS13 SSLProtocol = "TLSv1.3"
)

const (
	RootCertPath   = "/usr/share/ssl/director-ca"
	ClientCertPath = "/usr/share/ssl/director-south-server"
)

func (ta *SSLAuth) SimplifyPath() {
	if ta == nil {
		return
	}
	var tmpSlice []string
	tmpSlice = strings.Split(ta.RootCert, "/")
	ta.RootCert = tmpSlice[len(tmpSlice)-1]
	tmpSlice = strings.Split(ta.ClientCert, "/")
	ta.ClientCert = tmpSlice[len(tmpSlice)-1]
	tmpSlice = strings.Split(ta.ClientKey, "/")
	ta.ClientKey = tmpSlice[len(tmpSlice)-1]
}

func (ta *SSLAuth) CompletePath() {
	if ta == nil {
		return
	}
	if ta.RootCert != "" {
		ta.RootCert = fmt.Sprintf("%s/%s", RootCertPath, ta.RootCert)
	}
	if ta.ClientKey != "" {
		ta.ClientKey = fmt.Sprintf("%s/%s", ClientCertPath, ta.ClientKey)
	}
	if ta.ClientCert != "" {
		ta.ClientCert = fmt.Sprintf("%s/%s", ClientCertPath, ta.ClientCert)
	}
}

func (ta *SSLAuth) IsValid() bool {
	if ta == nil {
		return false
	}
	methods := map[SSLMethod]struct{}{SSLMethodOneWay: {}, SSLMethodTwoWay: {}, SSLMethodNoAuth: {}}
	if _, ok := methods[ta.Method]; !ok {
		return false
	}
	if ta.Method == SSLMethodNoAuth {
		if len(ta.RootCert) != 0 {
			return false
		}
	} else {
		protocols := map[SSLProtocol]struct{}{SSLProtocolSSL30: {}, SSLProtocolTLS10: {},
			SSLProtocolTLS11: {}, SSLProtocolTLS12: {}, SSLProtocolTLS13: {}}
		if _, ok := protocols[ta.SSLProtocol]; !ok {
			return false
		}
		if len(ta.RootCert) == 0 {
			return false
		}
	}
	if ta.Method == SSLMethodTwoWay {
		if len(ta.ClientCert) == 0 || len(ta.ClientKey) == 0 {
			return false
		}
	}

	return true
}
