package restful

import (
	"bytes"
	"crypto/tls"
	"crypto/x509"
	"cwsm/tools/commontools/logger"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"path"
	"strconv"
	"time"

	"github.com/beego/beego/v2/server/web/context"
)

func GetMethodNoRedirect(url string, headers map[string]string, auth *SSLAuth, authParams ...string) ([]byte,
	map[string][]string, int, error) {
	req, err := newRequest("GET", url, nil, headers, auth)
	if err != nil {
		return nil, nil, 500, err
	}
	if len(authParams) == 2 {
		req.SetBasicAuth(authParams[0], authParams[1])
	}
	client := http.DefaultClient
	client.CheckRedirect = func(req *http.Request, via []*http.Request) error {
		return http.ErrUseLastResponse
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, nil, 500, err
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, 500, err
	}
	logger.Debug("GetMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
		url, resp.Header, resp.StatusCode, string(res))
	return res, resp.Header, resp.StatusCode, nil
}

func GetMethod(url string, headers map[string]string, auth *SSLAuth) ([]byte,
	map[string][]string, int, error) {
	req, err := newRequest("GET", url, nil, headers, auth)
	if err != nil {
		return nil, nil, 500, err
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, nil, 500, err
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, 500, err
	}
	if !StatusOk(resp.StatusCode) {
		logger.Errorf("GetMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
			url, resp.Header, resp.StatusCode, string(res))
		return res, nil, resp.StatusCode, fmt.Errorf(string(res[:]))
	}
	logger.Debug("GetMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
		url, resp.Header, resp.StatusCode, string(res))
	return res, resp.Header, resp.StatusCode, nil
}

func GetMethodWithIo(url string, headers map[string]string, auth *SSLAuth) (io.ReadCloser,
	map[string][]string, int, int64, error) {
	req, err := newRequest("GET", url, nil, headers, auth)
	if err != nil {
		return nil, nil, 500, -1, err
	}

	http.DefaultClient.Timeout = 10 * 60 * 60 * time.Second

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, nil, 500, -1, err
	}

	if !StatusOk(resp.StatusCode) {
		logger.Errorf("GetMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
			url, resp.Header, resp.StatusCode, resp.Body)
		return resp.Body, nil, resp.StatusCode, -1, nil
	}
	logger.Infof("[GetMethodWithIo]: http response content length:%d", resp.ContentLength)
	return resp.Body, resp.Header, resp.StatusCode, resp.ContentLength, nil
}

func AsyncGetMethod(url string, headers map[string]string, auth *SSLAuth, beegoOutput *context.BeegoOutput) error {
	req, err := newRequestWithoutAuth("GET", url, nil, headers)
	if err != nil {
		return fmt.Errorf("newRequestWithoutAuth failed: %v", err)
	}

	tlsCfg, err := buildTLSConfig(auth)
	if err != nil {
		logger.Errorf("buildTLSConfig failed: %v", err)
		return fmt.Errorf("buildTLSConfig failed: %v", err)
	}

	http.DefaultClient.Transport = &http.Transport{
		TLSClientConfig: tlsCfg,
	}
	http.DefaultClient.Timeout = 10 * 60 * 60 * time.Second

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		logger.Errorf("http.DefaultClient.Do failed: %v", err)
		return fmt.Errorf("http.DefaultClient.Do failed:%v", err)
	}
	defer resp.Body.Close()

	if !StatusOk(resp.StatusCode) {
		buf := new(bytes.Buffer)
		buf.ReadFrom(resp.Body)
		newStr := buf.String()
		logger.Errorf("AsyncGetMethod: req url:%s, resp headers:%v, resp code:%d, resp body:%#v", url, resp.Header, resp.StatusCode, newStr)
		return fmt.Errorf("http client failed:%v", newStr)
	}

	beegoOutput.Header("Content-Length", strconv.FormatInt(resp.ContentLength, 10))
	fileSize, err := io.Copy(beegoOutput.Context.ResponseWriter, resp.Body)
	if err != nil {
		logger.Errorf("[AsyncGetMethod]: io.Copy failed: %v", err)
		return err
	}
	logger.Infof("[AsyncGetMethod]: fileSize: %d, ok...", fileSize)
	return nil
}

func PostMethod(url string, headers map[string]string, body []byte, auth *SSLAuth) ([]byte,
	map[string][]string, int, error) {
	var reqBuffer = bytes.NewBuffer(body)
	req, err := newRequest("POST", url, reqBuffer, headers, auth)
	if err != nil {
		return nil, nil, 500, err
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, nil, 500, err
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, 500, err
	}
	if !StatusOk(resp.StatusCode) {
		logger.Errorf("PostMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
			url, resp.Header, resp.StatusCode, string(res))
		return res, nil, resp.StatusCode, fmt.Errorf(string(res[:]))
	}
	logger.Debug("PostMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
		url, resp.Header, resp.StatusCode, string(res))
	return res, resp.Header, resp.StatusCode, nil
}

func PostMultipartMethod(url string, headers map[string]string, body io.Reader, auth *SSLAuth) ([]byte,
	map[string][]string, int, error) {
	req, err := newRequest("POST", url, body, headers, auth)
	if err != nil {
		return nil, nil, 500, err
	}
	http.DefaultClient.Timeout = 60 * 60 * time.Second
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, nil, 500, err
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, 500, err
	}
	if !StatusOk(resp.StatusCode) {
		logger.Errorf("PostMultipartMethod req url:%s, req headers:%v, resp headers:%v, resp code:%d, resp body:%s,",
			url, req.Header, resp.Header, resp.StatusCode, string(res))
		return res, nil, resp.StatusCode, fmt.Errorf(string(res[:]))
	}
	logger.Debug("PostMultipartMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
		url, resp.Header, resp.StatusCode, string(res))
	return res, resp.Header, resp.StatusCode, nil
}

func PostMethodWithTimeout(url string, headers map[string]string, body []byte, auth *SSLAuth, timeout int64) ([]byte,
	map[string][]string, int, error) {
	var reqBuffer = bytes.NewBuffer(body)
	req, err := newRequestWithoutAuth("POST", url, reqBuffer, headers)
	if err != nil {
		return nil, nil, 500, err
	}

	tlsCfg, err := buildTLSConfig(auth)
	if err != nil {
		logger.Errorf("buildTLSConfig failed: %v", err)
		return nil, nil, 500, fmt.Errorf("buildTLSConfig failed: %v", err)
	}

	http.DefaultClient.Transport = &http.Transport{
		TLSClientConfig: tlsCfg,
	}

	http.DefaultClient.Timeout = time.Duration(timeout) * time.Second

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, nil, 500, err
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, 500, err
	}
	if !StatusOk(resp.StatusCode) {
		logger.Errorf("PostMethodWithTimeout req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
			url, resp.Header, resp.StatusCode, string(res))
		return nil, nil, resp.StatusCode, fmt.Errorf(string(res[:]))
	}
	logger.Debug("PostMethodWithTimeout req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
		url, resp.Header, resp.StatusCode, string(res))
	return res, resp.Header, resp.StatusCode, nil
}

func StopMethod(url string, headers map[string]string, body []byte, auth *SSLAuth) ([]byte,
	map[string][]string, int, error) {
	var reqBuffer = bytes.NewBuffer(body)
	return doDeleteRequest(url, headers, reqBuffer, auth, "DeleteMethod")
}

func PutMethod(url string, headers map[string]string, body []byte, auth *SSLAuth) ([]byte,
	map[string][]string, int, error) {
	var reqBuffer = bytes.NewBuffer(body)
	req, err := newRequest("PUT", url, reqBuffer, headers, auth)
	if err != nil {
		return nil, nil, 500, err
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, nil, 500, err
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, 500, err
	}
	if !StatusOk(resp.StatusCode) {
		logger.Errorf("PutMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
			url, resp.Header, resp.StatusCode, string(res))
		return res, nil, resp.StatusCode, fmt.Errorf(string(res[:]))
	}

	logger.Debug("PutMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
		url, resp.Header, resp.StatusCode, string(res))
	return res, resp.Header, resp.StatusCode, nil
}

func PutStreamMethod(url string, headers map[string]string, body io.ReadCloser, auth *SSLAuth) ([]byte,
	map[string][]string, int, error) {
	req, err := newRequestWithoutAuth("PUT", url, body, headers)
	if err != nil {
		return nil, nil, 500, fmt.Errorf("newRequestWithoutAuth failed: %v", err)
	}

	tlsCfg, err := buildTLSConfig(auth)
	if err != nil {
		logger.Errorf("buildTLSConfig failed: %v", err)
		return nil, nil, 500, fmt.Errorf("buildTLSConfig failed: %v", err)
	}

	http.DefaultClient.Transport = &http.Transport{
		TLSClientConfig: tlsCfg,
	}
	http.DefaultClient.Timeout = 10 * 60 * 60 * time.Second

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		logger.Errorf("http.DefaultClient.Do failed: %v", err)
		return nil, nil, 500, fmt.Errorf("http.DefaultClient.Do failed:%v", err)
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, 500, err
	}
	if !StatusOk(resp.StatusCode) {
		logger.Errorf("PutStreamMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
			url, resp.Header, resp.StatusCode, string(res))
		return res, nil, resp.StatusCode, fmt.Errorf(string(res[:]))
	}

	return res, resp.Header, resp.StatusCode, nil
}

func PatchMethod(url string, headers map[string]string, body []byte, auth *SSLAuth) ([]byte,
	map[string][]string, int, error) {
	var reqBuffer = bytes.NewBuffer(body)
	req, err := newRequest("PATCH", url, reqBuffer, headers, auth)
	if err != nil {
		return nil, nil, 500, err
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, nil, 500, err
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, 500, err
	}
	if !StatusOk(resp.StatusCode) {
		logger.Errorf("PatchMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
			url, resp.Header, resp.StatusCode, string(res))
		return res, nil, resp.StatusCode, fmt.Errorf(string(res[:]))
	}
	logger.Debug("PatchMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
		url, resp.Header, resp.StatusCode, string(res))
	return res, resp.Header, resp.StatusCode, nil
}

func DeleteMethod(url string, headers map[string]string, auth *SSLAuth) ([]byte,
	map[string][]string, int, error) {
	req, err := newRequest("DELETE", url, nil, headers, auth)
	if err != nil {
		return nil, nil, 500, err
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, nil, 500, err
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, 500, err
	}
	if !StatusOk(resp.StatusCode) {
		logger.Errorf("DeleteMethod req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
			url, resp.Header, resp.StatusCode, string(res))
		return res, nil, resp.StatusCode, fmt.Errorf(string(res[:]))
	}
	logger.Debug("DeleteMethod req url:%s, req headers:%v, resp headers:%v, resp code:%d, resp body:%s,",
		url, headers, resp.Header, resp.StatusCode, string(res))
	return res, resp.Header, resp.StatusCode, nil
}

func DeleteMethodRequest(url string, headers map[string]string, auth *SSLAuth) ([]byte,
	map[string][]string, int, error) {
	return doDeleteRequest(url, headers, nil, auth, "DeleteMethod")
}

func DeleteWithBodyMethod(url string, headers map[string]string, body []byte, auth *SSLAuth) ([]byte,
	map[string][]string, int, error) {
	var reqBuffer = bytes.NewBuffer(body)
	return doDeleteRequest(url, headers, reqBuffer, auth, "DeleteWithBodyMethod")
}

func doDeleteRequest(url string, headers map[string]string, reqBuffer *bytes.Buffer, auth *SSLAuth, method string) ([]byte,
	map[string][]string, int, error) {
	req, err := newRequest("DELETE", url, reqBuffer, headers, auth)
	if err != nil {
		return nil, nil, 500, err
	}
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, nil, 500, err
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, 500, err
	}
	if !StatusOk(resp.StatusCode) {
		logger.Errorf("%s req url:%s, resp headers:%v, resp code:%d, resp body:%s,",
			method, url, resp.Header, resp.StatusCode, string(res))
		return res, nil, resp.StatusCode, fmt.Errorf(string(res[:]))
	}
	logger.Debug("%s req url:%s, req headers:%v, resp headers:%v, resp code:%d, resp body:%s,",
		method, url, headers, resp.Header, resp.StatusCode, string(res))
	return res, resp.Header, resp.StatusCode, nil
}

func newRequest(method, url string, body io.Reader, headers map[string]string, auth *SSLAuth) (*http.Request,
	error) {
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}
	if _, ok := headers["Content-Type"]; !ok {
		req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	}

	for k, v := range headers {
		req.Header.Set(k, v)
	}
	tlsCfg, err := buildTLSConfig(auth)
	if err != nil {
		logger.Errorf("newRequest failed: %v", err)
		return nil, fmt.Errorf("newRequest failed: %v", err)
	}
	http.DefaultClient.Transport = &http.Transport{
		TLSClientConfig: tlsCfg,
		IdleConnTimeout: 10 * time.Second,
	}
	http.DefaultClient.Timeout = 60 * time.Second

	return req, nil
}

func newRequestWithoutAuth(method, url string, body io.Reader, headers map[string]string) (*http.Request,
	error) {
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}
	if _, ok := headers["Content-Type"]; !ok {
		req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	}

	for k, v := range headers {
		req.Header.Set(k, v)
	}

	return req, nil
}

func getTrueSkipVerifyValue() bool {
	return "admin" != ""
}

func buildTLSConfig(auth *SSLAuth) (*tls.Config, error) {
	config := &tls.Config{}
	if auth == nil || auth.Method == "" || auth.Method == SSLMethodNoAuth {
		config.InsecureSkipVerify = getTrueSkipVerifyValue()
		return config, nil
	}
	certPool := x509.NewCertPool()
	rootCert, err := ioutil.ReadFile(path.Clean(auth.RootCert))
	if err != nil {
		logger.Errorf("buildTLSConfig read root certificate file failed: %v", err)
		return nil, fmt.Errorf("buildTLSConfig read root certificate file failed: %v", err)
	}

	certPool.AppendCertsFromPEM(rootCert)
	config.RootCAs = certPool
	config.MaxVersion = map[SSLProtocol]uint16{SSLProtocolTLS10: tls.VersionTLS10,
		SSLProtocolTLS11: tls.VersionTLS11, SSLProtocolTLS12: tls.VersionTLS12,
		SSLProtocolTLS13: tls.VersionTLS13}[auth.SSLProtocol]
	if auth.Method == SSLMethodOneWay {
		return config, nil
	}

	cert, err := tls.LoadX509KeyPair(auth.ClientCert, auth.ClientKey)
	if err != nil {
		logger.Errorf("buildTLSConfig tls.LoadX509KeyPair failed: %v", err)
		return nil, fmt.Errorf("buildTLSConfig tls.LoadX509KeyPair failed: %v", err)
	}
	config.Certificates = []tls.Certificate{cert}
	return config, nil
}

func StatusOk(statusCode int) bool {
	return statusCode < 300
}

func DefaultHeaders() map[string]string {
	return map[string]string{"Accept": "application/json", "Content-Type": "application/json"}
}
