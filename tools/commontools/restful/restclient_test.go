package restful

import (
	"io"
	"net/http"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
)

func TestStatusOk(t *testing.T) {
	Convey("Test StatusOk", t, func() {
		res := StatusOk(301)
		So(res, ShouldBeFalse)

	})
}

func TestGetTrueSkipVerifyValue(t *testing.T) {
	<PERSON>vey("Test GetTrueSkipVerifyValue", t, func() {
		res := getTrueSkipVerifyValue()
		So(res, ShouldBeTrue)

	})
}

func TestNewRequest(t *testing.T) {
	Convey("Test GetNewRequest", t, func() {
		patch := gomonkey.ApplyFunc(http.NewRequest, func(method string, url string, body io.Reader) (*http.Request, error) {
			return &http.Request{
				Header: map[string][]string{
					"1": {"1"},
				},
			}, nil
		})
		defer patch.Reset()
		newRequest("GET", "/V1", nil, map[string]string{}, nil)
	})
}
