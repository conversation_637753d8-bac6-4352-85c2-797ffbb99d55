package common_cloud_env

const (
	OPENSTACKKVMENVTYPE     = "openstack + kvm"
	OPENSTACKK8SENVTYPE     = "openstack + k8s"
	OPENSTACKVCENTERENVTYPE = "openstack + vcenter"
	OPENSTACKCASENVTYPE     = "openstack + cas"
	OPENSTACKIECSENVTYPE    = "openstack + iecs"
	OPENPALETTEENVTYPE      = "openpalette"
	TCFK8SENVTYPE           = "tcf-k8s"
	NETWORKENVTYPE          = "network"
	PHYSICALENVTYPE         = "physic"
)

const (
	AuthTypeOpenpalette = "openpalette"
	AuthTypeKeystoneV2  = "keystoneV2"
	AuthTypeKeystoneV3  = "keystoneV3"
	Keystone            = "keystone"
	AuthTypeNetInsight  = "netInsight"
)

var EnvTypeList = []string{OPENSTACKKVMENVTYPE, OPENSTACKK8SENVTYPE, OPENSTACKVCENTERENVTYP<PERSON>, OPENSTACKCASENVTYPE,
	OPENST<PERSON><PERSON><PERSON>CSENVTYPE, OPENPALETTEENVTYPE, TCFK8SENVTYPE, NETWORKENVTYPE, PHYSICALENVTYPE}

var EnvTypeList4License = []string{OPENSTACKKVMENVTYPE, OPENSTACKK8SENVTYPE, OPENSTACKVCENTERENVTYPE, OPENSTACKCASENVTYPE,
	OPENSTACKIECSENVTYPE, OPENPALETTEENVTYPE, TCFK8SENVTYPE, PHYSICALENVTYPE}

const (
	EVN_LINKSTATUS_NORMAL         = "normal"
	EVN_LINKSTATUS_URLUNREACHABLE = "URL unreachable"
	EVN_LINKSTATUS_UNAUTHORIZED   = "Unauthorized"
	EVN_LINKSTATUS_USERNAMEWRONG  = "UserName wrong"
	EVN_LINKSTATUS_PASSWORDWRONG  = "Password wrong"
	EVN_LINKSTATUS_NORESPONSE     = "No Response"
	EVN_LINKSTATUS_CONNECTING     = "connecting"
	EVN_LINKSTATUS_ABNORMAL       = "abnormal"
)

const (
	PROVIDER_ZTE    = "ZTE"
	PROVIDER_zte    = "zte"
	PROVIDER_REDHAT = "REDHAT"
	PROVIDER_CM     = "China Mobile"
	PROVIDER_HW     = "HUA WEI"
	PROVIDER_EC     = "Ericsson"
)

var ProviderList = []string{PROVIDER_ZTE, PROVIDER_REDHAT, PROVIDER_CM, PROVIDER_HW, PROVIDER_EC, PROVIDER_zte}

const (
	ENDPOINT_PUBLIC_AUTH    = "public-auth" // ssl
	ENDPOINT_PUBLIC_PORTAL  = "public-portal"
	ENDPOINT_PRIVATE_AUTH   = "private-auth"   // ssl
	ENDPOINT_PRIVATE_PORTAL = "private-portal" // no ssl
)

var OldEndPointsMapping = map[string]string{
	"openstack-auth":       ENDPOINT_PUBLIC_AUTH,
	"openstack-portal":     ENDPOINT_PUBLIC_PORTAL,
	"provider-auth":        ENDPOINT_PRIVATE_AUTH,
	"provider-portal":      ENDPOINT_PRIVATE_PORTAL,
	OldEndPointSelf4Pvrm:   ENDPOINT_PUBLIC_AUTH,
	OldEndPointPortal4Pvrm: ENDPOINT_PRIVATE_PORTAL,
}

const (
	OldEndPointSelf4Pvrm   = "self"
	OldEndPointPortal4Pvrm = "portal"
)

var OldEndTypesMapping = map[string]string{
	"openstacktrue":             OPENSTACKK8SENVTYPE,
	"openstackfalse":            OPENSTACKKVMENVTYPE,
	"vmwarefalse":               OPENSTACKVCENTERENVTYPE,
	"casfalse":                  OPENSTACKCASENVTYPE,
	"iecsfalse":                 OPENSTACKIECSENVTYPE,
	"physicfalse":               PHYSICALENVTYPE,
	CloudTypeOpenPalette4Pvrm:   OPENPALETTEENVTYPE,
	CloudTypeTCFKube4Pvrm:       TCFK8SENVTYPE,
	CloudTypeOpenStackKube4Pvrm: OPENSTACKK8SENVTYPE,
}

var EnvKubeMap = map[string]bool{
	OPENSTACKKVMENVTYPE:     false,
	OPENSTACKK8SENVTYPE:     true,
	OPENSTACKVCENTERENVTYPE: false,
	OPENSTACKCASENVTYPE:     false,
	OPENSTACKIECSENVTYPE:    false,
}

const (
	CloudTypeOpenPalette4Pvrm   = "openpalette"
	CloudTypeTCFKube4Pvrm       = "tcf-k8s"
	CloudTypeOpenStackKube4Pvrm = "openstack+k8s"
)

var C2PEnvTypesMap = map[string]string{
	OPENSTACKK8SENVTYPE: CloudTypeOpenStackKube4Pvrm,
	OPENPALETTEENVTYPE:  CloudTypeOpenPalette4Pvrm,
	TCFK8SENVTYPE:       CloudTypeTCFKube4Pvrm,
}

const (
	VRMOLDSTATUSOPENSTACK = "openstack"
	VRMOLDSTATUSVMWARE    = "vmware"
	VRMOLDSTATUSCAS       = "cas"
	VRMOLDSTATUSIECS      = "iecs"
)

var C2VEnvTypesMap = map[string]string{
	OPENSTACKKVMENVTYPE:     VRMOLDSTATUSOPENSTACK,
	OPENSTACKK8SENVTYPE:     VRMOLDSTATUSOPENSTACK,
	OPENSTACKVCENTERENVTYPE: VRMOLDSTATUSVMWARE,
	OPENSTACKCASENVTYPE:     VRMOLDSTATUSCAS,
	OPENSTACKIECSENVTYPE:    VRMOLDSTATUSIECS,
}

var C2VAndPEnvTypesMap = map[string]string{
	OPENSTACKKVMENVTYPE:     VRMOLDSTATUSOPENSTACK,
	OPENSTACKK8SENVTYPE:     VRMOLDSTATUSOPENSTACK,
	OPENSTACKVCENTERENVTYPE: VRMOLDSTATUSVMWARE,
	OPENSTACKCASENVTYPE:     VRMOLDSTATUSCAS,
	OPENSTACKIECSENVTYPE:    VRMOLDSTATUSIECS,
	OPENPALETTEENVTYPE:      CloudTypeOpenPalette4Pvrm,
	TCFK8SENVTYPE:           CloudTypeTCFKube4Pvrm,
}

func CheckHasOpenPalette(envType string) bool {
	return envType == OPENSTACKK8SENVTYPE || envType == OPENPALETTEENVTYPE || envType == TCFK8SENVTYPE
}

func CheckIsContainerCloud(envType string) bool {
	return envType == OPENPALETTEENVTYPE || envType == TCFK8SENVTYPE
}

func CheckIsOpenstackCloud(envType string) bool {
	return envType == OPENSTACKKVMENVTYPE || envType == OPENSTACKK8SENVTYPE || envType == OPENSTACKVCENTERENVTYPE || envType == OPENSTACKCASENVTYPE || envType == OPENSTACKIECSENVTYPE
}

func CheckIsNetWorkCloud(envType string) bool {
	return envType == NETWORKENVTYPE
}

const (
	CATALOG_NOVA        = "nova"
	CATALOG_PROVIDEROPS = "providerOps"
	CATALOG_OPENPALETTE = "openpalette"
	CATALOG_VIMOPS      = "vimops"
	CATALOG_VIMOPS_EX   = "vimOps"
)

const (
	NO_AUTH_CATALOG   = "openpalette_paas"
	NEED_AUTH_CATALOG = "openpalette_paas_https_twa"

	NO_AUTH_TYPE   = "paas"
	NEED_AUTH_TYPE = "paas_https_twa"
)

const (
	ENV_STATUS_STOP                            = "stop"
	ENV_STATUS_CONFLICT_DELETED_STOP           = "conflict_deleted_stop"
	ENV_STATUS_CONFLICT_DUPLICATE_STOP         = "conflict_duplicate_stop"
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_STOP = "conflict_duplicate_deleted_stop"

	ENV_STATUS_RUNNING                            = "running"
	ENV_STATUS_CONFLICT_DELETED_RUNNING           = "conflict_deleted_running"
	ENV_STATUS_CONFLICT_DUPLICATE_RUNNING         = "conflict_duplicate_running"
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_RUNNING = "conflict_duplicate_deleted_running"
)

var ENV_STATUS_LIST = []string{
	ENV_STATUS_STOP,
	ENV_STATUS_CONFLICT_DELETED_STOP,
	ENV_STATUS_CONFLICT_DUPLICATE_STOP,
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_STOP,
	ENV_STATUS_RUNNING,
	ENV_STATUS_CONFLICT_DELETED_RUNNING,
	ENV_STATUS_CONFLICT_DUPLICATE_RUNNING,
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_RUNNING,
}

var ENV_STATUS_DUPLICATE_ASCEND = map[string]string{
	ENV_STATUS_STOP:                            ENV_STATUS_CONFLICT_DUPLICATE_STOP,
	ENV_STATUS_CONFLICT_DELETED_STOP:           ENV_STATUS_CONFLICT_DUPLICATE_DELETED_STOP,
	ENV_STATUS_CONFLICT_DUPLICATE_STOP:         ENV_STATUS_CONFLICT_DUPLICATE_STOP,
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_STOP: ENV_STATUS_CONFLICT_DUPLICATE_DELETED_STOP,

	ENV_STATUS_RUNNING:                            ENV_STATUS_CONFLICT_DUPLICATE_RUNNING,
	ENV_STATUS_CONFLICT_DELETED_RUNNING:           ENV_STATUS_CONFLICT_DUPLICATE_DELETED_RUNNING,
	ENV_STATUS_CONFLICT_DUPLICATE_RUNNING:         ENV_STATUS_CONFLICT_DUPLICATE_RUNNING,
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_RUNNING: ENV_STATUS_CONFLICT_DUPLICATE_DELETED_RUNNING,
}

var ENV_STATUS_DUPLICATE_DESCEND = map[string]string{
	ENV_STATUS_STOP:                            ENV_STATUS_STOP,
	ENV_STATUS_CONFLICT_DELETED_STOP:           ENV_STATUS_CONFLICT_DELETED_STOP,
	ENV_STATUS_CONFLICT_DUPLICATE_STOP:         ENV_STATUS_STOP,
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_STOP: ENV_STATUS_CONFLICT_DELETED_STOP,

	ENV_STATUS_RUNNING:                            ENV_STATUS_RUNNING,
	ENV_STATUS_CONFLICT_DELETED_RUNNING:           ENV_STATUS_CONFLICT_DELETED_RUNNING,
	ENV_STATUS_CONFLICT_DUPLICATE_RUNNING:         ENV_STATUS_RUNNING,
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_RUNNING: ENV_STATUS_CONFLICT_DELETED_RUNNING,
}

var ENV_STATUS_DELETE_ASCEND = map[string]string{
	ENV_STATUS_STOP:                            ENV_STATUS_CONFLICT_DELETED_STOP,
	ENV_STATUS_CONFLICT_DELETED_STOP:           ENV_STATUS_CONFLICT_DELETED_STOP,
	ENV_STATUS_CONFLICT_DUPLICATE_STOP:         ENV_STATUS_CONFLICT_DUPLICATE_DELETED_STOP,
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_STOP: ENV_STATUS_CONFLICT_DUPLICATE_DELETED_STOP,

	ENV_STATUS_RUNNING:                            ENV_STATUS_CONFLICT_DELETED_RUNNING,
	ENV_STATUS_CONFLICT_DELETED_RUNNING:           ENV_STATUS_CONFLICT_DELETED_RUNNING,
	ENV_STATUS_CONFLICT_DUPLICATE_RUNNING:         ENV_STATUS_CONFLICT_DUPLICATE_DELETED_RUNNING,
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_RUNNING: ENV_STATUS_CONFLICT_DUPLICATE_DELETED_RUNNING,
}

var ENV_STATUS_DELETE_DESCEND = map[string]string{
	ENV_STATUS_STOP:                            ENV_STATUS_STOP,
	ENV_STATUS_CONFLICT_DELETED_STOP:           ENV_STATUS_STOP,
	ENV_STATUS_CONFLICT_DUPLICATE_STOP:         ENV_STATUS_CONFLICT_DUPLICATE_STOP,
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_STOP: ENV_STATUS_CONFLICT_DUPLICATE_STOP,

	ENV_STATUS_RUNNING:                            ENV_STATUS_RUNNING,
	ENV_STATUS_CONFLICT_DELETED_RUNNING:           ENV_STATUS_RUNNING,
	ENV_STATUS_CONFLICT_DUPLICATE_RUNNING:         ENV_STATUS_CONFLICT_DUPLICATE_RUNNING,
	ENV_STATUS_CONFLICT_DUPLICATE_DELETED_RUNNING: ENV_STATUS_CONFLICT_DUPLICATE_RUNNING,
}
