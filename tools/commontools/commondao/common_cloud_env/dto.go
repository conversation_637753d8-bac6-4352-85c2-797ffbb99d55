package common_cloud_env

type CloudCreateReq struct {
	CloudEnv *CloudDto `json:"cloudEnv"`
	Dc       Dc        `json:"dc"`
}

type Dc struct {
	ID string `json:"id"`
}

type CloudDto struct {
	CloudCommon
}

type CloudChangeMsg struct {
	EnvID       string      `json:"envId"`
	EnvName     string      `json:"envName"`
	EnvType     string      `json:"envType"`
	DcId        string      `json:"dcId"`
	Description string      `json:"description"`
	Area        string      `json:"area"`
	Provider    string      `json:"provider"`
	Status      string      `json:"status"`
	Endpoints   []*Endpoint `json:"endpoints"`
	Operate     string      `json:"operate"`
}
