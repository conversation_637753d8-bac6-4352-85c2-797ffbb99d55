package common_cloud_env

import (
	"cwsm/tools/commontools/commondao/common_scen"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

type Endpoint struct {
	// Example: https://10.230.10.10:5000
	URL string `json:"url"`
	// Example: public-auth
	Name  string `json:"name"`
	Scope string `json:"scope"`
	// Example: v3
	Version  string `json:"version"`
	Password string `json:"password"`
	// Example: admin
	UserName string `json:"userName"`
	// Example: admin
	TenantName  string `json:"tenantName"`
	KmsPassword string `json:"kmsPassword"`
	// Example: b5634b6a-9240-4b3d-8bff-c433104deb35
	KmsKeyID          string           `json:"kmsKeyId"`
	SslAuthentication *restful.SSLAuth `json:"sslAuthentication"`
}

type Endpoint4Redis struct {
	Endpoint
	Base64OriginalPwd string `json:"504143c6e2a98f3b84f1541705d2b665"` // 'cipher_code' encoded by MD5
}

type ResourcePolicy struct {
	Name         string   `json:"name"`
	ResourceType []string `json:"resourceType"`
}

type Sso struct {
	IdpID string `json:"idpId"`
	SpURL string `json:"spUrl"`
}

type BridgeInfo struct {
	SpecialVimId string `json:"specialVimId"`
}

type VMBackupConfig struct {
	// Example: false
	Enabled bool   `json:"enabled"`
	IecsURL string `json:"iecs_url"`
	// Example: xin_zhi_dian
	ThirdPartyType   string `json:"third_party_type"`
	ThirdPartyAPIURL string `json:"third_party_api_url"`
}

type CheckItem struct {
	Item    string `json:"item"`
	Result  bool   `json:"result"`
	Message string `json:"message"`
}

type Net struct {
	Type     string `json:"netType"`
	IsTenant bool   `json:"isTenant"`
	Physnets string `json:"physnets"`
}

type Metadata4Oauth2 struct {
	IdpId      string `json:"idpId"`
	DomainId   string `json:"domainId"`
	ClientUUID string `json:"clientUUID"`
}

type CloudCommon struct {
	// Example: 3fea0957-e28e-4de1-b46b-67de3abb79b9
	ID string `json:"id" pgdb:"id"`
	// Example:
	Area string `json:"area" pgdb:"area"`
	// Example: test_ly02
	Name   string                 `json:"name" pgdb:"name"`
	Nets   []*Net                 `json:"nets" pgdb:"nets"`
	Sso    *Sso                   `json:"sso" pgdb:"sso"`
	Labels map[string]interface{} `json:"labels" pgdb:"labels"`
	// Example: openstack + kvm
	EnvType string `json:"envType" pgdb:"env_type"`
	// Example: ZTE
	Provider string `json:"provider" pgdb:"provider"`
	// Example: a62d391b-7ce4-4873-b3e9-a56745d99f7f
	DcID      string                 `json:"dcId" pgdb:"dc_id"`
	Metadata  map[string]interface{} `json:"metadata" pgdb:"metadata"`
	NFVHa     []*ResourcePolicy      `json:"NFVHa" pgdb:"nfv_ha"`
	ComputeHa []*ResourcePolicy      `json:"computeHa" pgdb:"compute_ha"`
	Endpoints []*Endpoint            `json:"endpoints" pgdb:"endpoints"`
	// Example: 2013-09-14T07:45:45Z
	CreatedAt string `json:"created_at" pgdb:"created_at"`
	// Example: 2013-09-14T07:45:45Z
	UpdatedAt      string            `json:"updated_at" pgdb:"updated_at"`
	APIVersions    map[string]string `json:"apiVersions" pgdb:"api_versions"`
	Description    string            `json:"description" pgdb:"description"`
	VMBackupConfig *VMBackupConfig   `json:"vmBackupConfig" pgdb:"vm_backup_config"`
	PoolPortalURL  string            `json:"poolPortalURL" pgdb:"pool_portal_url"`
	BridgeInfo     *BridgeInfo       `json:"bridgeInfo" pgdb:"bridge_info"`
	// Example: share
	ResourceType string `json:"resource_type" pgdb:"resource_type"`
	RelatedEnvID string `json:"related_env_id" pgdb:"related_env_id"`
	// Example: running
	Status         string          `json:"status" pgdb:"status"`
	StatusDescribe *StatusDescribe `json:"status_describe" pgdb:"status_describe"`
	// Example: cloudfuze
	SyncFrom string `json:"sync_from" pgdb:"sync_from"`
}

func (cr *CloudCommon) FetchAuthEndPoint() *Endpoint {
	for _, e := range cr.Endpoints {
		if e.Name == "public-auth" {
			return e
		}
	}
	return &Endpoint{}
}

func (cr *CloudCommon) FindEndPointInfoByName(endpointName string) (string,
	*Endpoint, error) {
	for _, e := range cr.Endpoints {
		if e.Name == endpointName {
			if strings.EqualFold(cr.EnvType, OPENPALETTEENVTYPE) {
				return AuthTypeOpenpalette, e, nil
			}
			if strings.EqualFold(e.Version, "v2") {
				return AuthTypeKeystoneV2, e, nil
			}
			return AuthTypeKeystoneV3, e, nil
		}
	}
	return "", nil, fmt.Errorf("FindEndPointInfoByName endpoint_name:%s failed", endpointName)
}

func (cr *CloudCommon) CheckIsCCM() bool {
	return cr.IsCCMDeploy() && (os.Getenv("scene") ==
		common_scen.GENARAL_SCENE || os.Getenv("scene") == common_scen.CCM_SCENE)
}

func (cr *CloudCommon) IsCCMDeploy() bool {
	m, ok := cr.Metadata["ccmdeploy"]
	return ok && m == "True"
}

func (cr *CloudCommon) NeedToRedis() bool {
	return cr.Status == ENV_STATUS_RUNNING
}

type StatusDescribe struct {
	MsgInfo map[string]string `json:"msgInfo"`
}

func (cr *CloudCommon) GetIdpInfoByKind(key string) (*Metadata4Oauth2, error) {
	idp := &Metadata4Oauth2{}
	if idpInfo, ok := cr.Metadata[key]; ok {
		err := json.Unmarshal([]byte(util.ToStr(idpInfo)), idp)
		return idp, err
	}
	logger.Info(fmt.Sprintf("%s not found in metadata: %s", key, util.ToStr(cr.Metadata)))
	return idp, fmt.Errorf("key:%s not found in metadata", key)
}
