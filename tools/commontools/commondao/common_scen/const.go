package common_scen

import "os"

const (
	GENARAL_SCENE      = "2"
	TOB_SCENE          = "5"
	CCM_SCENE          = "6"
	MEO_SCENE          = "7"
	MCM_SCENE          = "8"
	CIIA_SCENE         = "11"
	ICF_DIRECTOR_SCENE = "12"
)

func GENARALSCENE() bool {
	return os.Getenv("scene") == GENARAL_SCENE
}

func TOBSCENE() bool {
	return os.Getenv("scene") == TOB_SCENE
}

func ICFDIRECTORSCENE() bool {
	return os.Getenv("scene") == ICF_DIRECTOR_SCENE
}

func CCMSCENE() bool {
	return os.Getenv("scene") == CCM_SCENE
}

func MEOSCENE() bool {
	return os.Getenv("scene") == MEO_SCENE
}

func MCMSCENE() bool {
	return os.Getenv("scene") == MCM_SCENE
}
