package logger

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/fx"
	"zte.com.cn/dexcloud/go-mars/config"
	"zte.com.cn/dexcloud/go-mars/logger"
)

func Init() {
	go InitSysLog("/var/zte-log/testlog/zte-cwsm/logs/all/cwsm.log")
	app := fx.New(
		config.NewModule("conf/go-mars.yaml"),
	)
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	if err := app.Start(ctx); err != nil {
		Errorf("go mars start error: %v", err)
		return
	}
	Infof("go mars start success")
}

func FilterIllegalChar(desc *string, msg ...interface{}) {
	*desc = replaceIllegalChar4One(*desc)
	for i := range msg {
		if m, ok := msg[i].(string); ok {
			msg[i] = replaceIllegalChar4One(m)
		}
		if m, ok := msg[i].([]byte); ok {
			msg[i] = []byte(replaceIllegalChar4One(string(m)))
		}
	}
}

func replaceIllegalChar4One(orig string) string {
	return strings.ReplaceAll(strings.ReplaceAll(orig, "assword", "**"), "oken", "**")
}

func InitSysLog(logPath string) {
	if _, e := os.Stat(filepath.Dir(logPath)); e != nil {
		log.Printf("log file %s is not existence: %v", logPath, e)
		os.MkdirAll(filepath.Dir(logPath), 750)
	}

	path, _ := os.Getwd()
	yamlPath := path + "/conf/go-mars.yaml"

	l, err := logger.NewLogger(yamlPath, logger.TimeEncoder("2006-01-02 15:04:05,000"))
	if err != nil {
		log.Fatalf("yaml file path %s failed create go-mars logger: %v", yamlPath, err)
		return
	}
	SetLogger(l)
	logger.SetLogger(l)
	fx.Logger(GetLogger().CreateStdLogger())
}

func SetLogLevel(value interface{}) error {
	if level, ok := value.(string); ok {
		logger.SetLevel(strings.ToLower(level))
		return nil
	}
	return fmt.Errorf("set log level error")
}

func GetLogLevel() string {
	return logger.GetLevel()
}
