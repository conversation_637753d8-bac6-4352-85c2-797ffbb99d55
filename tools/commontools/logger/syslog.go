package logger

import (
	"zte.com.cn/dexcloud/go-mars/logger"
)

var _log logger.Logger

func GetLogger() logger.Logger {
	return _log
}

func SetLogger(l logger.Logger) {
	_log = l
}

func Debug(format string, args ...interface{}) {
	Debugf(format, args...)
}

func Debugf(format string, args ...interface{}) {
	if _log == nil {
		logger.Debugf(format, args...)
	} else {
		FilterIllegalChar(&format, args...)
		_log.AddCallerSkip(2).Debugf(format, args...)
	}
}

func Info(format string, args ...interface{}) {
	Infof(format, args...)
}

func Infof(format string, args ...interface{}) {
	if _log == nil {
		logger.Infof(format, args...)
	} else {
		FilterIllegalChar(&format, args...)
		_log.AddCallerSkip(2).Infof(format, args...)
	}
}

func Warn(format string, args ...interface{}) {
	Warnf(format, args...)
}

func Warnf(format string, args ...interface{}) {
	if _log == nil {
		logger.Warnf(format, args...)
	} else {
		FilterIllegalChar(&format, args...)
		_log.AddCallerSkip(2).Warnf(format, args...)
	}
}

func Error(format string, args ...interface{}) {
	Errorf(format, args...)
}

func Errorf(format string, args ...interface{}) {
	if _log == nil {
		logger.Errorf(format, args...)
	} else {
		FilterIllegalChar(&format, args...)
		_log.AddCallerSkip(2).Errorf(format, args...)
	}
}
