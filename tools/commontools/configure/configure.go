package configure

import (
	"cwsm/tools/commontools/infa/util"
	"encoding/json"
)

type ConfigInF interface {
	//MSB配置方法，返回MSBDto类型
	GetMSBConfig() *MSBDto
	//日志配置方法，返回LogConfigDto类型
	GetLogConfig() *LogConfigDto
	//其他配置信息
	GetOtherConfig() *OtherDto
	//开发模式使用的配置信息
	GetDevConfig() *DevConfDto
}

// SettingDto的实现类
type Configure struct {
	SettingDto
}

func NewConfigure(filePath string) (ConfigInF, error) {
	//var absFilePath = "/home/<USER>/pvrm/go/src/pvrm/" + filePath
	var absFilePath = util.GetFilePath(filePath)
	//logger.Infof("settings:" + absFilePath)

	fileContent, err := util.ReadFile(absFilePath)
	if err != nil {
		//logger.Errorf("Open "+absFilePath+" failed,err:", err)
		return nil, err
	}
	//logger.Infof("load setting.json file successfully.")

	var setting SettingDto
	err = json.Unmarshal(fileContent, &setting)
	if err != nil {
		//logger.Errorf("Unmarshal fileContenterr!", setting)
		return nil, err
	}

	return &Configure{setting}, nil
}

func (c *Configure) GetMSBConfig() *MSBDto {
	var msbConfig = c.MSBInfo
	return &msbConfig
}

func (c *Configure) GetLogConfig() *LogConfigDto {
	var logConf = c.LogConfigInfo
	return &logConf
}

func (c *Configure) GetDevConfig() *DevConfDto {
	var devConf = c.DevConfInfo
	return &devConf
}

func (c *Configure) GetOtherConfig() *OtherDto {
	var otherConfig = c.OtherInfo
	return &otherConfig
}
