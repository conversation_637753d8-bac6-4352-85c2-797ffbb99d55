package configure

type SettingDto struct {
	DevConfInfo   DevConfDto   `json:"devconf"`
	LogConfigInfo LogConfigDto `json:"logger"`
	MSBInfo       MSBDto       `json:"msb"`
	OtherInfo     OtherDto     `json:"other"`
}

type OtherDto struct {
	Language string `json:"language"`
}

type MSBDto struct {
	MsbIP        string `json:"msbip"`
	MsbInnerPort string `json:"msbinnerport"`
}

type FTPConfigDto struct {
	FTPAddress  string `json:"ftpaddress"`
	FTPPort     string `json:"ftpport"`
	FTPUser     string `json:"ftpuser"`
	FTPPassword string `json:"ftppassword"`
}

type DBConfigDto struct {
	DBDriver   string `json:"dbdriver"`
	DBType     string `json:"dbtype"`
	DBHost     string `json:"dbhost"`
	DBPort     string `json:"dbport"`
	DBUser     string `json:"dbuser"`
	DBPassword string `json:"dbpassword"`
	DBName     string `json:"dbname"`
}

type DevConfDto struct {
	Isdev         bool          `json:"isdev"`
	MsbIP         string        `json:"msbip"`
	MsbInnerPort  string        `json:"msbinnerport"`
	Header        *HeaderDto    `json:"header"`
	DBConfigInfo  *DBConfigDto  `json:"database"`
	FTPConfigInfo *FTPConfigDto `json:"ftp"`
}

type HeaderDto struct {
	CsrfToken string `json:"csrftoken"`
	Cookie    string `json:"Cookie"`
	AuthCode  string `json:"Z-AUTH-CODE"`
	UserAgent string `json:"UserAgent"`
}

// beego 日志配置结构体
type LogConfigDto struct {
	AdapterMultiFileDto
	Level               int     `json:"level"`               // 日志保存的时候的级别，默认是 Trace 级别
	Maxlines            int     `json:"maxlines"`            // 每个文件保存的最大行数，默认值 1000000
	RotatePerm          string  `json:"rotateperm"`          // 应该是权限
	EnableFuncCallDepth bool    `json:"enablefunccalldepth"` // 输出文件名和行号
	LogFuncCallDepth    int     `json:"logfunccalldepth"`    // 函数调用层级
	ErrorFileName       string  `json:"errorfilename"`       // 项目增加的参数，用于区分日志目录
	FilePath            string  `json:"filePath"`            // 项目增加的参数，用于统计日志目录大小
	ClearMinute         float64 `json:"clearMinute"`         // 项目增加的参数，定时清理日志的时间间隔
	LogDirMaxSize       float64 `json:"logDirMaxSize"`       // 项目增加的参数，日志目录大小
}

type AdapterMultiFileDto struct {
	FileName string   `json:"filename"`
	Separate []string `json:"separate"` // 单独记录的日记级别
	Maxsize  int      `json:"maxsize"`  // 每个文件保存的最大尺寸，默认值是 1 << 28, //256 MB
	Daily    bool     `json:"daily"`    // 是否按照每天 logrotate，默认是 true
	Maxdays  int      `json:"maxdays"`  // 文件最多保存多少天，默认保存 7 天
	Rotate   bool     `json:"rotate"`   // 是否开启 logrotate，默认是 true
	Perm     string   `json:"perm"`     // 日志文件权限
}
