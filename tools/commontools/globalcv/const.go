package globalcv

const (
	RegExpStrUUID   = "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[12345][0-9a-fA-F]{3}-[89ab][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$"
	RegExpNamespace = "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$"
)

// http状态码
const (
	HTTPCodeContinue100 = 100

	HTTPCodeSuccess200  = 200
	HTTPCodeCreated201  = 201
	HTTPCodeAccepted202 = 202
	HTTPCodeDelete204   = 204

	HTTPCodeSuccessMax300 = 300

	HTTPCodeBadRequest400    = 400
	HTTPCodeUnAuthorized401  = 401
	HTTPCodeForbidden403     = 403
	HTTPCodeNotFound404      = 404
	HTTPCodeNotAcceptable406 = 406
	HTTPCodeConflict409      = 409

	HTTPCodeServerError500 = 500
	HTTPCodeBadGateway502  = 502
)

// http请求方法
const (
	HTTPRequestMethodGet    = "GET"
	HTTPRequestMethodPost   = "POST"
	HTTPRequestMethodPut    = "PUT"
	HTTPRequestMethodPatch  = "PATCH"
	HTTPRequestMethodDelete = "DELETE"
)

const (
	HTTPPrefix  = "http://"
	GatewayHost = "gateway_host"
)

const (
	HeaderOperateuser         = "username"
	HeaderAccessToken         = "Access-Token"
	HeaderXAuthToken          = "X-Auth-Token"
	ProviderHeaderAccessToken = "access-token"
	HeaderXForwardedFor       = "X-Forwarded-For"
	ResoucesIdsKey            = "AuthResourceIds"
	XSubjectTokenHeader       = "x-subject-token"
	XAuthToken                = "X-Auth-Token"
)

const (
	FtpConfName          = "OPENPALETTE_FTPSERVICECONFIG_USERPASSWORD"
	SecreteKeyIDFileName = "OPENPALETTE_INSTANCE_SECRETKEY_ID"
)

const (
	OPAPIURLFmt      = "/opapi/kms/v1/tenants"
	SYSURLFmt        = "/system_type/ume/secretkey"
	KMSNATIVEFmt     = "kms-native:443/tenants"
	SYSTYPEURLFmt    = "/system_type/ume/secretkey"
	SYSTYPEURLAPPFmt = "/system_type/ume/applications"

	URLFmt                  = "http://%s:%s" + OPAPIURLFmt + "/%s" + SYSURLFmt
	URLFmtCloudNative       = "https://" + KMSNATIVEFmt + "/%s" + SYSTYPEURLFmt
	DetailURLFmt            = URLFmt + "/%s"
	DetailURLFmtCloudNative = URLFmtCloudNative + "/%s"
	RevokeURLFmt            = URLFmt + "/revoke_notification"
	RevokeURLFmtCloudNative = URLFmtCloudNative + "/revoke_notification"

	LatestURLFmt            = "http://%s:%s" + OPAPIURLFmt + "/%s" + SYSTYPEURLAPPFmt
	LatestURLFmtCloudNative = "https://" + KMSNATIVEFmt + "/%s" + SYSTYPEURLAPPFmt
)

// Cloud Types
const (
	CloudTypeOpenPalette   = "openpalette"
	CloudTypeTCFKube       = "tcf-k8s"
	CloudTypeOpenStackKube = "openstack+k8s"
)

// openstack component
const (
	InterfaceType     = "public"
	KeystoneVersionV3 = "v3"
	GlanceName        = "glance"
	ImageName         = "Image Service"
	NovaName          = "nova"
)

const (
	FakeKey4VolumeDB = "fake_volumes"

	IdNotExist = "idNotExist"
	VimID1     = "vim-id1"
	VimID2     = "vim-id2"
	VimID3     = "vim-id3"
	VimID4     = "vim-id4"
	CloudID1   = "env-id1"
	CloudID2   = "env-id2"
	CloudID3   = "env-id3"
	CloudID4   = "env-id4"
	FlavorId1  = "flv-id-1"
	ProjectId1 = "proj-id-1"
	ProjectId2 = "proj-id-2"
	VdcId1     = "fake-vdc-1"
	VdcId2     = "fake-vdc-2"
	Ipv4_1     = "***********"
	Ipv4_2     = "***********"
	Ipv6_1     = "2001:db8:0:1::101"
	Ipv6_2     = "2001:db8:0:1::102"
	ImageId1   = "imageId_1"

	VolumeTypeID1 = "volume-type-id1"
	VolumeTypeID2 = "volume-type-id2"

	VolumeID1      = "volume-id1"
	VolumeID2      = "volume-id2"
	VolumeGroupID1 = "volume-group-id1"
	VolumeGroupID2 = "volume-group-id2"
)

const (
	CATALOG_NEUTRON  = "neutron"
	CATALOG_FREEZER  = "freezer"
	CATALOG_KEYSTONE = "keystone"
	CATALOG_NOVA     = "nova"
	CATALOG_GLANCE   = "glance"
	CATALOG_CINDERV2 = "cinderv2"
	CATALOG_CINDERV3 = "cinderv3"
)

const (
	NORMAL_ENV_STATUS = "normal"
)
