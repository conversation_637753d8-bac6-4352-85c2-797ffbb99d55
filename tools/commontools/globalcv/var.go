package globalcv

var (
	DbDriver   = ""
	DbType     = ""
	DbHost     = ""
	DbPort     = ""
	DbUser     = ""
	DbPassword = ""
	DbName     = ""
)

type MsbRestClientInfoDto struct {
	ServiceName string `json:"serviceName"`
	URL         string `json:"url"`
	NameSpace   string `json:"namespace"`
	Port        string `json:"port"`
}

var ServiceURLMaps = make(map[string]MsbRestClientInfoDto)

var (
	MsbURLPath = ""
)
