package userdomain

import (
	"cwsm/tools/commontools/cloudtoken"
	"cwsm/tools/commontools/commondao/common_cloud_env"
	"cwsm/tools/commontools/storage/rediz/storage/cloud_env_redis"
	"cwsm/tools/commontools/storage/rediz/storage/tenant_redis"
	"fmt"
)

/* Started by AICoder, pid:1da31201e1e04d7bbf62381a810b3e54 */
func GetLuckyUser(envId, tenantId, endpointname string) (string, string, string, cloudtoken.AuthType, *common_cloud_env.Endpoint4Redis, error) {
	env4Redis, err := cloud_env_redis.Get(envId)
	if err != nil {
		return "", "", "", "", nil, fmt.Errorf("fetchLuckerUser cloud_env_redis Get failed: envId %s is not exist: %v", envId, err)
	}

	authType, endPoint, err := env4Redis.FindSpecialEndPointInfo(endpointname)
	if err != nil {
		return "", "", "", "", nil, fmt.Errorf("fetchLuckerUser FindSpecialEndPointInfo failed: envId %s endpoint %s is not exist: %v", envId, endpointname, err)
	}

	if common_cloud_env.CheckIsOpenstackCloud(env4Redis.EnvType) {
		return getLuckyUserOpenstack(envId, tenantId, authType, endPoint, env4Redis)
	} else {
		return endPoint.UserName, endPoint.Base64OriginalPwd, endPoint.TenantName, authType, endPoint, nil
	}
}

func getLuckyUserOpenstack(envId, tenantId string, authType cloudtoken.AuthType, endPoint *common_cloud_env.Endpoint4Redis, env4Redis *cloud_env_redis.CloudEnvRedisDao) (string, string, string, cloudtoken.AuthType, *common_cloud_env.Endpoint4Redis, error) {
	tenant4Redis, err := tenant_redis.Get(envId, tenantId)
	if err != nil {
		return "", "", "", "", nil, fmt.Errorf("fetchLuckerUser tenant_redis Get failed: tenant %s not under envId %s: %v", tenantId, envId, err)
	}

	if authType == cloudtoken.AuthTypeKeystoneV3 {
		return getLuckyUserOpenstackKeystoneV3(tenant4Redis, authType, endPoint, env4Redis)
	} else {
		return endPoint.UserName, endPoint.Base64OriginalPwd, tenant4Redis.Name, authType, endPoint, nil
	}
}

func getLuckyUserOpenstackKeystoneV3(tenant4Redis *tenant_redis.Tenant4Redis, authType cloudtoken.AuthType, endPoint *common_cloud_env.Endpoint4Redis, env4Redis *cloud_env_redis.CloudEnvRedisDao) (string, string, string, cloudtoken.AuthType, *common_cloud_env.Endpoint4Redis, error) {
	validUsers := getValidUsers(tenant4Redis)
	leftUsers := getLeftUsers(validUsers)

	if len(leftUsers) != 0 {
		return leftUsers[0].Name, leftUsers[0].Password, tenant4Redis.Name, authType, endPoint, nil
	} else if len(validUsers) != 0 {
		return validUsers[0].Name, leftUsers[0].Password, tenant4Redis.Name, authType, endPoint, nil
	} else {
		return "", "", "", "", nil, fmt.Errorf("fetchLuckerUser failed: can not find valid user under tenant:%s env:%s ", tenant4Redis.ID, env4Redis.ID)
	}
}

func getValidUsers(tenant4Redis *tenant_redis.Tenant4Redis) []*tenant_redis.UserRoles4Redis {
	validUsers := []*tenant_redis.UserRoles4Redis{}
	for _, userRole := range tenant4Redis.UserRoles {
		if userRole.Name != "" && userRole.Password != "" {
			validUsers = append(validUsers, userRole)
		}
	}
	return validUsers
}

func getLeftUsers(validUsers []*tenant_redis.UserRoles4Redis) []*tenant_redis.UserRoles4Redis {
	leftUsers := []*tenant_redis.UserRoles4Redis{}
	for _, userRole := range validUsers {
		for _, role := range userRole.Roles {
			if role.Name == "prj_manager" {
				leftUsers = append(leftUsers, userRole)
			}
		}
	}
	if len(leftUsers) == 0 {
		for _, userRole := range validUsers {
			for _, role := range userRole.Roles {
				if role.Name == "admin" {
					leftUsers = append(leftUsers, userRole)
				}
			}
		}
	}
	return leftUsers
}

/* Ended by AICoder, pid:1da31201e1e04d7bbf62381a810b3e54 */
