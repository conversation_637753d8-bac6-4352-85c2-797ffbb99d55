package tokendomain

import (
	"cwsm/tools/commontools/cloudtoken"
	"cwsm/tools/commontools/commondao/common_cloud_env"
	"cwsm/tools/commontools/director/cloudfuze"
	"cwsm/tools/commontools/domainservice/userdomain"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz/storage/cloud_env_redis"
	"cwsm/tools/commontools/storage/rediz/storage/vdc_tenant_redis"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
)

func GetAccessCloud(cloudEnvId, tenantId string, catalogs []string) (map[string]*cloudtoken.AccessInfo, *cloud_env_redis.CloudEnvRedisDao, error) {
	accessInfos, cloud4redis, err := GetAccessByEtvKey(&cloudtoken.EtvKey{EnvId: cloudEnvId, TenantId: tenantId,
		Catalogs: catalogs})
	if err != nil {
		logger.Error("[GetAccessCloud] GetAccessByEtvKey failed: %v", err)
		return nil, nil, err
	}

	cloudAuths := map[string]*cloudtoken.AccessInfo{}
	for _, catalog := range catalogs {
		endpointUrl := accessInfos[catalog].Url

		if endpointUrl == "" {
			logger.Error("[GetAccessCloud]: public url is empty")
			return nil, nil, fmt.Errorf("public url is empty")
		}
		cloudAuthOpenstack := &cloudtoken.AccessInfo{}
		cloudAuthOpenstack.Url = endpointUrl
		cloudAuthOpenstack.Token = accessInfos[catalog].Token
		cloudAuthOpenstack.SSlAuth = accessInfos[catalog].SSlAuth
		cloudAuths[catalog] = cloudAuthOpenstack
	}

	return cloudAuths, cloud4redis, nil
}

func GetAccessByEtvKey(etvKey *cloudtoken.EtvKey) (map[string]*cloudtoken.AccessInfo, *cloud_env_redis.CloudEnvRedisDao, error) {
	etvKey.Interfaces = []string{"public"}
	authEndPoint, cloud4Redis, universalRSP, err := GetCloudAndToken(etvKey)

	if err != nil {
		logger.Error("GetAccessByEtvKey Error: GetCloudAndToken failed, error: %v", err)
		return nil, nil, err
	}
	accessInfos := map[string]*cloudtoken.AccessInfo{}
	for _, catalog := range etvKey.Catalogs {
		accessInfo := &cloudtoken.AccessInfo{}
		accessInfo.SSlAuth = authEndPoint.SslAuthentication
		accessInfo.Token = universalRSP.GetToken()
		for _, cata := range universalRSP.Catalog {
			if strings.EqualFold(cata.Name, catalog) {
				if len(cata.Endpoints) >= 1 {
					accessInfo.Url = cata.Endpoints[0].URL
				}
			}
		}
		logger.Info("[GetAccessByEtvKey] url: %s ,for catalog: %s", accessInfo.Url, catalog)
		accessInfos[catalog] = accessInfo
	}
	return accessInfos, cloud4Redis, nil
}

func GetCloudAndToken(etv *cloudtoken.EtvKey) (*common_cloud_env.Endpoint4Redis, *cloud_env_redis.CloudEnvRedisDao, *cloudtoken.UniversalRSP, error) {
	cloud4Redis, err := cloud_env_redis.Get(etv.EnvId)
	if err != nil {
		logger.Errorf("[GetCloudAndRspByEtvKey] cloud_env_redis Get failed, cloud: %s, error: %v", etv.EnvId, err)
		return nil, nil, nil, err
	}

	if etv.EndpointName == "" {
		etv.EndpointName = common_cloud_env.ENDPOINT_PUBLIC_AUTH
	}
	authType, endPoint, err := cloud4Redis.FindSpecialEndPointInfo(etv.EndpointName)
	if err != nil {
		logger.Errorf("[GetCloudAndRspByEtvKey] failed, error %v", err)
		return nil, nil, nil, err
	}

	key, err := EtvToRedisKey(etv, endPoint, authType)
	if err != nil {
		logger.Errorf("[GetCloudAndRspByEtvKey] etv.ToRedisKey failed, error %v", err)
	} else {
		logger.Debugf("[GetCloudAndRspByEtvKey] key = %s", key)
		universalRSP, err := cloudtoken.ReadOnly(key, etv.Catalogs, etv.Interfaces)
		if err == nil {
			return endPoint, cloud4Redis, universalRSP, nil
		}
		logger.Errorf("[GetCloudAndRspByEtvKey] get universalRSP from redis failed, error %v", err)
	}

	universalRSP, err := GetTokenByEtvKey(etv)
	if err != nil {
		logger.Errorf("[GetTokenByEtvKey] failed, error: %v", err)
		return nil, nil, nil, err
	}

	return endPoint, cloud4Redis, universalRSP, nil
}

func EtvToRedisKey(etvKey *cloudtoken.EtvKey, endPoint *common_cloud_env.Endpoint4Redis, authType cloudtoken.AuthType) (string, error) {
	eId, tId, vId := etvKey.EnvId, etvKey.TenantId, etvKey.VdcId
	if vId != "" {
		vdcTenant4Redis, err := vdc_tenant_redis.GetVdcEnv(vId, eId)
		if err != nil {
			logger.Errorf("[EtvToRedisKey] vdc_tenant_redis GetVdcEnv failed vdc %s env %s: %v", vId, eId, err)
			return "", err
		}
		tId = vdcTenant4Redis.TenantId
	}
	if tId != "" {
		username, _, tenantName, authType, endpoint, err := userdomain.GetLuckyUser(eId, tId, etvKey.EndpointName)
		if err != nil {
			logger.Errorf("[EtvToRedisKey] GetLuckyUser envId:%s, tenantId:%s failed: %v", eId, tId, err)
			return "", err
		}
		return cloudtoken.Key(string(authType), endpoint.URL, username, tenantName, endpoint.Scope, "", "", ""), nil
	}
	return cloudtoken.Key(string(authType), endPoint.URL, endPoint.UserName, endPoint.TenantName, endPoint.Scope, "", "", ""), nil
}

func GetTokenByEtvKey(etv *cloudtoken.EtvKey) (*cloudtoken.UniversalRSP, error) {
	var body []byte
	var err error
	if etv.EnvId != "" {
		if etv.VdcId != "" {
			body, err = cloudfuze.GetTokenBodyByEnvIdAndVdcId(etv)
		} else if etv.TenantId != "" {
			body, err = cloudfuze.GetTokenBodyByEnvIdAndTenantId(etv)
		} else {
			body, err = cloudfuze.GetTokenBodyByEnvId(etv)
		}
	} else {
		err = errors.New("empty envId")
	}

	if err != nil {
		logger.Errorf("[GetTokenByEtvKey] failed: %v", err.Error())
		return nil, err
	}

	rsp := &struct {
		Token *cloudtoken.UniversalRSP `json:"tokens"`
	}{}
	err = json.Unmarshal(body, rsp)
	if err != nil {
		logger.Errorf("[GetTokenByEtvKey] failed: %v", err.Error())
		return nil, err
	}

	return rsp.Token, nil
}

func Get_Auth_Token(cloud4Redis *cloud_env_redis.CloudEnvRedisDao) (*cloudtoken.UniversalRSP, error) {
	return Get_Token(cloud4Redis, common_cloud_env.ENDPOINT_PUBLIC_AUTH, []string{"all"}, []string{"all"})
}

func Get_Private_Auth_Token(cloud4Redis *cloud_env_redis.CloudEnvRedisDao) (*cloudtoken.UniversalRSP, error) {
	return Get_Token(cloud4Redis, common_cloud_env.ENDPOINT_PRIVATE_AUTH, []string{"all"}, []string{"all"})
}

func Get_Private_Portal_Token(cloud4Redis *cloud_env_redis.CloudEnvRedisDao) (*cloudtoken.UniversalRSP, error) {
	return Get_Token(cloud4Redis, common_cloud_env.ENDPOINT_PRIVATE_PORTAL, []string{"all"}, []string{"all"})
}

func Get_Token(cloud4Redis *cloud_env_redis.CloudEnvRedisDao, endpoint_name string,
	catalogs, interfaces []string) (*cloudtoken.UniversalRSP, error) {
	logger.Debugf("[Get_Token] cloud id:%s start", cloud4Redis.ID)
	authType, endPoint4Auth, err := cloud4Redis.FindSpecialEndPointInfo(endpoint_name)
	if err != nil {
		logger.Errorf("[Get_Token] findSpecialEndPoint error: %v", err)
		return nil, err
	}
	request := &cloudtoken.UniversalREQ{
		URL:        endPoint4Auth.URL,
		Type:       authType,
		User:       endPoint4Auth.UserName,
		Domain:     endPoint4Auth.Scope,
		CipherCode: endPoint4Auth.Base64OriginalPwd,
		Project:    endPoint4Auth.TenantName,
		SSLAuth:    endPoint4Auth.SslAuthentication,
	}
	return Get_Token_REQ(request, catalogs, interfaces)
}

func Get_Token_REQ(request *cloudtoken.UniversalREQ, catalogs, interfaces []string) (*cloudtoken.UniversalRSP, error) {
	logger.Debugf("[Get_Token_REQ] cloud request:%s start", util.ToJSONStr(request))
	if response, err := cloudtoken.Load(request.Key(), catalogs, interfaces, false); err == nil {
		logger.Debugf("[Get_Token_REQ] load cata_type_size: %d", len(response.Catalog))
		return response, nil
	}
	if err := cloudtoken.Create(request, true); err != nil {
		logger.Debugf("[Get_Token_REQ] token.Create error: %v", err)
		return nil, err
	}
	response, err := cloudtoken.Load(request.Key(), catalogs, interfaces, false)
	if err != nil {
		logger.Errorf("[Get_Token_REQ] token.Load error: %v", err)
		return nil, err
	}
	logger.Debugf("[Get_Token_REQ] cloud id:%s end:%s", util.ToJSONStr(response))
	return response, nil
}

func Post_Auth_Token(cloud4Redis *cloud_env_redis.CloudEnvRedisDao) (*cloudtoken.UniversalRSP, error) {
	return Post_Token(cloud4Redis, common_cloud_env.ENDPOINT_PUBLIC_AUTH, []string{"all"}, []string{"all"})
}

func Post_Token(cloud4Redis *cloud_env_redis.CloudEnvRedisDao, endpoint_name string,
	catalogs, interfaces []string) (*cloudtoken.UniversalRSP, error) {
	logger.Debugf("[Post_Token] cloud id:%s start", cloud4Redis.ID)
	authType, endPoint4Auth, err := cloud4Redis.FindSpecialEndPointInfo(endpoint_name)
	if err != nil {
		logger.Errorf("[Post_Token] findSpecialEndPoint error: %v", err)
		return nil, err
	}
	request := &cloudtoken.UniversalREQ{
		URL:        endPoint4Auth.URL,
		Type:       authType,
		User:       endPoint4Auth.UserName,
		Domain:     endPoint4Auth.Scope,
		CipherCode: endPoint4Auth.Base64OriginalPwd,
		Project:    endPoint4Auth.TenantName,
		SSLAuth:    endPoint4Auth.SslAuthentication,
	}
	return Post_Token_REQ(request, catalogs, interfaces)
}

func Post_Token_REQ(request *cloudtoken.UniversalREQ, catalogs, interfaces []string) (*cloudtoken.UniversalRSP, error) {

	logger.Debugf("[Post_Token_UniversalREQ] cloud request:%s start", util.ToJSONStr(request))
	if err := cloudtoken.Create(request, true); err != nil {
		logger.Errorf("[Post_Token_UniversalREQ] token.Create error: %v", err)
		return nil, err
	}
	response, err := cloudtoken.Load(request.Key(), catalogs, interfaces, false)
	if err != nil {
		logger.Errorf("[Post_Token_UniversalREQ] token.Load error: %v", err)
		return nil, err
	}
	logger.Debugf("[Post_Token_UniversalREQ] end:%s", util.ToJSONStr(response))
	return response, nil
}
