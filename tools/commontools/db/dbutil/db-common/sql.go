package dbcommon

import (
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/logger"
	"database/sql"
	"strconv"
)

const PlaceHolder string = "$PlaceHolder$"

func ConnectDB(driver string, dataSource string) (*sql.DB, error) {
	conn, err := sql.Open(driver, dataSource)
	if err != nil {
		//logger.Fatal("", "Cannot connect to ", driver, "!", err)
		logger.Errorf("Cannot connect to ", driver, "!", err)
	} else {
		err = isValidDbConn(conn)
	}
	conn.SetMaxOpenConns(100)
	conn.SetMaxIdleConns(10)
	return conn, err
}

func isValidDbConn(conn *sql.DB) error {
	validateSQL := "select 1 as one"
	results, err := conn.Query(validateSQL)
	if err == nil {
		err = results.Close()
	} else {
		//logger.Fatal("", "Validate db connection failed! Error:", err)
		logger.Errorf("Validate db connection failed! Error:", err)
	}
	return err
}

func ParseSQLResults(rows *sql.Rows) []map[string]interface{} {
	columns, err := rows.Columns()
	if err != nil {
		logger.Errorf("error in scanning columns:", err)
		return nil
	}
	cellsPerRow := make([]interface{}, len(columns))
	valPointers := make([]interface{}, len(columns))
	for i := range cellsPerRow {
		valPointers[i] = &cellsPerRow[i]
	}

	results := []map[string]interface{}{}
	for rows.Next() {
		err = rows.Scan(valPointers...)
		if err != nil {
			logger.Errorf("Error in scanning row: ", err)
			continue
		}
		result := map[string]interface{}{}
		for i, col := range columns {
			result[col] = cellsPerRow[i]
		}
		results = append(results, result)
	}

	return results
}

func ConvertColValues(rows []map[string]string, cols map[string]*dbcore.Column) []map[string]interface{} {
	convertedRows := make([]map[string]interface{}, len(rows))
	var err error
	for idx, row := range rows {
		convertedRows[idx] = map[string]interface{}{}
		for k, v := range row {
			col, found := cols[k]
			if found && "" != v {
				switch col.ColumnType {
				case dbcore.ColType.BOOL:
					convertedRows[idx][k], err = strconv.ParseBool(v)
				case dbcore.ColType.FLOAT:
					convertedRows[idx][k], err = strconv.ParseFloat(v, 64)
				case dbcore.ColType.INT, dbcore.ColType.BIGINT, dbcore.ColType.SMALLINT:
					convertedRows[idx][k], err = strconv.ParseInt(v, 10, 64)
				default:
					convertedRows[idx][k] = v
				}
			} else {
				convertedRows[idx][k] = v
			}
		}
	}
	if err != nil {
		logger.Errorf(err.Error())
	}
	return convertedRows
}

func GetQueryMode(mode int) string {
	switch mode {
	case dbcore.QueryMode.DISTINCT:
		return "distinct"
	default:
		return ""
	}
}
