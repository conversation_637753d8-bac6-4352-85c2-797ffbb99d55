package dbcommon

import (
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"fmt"
)

type DbInfo struct {
	Driver   int
	Hosts    []string
	Port     int
	User     string
	Password string
	Db       string
}

func (d *DbInfo) ToString() string {
	return fmt.Sprintf("%s://%s:%d/?user=%s", d.<PERSON>(), d.<PERSON>[0], d.Port, d.User)
}

func (d *DbInfo) DriverName() string {
	switch d.Driver {
	case dbcore.DbDriver.POSTGRESQL:
		return "postgres"
	case dbcore.DbDriver.CASSANDRA:
		return "cassandra"
	case dbcore.DbDriver.MEMORY:
		return "in-memory"
	default:
		return "mysql"
	}
}
