package db_init

import (
	"cwsm/tools/commontools/db/dbutil"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/pwdservice"
	"os"

	"zte.com.cn/cms/crmX/commontools/devconfig"
	"zte.com.cn/cms/crmX/commontools/infa/util"

	"zte.com.cn/cms/crmX/commontools/configure"
)

func InitDB(devConfigInfo configure.DevConfDto) {
	initPgDb(devConfigInfo)
}

func initPgDb(devConfigInfo configure.DevConfDto) {
	logger.Infof("InitDB isdev:", util.ToStr(devconfig.IsDev))

	if devconfig.IsDev {
		setDBConfigFromJSONFile(devConfigInfo.DBConfigInfo)
	} else {
		SetDBConfig()
	}

	logger.Infof("DBConfig:dbUser=" + globalcv.DbUser +
		",dbName=" + globalcv.DbName +
		",dbHost=" + globalcv.DbHost +
		",dbPort=" + util.ToStr(globalcv.DbPort))

	if dbutil.PostgreSQL == globalcv.DbType || dbutil.Memory == globalcv.DbType {
		err := dbutil.InitDbClient()
		if err != nil {
			logger.Errorf("conn db failed ", globalcv.DbName)
		}
	}
}

func SetDBConfig() {
	postgresDbHostAddress := os.Getenv("OPENPALETTE_PG_ADDRESS")
	//postgresDbHostAddress := "pg"
	if postgresDbHostAddress != "" {
		globalcv.DbType = dbutil.PostgreSQL
		setPgConfig()
	} else {
		logger.Errorf("SetDBConfig failed ")
	}
}

func setPgConfig() {
	logger.Infof("set setPgConfig begin!!!")
	globalcv.DbDriver = GetPgDbdrivername()
	globalcv.DbHost = GetPgDbHostAddress()
	globalcv.DbPort = GetPgDbPort()
	globalcv.DbName = GetPgDbName()
	globalcv.DbPassword = GetPgDbPassword()
	globalcv.DbUser = GetPgDbUserName()
	logger.Infof("set setPgConfig end!!!")
}

func setDBConfigFromJSONFile(dbConfigInfo *configure.DBConfigDto) {
	globalcv.DbType = dbConfigInfo.DBType
	globalcv.DbDriver = dbConfigInfo.DBDriver
	globalcv.DbUser = dbConfigInfo.DBUser
	globalcv.DbPassword = dbConfigInfo.DBPassword
	globalcv.DbName = dbConfigInfo.DBName
	globalcv.DbHost = dbConfigInfo.DBHost
	globalcv.DbPort = dbConfigInfo.DBPort
}

func GetPgDbdrivername() string {
	return "postgres"
}

func GetPgDbHostAddress() string {
	//return "***********"
	return os.Getenv("OPENPALETTE_PG_ADDRESS")
}

func GetPgDbPort() string {
	//return "6789"
	return os.Getenv("OPENPALETTE_PG_PORT")
}

func GetPgDbUserName() string {
	//return "postgres"
	return os.Getenv("OPENPALETTE_PG_USERNAME")

}

func GetPgDbPassword() string {
	pwdservice.StartListenPassword()
	return pwdservice.DbPassword
}

func GetPgDbName() string {
	//return "pvrm"
	return os.Getenv("OPENPALETTE_PG_DBNAME")
}
