package dbutil

import (
	"sync"
)

var clientRepo dbClientRepository = dbClientRepository{}

type dbClientRepository struct {
	repo sync.Map
}

func (d *dbClientRepository) Save(dbType string, dbName string, client *DBClient) {
	d.repo.Store(d.clientKey(dbType, dbName), client)
}

func (d *dbClientRepository) Exists(dbType string, dbName string) bool {
	_, ok := d.repo.Load(d.clientKey(dbType, dbName))
	return ok
}

func (d *dbClientRepository) Retrieve(dbType string, dbName string) *DBClient {
	client, ok := d.repo.Load(d.clientKey(dbType, dbName))
	if !ok {
		return nil
	}
	return client.(*DBClient)
}

func (d *dbClientRepository) Delete(dbType string, dbName string) {
	d.repo.Delete(d.client<PERSON>ey(dbType, dbName))
}

func (d *dbClientRepository) Clear() {
	d.repo.Range(func(key interface{}, value interface{}) bool {
		d.repo.Delete(key)
		return true
	})
}

func (d *dbClientRepository) clientKey(dbType string, dbName string) string {
	return dbType + "." + dbName
}
