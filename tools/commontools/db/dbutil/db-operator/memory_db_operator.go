package dboperator

import dbcommon "cwsm/tools/commontools/db/dbutil/db-common"

type memoryDbOperator struct {
	db        string
	destroyed bool
}

func newMemoryDbOperator(dbInfo dbcommon.DbInfo) (*memoryDbOperator, error) {
	return &memoryDbOperator{dbInfo.Db, false}, nil
}

func (m *memoryDbOperator) Create() bool {
	return true
}

func (m *memoryDbOperator) Destroy() bool {
	m.destroyed = true
	return true
}

func (m *memoryDbOperator) Exists() bool {
	if m.destroyed {
		return false
	}
	return true
}

func (m *memoryDbOperator) Close() bool {
	return true
}

func (m *memoryDbOperator) GrantAllPrivilegesTo(db string, user string) bool {
	return true
}
