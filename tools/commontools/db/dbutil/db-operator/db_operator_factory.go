package dboperator

import (
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"errors"
	"fmt"
)

func NewDbOperator(dbInfo dbcommon.DbInfo) (DBOperator, error) {
	switch dbInfo.Driver {
	case dbcore.DbDriver.POSTGRESQL:
		return newPgOperator(dbInfo)
	case dbcore.DbDriver.MEMORY:
		return newMemoryDbOperator(dbInfo)
	}
	return nil, errors.New("Unkown db driver:" + fmt.Sprint(dbInfo.Driver))
}
