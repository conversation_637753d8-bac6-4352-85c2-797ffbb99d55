package dboperator

import (
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	dbdialect "cwsm/tools/commontools/db/dbutil/db-dialect"
)

type pgOperator struct {
	*baseDbOperator
}

func newPgOperator(dbInfo dbcommon.DbInfo) (DBOperator, error) {
	operator := &pgOperator{newDbBaseOperator(dbInfo.Db, dbInfo.DriverName(), &dbdialect.PgDialect{})}
	err := operator.Connect(dbInfo.Hosts[0], dbInfo.Port, dbInfo.User, dbInfo.Password)
	if err != nil {
		return nil, err
	}
	return operator, err
}
