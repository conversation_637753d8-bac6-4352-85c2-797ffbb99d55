package dboperator

import (
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	dbdialect "cwsm/tools/commontools/db/dbutil/db-dialect"
	"cwsm/tools/commontools/logger"
	"database/sql"
)

type baseDbOperator struct {
	dbName string
	driver string
	conn   *sql.DB
	dbdialect.SQLDbDialect
}

func newDbBaseOperator(name string, driver string, dialect dbdialect.SQLDbDialect) *baseDbOperator {
	return &baseDbOperator{name, driver, nil, dialect}
}

func (b *baseDbOperator) Connect(ip string, port int, user string, pwd string) error {
	conn, err := dbcommon.ConnectDB(b.driver, b.ConnectionString(ip, port, user, pwd, ""))
	if err != nil {
		//logger.Fatal("", "Cannot connect to ", b.driver, "! Error:", err)
		logger.Errorf("", "Cannot connect to ", b.driver, "! Error:", err)
		return err
	}
	b.conn = conn
	return nil
}

func (b *baseDbOperator) Create() bool {
	sqlStr := b.SQLOfCreateDB(b.dbName)
	logger.Infof("Create database:", b.dbName, ", sqlStr:", sqlStr)
	_, err := b.conn.Exec(sqlStr)
	if err != nil {
		logger.Errorf("Create database", b.dbName, "failed!", err)
		return false
	}
	return b.Exists()
}

func (b *baseDbOperator) Exists() bool {
	sqlStr := b.SQLOfCountingDB(b.dbName)
	result, err := b.conn.Query(sqlStr)
	if err != nil {
		logger.Errorf("", "Query database failed!", err)
		return false
	}
	rows := dbcommon.ParseSQLResults(result)[0]["count"].(int64)
	return rows > 0
}

func (b *baseDbOperator) Destroy() bool {
	sqlStr := b.SQLOfDestroyDB(b.dbName)
	logger.Infof("Drop database:", b.dbName)
	_, err := b.conn.Exec(sqlStr)
	if err != nil {
		logger.Errorf("Drop database", b.dbName, " failed!", err)
		return false
	}
	err = b.conn.Close()
	if err != nil {
		logger.Errorf(err.Error())
	}
	return true
}

func (b *baseDbOperator) Close() bool {
	err := b.conn.Close()
	if err != nil {
		logger.Errorf("", "Close db client failed!", err)
		return false
	}
	return true
}

func (b *baseDbOperator) GrantAllPrivilegesTo(db string, user string) bool {
	_, err := b.conn.Exec(b.SQLOfGrantAllPrivileges(db, user))
	if err != nil {
		logger.Errorf("", "Grant all privileges to ", db, " failed:", err)
		return false
	}
	return true
}
