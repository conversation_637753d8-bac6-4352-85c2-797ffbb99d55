package dbutil

import (
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	dboperator "cwsm/tools/commontools/db/dbutil/db-operator"
	tableoperator "cwsm/tools/commontools/db/dbutil/table-operator"
	"cwsm/tools/commontools/logger"
	"errors"
	"strings"
)

type DBClient struct {
	info          dbcommon.DbInfo
	dbOperator    dboperator.DBOperator
	tableOperator tableoperator.TableOperator
}

func (d *DBClient) CreateDB() bool {
	if d.dbOperator == nil {
		return false
	}
	created := d.dbOperator.Create()
	if created {
		tableOperator, err := newTableOperator(d.info)
		if err != nil {
			return false
		}
		d.tableOperator = tableOperator
	}
	return created
}

func (d *DBClient) DestroyDB() bool {
	if d.dbOperator == nil {
		return false
	}
	if d.tableOperator != nil {
		d.tableOperator.Close()
	}
	return d.dbOperator.Destroy()
}

func (d *DBClient) GrantAllPrivilegesTo(db string, user string) bool {
	if d.dbOperator == nil {
		return false
	}
	return d.dbOperator.GrantAllPrivilegesTo(db, user)
}

func (d *DBClient) DBExists() bool {
	if d.dbOperator == nil {
		return false
	}
	return d.dbOperator.Exists()
}

func (d *DBClient) close() bool {
	dbClosed, tableClosed := true, true

	if d.dbOperator != nil {
		dbClosed = d.dbOperator.Close()
	}
	if d.tableOperator != nil {
		tableClosed = d.tableOperator.Close()
	}
	return dbClosed && tableClosed
}

func (d *DBClient) CreateTable(table string, columns ...*dbcore.Column) bool {
	if d.tableOperator == nil {
		return false
	}
	return d.tableOperator.Create(strings.ToLower(table), columns...)
}

func (d *DBClient) Insert(table string, colNames []string, rows ...*dbcore.Row) bool {
	if d.tableOperator == nil {
		return false
	}
	return d.tableOperator.Insert(strings.ToLower(table), colNames, rows...)
}

func (d *DBClient) InsertOnCol(table string, colNames [][]string, rows ...*dbcore.Row) bool {
	if d.tableOperator == nil {
		return false
	}
	return d.tableOperator.InsertOnCol(strings.ToLower(table), colNames, rows...)
}

func (d *DBClient) InsertOnDuplicate(table string, colNames []string, primarykeys []string, rows ...*dbcore.Row) bool {
	if d.tableOperator == nil {
		return false
	}
	return d.tableOperator.InsertOnDuplicate(strings.ToLower(table), colNames, primarykeys, rows...)
}

func (d *DBClient) Delete(table string, condition *dbcore.Condition) bool {
	if d.tableOperator == nil {
		return false
	}
	return d.tableOperator.Delete(strings.ToLower(table), condition)
}

func (d *DBClient) Count(table string, condition *dbcore.Condition) int {
	if d.tableOperator == nil {
		return -1
	}
	return d.tableOperator.Count(strings.ToLower(table), condition)
}

func (d *DBClient) ContentCount(table string, condition *dbcore.Condition) int {
	if d.tableOperator == nil {
		return -1
	}
	return d.tableOperator.ContentCount(strings.ToLower(table), condition)
}

func (d *DBClient) AddCols(table string, cols ...*dbcore.Column) bool {
	if d.tableOperator == nil {
		return false
	}
	return d.tableOperator.AddCols(strings.ToLower(table), cols...)
}

func (d *DBClient) ColNames(table string) []string {
	if d.tableOperator == nil {
		return []string{}
	}
	return d.tableOperator.ColNames(strings.ToLower(table))
}

func (d *DBClient) Query(table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
	if d.tableOperator == nil {
		return []map[string]interface{}{}, errors.New("[Query] The table operator in the db client is empty")
	}
	return d.tableOperator.Query(strings.ToLower(table), condition)
}

func (d *DBClient) InnerJoinQuery(table1, table2 string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
	if d.tableOperator == nil {
		return []map[string]interface{}{}, errors.New("[InnerJoinQuery] The table operator in the db client is empty")
	}
	return d.tableOperator.InnerJoinQuery(table1, table2, condition)
}

func (d *DBClient) InnerJoinCount(table1, table2 string, condition *dbcore.Condition) int {
	if d.tableOperator == nil {
		return -1
	}
	return d.tableOperator.InnerJoinCount(table1, table2, condition)
}

func (d *DBClient) QueryDistinct(table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
	if d.tableOperator == nil {
		return []map[string]interface{}{}, errors.New("[QueryDistinct] The table operator in the db client is empty")
	}
	return d.tableOperator.QueryDistinct(strings.ToLower(table), condition)
}

func (d *DBClient) QueryCertainCols(table string, cols []string, mode int, cond *dbcore.Condition) ([]map[string]interface{}, error) {
	if d.tableOperator == nil {
		return []map[string]interface{}{}, errors.New("[QueryCertainCols] The table operator in the db client is empty")
	}
	return d.tableOperator.QueryCols(strings.ToLower(table), cols, cond, mode)
}

func (d *DBClient) QueryCertainContentInCols(table string, cols []string, mode int, cond *dbcore.Condition) []map[string]interface{} {
	if d.tableOperator == nil {
		return []map[string]interface{}{}
	}
	return d.tableOperator.QueryContentInCols(strings.ToLower(table), cols, cond, mode)
}

func (d *DBClient) Update(table string, cols map[string]interface{}, condition *dbcore.Condition) bool {
	if d.tableOperator == nil {
		return false
	}
	return d.tableOperator.Update(strings.ToLower(table), cols, condition)
}

func (d *DBClient) DestroyTable(table string) bool {
	if d.tableOperator == nil {
		return false
	}
	return d.tableOperator.Destroy(strings.ToLower(table))
}

func (d *DBClient) TableExists(table string) bool {
	if d.tableOperator == nil {
		return false
	}
	return d.tableOperator.Exists(strings.ToLower(table))
}

func (d *DBClient) Exec(sql string, values ...interface{}) bool {
	if d.tableOperator == nil {
		logger.Errorf("[Exec] The table operator in the db client is empty")
		return false
	}
	return d.tableOperator.Exec(sql, values...)
}

func (d *DBClient) ExecQuery(sql string, values ...interface{}) ([]map[string]interface{}, error) {
	if d.tableOperator == nil {
		return nil, errors.New("[ExecQuery] The table operator in the db client is empty")
	}
	return d.tableOperator.ExecQuery(sql, values...)
}

func (d *DBClient) ExecCount(sql string, values ...interface{}) int {
	return d.tableOperator.ExecCount(sql, values...)
}

// 组装查询sql语句，用于调用 dbClient.ExecQuery 查询数据库时使用
// 入参：q -- 带占位符的sql语句 values -- 查询所带参数
// 返回值：根据底层数据库类型得到的sql语句
func (d *DBClient) FormatSQL(sql string, values ...interface{}) (string, error) {
	if d.tableOperator == nil {
		return sql, errors.New("[FormatSQL] The table operator in the db client is empty")
	}
	return d.tableOperator.FormatSQL(sql, values...), nil
}
