package tableoperator

import (
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"errors"
	"fmt"
)

func NewTableOperator(dbInfo dbcommon.DbInfo) (TableOperator, error) {
	switch dbInfo.Driver {
	case dbcore.DbDriver.POSTGRESQL:
		return newPgTableOperator(dbInfo)
	case dbcore.DbDriver.MEMORY:
		return newMemoryTableOperator(dbInfo)
	}
	return nil, errors.New("Unkown db driver:" + fmt.Sprint(dbInfo.Driver))
}
