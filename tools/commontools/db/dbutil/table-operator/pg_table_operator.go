package tableoperator

import (
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	dialect "cwsm/tools/commontools/db/dbutil/db-dialect"
)

type pgTableOperator struct {
	*baseTableOperator
}

func newPgTableOperator(dbInfo dbcommon.DbInfo) (TableOperator, error) {
	operator := &pgTableOperator{newBaseTableClient(dbInfo.Db, dbInfo.DriverName(), &dialect.PgDialect{})}
	err := operator.Connect(dbInfo.Hosts[0], dbInfo.Port, dbInfo.User, dbInfo.Password, dbInfo.Db)
	if err != nil {
		return nil, err
	}
	return operator, err
}
