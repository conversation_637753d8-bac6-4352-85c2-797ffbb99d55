package tableoperator

import dbcore "cwsm/tools/commontools/db/dbutil/db-core"

type TableOperator interface {
	Create(string, ...*dbcore.Column) bool
	Insert(string, []string, ...*dbcore.Row) bool
	InsertOnDuplicate(string, []string, []string, ...*dbcore.Row) bool
	InsertOnCol(string, [][]string, ...*dbcore.Row) bool
	Query(string, *dbcore.Condition) ([]map[string]interface{}, error)
	QueryDistinct(string, *dbcore.Condition) ([]map[string]interface{}, error)
	QueryCols(string, []string, *dbcore.Condition, int) ([]map[string]interface{}, error)
	QueryContentInCols(string, []string, *dbcore.Condition, int) []map[string]interface{}
	InnerJoinQuery(string, string, *dbcore.Condition) ([]map[string]interface{}, error)
	InnerJoinCount(string, string, *dbcore.Condition) int
	Update(string, map[string]interface{}, *dbcore.Condition) bool
	Delete(string, *dbcore.Condition) bool
	Count(string, *dbcore.Condition) int
	ContentCount(string, *dbcore.Condition) int
	AddCols(string, ...*dbcore.Column) bool
	ColNames(string) []string
	Destroy(string) bool
	Exists(string) bool
	Close() bool
	Exec(string, ...interface{}) bool
	ExecQuery(string, ...interface{}) ([]map[string]interface{}, error)
	ExecCount(string, ...interface{}) int
	FormatSQL(string, ...interface{}) string
}
