package tableoperator

import (
	"bytes"
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/infa/util"
	"encoding/json"
	"errors"
	"math"
	"reflect"
	"strings"
	"sync"
)

func newMemoryTableOperator(dbInfo dbcommon.DbInfo) (*memoryTableOperator, error) {
	return &memoryTableOperator{dbInfo.Db, sync.Map{}}, nil
}

type memoryRow struct {
	idx   int
	dbRow *dbcore.Row
}

type memoryTable struct {
	nextRowIdx int
	cols       []*dbcore.Column
	rows       []*memoryRow
}

func (m *memoryTable) insert(colNames []string, rows []*dbcore.Row) {
	newRows := make([]*memoryRow, len(rows))
	indexMap := make(map[int]int)
	for i, col := range m.cols {
		for j, name := range colNames {
			if strings.ToLower(col.Name) == strings.ToLower(name) {
				indexMap[i] = j
			}
		}
	}

	for idx, row := range rows {
		newRows[idx] = &memoryRow{m.nextRowIdx, &dbcore.Row{Values: make([]interface{}, len(m.cols))}}
		for i := range m.cols {
			j, ok := indexMap[i]
			if ok && j < len(m.cols) {
				newRows[idx].dbRow.Values[i] = row.Values[j]
			} else {
				newRows[idx].dbRow.Values[i] = ""
			}

		}
		m.nextRowIdx++
	}
	m.rows = append(m.rows, newRows...)
}

func (m *memoryTable) clear() {
	m.nextRowIdx = 0
	m.rows = []*memoryRow{}
}

func (m *memoryTable) update(rows []*memoryRow, newValues map[int]interface{}) {
	for _, row := range rows {
		for colIdx, value := range newValues {
			row.dbRow.Values[colIdx] = value
		}
	}
}

func (m *memoryTable) delete(rows []*memoryRow) {
	deleteFlag := map[int]bool{}
	for _, row := range rows {
		idx := m.find(row)
		if idx >= 0 {
			deleteFlag[idx] = true
		}
	}
	leftRows := []*memoryRow{}
	for _, row := range m.rows {
		_, found := deleteFlag[row.idx]
		if !found {
			leftRows = append(leftRows, row)
		}
	}
	m.rows = leftRows
}

func (m *memoryTable) find(target *memoryRow) int {
	for _, row := range m.rows {
		if row.idx == target.idx {
			return row.idx
		}
	}
	return -1
}

type memoryTableOperator struct {
	db     string
	tables sync.Map
}

func (m *memoryTableOperator) memTable(table string) *memoryTable {
	if !m.Exists(table) {
		return nil
	}
	value, _ := m.tables.Load(table)
	return value.(*memoryTable)
}

func (m *memoryTableOperator) Create(table string, columns ...*dbcore.Column) bool {
	m.tables.Store(table, &memoryTable{0, columns, []*memoryRow{}})
	return m.Exists(table)
}

func (m *memoryTableOperator) Insert(table string, colNames []string, rows ...*dbcore.Row) bool {
	value, _ := m.tables.Load(table)
	memTable := value.(*memoryTable)
	memTable.insert(colNames, rows)
	return true
}

// TODO weili 03/25/2020 not implemented yet
func (m *memoryTableOperator) InsertOnCol(table string, colNames [][]string, rows ...*dbcore.Row) bool {
	return true
}

// TODO weili 03/25/2020 not implemented yet
func (m *memoryTableOperator) InsertOnDuplicate(table string, colNames []string, primarykeys []string, rows ...*dbcore.Row) bool {
	return true
}

func (m *memoryTableOperator) Query(table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
	memTable := m.memTable(table)
	if memTable == nil {
		return []map[string]interface{}{}, errors.New("fetch memTable failed")
	}

	return m.convertRows(memTable, m.query(memTable, condition)), nil
}

// TODO weili 03/25/2020 not implemented yet
func (m *memoryTableOperator) QueryDistinct(table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
	return []map[string]interface{}{}, nil
}

func (m *memoryTableOperator) QueryCols(table string, cols []string, condition *dbcore.Condition, mode int) ([]map[string]interface{}, error) {
	return queryCols(m, table, condition, mode, cols)
}

func (m *memoryTableOperator) QueryContentInCols(table string, cols []string, condition *dbcore.Condition, mode int) []map[string]interface{} {
	return queryContentInCols(m, table, condition, mode, cols)
}

func (m *memoryTableOperator) InnerJoinQuery(table1, table2 string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
	memTable1, memTable2 := m.memTable(table1), m.memTable(table2)
	if memTable1 == nil || memTable2 == nil {
		return []map[string]interface{}{}, errors.New("fetch memTable failed")
	}

	var con1, con2 *dbcore.Condition
	if strings.Contains(condition.Column, table1) {
		condition.Column = strings.ReplaceAll(condition.Column, table1+".", "")
		con1 = &dbcore.Condition{Column: condition.Column, Operator: condition.Operator, Values: condition.Values}
	}
	if strings.Contains(condition.Column, table2) {
		condition.Column = strings.ReplaceAll(condition.Column, table2+".", "")
		con2 = &dbcore.Condition{Column: condition.Column, Operator: condition.Operator, Values: condition.Values}
	}

	for _, con := range condition.Next {
		if strings.Contains(con.Column, table1) {
			con.Column = strings.ReplaceAll(con.Column, table1+".", "")
			if con1 != nil {
				con1.And(con)
			} else {
				con1 = con
			}
		}
		if strings.Contains(con.Column, table2) {
			con.Column = strings.ReplaceAll(con.Column, table2+".", "")
			if con2 != nil {
				con2.And(con)
			} else {
				con2 = con
			}
		}
	}

	record1 := m.convertRows(memTable1, m.query(memTable1, con1))
	record2 := m.convertRows(memTable2, m.query(memTable2, con2))

	var results []map[string]interface{}
	for _, r1 := range record1 {
		for _, r2 := range record2 {
			if r1["id"] == r2["id"] {
				for k, v := range r2 {
					r1[k] = v
				}
				results = append(results, r1)
				continue
			}
		}
	}

	return results, nil
}

func (m *memoryTableOperator) InnerJoinCount(table1, table2 string, condition *dbcore.Condition) int {
	results, _ := m.InnerJoinQuery(table1, table2, condition)
	return len(results)
}

func (m *memoryTableOperator) query(memTable *memoryTable, condition *dbcore.Condition) []*memoryRow {
	if condition == nil {
		return memTable.rows
	}
	if strings.Contains(condition.Column, "content") {
		return m.queryContent(memTable, condition)
	}
	for _, condition := range condition.Next {
		if strings.Contains(condition.Column, "content") {
			return m.queryContent(memTable, condition)
		}
	}
	return m.scanRows(memTable.rows, memTable.cols, condition)
}

func (m *memoryTableOperator) queryContent(memTable *memoryTable, condition *dbcore.Condition) []*memoryRow {
	var rows []*memoryRow
	for _, row := range memTable.rows {
		if strings.Contains(string(row.dbRow.Values[1].([]uint8)), condition.Values[0].(string)) {
			rows = append(rows, row)
		}
	}
	return rows
}

func (m *memoryTableOperator) queryContentInCol(memTable *memoryTable, condition *dbcore.Condition) []*memoryRow {
	if condition == nil {
		return memTable.rows
	}
	return m.scanRowsForContentInCol(memTable.rows, memTable.cols, condition)
}

func queryCols(m *memoryTableOperator, table string, condition *dbcore.Condition, mode int, cols []string) ([]map[string]interface{}, error) {
	memTable := m.memTable(table)
	if memTable == nil {
		return []map[string]interface{}{}, errors.New("fetch memTable failed")
	}

	result := []map[string]interface{}{}
	rows := m.query(memTable, condition)
	if mode == dbcore.QueryMode.DISTINCT {
		result = m.distinct(memTable, rows, m.colIndices(memTable, cols))
	} else {
		result = m.convertRows(memTable, rows)
	}
	for i := range result {
		for key := range result[i] {
			if !util.Contains(key, cols) {
				delete(result[i], key)
			}
		}
	}
	return result, nil
}

func queryContentInCols(m *memoryTableOperator, table string, condition *dbcore.Condition, mode int, cols []string) []map[string]interface{} {
	memTable := m.memTable(table)
	if memTable == nil {
		return []map[string]interface{}{}
	}

	result := []map[string]interface{}{}
	rows := m.queryContentInCol(memTable, condition)
	if mode == dbcore.QueryMode.DISTINCT {
		result = m.distinct(memTable, rows, m.colIndices(memTable, cols))
	} else {
		result = m.convertRows(memTable, rows)
	}

	for i := range result {
		for key := range result[i] {
			if !util.Contains(key, cols) {
				delete(result[i], key)
			}
		}
	}
	return result
}

func (m *memoryTableOperator) colIndices(memTable *memoryTable, cols []string) map[string]int {
	colIndices := map[string]int{}
	for idx, col := range memTable.cols {
		for _, c := range cols {
			if strings.ToLower(col.Name) == strings.ToLower(c) {
				colIndices[col.Name] = idx
				break
			}
		}
	}
	return colIndices
}

func (m *memoryTableOperator) convertRows(memTable *memoryTable, rows []*memoryRow) []map[string]interface{} {
	results := []map[string]interface{}{}
	for _, row := range rows {
		result := map[string]interface{}{}
		for idx, col := range memTable.cols {
			result[col.Name] = row.dbRow.Values[idx]
		}
		results = append(results, result)
	}
	return results
}

func (m *memoryTableOperator) Update(table string, cols map[string]interface{}, condition *dbcore.Condition) bool {
	memTable := m.memTable(table)
	if memTable == nil {
		return false
	}

	colValues := map[int]interface{}{}
	for idx, col := range memTable.cols {
		value, found := cols[col.Name]
		if found {
			colValues[idx] = value
		}
	}

	if condition == nil {
		memTable.update(memTable.rows, colValues)
	} else {
		rows := m.scanRows(memTable.rows, memTable.cols, condition)
		memTable.update(rows, colValues)
	}

	return true
}

func (m *memoryTableOperator) Delete(table string, condition *dbcore.Condition) bool {
	memTable := m.memTable(table)
	if memTable == nil {
		return false
	}

	if condition == nil {
		memTable.clear()
	} else {
		rows := m.scanRows(memTable.rows, memTable.cols, condition)
		memTable.delete(rows)
	}

	return true
}

func (m *memoryTableOperator) Count(table string, condition *dbcore.Condition) int {
	memTable := m.memTable(table)
	if memTable == nil {
		return 0
	}

	rows := []*memoryRow{}
	if condition == nil {
		rows = memTable.rows
	} else {
		rows = m.scanRows(memTable.rows, memTable.cols, condition)
	}

	return len(rows)
}

func (m *memoryTableOperator) ContentCount(table string, condition *dbcore.Condition) int {
	memTable := m.memTable(table)
	if memTable == nil {
		return 0
	}

	rows := []*memoryRow{}
	if condition == nil {
		rows = memTable.rows
	} else {
		rows = m.scanRowsForContentInCol(memTable.rows, memTable.cols, condition)
	}

	return len(rows)
}

// TODO weili 03/26/2020 not implemented yet
func (m *memoryTableOperator) AddCols(table string, cols ...*dbcore.Column) bool {
	memTable := m.memTable(table)
	if memTable == nil {
		return false
	}
	for i, mc := range memTable.cols {
		for _, c := range cols {
			if mc.Name == c.Name {
				memTable.cols[i] = c
			} else {
				memTable.cols = append(memTable.cols, c)
			}
		}
	}
	return true
}

func (m *memoryTableOperator) ColNames(table string) []string {
	memTable := m.memTable(table)
	if memTable == nil {
		return []string{}
	}
	var names = make([]string, len(memTable.cols))
	for i, col := range memTable.cols {
		names[i] = col.Name
	}
	return names
}

func (m *memoryTableOperator) Exec(sql string, values ...interface{}) bool {
	return true
}
func (m *memoryTableOperator) ExecQuery(sql string, values ...interface{}) ([]map[string]interface{}, error) {
	return []map[string]interface{}{}, nil
}

func (m *memoryTableOperator) ExecCount(sql string, values ...interface{}) int {
	return 0
}
func (m *memoryTableOperator) FormatSQL(sql string, values ...interface{}) string {
	return ""
}

/* Started by AICoder, pid:2ddfd73163a7406d825e0614ff31a07f */
func (m *memoryTableOperator) scanRowsForContentInCol(candidates []*memoryRow, cols []*dbcore.Column, condition *dbcore.Condition) []*memoryRow {
	cs := make([]string, len(cols))
	for i, c := range cols {
		cs[i] = c.Name
	}
	rows := []*memoryRow{}
	for idx, col := range cols {
		if strings.ToLower(col.Name) == strings.ToLower("content") {
			rows = m.processContent(candidates, rows, idx, col, condition)
		}
	}
	for idx, c := range condition.Next {
		switch condition.Relations[idx] {
		case dbcore.Relation.AND:
			rows = m.scanRowsForContentInCol(rows, cols, c)
		case dbcore.Relation.OR:
			rows = m.combine(rows, m.scanRowsForContentInCol(candidates, cols, c))
		}
	}
	return rows
}

func (m *memoryTableOperator) processContent(candidates []*memoryRow, rows []*memoryRow, idx int, col *dbcore.Column, condition *dbcore.Condition) []*memoryRow {
	for _, row := range candidates {
		rows = m.processRow(row, rows, idx, col, condition)
	}
	return rows
}

func (m *memoryTableOperator) processRow(row *memoryRow, rows []*memoryRow, idx int, col *dbcore.Column, condition *dbcore.Condition) []*memoryRow {
	for _, v := range condition.Values {
		var imageContent map[string]interface{}
		if err := json.Unmarshal(row.dbRow.Values[idx].([]byte), &imageContent); err != nil {
			return rows
		}
		var buffer bytes.Buffer
		str := strings.Split(condition.Column, "'")[1]
		if strings.Contains(str, "{") && strings.Contains(str, "}") {
			str = strings.ReplaceAll(strings.ReplaceAll(str, "{", ""), "}", "")
			buffer.WriteString(str)
		} else {
			buffer.WriteString(str)
		}
		rows = m.matchContent(row, rows, col, condition, imageContent, &buffer, v)
	}
	return rows
}

func (m *memoryTableOperator) matchContent(row *memoryRow, rows []*memoryRow, col *dbcore.Column, condition *dbcore.Condition, imageContent map[string]interface{}, buffer *bytes.Buffer, value interface{}) []*memoryRow {
	for key, val := range imageContent {
		if val != nil && reflect.TypeOf(val).Kind() == reflect.String && buffer.String() == key {
			if condition.Operator == dbcore.Operator.ILIKE {
				if buffer.String() == "name" || buffer.String() == "disk_format" || buffer.String() == "created_at" {
					if compareValues(condition.Operator, col.ColumnType, val, value) {
						return append(rows, row)
					}
				}
			} else if compareValues(condition.Operator, col.ColumnType, val, value) {
				return append(rows, row)
			}
		}
	}
	return rows
}

/* Ended by AICoder, pid:2ddfd73163a7406d825e0614ff31a07f */

func (m *memoryTableOperator) scanRows(candidates []*memoryRow, cols []*dbcore.Column, condition *dbcore.Condition) []*memoryRow {
	cs := make([]string, len(cols))
	for i, c := range cols {
		cs[i] = c.Name
	}
	rows := []*memoryRow{}
	for idx, col := range cols {
		if strings.ToLower(col.Name) == strings.ToLower(condition.Column) {
			for _, row := range candidates {
				for _, v := range condition.Values {
					if compareValues(condition.Operator, col.ColumnType, row.dbRow.Values[idx], v) {
						rows = append(rows, row)
						break
					}
				}
			}
		}
	}

	for idx, c := range condition.Next {
		switch condition.Relations[idx] {
		case dbcore.Relation.AND:
			rows = m.scanRows(rows, cols, c)
		case dbcore.Relation.OR:
			rows = m.combine(rows, m.scanRows(candidates, cols, c))
		}
	}

	return rows
}

func (m *memoryTableOperator) combine(these []*memoryRow, those []*memoryRow) []*memoryRow {
	rowMappings := map[int]*memoryRow{}
	for _, row := range these {
		_, found := rowMappings[row.idx]
		if !found {
			rowMappings[row.idx] = row
		}
	}
	for _, row := range those {
		_, found := rowMappings[row.idx]
		if !found {
			rowMappings[row.idx] = row
		}
	}
	combinedRows := []*memoryRow{}
	for _, row := range rowMappings {
		combinedRows = append(combinedRows, row)
	}
	return combinedRows
}

func (m *memoryTableOperator) distinct(memTable *memoryTable, rows []*memoryRow, colIndices map[string]int) []map[string]interface{} {
	results := []map[string]interface{}{}
	for _, row := range rows {
		result := map[string]interface{}{}
		for k, v := range colIndices {
			result[k] = row.dbRow.Values[v]
		}
		if len(results) == 0 {
			results = append(results, result)
		} else {
		COMPARE:
			for _, res := range results {
				for k, v := range res {
					if compareValues(dbcore.Operator.EQ, memTable.cols[colIndices[k]].ColumnType, v, result[k]) {
						break COMPARE
					}
				}
				results = append(results, result)
			}
		}
	}
	return results
}

func (m *memoryTableOperator) Destroy(table string) bool {
	m.tables.Delete(table)
	return !m.Exists(table)
}

func (m *memoryTableOperator) Exists(table string) bool {
	_, existed := m.tables.Load(table)
	return existed
}

func (m *memoryTableOperator) Close() bool {
	return true
}

type compare interface {
	eq(v1 interface{}, v2 interface{}) bool
	gt(v1 interface{}, v2 interface{}) bool
	gte(v1 interface{}, v2 interface{}) bool
	lt(v1 interface{}, v2 interface{}) bool
	lte(v1 interface{}, v2 interface{}) bool
	ne(v1 interface{}, v2 interface{}) bool
	ilike(v1 interface{}, v2 interface{}) bool
	include(v1 interface{}, v2 interface{}) bool
}

var comparisons = map[int]compare{
	dbcore.ColType.BOOL:   &boolComparison{},
	dbcore.ColType.FLOAT:  &floatComparison{},
	dbcore.ColType.INT:    &intComparison{},
	dbcore.ColType.STRING: &stringComparison{},
	dbcore.ColType.TEXT:   &stringComparison{},
}

func compareValues(operator int, colType int, v1 interface{}, v2 interface{}) bool {
	cp := comparisons[colType]
	switch operator {
	case dbcore.Operator.EQ:
		return cp.eq(v1, v2)
	case dbcore.Operator.IN:
		return cp.eq(v1, v2)
	case dbcore.Operator.GT:
		return cp.gt(v1, v2)
	case dbcore.Operator.GTE:
		return cp.gte(v1, v2)
	case dbcore.Operator.LT:
		return cp.lt(v1, v2)
	case dbcore.Operator.LTE:
		return cp.lte(v1, v2)
	case dbcore.Operator.ILIKE:
		return cp.ilike(v1, v2)
	case dbcore.Operator.NE:
		return cp.ne(v1, v2)
	case dbcore.Operator.INCLUDE:
		return cp.include(v1, v2)
	default:
		return cp.ne(v1, v2)
	}
}

type boolComparison struct {
	compare
}

func (b *boolComparison) eq(v1 interface{}, v2 interface{}) bool {
	return v1.(bool) == v2.(bool)
}

func (b *boolComparison) gt(v1 interface{}, v2 interface{}) bool {
	return !b.eq(v1, v2) && v1.(bool)
}

func (b *boolComparison) gte(v1 interface{}, v2 interface{}) bool {
	return b.eq(v1, v2) || b.gt(v1, v2)
}

func (b *boolComparison) lt(v1 interface{}, v2 interface{}) bool {
	return !b.eq(v1, v2) && !v1.(bool)
}

func (b *boolComparison) lte(v1 interface{}, v2 interface{}) bool {
	return b.eq(v1, v2) || b.lt(v1, v2)
}

func (b *boolComparison) ne(v1 interface{}, v2 interface{}) bool {
	return v1.(bool) != v2.(bool)
}

type floatComparison struct {
	compare
}

func (f *floatComparison) eq(v1 interface{}, v2 interface{}) bool {
	precision := 0.0000001
	return math.Abs(v1.(float64)-v2.(float64)) < precision
}

func (f *floatComparison) gt(v1 interface{}, v2 interface{}) bool {
	return v1.(float64) > v2.(float64)
}

func (f *floatComparison) gte(v1 interface{}, v2 interface{}) bool {
	return v1.(float64) >= v2.(float64)
}

func (f *floatComparison) lt(v1 interface{}, v2 interface{}) bool {
	return v1.(float64) < v2.(float64)
}

func (f *floatComparison) lte(v1 interface{}, v2 interface{}) bool {
	return v1.(float64) <= v2.(float64)
}

func (f *floatComparison) ne(v1 interface{}, v2 interface{}) bool {
	return !f.gt(v1, v2)
}

type intComparison struct {
	compare
}

func (i *intComparison) eq(v1 interface{}, v2 interface{}) bool {
	return v1.(int) == v2.(int)
}

func (i *intComparison) gt(v1 interface{}, v2 interface{}) bool {
	return v1.(int) > v2.(int)
}

func (i *intComparison) gte(v1 interface{}, v2 interface{}) bool {
	return v1.(int) >= v2.(int)
}

func (i *intComparison) lt(v1 interface{}, v2 interface{}) bool {
	return v1.(int) < v2.(int)
}

func (i *intComparison) lte(v1 interface{}, v2 interface{}) bool {
	return v1.(int) <= v2.(int)
}

func (i *intComparison) ne(v1 interface{}, v2 interface{}) bool {
	return v1.(int) != v2.(int)
}

type stringComparison struct {
	compare
}

func (s *stringComparison) eq(v1 interface{}, v2 interface{}) bool {
	return v1.(string) == v2.(string)
}

func (s *stringComparison) gt(v1 interface{}, v2 interface{}) bool {
	return v1.(string) > v2.(string)
}

func (s *stringComparison) gte(v1 interface{}, v2 interface{}) bool {
	return v1.(string) >= v2.(string)
}

func (s *stringComparison) lt(v1 interface{}, v2 interface{}) bool {
	return v1.(string) < v2.(string)
}

func (s *stringComparison) lte(v1 interface{}, v2 interface{}) bool {
	return v1.(string) <= v2.(string)
}

func (s *stringComparison) ne(v1 interface{}, v2 interface{}) bool {
	return v1.(string) != v2.(string)
}

func (s *stringComparison) ilike(v1 interface{}, v2 interface{}) bool {
	return strings.Contains(v1.(string), v2.(string))
}

func (s *stringComparison) include(v1 interface{}, v2 interface{}) bool {
	v := v1.([]byte)
	return strings.Contains(string(v), v2.(string))
}
