package tableoperator

import (
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	db "cwsm/tools/commontools/db/dbutil/db-core"
	dbdialect "cwsm/tools/commontools/db/dbutil/db-dialect"
	"cwsm/tools/commontools/logger"
	"database/sql"
	"fmt"
	"strings"
	"sync"
)

type baseTableOperator struct {
	db     string
	driver string
	conn   *sql.DB
	cols   map[string][]*db.Column
	dbdialect.SQLDbDialect
	lock sync.RWMutex
}

func newBaseTableClient(dbName string, driver string, dialect dbdialect.SQLDbDialect) *baseTableOperator {
	return &baseTableOperator{
		db:           dbName,
		driver:       driver,
		cols:         map[string][]*db.Column{},
		SQLDbDialect: dialect,
	}
}

func (b *baseTableOperator) Connect(ip string, port int, user string, pwd string, db string) error {
	conn, err := dbcommon.ConnectDB(b.driver, b.ConnectionString(ip, port, user, pwd, db))
	if err == nil {
		b.conn = conn
	}
	return err
}

func (b *baseTableOperator) Create(table string, columns ...*db.Column) bool {
	q := b.SQLOfCreateTable(table, columns...)
	logger.Infof("Create table in db:%s, sql:%s", b.db, q)
	_, err := b.exec(q)
	if err != nil {
		logger.Errorf("Create table failed! error: %v", err)
		return false
	}
	return true
}

func (b *baseTableOperator) Destroy(table string) bool {
	q := b.SQLOfDestroyTable(table)
	logger.Infof("Drop table in db:%s, sql:%s", b.db, q)
	_, err := b.exec(q)
	if err != nil {
		logger.Errorf("Drop table failed!", err)
		return false
	}
	err = b.conn.Close()
	return true
}

func (b *baseTableOperator) Insert(table string, colNames []string, rows ...*db.Row) bool {
	if len(rows) == 0 {
		return true
	}
	rowNum, colNum := len(rows), len(colNames)
	q := fmt.Sprintf("insert into %s(%s) values%s", table, strings.Join(colNames, ","),
		b.buildRowMarks(colNum, rowNum))
	cells := make([]interface{}, rowNum*colNum)
	for rowIdx, row := range rows {
		for colIdx, v := range row.Values {
			cells[b.cellIdx(rowIdx, colNum, colIdx)] = v
		}
	}
	logger.Debug("Insert into db %s, sql: %s", b.db, q)
	result, err := b.exec(q, cells...)
	if err != nil {
		logger.Errorf("Insert into table %s failed: %v", table, err)
		return false
	}

	rowsInserted, err := result.RowsAffected()
	if rowsInserted != int64(rowNum) {
		/*logger.Warn("Some records insert into ", table, " failed!" +
		  " Expected:", rowNum, ",actual:", rowsInserted)*/
		logger.Errorf("", "Some records insert into ", table, " failed!"+
			" Expected:", rowNum, ",actual:", rowsInserted, ", sql:", q, ",cells:", cells)
	}
	return true
}

const (
	moIDField    = "resourceId"
	minutesField = "minutes"
)

func (b *baseTableOperator) InsertOnDuplicate(table string, colNames []string, primarykeys []string, rows ...*db.Row) bool {
	if len(rows) == 0 {
		return true
	}
	rowNum, colNum := len(rows), len(colNames)

	onCols := []string{}
	for _, col := range colNames {
		if col == moIDField || col == minutesField {
			continue
		}
		if b.driver == "postgres" {
			onCols = append(onCols, col+"=EXCLUDED."+col)
		} else {
			onCols = append(onCols, col+"=values("+col+")")
		}
	}

	q := fmt.Sprintf("insert into %s(%s) values%s on duplicate key update %s", table, strings.Join(colNames, ","),
		b.buildRowMarks(colNum, rowNum), strings.Join(onCols, ","))

	if b.driver == "postgres" { //pg数据库语法不同
		q = fmt.Sprintf("insert into %s(%s) values%s on CONFLICT (%s) do update set %s", table, strings.Join(colNames, ","),
			b.buildRowMarks(colNum, rowNum), strings.Join(primarykeys, ","), strings.Join(onCols, ","))
	}

	cells := make([]interface{}, rowNum*colNum)
	for rowIdx, row := range rows {
		for colIdx, v := range row.Values {
			cells[b.cellIdx(rowIdx, colNum, colIdx)] = v
		}
	}
	logger.Debug("Insert into db %s, sql: %s", b.db, q)
	result, err := b.exec(q, cells...)
	if err != nil {
		logger.Errorf("Insert into table %s failed! SQL: %s, error: %v", table, q, err)
		return false
	}

	rowsInserted, err := result.RowsAffected()
	if rowsInserted > 2*int64(rowNum) {
		logger.Debug("records replace: %d, insert: %d", rowsInserted-int64(rowNum), 2*int64(rowNum)-rowsInserted)
	}
	return true
}

func (b *baseTableOperator) InsertOnCol(table string, colNames [][]string, rows ...*db.Row) bool {
	return true
}

func (b *baseTableOperator) buildRowMarks(colNum int, rowNum int) string {
	cellMarks := make([]string, colNum*rowNum)
	for rowIdx := 0; rowIdx < rowNum; rowIdx++ {
		for colIdx := 0; colIdx < colNum; colIdx++ {
			celIdx := b.cellIdx(rowIdx, colNum, colIdx)
			cellMarks[celIdx] = b.CellMark(celIdx)
		}
	}
	rowMarks := make([]string, rowNum)
	for rowIdx := 0; rowIdx < rowNum; rowIdx++ {
		rowMarks[rowIdx] = "(" + strings.Join(cellMarks[(rowIdx*colNum):((rowIdx+1)*colNum)], ",") + ")"
	}
	//pg:($1,$2,$3),($4,$5,$6)
	//mysql:(?,?,?),(?,?,?)...
	return strings.Join(rowMarks, ",")
}

func (b *baseTableOperator) cellIdx(rowIdx int, colNum int, colIdx int) int {
	return rowIdx*colNum + colIdx
}

func (b *baseTableOperator) ColNames(table string) []string {
	names := []string{}
	for _, col := range b.getColumns(table) {
		names = append(names, col.Name)
	}
	return names
}

func (b *baseTableOperator) getColumns(table string) []*db.Column {
	b.lock.Lock()
	defer b.lock.Unlock()

	if len(b.cols[table]) == 0 {
		results, err := b.conn.Query(b.SQLOfColumnsQuery(table))
		if err != nil {
			logger.Errorf("Query columns from failed! sql: %s, error: %v", b.SQLOfColumnsQuery(table), err)
			return []*db.Column{}
		}
		defer results.Close()

		cols := []*db.Column{}
		for results.Next() {
			var name, typeName, nullable string
			err := results.Scan(&name, &typeName, &nullable)
			if err != nil {
				logger.Errorf("Query columns from failed! sql: %s, error: %v", b.SQLOfColumnsQuery(table), err)
				return []*db.Column{}
			}
			cols = append(cols, db.NewColumn(name, b.getColumnType(typeName, b.DataTypes()))) //TODO: need to find out primary key?
		}
		b.cols[table] = cols
	}
	return b.cols[table]
}

func (b *baseTableOperator) getColumnType(typeName string, types map[int]string) int {
	for k, v := range types {
		if strings.HasPrefix(strings.ToLower(typeName), strings.ToLower(v)) {
			return k
		}
	}
	return -1
}

func (b *baseTableOperator) Delete(table string, condition *db.Condition) bool {
	conditions, values := b.sqlOfConditions(0, condition)
	q := fmt.Sprintf("delete from %s %s", table, conditions)
	logger.Debug("Delete from db:%s, sql:%s", b.db, q)
	_, err := b.exec(q, values...)
	if err != nil {
		logger.Errorf("Delete from DB failed! sql: %s, error: %v", q, err)
		return false
	}
	return true
}

func (b *baseTableOperator) Exists(table string) bool {
	q := b.SQLOfCountingTable(b.db, table)
	result, err := b.query(q)
	if err != nil {
		logger.Errorf("Query table failed! error: %s", err)
		return false
	}
	rows, _ := result[0]["count"].(int64)
	return rows > 0
}

func (b *baseTableOperator) Count(table string, condition *db.Condition) int {
	conditions, values := b.sqlOfConditions(0, condition)
	q := fmt.Sprintf("select count(1) as count from %s %s", table, conditions)
	logger.Debug("Count in db: %v, sql: %s", b.db, q)
	result, err := b.query(q, values...)
	if err != nil {
		logger.Errorf("Count from DB failed! sql: %s, error: %v", q, err)
		return -1
	}
	count, _ := result[0]["count"].(int64)
	return int(count)
}

func (b *baseTableOperator) ContentCount(table string, condition *db.Condition) int {
	conditions, values := b.sqlOfConditions(0, condition)
	q := fmt.Sprintf("select count(1) as count from %s %s", table, conditions)
	logger.Debug("ContentCount in db: %v, sql: %s", b.db, q)
	result, err := b.query(q, values...)
	if err != nil {
		logger.Errorf("ContentCount from DB failed! sql: %s, error: %v", q, err)
		return -1
	}
	count, _ := result[0]["count"].(int64)
	return int(count)
}

func (b *baseTableOperator) AddCols(table string, cols ...*db.Column) bool {
	q := b.SQLOfAddingCols(table, cols...)
	_, err := b.exec(q)
	if err != nil {
		logger.Errorf("Add columns for table %s failed: %v", table, err)
		return false
	}
	return true
}

func (b *baseTableOperator) Query(table string, condition *db.Condition) ([]map[string]interface{}, error) {
	return b.QueryCols(table, []string{"*"}, condition, db.QueryMode.ALL)
}

func (b *baseTableOperator) QueryDistinct(table string, condition *db.Condition) ([]map[string]interface{}, error) {
	return b.QueryCols(table, []string{"*"}, condition, db.QueryMode.DISTINCT)
}

func (b *baseTableOperator) QueryCols(table string, cols []string, condition *db.Condition, mode int) (dbRows []map[string]interface{}, err error) {
	conditions, values := b.sqlOfConditions(0, condition)
	q := fmt.Sprintf("select %s %s from %s%s", dbcommon.GetQueryMode(mode), strings.Join(cols, ","), table, conditions)
	//now := time.Now()
	dbRows, err = b.query(q, values...)
	logger.Debug("Query in db %v, sql: %s", b.db, q)
	if err != nil {
		logger.Errorf("Query from %s failed! SQL: %s, error: %v", table, q, err)
		return []map[string]interface{}{}, err
	}
	/*columns := map[string]*db.Column{}
	for _, col := range b.getColumns(table) {
		columns[col.Name] = col
	}
	return dbcommon.ConvertColValues(dbRows, columns)*/
	return dbRows, nil
}

func (b *baseTableOperator) QueryContentInCols(table string, cols []string, condition *db.Condition, mode int) []map[string]interface{} {
	conditions, values := b.sqlOfConditions(0, condition)
	q := fmt.Sprintf("select %s %s from %s%s", dbcommon.GetQueryMode(mode), strings.Join(cols, ","), table, conditions)
	dbRows, err := b.query(q, values...)
	logger.Debug("Query in db %v, sql: %s", b.db, q)
	if err != nil {
		logger.Errorf("Query from %s failed! SQL: %s, error: %v", table, q, err)
		return []map[string]interface{}{}
	}

	return dbRows
}

func (b *baseTableOperator) InnerJoinQuery(table1, table2 string, condition *db.Condition) ([]map[string]interface{}, error) {
	conditions, values := b.sqlOfConditions(0, condition)
	q := fmt.Sprintf("select * from %s as t1 inner join %s as t2 on t1.id=t2.id %s", table1, table2, conditions)
	dbRows, err := b.query(q, values...)
	logger.Debug("InnerJoinQuery in db %v, sql: %s", b.db, q)
	if err != nil {
		logger.Errorf("Inner join query from %s and %s failed! SQL: %s, error: %v", table1, table2, q, err)
		return []map[string]interface{}{}, err
	}

	return dbRows, err
}

func (b *baseTableOperator) InnerJoinCount(table1, table2 string, condition *db.Condition) int {
	conditions, values := b.sqlOfConditions(0, condition)
	q := fmt.Sprintf("select count(*) from %s as t1 inner join %s as t2 on t1.id=t2.id %s", table1, table2, conditions)
	dbRows, err := b.query(q, values...)
	logger.Debug("InnerJoinQuery in db %v, sql: %s", b.db, q)
	if err != nil {
		logger.Errorf("Inner join query from %s and %s failed! SQL: %s, error: %v", table1, table2, q, err)
		return -1
	}

	return dbRows[0]["count"].(int)
}

func (b *baseTableOperator) Update(table string, cols map[string]interface{}, condition *db.Condition) bool {
	if len(cols) == 0 {
		return true
	}
	q := fmt.Sprintf("update %s set ", table)
	kvs, cells, cellIdx := []string{}, []interface{}{}, 0
	for k, v := range cols {
		kvs = append(kvs, fmt.Sprintf("%s=%s", k, b.CellMark(cellIdx)))
		cellIdx++
		cells = append(cells, v)
	}
	conditions, values := b.sqlOfConditions(cellIdx, condition)
	q += strings.Join(kvs, ",") + conditions
	cells = append(cells, values...)
	logger.Debug("baseTableOperator update database, db: %v, sql: %s", b.db, q)
	_, err := b.exec(q, cells...)
	if err != nil {
		logger.Errorf("Update table %s failed! SQL: %s, error: %s", table, q, err)
		return false
	}
	return true
}

func (b *baseTableOperator) sqlOfConditions(baseCellIdx int, condition *db.Condition) (string, []interface{}) {
	if condition == nil {
		return "", []interface{}{}
	}
	q, orderBy, limit, values := "", "", "", []interface{}{}
	if condition.Column != "" {
		depth := 0
		q, values = b.parseConditionTree(condition, &baseCellIdx, &depth)
		q = " where " + q
	}
	if len(condition.OrderCols) > 0 {
		orderBy = fmt.Sprintf(" order by %s %s", strings.Join(condition.OrderCols, ","), condition.Order)
	}
	if condition.Limit > 0 {
		limit = fmt.Sprintf(" limit %d offset %d", condition.Limit, condition.Offset)
	}
	q += orderBy + limit
	return q, values
}

func (b *baseTableOperator) parseConditionTree(condition *db.Condition, cellIdx *int, dep *int) (string, []interface{}) {
	values := []interface{}{}
	q := b.handleCondition(condition, cellIdx, &values)
	*dep++
	if len(condition.Next) >= 0 {
		for idx, c := range condition.Next {
			q += fmt.Sprintf(" %s ", b.Relations()[condition.Relations[idx]])
			depth := *dep
			subTreeSQL, subTreeValues := b.parseConditionTree(c, cellIdx, dep)
			if (*dep - depth) > 1 {
				q += fmt.Sprintf("(%s)", subTreeSQL)
			} else {
				q += subTreeSQL
			}
			values = append(values, subTreeValues...)
		}
	}
	return q, values
}

func (b *baseTableOperator) handleCondition(condition *db.Condition, cellIdx *int, values *[]interface{}) string {
	q := b.conditionClause(condition, *cellIdx)
	*values = append(*values, condition.Values...)
	(*cellIdx) += len(condition.Values)
	return q
}

func (b *baseTableOperator) conditionClause(cond *db.Condition, cellIdx int) string {
	format := " %s %s %s "
	switch cond.Operator {
	case db.Operator.ILIKE:
		raw := fmt.Sprintf(" %s %s ", cond.Column, b.Operators()[cond.Operator])
		return strings.Replace(raw, dbcommon.PlaceHolder, b.CellMark(cellIdx), -1)
	case db.Operator.LIKE:
		raw := fmt.Sprintf(" %s %s ", cond.Column, b.Operators()[cond.Operator])
		return strings.Replace(raw, dbcommon.PlaceHolder, b.CellMark(cellIdx), -1)
	case db.Operator.BETWEEN:
		return fmt.Sprintf(" %s %s %s and %s ", cond.Column, b.Operators()[cond.Operator], b.CellMark(cellIdx), b.CellMark(cellIdx+1))
	case db.Operator.IN:
		format = " %s %s (%s) "
	case db.Operator.NOTIN:
		format = " %s %s (%s) "
	case db.Operator.INCLUDE:
		format = " %s %s (%s)"
	default:
		if len(cond.Values) > 1 {
			format = " %s %s (%s) "
		}
	}
	cellMarks := []string{}
	for idx := range cond.Values {
		cellMarks = append(cellMarks, b.CellMark(cellIdx+idx))
	}
	return fmt.Sprintf(format, cond.Column, b.Operators()[cond.Operator], strings.Join(cellMarks, ","))
}

func (b *baseTableOperator) Close() bool {
	err := b.conn.Close()
	if err != nil {
		logger.Errorf("", "Close db client failed!", err)
		return false
	}
	return true
}

func (b *baseTableOperator) query(sql string, values ...interface{}) (rows []map[string]interface{}, err error) {
	results, err := b.conn.Query(sql, values...)
	logger.Debug("Query table sql: %s, values:%v", sql, values)
	if err != nil {
		logger.Errorf("", "Query table failed! sql:", sql, ", values:", values, ", err:", err)
		return nil, err
	}
	defer results.Close()
	rows = dbcommon.ParseSQLResults(results)
	return rows, err
}

func (b *baseTableOperator) exec(sql string, values ...interface{}) (res sql.Result, err error) {
	tx, err := b.conn.Begin()
	if err != nil {
		logger.Errorf("baseTableOperator connect to database failed: %v", err)
		return nil, err
	}
	res, err = tx.Exec(sql, values...)
	if err != nil {
		logger.Errorf("baseTableOperator exec sql failed: %v", err)
		rollErr := tx.Rollback()
		if rollErr != nil {
			logger.Errorf(rollErr.Error())
		}
		return nil, err
	}
	if errCmt := tx.Commit(); errCmt != nil {
		return nil, errCmt
	}
	return res, err
}

func (b *baseTableOperator) Exec(sql string, values ...interface{}) bool {
	_, err := b.exec(sql, values...)
	if err != nil {
		logger.Errorf("Exec sql failed! sql: %s, error: %v", sql, err)
		return false
	}
	return true
}

func (b *baseTableOperator) ExecQuery(sql string, values ...interface{}) (rows []map[string]interface{}, err error) {
	return b.query(sql, values...)
}

func (b *baseTableOperator) ExecCount(sql string, values ...interface{}) int {
	result, err := b.query(sql, values...)
	if err != nil {
		logger.Errorf("Count from DB failed! sql: %s, error: %v", sql, err)
		return -1
	}
	count, _ := result[0]["count"].(int64)
	return int(count)
}
