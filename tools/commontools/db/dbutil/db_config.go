package dbutil

import (
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"strings"
)

type DbConfig struct {
	Type     string
	Hosts    []string
	Port     int
	User     string
	Password string
	Driver   string
}

func (d *DbConfig) ToDbInfo(db string) dbcommon.DbInfo {
	return dbcommon.DbInfo{
		Driver:   d.dbDriver(),
		Hosts:    d.handleIP(),
		Port:     d.Port,
		User:     d.User,
		Password: d.Password,
		Db:       db,
	}
}

func (d *DbConfig) dbDriver() int {
	switch d.Type {
	case PostgreSQL:
		return dbcore.DbDriver.POSTGRESQL
	case Memory:
		return dbcore.DbDriver.MEMORY
	default:
		return dbcore.DbDriver.MYSQL
	}
}

func (d *DbConfig) handleIP() []string {
	hosts := make([]string, len(d.Hosts))
	for idx, host := range d.Hosts {
		isIPV6 := strings.IndexByte(host, ':') >= 0
		if isIPV6 && !strings.Contains(host, "[") {
			hosts[idx] = "[" + host + "]"
		} else {
			hosts[idx] = host
		}
	}
	return hosts
}
