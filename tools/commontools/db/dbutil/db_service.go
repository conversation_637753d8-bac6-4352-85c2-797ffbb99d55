package dbutil

import (
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
	"errors"
	"strconv"
	"strings"
)

var dbClient *DBClient

func GetDbClient() *DBClient {
	return dbClient
}

func SetDbClient(client *DBClient) {
	dbClient = client
}

func InitDbClient() error {
	//string to int
	dbPort, err := strconv.Atoi(globalcv.DbPort)
	conf := DbConfig{
		Type:     globalcv.DbType,
		Hosts:    []string{globalcv.DbHost},
		Port:     dbPort,
		User:     globalcv.DbUser,
		Password: globalcv.DbPassword,
	}
	client, err := applyDbClient(conf, globalcv.DbName)
	if err != nil {
		logger.Errorf("conn to db fail : ", globalcv.DbName)
		return err
	}
	SetDbClient(client)
	return nil
}

func applyDbClient(conf DbConfig, db string) (client *DBClient, err error) {
	err = isValidDbConf(conf, db)
	if err != nil {
		logger.Errorf("applyDbClient isValidDbConf error:", err.Error())
		return nil, err
	}

	if clientRepo.Exists(conf.Type, db) {
		client = clientRepo.Retrieve(conf.Type, db)
	} else {
		client, err = newDBClient(conf.ToDbInfo(db))
		if err == nil {
			clientRepo.Save(conf.Type, db, client)
		}
	}
	return client, err
}

func isValidDbConf(conf DbConfig, db string) error {
	if db == "" {
		return errors.New("cannot apply DB client due to empty db name")
	}
	if len(conf.Hosts) == 0 {
		return errors.New("DB hosts is empty")
	}
	if !isSupportedDbType(conf.Type) {
		return errors.New("unsupported db type, this should be: PG or MariaDB or MYSQL or Cassandra")
	}
	return nil
}

func isSupportedDbType(dbType string) bool {
	for _, t := range supportedDbTypes {
		if strings.EqualFold(strings.ToLower(dbType), strings.ToLower(t)) {
			return true
		}
	}
	return false
}

func CloseDbClient(dbType string, db string) bool {
	client := clientRepo.Retrieve(dbType, db)
	if client != nil {
		clientRepo.Delete(dbType, db)
		return client.close()
	}
	return true
}
