package dbutil

import (
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	dboperator "cwsm/tools/commontools/db/dbutil/db-operator"
	tableoperator "cwsm/tools/commontools/db/dbutil/table-operator"
	"reflect"
	"strings"
)

func SetMemoDbClient(dbInfo dbcommon.DbInfo) {
	tOp, _ := tableoperator.NewTableOperator(dbInfo)
	dbOp, _ := dboperator.NewDbOperator(dbInfo)
	SetDbClient(&DBClient{info: dbInfo, tableOperator: tOp, dbOperator: dbOp})
}

func CreateTable4Memo(table string, instance interface{}) {
	insType := reflect.TypeOf(instance)
	for insType.Kind() == reflect.Ptr {
		insType = insType.Elem()
	}
	var cols []*dbcore.Column
	extractCols(insType, &cols)

	dbClient := GetDbClient()
	if !dbClient.TableExists(table) {
		dbClient.CreateTable(table, cols...)
	}
}

func extractCols(insType reflect.Type, cols *[]*dbcore.Column) {
	for i := 0; i < insType.NumField(); i++ {
		if insType.Field(i).Anonymous {
			extractCols(insType.Field(i).Type, cols)
		} else {
			pgTag := strings.Split(insType.Field(i).Tag.Get("pgdb"), ",")[0]
			*cols = append(*cols, dbcore.NewColumn(pgTag, dbcore.ColType.STRING))
		}
	}
}
