package dbutil

import (
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	dboperator "cwsm/tools/commontools/db/dbutil/db-operator"
	tableoperator "cwsm/tools/commontools/db/dbutil/table-operator"
	"cwsm/tools/commontools/logger"
	"errors"
)

func newDBClient(dbInfo dbcommon.DbInfo) (client *DBClient, err error) {
	if dbInfo.Db == "" {
		return nil, errors.New("create DB client failed due to empty db name")
	}
	client = &DBClient{info: dbInfo}
	client.dbOperator, err = newDbOperator(dbInfo)
	if err == nil && client.DBExists() {
		client.tableOperator, err = newTableOperator(dbInfo)
	}
	return client, err
}

func newDbOperator(dbInfo dbcommon.DbInfo) (dboperator.DBOperator, error) {
	logger.Infof("Connect ", dbInfo.Db, " with config:", dbInfo.ToString())
	return dboperator.NewDbOperator(dbInfo)
}

func newTableOperator(dbInfo dbcommon.DbInfo) (tableoperator.TableOperator, error) {
	logger.Infof("Connect ", dbInfo.Db, " with config:", dbInfo.ToString())
	return tableoperator.NewTableOperator(dbInfo)
}
