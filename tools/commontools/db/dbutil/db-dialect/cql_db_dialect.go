package dbdialect

import (
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"fmt"
	"strings"
)

type CassandraDialect struct{}

var ReplicationFactor = 2

func (c *CassandraDialect) CqlOfCreateKeySpace(db string) string {
	return fmt.Sprintf(`CREATE KEYSPACE %s WITH replication = {
        'class' : 'SimpleStrategy',
        'replication_factor' : %d
    }`, db, ReplicationFactor)
}

func (c *CassandraDialect) CqlOfCountingKeySpace(db string) string {
	return fmt.Sprintf("select count(1) as count from system_schema.keyspaces where keyspace_name='%s'", db)
}

func (c *CassandraDialect) CqlOfDestroyKeySpace(db string) string {
	return "drop keyspace " + db
}

func (c *CassandraDialect) CqlOfGrantAllPrivileges(db string, user string) string {
	return "GRANT all PERMISSIONS ON KEYSPACE " + db + " to " + user
}

func (c *CassandraDialect) CqlOfCreateTable(table string, cols []*dbcore.Column) string {
	return fmt.Sprintf("create table if not exists %s(%s)", table, c.cqlOfColumns(cols))
}

func (c *CassandraDialect) cqlOfColumns(columns []*dbcore.Column) (cql string) {
	primaryKeys := []string{}
	partitionKeys := []string{}
	for idx, col := range columns {
		if idx > 0 {
			cql += ","
		}
		cql += fmt.Sprintf("%s %s", col.Name, cassandraTypes[col.ColumnType])
		if col.HasProperty(dbcore.ColProperties.CassandraPartitionKey) {
			partitionKeys = append(partitionKeys, col.Name)
		} else if col.HasProperty(dbcore.ColProperties.PrimaryKey) {
			primaryKeys = append(primaryKeys, col.Name)
		}
	}
	if len(partitionKeys) != 0 || len(primaryKeys) != 0 {
		cql += fmt.Sprintf(", primary key(%s,%s)",
			"("+strings.Join(partitionKeys, ",")+")", strings.Join(primaryKeys, ","))
	}
	return cql
}

func (c *CassandraDialect) CqlOfDestroyTable(table string) string {
	return "drop table " + table
}

func (c *CassandraDialect) CqlOfCountingTable(keySpace string, table string) string {
	return fmt.Sprintf(
		"select count(1) as count from system_schema.tables where keyspace_name='%s' and table_name='%s'",
		keySpace, table,
	)
}

func (c *CassandraDialect) CqlOfColumnsQuery(keySpace string, table string) string {
	return fmt.Sprintf(
		"select * from system_schema.columns where keyspace_name='%s' and table_name='%s'",
		keySpace, table,
	)
}

func (c *CassandraDialect) CqlOfAddingCols(table string, cols ...*dbcore.Column) []string {
	cqlGroup := []string{}
	for _, col := range cols {
		cqlGroup = append(cqlGroup, fmt.Sprintf("alter table %s add %s %s", table, col.Name, cassandraTypes[col.ColumnType]))
	}
	return cqlGroup
}

var cassandraTypes = map[int]string{
	dbcore.ColType.BOOL:     "boolean",
	dbcore.ColType.FLOAT:    "double",
	dbcore.ColType.INT:      "int",
	dbcore.ColType.BIGINT:   "bigint",
	dbcore.ColType.SMALLINT: "int",
	dbcore.ColType.STRING:   "varchar",
	dbcore.ColType.TEXT:     "text",
}

func (c *CassandraDialect) DataTypes() map[int]string {
	return cassandraTypes
}

var cassandraRelations = map[int]string{
	dbcore.Relation.AND: "and",
}

func (c *CassandraDialect) Relations() map[int]string {
	return cassandraRelations
}

var cassandraOperators = map[int]string{
	dbcore.Operator.EQ:  "=",
	dbcore.Operator.GT:  ">",
	dbcore.Operator.GTE: ">=",
	dbcore.Operator.LT:  "<",
	dbcore.Operator.LTE: "<=",
	dbcore.Operator.IN:  "in",
}

func (c *CassandraDialect) Operators() map[int]string {
	return cassandraOperators
}

func (c *CassandraDialect) CellMark() string {
	return "?"
}
