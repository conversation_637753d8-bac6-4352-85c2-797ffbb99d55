package dbdialect

import (
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"fmt"
	"strings"
)

type SQLDbDialect interface {
	SQLOfCreateDB(db string) string
	SQLOfCountingDB(db string) string
	SQLOfDestroyDB(db string) string
	SQLOfGrantAllPrivileges(db string, user string) string

	SQLOfCreateTable(table string, cols ...*dbcore.Column) string
	SQLOfDestroyTable(table string) string
	SQLOfCountingTable(db string, table string) string
	SQLOfColumnsQuery(table string) string
	SQLOfAddingCols(table string, cols ...*dbcore.Column) string

	ConnectionString(ip string, port int, user string, pwd string, db string) string
	DataTypes() map[int]string
	Relations() map[int]string
	Operators() map[int]string
	CellMark(cellIdx int) string
	FormatSQL(string, ...interface{}) string
}

func sqlOfColumns(dialect SQLDbDialect, columns ...*dbcore.Column) string {
	var primaryKeys []string
	q := "("
	for idx, col := range columns {
		if idx > 0 {
			q += ","
		}
		q += fmt.Sprintf("%s %s not null", col.Name, dialect.DataTypes()[col.ColumnType])
		if col.HasProperty(dbcore.ColProperties.PrimaryKey) {
			primaryKeys = append(primaryKeys, col.Name)
		}
	}
	if len(primaryKeys) != 0 {
		q += fmt.Sprintf(", primary key(%s)", strings.Join(primaryKeys, ","))
	}
	q += ")"
	return q
}
