package dbdialect

import (
	dbcommon "cwsm/tools/commontools/db/dbutil/db-common"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/logger"
	"fmt"
	nurl "net/url"
	"reflect"
	"strings"

	"github.com/lib/pq"
)

type PgDialect struct {
	SQLDbDialect
}

func (p *PgDialect) ConnectionString(ip string, port int, user string, pwd string, db string) string {
	url := ""
	if db == "" {
		db = "postgres"
	}
	pwd = nurl.QueryEscape(pwd)
	url = fmt.Sprintf("postgres://%s:%d/%s?user=%s&password=%s&sslmode=require", ip, port, db, user, pwd)
	dataSource, err := pq.ParseURL(url)
	if err != nil {
		logger.Errorf("ParseURL pg url failed!")
		return ""
	}
	return dataSource
}

func (p *PgDialect) SQLOfCreateDB(db string) string {
	return fmt.Sprintf("create database %s Encoding=UTF8 lc_collate='en_US.utf8'", db)
}

func (p *PgDialect) SQLOfCountingDB(db string) string {
	return fmt.Sprintf("select count(1) as count from pg_database where datname='%s'", db)
}

func (p *PgDialect) SQLOfDestroyDB(db string) string {
	return fmt.Sprintf("drop database %s", db)
}

func (p *PgDialect) SQLOfGrantAllPrivileges(db string, user string) string {
	return "GRANT ALL PRIVILEGES ON DATABASE " + db + " to " + user
}

func (p *PgDialect) SQLOfColumnsQuery(table string) string {
	return fmt.Sprintf("select column_name, data_type, is_nullable from information_schema.columns where "+
		"table_schema not in ('pg_catalog', 'information_schema') and table_name = '%s'", table)
}

func (p *PgDialect) SQLOfAddingCols(table string, cols ...*dbcore.Column) string {
	var sqlOfCols []string
	for _, col := range cols {
		sqlOfCols = append(sqlOfCols, "add column "+col.Name+" "+p.DataTypes()[col.ColumnType])

	}
	return fmt.Sprintf("alter table %s %s", table, strings.Join(sqlOfCols, ","))
}

var pgTypes = map[int]string{
	dbcore.ColType.BOOL:     "boolean",
	dbcore.ColType.FLOAT:    "double precision",
	dbcore.ColType.INT:      "integer",
	dbcore.ColType.BIGINT:   "bigint",
	dbcore.ColType.SMALLINT: "smallint",
	dbcore.ColType.STRING:   "character varying(255)",
	dbcore.ColType.TEXT:     "text",
}

func (p *PgDialect) DataTypes() map[int]string {
	return pgTypes
}

func (p *PgDialect) SQLOfCreateTable(table string, columns ...*dbcore.Column) string {
	return fmt.Sprintf("create table %s%s", table, sqlOfColumns(p, columns...))
}

func (p *PgDialect) SQLOfDestroyTable(table string) string {
	return fmt.Sprintf("drop table %s", table)
}

func (p *PgDialect) SQLOfCountingTable(db string, table string) string {
	return fmt.Sprintf("select count(1) as count from information_schema.tables where table_type = 'BASE TABLE' "+
		"and table_schema not in ('pg_catalog', 'information_schema') and table_name='%s'", table)
}

var pgRelations = map[int]string{
	dbcore.Relation.AND: "and",
	dbcore.Relation.OR:  "or",
}

func (p *PgDialect) Relations() map[int]string {
	return pgRelations
}

var pgOperators = map[int]string{
	dbcore.Operator.EQ:      "=",
	dbcore.Operator.NE:      "!=",
	dbcore.Operator.GT:      ">",
	dbcore.Operator.GTE:     ">=",
	dbcore.Operator.LT:      "<",
	dbcore.Operator.LTE:     "<=",
	dbcore.Operator.LIKE:    "like '%'||" + dbcommon.PlaceHolder + "||'%'",
	dbcore.Operator.ILIKE:   "ilike '%'||" + dbcommon.PlaceHolder + "||'%'",
	dbcore.Operator.IN:      "in",
	dbcore.Operator.NOTIN:   "not in",
	dbcore.Operator.INCLUDE: "@>",
}

func (p *PgDialect) Operators() map[int]string {
	return pgOperators
}

func (p *PgDialect) CellMark(cellIdx int) string {
	return fmt.Sprintf("$%d", cellIdx+1)
}

// 组装查询sql语句，用于调用 dbClient.ExecQuery 查询数据库时使用
// 入参：q -- 带占位符的sql语句 values -- 查询所带参数
// 返回值：根据底层数据库类型得到的sql语句
func (p *PgDialect) FormatSQL(q string, values ...interface{}) string {
	//tmp数组是为了得到values的个数,用于拼sql
	var tmp []interface{}
	for _, value := range values {
		switch reflect.TypeOf(value).Kind() {
		case reflect.Array, reflect.Slice:
			slice := reflect.ValueOf(value)
			for idx := 0; idx < slice.Len(); idx++ {
				v := slice.Index(idx)
				if v.CanInterface() {
					tmp = append(tmp, v.Interface())
				}
			}
		default:
			tmp = append(tmp, value)
		}
	}
	var cellMarks []interface{}
	for i := 0; i < len(tmp); i++ {
		cellMarks = append(cellMarks, fmt.Sprintf("$%d", i+1))
	}
	return fmt.Sprintf(q, cellMarks...)
}
