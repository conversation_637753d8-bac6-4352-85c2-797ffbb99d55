package dbcore

const (
	boolean = iota
	varchar
	text
	integer
	float
	bigint
	smallint
)

var ColType = columnType{
	BOOL:     boolean,
	STRING:   varchar,
	TEXT:     text,
	INT:      integer,
	FLOAT:    float,
	BIGINT:   bigint,
	SMALLINT: smallint,
}

type columnType struct {
	BO<PERSON>     int
	STRING   int
	TEXT     int
	INT      int //4 bytes, signed
	BIGINT   int //8 bytes, signed
	SM<PERSON><PERSON>INT int //2 bytes, signed
	FLOAT    int
}
