package dbcore

type columnProperty interface {
	Name() string
	Value() interface{}
}

type booleanProperty struct {
	name string
	columnProperty
}

func (b *booleanProperty) Name() string {
	return b.name
}

func (b *booleanProperty) Value() interface{} {
	return true
}

type columnProperties struct {
	PrimaryKey            *booleanProperty
	CassandraPartitionKey *booleanProperty
}

var ColProperties = columnProperties{
	&booleanProperty{name: "primary key"},
	&booleanProperty{name: "cassandra partition key"},
}
