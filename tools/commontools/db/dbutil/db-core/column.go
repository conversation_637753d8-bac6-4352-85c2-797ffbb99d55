package dbcore

import "errors"

type Column struct {
	Name       string
	ColumnType int
	Properties map[string]interface{}
}

func NewColumn(name string, colType int) *Column {
	return &Column{name, colType, map[string]interface{}{}}
}

func (c *Column) AddProperty(property columnProperty) *Column {
	c.Properties[property.Name()] = property.Value()
	return c
}

func (c *Column) HasProperty(property columnProperty) bool {
	_, exists := c.Properties[property.Name()]
	return exists
}

func (c *Column) ValueOfProperty(property columnProperty) (interface{}, error) {
	value, exists := c.Properties[property.Name()]
	if !exists {
		return nil, errors.New("Property:" + property.Name() + " does not exists in column:" + c.Name)
	}
	return value, nil
}
