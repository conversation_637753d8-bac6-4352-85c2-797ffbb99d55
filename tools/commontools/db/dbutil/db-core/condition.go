package dbcore

import (
	"reflect"
)

// TODO wl 04/18/2020 And function is not working correctly
type Condition struct {
	Column    string
	Operator  int
	Values    []interface{}
	Next      []*Condition
	Relations []int
	OrderCols []string
	Order     string
	Limit     int
	Offset    int
}

func NewCondition(col string, operator int, values ...interface{}) *Condition {
	var tmp []interface{}
	tmp = dealWithValues(values, tmp)

	return &Condition{
		Operator: operator,
		Column:   col,
		Values:   tmp,
	}
}

func NewQueryCondition(col string, operator int, orderCols []string, order string, limint, offset int, values ...interface{}) *Condition {
	var tmp []interface{}
	tmp = dealWithValues(values, tmp)

	return &Condition{
		Operator:  operator,
		Column:    col,
		Values:    tmp,
		OrderCols: orderCols,
		Order:     order,
		Limit:     limint,
		Offset:    offset,
	}
}

func dealWithValues(values []interface{}, tmp []interface{}) []interface{} {
	for _, value := range values {
		switch reflect.TypeOf(value).Kind() {
		case reflect.Array, reflect.Slice:
			slice := reflect.ValueOf(value)
			for idx := 0; idx < slice.Len(); idx++ {
				v := slice.Index(idx)
				if v.CanInterface() {
					tmp = append(tmp, v.Interface())
				}
			}
		default:
			tmp = append(tmp, value)
		}
	}
	return tmp
}

func OrderBy(order int, cols ...string) *Condition {
	return &Condition{
		OrderCols: cols,
		Order:     getOrder(order),
	}
}

// Note: Cassandra does not support this method
func Paging(limit int, offset int) *Condition {
	return &Condition{
		Limit:  limit,
		Offset: offset,
	}
}

func PagingFromBegging(limit int) *Condition {
	return &Condition{
		Limit:  limit,
		Offset: 0,
	}
}

func (c *Condition) And(condition *Condition) *Condition {
	c.add(condition, and)
	return c
}

func (c *Condition) Or(condition *Condition) *Condition {
	c.add(condition, or)
	return c
}

func (c *Condition) add(condition *Condition, relation int) {
	c.Next = append(c.Next, condition)
	c.Relations = append(c.Relations, relation)
}

func (c *Condition) OrderBy(order int, cols ...string) *Condition {
	c.OrderCols = cols
	c.Order = getOrder(order)
	return c
}

func getOrder(order int) string {
	switch order {
	case Order.DESC:
		return "desc"
	default:
		return "asc"
	}
}

// Note: Cassandra does not support this method
func (c *Condition) Paging(limit int, offset int) *Condition {
	c.Limit = limit
	c.Offset = offset
	return c
}

func (c *Condition) PagingFromBegging(limit int) *Condition {
	c.Limit = limit
	c.Offset = 0
	return c
}
