package otcpsm

import (
	"encoding/json"
	"errors"
	"fmt"
	"cwsm/tools/commontools/director"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/httpservice"
)

func GetToken(request *GetAccessTokenReq) (string, error) {
	logger.Infof("[GetToken] request: %s", util.ToJSONStr(request))
	bs, _, _, err := httpservice.Post(OesSmURLAccessToken, httpservice.DefaultHeaders(), request, nil)
	if err != nil {
		logger.Errorf("[GetAccessToken] Post failed, url: %s, error: %v", OesSmURLAccessToken, err)
		return "", fmt.<PERSON><PERSON><PERSON>(director.ParseErrorResponse(err))
	}
	response := GetAccessTokenRsp{}
	if err = json.Unmarshal(bs, &response); err != nil {
		logger.Errorf("[GetAccessToken] json.Unmarshal failed: %v", err)
		return "", fmt.<PERSON><PERSON><PERSON>("unmarshal response body failed: %v", err)
	}
	return response.AccessToken, nil
}

func SmRegOperateCode(body interface{}) error {
	_, _, _, err := httpservice.Post(OesSmURLOpeModel, httpservice.DefaultHeaders(), body, nil)
	if err != nil {
		logger.Errorf("SmRegOperateCode failed:", err.Error())
		return err
	}
	return nil
}

func SmAuthByOperate(token string, operationSet []string) (*AuthRespBodyDto, error) {
	reqByte, err := json.Marshal(AuthRequestDto{AccessToken: token, Operations: operationSet})
	if err != nil {
		return nil, err
	}
	body, _, _, err := httpservice.Post(OesSmURLINFAuth, httpservice.DefaultHeaders(), reqByte, nil)
	logger.Debug("SmAuthByOperate get auth check url: "+OesSmURLINFAuth+
		"operationSet: ", operationSet)
	if err != nil {
		logger.Errorf("SmAuthByOperate Post failed,err:", err)
		return nil, err
	}
	resp := &AuthRespBodyDto{}
	err = json.Unmarshal(body, resp)
	if err != nil {
		logger.Errorf("SmAuthByOperate get auth check json.Unmarshal failed,err:", err)
		return nil, err
	}

	logger.Debug("SmAuthByOperate operation Set:%#v", resp.Operations)
	return resp, nil
}

func SmAuthByOpResources(token string, operationSet []string, reqIds []string) ([]string, error) {
	var authResourceIds []string
	reqBody := UserDomainParamDto{
		AccessToken:    token,
		Operations:     operationSet,
		ResourceGroups: reqIds,
	}
	logger.Debug("SmAuthByOpResources reqBody:%s", util.ToJSONStr(reqBody))
	body, _, _, err := httpservice.Post(OesSmURLUserDomainURL, httpservice.DefaultHeaders(), reqBody, nil)
	if err != nil {
		logger.Errorf("SmAuthByOpResources get auth resource ids by oes failed, err:", err)
		return authResourceIds, err
	}
	resp := &SmAuthOpResourcesRespDto{}
	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Errorf("SmAuthByOpResources get auth resource ids json.Unmarshal failed, err:", err)
		return authResourceIds, err
	}
	authResourceIds = resp.ResourceGroups
	logger.Debug("SmAuthByOpResources get resource ids:%#v", authResourceIds)
	return authResourceIds, nil
}

func ReportClientIdToSm(req ClientIDReportReq) (int, error) {
	_, _, statusCode, err := httpservice.Put(OesSmURLClientid, httpservice.DefaultHeaders(), req, nil)
	return statusCode, err
}

func CheckAccessToken(token string) (bool, *CheckAccessTokenResp, error) {
	reqByte, err := json.Marshal(CheckAccessTokenReq{AccessToken: []string{token}})
	if err != nil {
		logger.Errorf("[CheckAccessToken] json.Marshal failed: %v", err)
		return false, nil, err
	}

	body, _, _, err := httpservice.Post(OesSmCheckAccessToken, httpservice.DefaultHeaders(), reqByte, nil)
	if err != nil {
		logger.Errorf("[CheckAccessToken] oes sm check access token failed: %v", err)
		return false, nil, err
	}

	resp := &CheckAccessTokenResp{}
	err = json.Unmarshal(body, resp)
	if err != nil {
		logger.Errorf("[CheckAccessToken] get auth check json.Unmarshal failed,err:", err)
		return false, resp, err
	}

	if resp.Result[0].StatusCode != 0 {
		logger.Errorf("[CheckAccessToken] access token is invalid")
		return false, resp, errors.New("[CheckAccessToken] access token is invalid")
	}

	return true, resp, nil
}

func GenerateClientId(req CreateOauth2ClientReq) (*CreateOauth2ClientRsp, error) {
	logger.Info("[GenerateClientId] req: %s", util.ToStr(req))
	body, _, _, err := httpservice.Post(OesAppManage, httpservice.DefaultHeaders(), req, nil)
	if err != nil {
		logger.Errorf("[GenerateClientId] failed: %v", err)
		return nil, err
	}
	resp := &CreateOauth2ClientRsp{}
	err = json.Unmarshal(body, resp)
	if err != nil {
		logger.Errorf("[GenerateClientId] Unmarshal failed: %v", err)
		return nil, err
	}
	return resp, err
}

func GetClientInfo(id string) (*CreateOauth2ClientRsp, error) {
	logger.Info("[GetClientInfo] id: %s", id)
	body, _, _, err := httpservice.Get(OesAppManage+"/query/"+id, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetClientInfo] failed: %v", err)
		return nil, err
	}
	resp := &CreateOauth2ClientRsp{}
	err = json.Unmarshal(body, resp)
	if err != nil {
		logger.Errorf("[GetClientInfo] Unmarshal failed: %v", err)
		return nil, err
	}
	return resp, err
}

func DeleteClientId(id string) error {
	logger.Info("[DeleteClientId] id: %s", id)
	_, _, _, err := httpservice.Delete(OesAppManage+"/delete/"+id, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Error("DeleteClientId faild:", err.Error())
	}
	return err
}
