package otcpsm

import (
	"cwsm/tools/commontools/commondao/common_cloud_env"
	"cwsm/tools/commontools/commondao/common_scen"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/director/cloudfuze"
	"cwsm/tools/commontools/domainservice/storagedomain"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/pg/storagex"
	"cwsm/tools/commontools/storage/pg/table_operator"
	"cwsm/tools/commontools/storage/rediz/storage/cloud_env_redis"
	"cwsm/tools/commontools/storage/res/storage/dcgroup_res"
	"encoding/json"
	"net/url"
	"os"
	"regexp"
	"strings"

	"github.com/beego/beego/v2/core/utils"
	beego "github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
	"github.com/tidwall/gjson"
)

var AuthCheckModel = AuthOperationDto{}

// 资源分权接口
var OesSmAuthFilter = func(ctx *context.Context) {
	_, ok1 := ctx.Request.Header[globalcv.HeaderAccessToken]
	_, ok2 := ctx.Request.Header[globalcv.HeaderXAuthToken]
	token := ctx.Request.Header.Get(globalcv.HeaderAccessToken)
	if !ok1 && !ok2 && len(token) == 0 { //token为空，鉴权通过
		logger.Debug("OesSmAuthFilter empty token means authorized")
		return
	}
	valid, tokenRsp, err := CheckAccessToken(token)
	if !valid {
		logger.Errorf("OesSmAuthFilter invalid token: %v", err.Error())
		ctx.Output.SetStatus(403)
		_ = ctx.Output.JSON("invalid token", false, false)
		panic(beego.ErrAbort)
	}
	if valid && len(ctx.Request.Header.Get(globalcv.HeaderOperateuser)) == 0 {
		ctx.Request.Header.Set(globalcv.HeaderOperateuser, tokenRsp.Result[0].UserName)
	}

	reqURL := ctx.Request.URL.Path + "_" + strings.ToUpper(ctx.Request.Method)
	logger.Debugf("OesSmAuthFilter ctx.Request.URL:", reqURL)

	var opCodes []string
	var funcResourceIds FuncFetchResourceIds
	for _, authCheck := range AuthCheckModel.OperationList {
		for _, url := range authCheck.URLMap {
			match := findOperationAndResources(ServiceUrl+url, reqURL, &funcResourceIds)
			if match {
				opCodes = append(opCodes, authCheck.NodeID)
				break
			}
		}
	}
	logger.Debugf("OesSmAuthFilter operationCodes:v", opCodes)

	if len(opCodes) > 0 {
		tag, newOpCodes := checkAuthBySm(token, opCodes)
		opCodes = newOpCodes
		if !tag {
			logger.Errorf("OesSmAuthFilter checkAuthBySm failed: not authorized")
			ctx.Output.SetStatus(401)
			_ = ctx.Output.JSON("user is not authorized", false, false)
			panic(beego.ErrAbort)
		}
		if funcResourceIds != nil && !filterResourcesBySm(token, opCodes, funcResourceIds, ctx) {
			logger.Errorf("OesSmAuthFilter filterResourcesBySm failed: not authorized")
			ctx.Output.SetStatus(401)
			_ = ctx.Output.JSON("user is not authorized", false, false)
			panic(beego.ErrAbort)
		}
	}
}

/* Started by AICoder, pid:2682baf3eb3644e191dfda6e59ef7563 */
func findOperationAndAuthResourceType(reqURL string, reqURLQueryParams url.Values, reqBody []byte, reqURLPathParams map[string]string) ([]string, map[string]string) {
	var opCodes []string
	var opCodeAndAuthResourceType = make(map[string]string)
	for _, authCheck := range AuthCheckModel.OperationList {
		for _, api := range authCheck.ApiMap {
			match, authResourceType := findOperationByApi(api, reqURL, reqURLQueryParams, reqBody, reqURLPathParams)
			if match {
				opCodes = append(opCodes, authCheck.NodeID)
				if !util.IsEmpty(authResourceType) {
					opCodeAndAuthResourceType[authCheck.NodeID] = authResourceType
				}
				break
			}
		}
		for _, url := range authCheck.URLMap {
			match, authResourceType := findOperation(ServiceUrl+url, reqURL)
			if match {
				opCodes = append(opCodes, authCheck.NodeID)
				if !util.IsEmpty(authResourceType) {
					opCodeAndAuthResourceType[authCheck.NodeID] = authResourceType
				}
				break
			}
		}
	}
	return opCodes, opCodeAndAuthResourceType
}

func handleAuthorization(ctx *context.Context, token string, opCodes []string, opCodeAndAuthResourceType map[string]string) {
	if len(opCodes) > 0 {
		tag, newOpCodes := checkAuthBySm(token, opCodes)
		opCodes = newOpCodes
		if !tag {
			logger.Errorf("OesSmAuthFilterEx checkAuthBySm failed: not authorized")
			ctx.Output.SetStatus(401)
			_ = ctx.Output.JSON("user is not authorized", false, false)
			panic(beego.ErrAbort)
		}

		var resourceIds = findResources(opCodes, opCodeAndAuthResourceType)
		logger.Debugf("OesSmAuthFilterEx newOpCodes:%v, resourceIds:%v", newOpCodes, resourceIds)

		if !util.IsEmpty(opCodeAndAuthResourceType) && !filterResourcesBySmAndSet(token, opCodes, resourceIds, ctx) {
			logger.Errorf("OesSmAuthFilterEx filterResourcesBySm failed: not authorized")
			ctx.Output.SetStatus(401)
			_ = ctx.Output.JSON("user is not authorized", false, false)
			panic(beego.ErrAbort)
		}
	}
}

var OesSmAuthFilterEx = func(ctx *context.Context) {
	_, ok1 := ctx.Request.Header[globalcv.HeaderAccessToken]
	_, ok2 := ctx.Request.Header[globalcv.HeaderXAuthToken]
	token := ctx.Request.Header.Get(globalcv.HeaderAccessToken)
	reqURL := ctx.Request.URL.Path + "_" + strings.ToUpper(ctx.Request.Method)
	reqURLQueryParams := ctx.Request.URL.Query()
	reqBody := ctx.Input.RequestBody
	reqURLPathParams := ctx.Input.Params()
	logger.Debugf("OesSmAuthFilterEx reqURL:%s, reqURLQueryParams:%v, reqBody:%v, reqURLPathParams:%v",
		reqURL, reqURLQueryParams, reqBody, reqURLPathParams)
	opCodes, opCodeAndAuthResourceType := findOperationAndAuthResourceType(reqURL, reqURLQueryParams, reqBody, reqURLPathParams)
	logger.Debugf("OesSmAuthFilterEx operationCodes:%v, opCodeAndAuthResourceType:%v", opCodes, opCodeAndAuthResourceType)
	if !ok1 && !ok2 && len(token) == 0 { //token为空，鉴权通过
		var resourceIds = findResources(opCodes, opCodeAndAuthResourceType)
		ctx.Input.SetData(globalcv.ResoucesIdsKey, resourceIds)
		logger.Debug("OesSmAuthFilterEx empty token means authorized")
		return
	}
	valid, tokenRsp, err := CheckAccessToken(token)
	if !valid {
		logger.Errorf("OesSmAuthFilterEx invalid token: %v", err.Error())
		ctx.Output.SetStatus(403)
		_ = ctx.Output.JSON("invalid token", false, false)
		panic(beego.ErrAbort)
	}
	if valid && len(ctx.Request.Header.Get(globalcv.HeaderOperateuser)) == 0 {
		ctx.Request.Header.Set(globalcv.HeaderOperateuser, tokenRsp.Result[0].UserName)
	}
	handleAuthorization(ctx, token, opCodes, opCodeAndAuthResourceType)
}

/* Ended by AICoder, pid:2682baf3eb3644e191dfda6e59ef7563 */

func checkAuthBySm(token string, operationSet []string) (bool, []string) {
	if token == "" {
		logger.Infof("empty token is authorized")
		return true, operationSet
	}
	operationsRes, err := SmAuthByOperate(token, operationSet)
	if err != nil || len(operationsRes.Operations) == 0 {
		logger.Infof("authorization failed")
		return false, []string{}
	}
	return true, operationsRes.Operations
}

func filterResourcesBySm(token string, operationSet []string, funcResouces FuncFetchResourceIds, ctx *context.Context) bool {
	allResouces := funcResouces()
	authResourceIds, err := SmAuthByOpResources(token, operationSet, allResouces)
	if err != nil {
		logger.Errorf("filterResourcesBySm authorization failed")
		return false
	}
	ctx.Input.SetData(globalcv.ResoucesIdsKey, authResourceIds)
	return true

}

func filterResourcesBySmAndSet(token string, operationSet []string, resourceIds []string, ctx *context.Context) bool {
	authResourceIds, err := SmAuthByOpResources(token, operationSet, resourceIds)
	if err != nil {
		logger.Errorf("filterResourcesBySmAndSet authorization failed")
		return false
	}
	ctx.Input.SetData(globalcv.ResoucesIdsKey, authResourceIds)
	return true
}

func fetchResoucesDcGroupIds() []string {
	var allDcGroupIds []string
	allDcGroups, err := dcgroup_res.Gets(nil)
	if err != nil {
		logger.Errorf("[fetchResoucesDcGroupIds] get all dc groups from res, failed: %v", err.Error())
		return []string{}
	}
	if len(allDcGroups) == 0 {
		return []string{}
	}

	for _, dcGroup := range allDcGroups {
		allDcGroupIds = append(allDcGroupIds, dcGroup.ID)
	}
	return allDcGroupIds
}

func fetchResoucesDcIds() []string {
	var allDcIds []string

	var allDcs []*cloudfuze.DataCenterDao
	if err := storagex.FetchInstances("dcs", nil, &allDcs); err != nil {
		logger.Errorf("[fetchResoucesDcIds] get all dcs from pg, failed: %v", err.Error())
		return []string{}
	}

	if len(allDcs) == 0 {
		return []string{}
	}

	for _, dc := range allDcs {
		allDcIds = append(allDcIds, dc.DcID)
	}
	return allDcIds
}

func fetchResoucesCloudIds() []string {
	var allCloudIds []string
	envs4Vrm, err := cloud_env_redis.GetEnvs4Vrm()
	if util.IsEmpty(envs4Vrm) || err != nil {
		return []string{}
	}
	for _, cloud := range envs4Vrm {
		allCloudIds = append(allCloudIds, cloud.ID)
	}
	return allCloudIds
}

func fetchResoucesPCloudIds() []string {
	var allCloudIds []string
	envs4Pvrm, err := cloud_env_redis.GetEnvs4Pvrm()
	if util.IsEmpty(envs4Pvrm) || err != nil {
		return allCloudIds
	}
	for _, cloud := range envs4Pvrm {
		allCloudIds = append(allCloudIds, cloud.ID)
	}
	return allCloudIds
}

func fetchResoucesVdcIds() []string {
	return []string{}

}

func fetchResourcesOfNClouds() []string {
	var allCloudIds []string
	envs4Net, err := cloud_env_redis.GetEnvs4Net()
	if util.IsEmpty(envs4Net) || err != nil {
		return allCloudIds
	}
	for _, cloud := range envs4Net {
		allCloudIds = append(allCloudIds, cloud.ID)
	}
	return allCloudIds
}

func fetchResourcesOfPHClouds() []string {
	var allCloudIds []string
	envs4Phy, err := cloud_env_redis.GetEnvs4Phy()
	if util.IsEmpty(envs4Phy) || err != nil {
		return allCloudIds
	}
	for _, cloud := range envs4Phy {
		allCloudIds = append(allCloudIds, cloud.ID)
	}
	return allCloudIds
}

func fetchResoucesCloudEnvIds(envTypes []string) []string {
	var cond *dbcore.Condition
	if util.IsEmpty(envTypes) {
		return []string{}
	}
	cond = table_operator.And(cond, dbcore.NewCondition("env_type", dbcore.Operator.IN, envTypes))
	var allClouds []*common_cloud_env.CloudCommon
	if err := storagex.FetchInstances("cloud", cond, &allClouds); err != nil {
		logger.Errorf("[fetchResoucesCloudEnvIds] get all clouds from pg, failed: %v", err.Error())
		return []string{}
	}

	if len(allClouds) == 0 {
		return []string{}
	}

	var allCloudIds []string
	for _, cloud := range allClouds {
		allCloudIds = append(allCloudIds, cloud.ID)
	}
	return allCloudIds
}

type FuncFetchResourceIds func() []string

func findOperationByApi(apiInfo ApiInfo, reqURL string, reqURLQuery url.Values, reqBody []byte,
	reqURLPathParams map[string]string) (bool, string) {
	match, authResourceType := findOperation(ServiceUrl+apiInfo.UrlPath, reqURL)
	logger.Debugf("[findOperationByApi] match:%s, authResourceType:%s", match, authResourceType)
	if !match {
		return false, ""
	}

	if !util.IsEmpty(apiInfo.UrlQueryParam) {
		fields := strings.Split(apiInfo.UrlQueryParam, ":")
		reqURLQueryParam := reqURLQuery.Get(fields[0])
		values := strings.Split(fields[1], ",")
		logger.Debugf("[findOperationByApi] fields:%v, reqURLQueryParam:%s, values:%v", fields, reqURLQueryParam, values)
		if !utils.InSlice(reqURLQueryParam, values) {
			return false, ""
		}
	}

	if !util.IsEmpty(apiInfo.ReqBody) {
		fields := strings.Split(apiInfo.ReqBody, ":")
		value := gjson.Get(string(reqBody), fields[0])
		values := strings.Split(fields[1], ",")
		logger.Debugf("[findOperationByApi] fields:%v, value:%v, values:%v", fields, value, values)
		if !utils.InSlice(value.String(), values) {
			return false, ""
		}
	}

	if !util.IsEmpty(apiInfo.UrlPathParam) {
		fields := strings.Split(apiInfo.UrlPathParam, ":")
		reqID := reqURLPathParams[":"+fields[0]]
		values := strings.Split(fields[1], ",")
		logger.Debugf("[findOperationByApi] fields:%v, reqID:%s, values:%v", fields, reqID, values)
		dao := &common_cloud_env.CloudCommon{}
		if err := storagex.FetchInstanceByID("cloud", reqID, dao, storagedomain.SIFP4Common); err != nil {
			return false, ""
		}
		if !utils.InSlice(dao.EnvType, values) {
			return false, ""
		}
	}

	return true, authResourceType
}

func findOperation(checkUrl string, reqURL string) (bool, string) {
	logger.Debugf("[findOperation] checkUrl:%s, reqURL:%s", checkUrl, reqURL)
	re := regexp.MustCompile(checkUrl)
	authResourceTypeSet := []string{"", "_CLOUD", "_PCLOUD", "_NCLOUD", "_DC", "_DCGROUP", "_PHCLOUD"}
	for _, rType := range authResourceTypeSet {
		matchString := re.MatchString(reqURL + rType)
		if matchString {
			return true, rType
		}
	}
	return false, ""
}

func findResources(operationSet []string, opCodeAndAuthResourceType map[string]string) []string {
	var filterAuthResourceType []string
	for _, op := range operationSet {
		if rType, ok := opCodeAndAuthResourceType[op]; ok {
			filterAuthResourceType = append(filterAuthResourceType, rType)
		}
	}

	if util.IsEmpty(filterAuthResourceType) {
		return []string{}
	}

	var envTypes []string
	for _, rType := range filterAuthResourceType {
		switch rType {
		case "_DCGROUP":
			return fetchResoucesDcGroupIds()
		case "_DC":
			return fetchResoucesDcIds()
		case "_CLOUD":
			envTypes = append(envTypes, common_cloud_env.OPENSTACKKVMENVTYPE, common_cloud_env.OPENSTACKK8SENVTYPE,
				common_cloud_env.OPENSTACKVCENTERENVTYPE, common_cloud_env.OPENSTACKCASENVTYPE,
				common_cloud_env.OPENSTACKIECSENVTYPE)
		case "_PCLOUD":
			envTypes = append(envTypes, common_cloud_env.TCFK8SENVTYPE, common_cloud_env.OPENPALETTEENVTYPE)
		case "_NCLOUD":
			envTypes = append(envTypes, common_cloud_env.NETWORKENVTYPE)
		case "_PHCLOUD":
			envTypes = append(envTypes, common_cloud_env.PHYSICALENVTYPE)
		default:
		}
	}
	return fetchResoucesCloudEnvIds(envTypes)
}

/* Started by AICoder, pid:affd159a00a3494cbdec4cb48ef80cfb */
func matchAndLog(suffix string, checkUrl string, reqURL string, funcp *FuncFetchResourceIds, fetchFunc FuncFetchResourceIds) bool {
	re := regexp.MustCompile(checkUrl)
	if re.MatchString(reqURL + suffix) {
		if *funcp == nil {
			*funcp = fetchFunc
		}
		logger.Infof("findOperationAndResources %s checkUrl:%s, reqURL:%s, funcp:%v", suffix[1:], checkUrl, reqURL, funcp)
		return true
	}
	return false
}

func findOperationAndResources(checkUrl string, reqURL string, funcp *FuncFetchResourceIds) bool {
	suffixes := map[string]FuncFetchResourceIds{
		"_DC":      fetchResoucesDcIds,
		"_CLOUD":   fetchResoucesCloudIds,
		"_PCLOUD":  fetchResoucesPCloudIds,
		"_DCGROUP": fetchResoucesDcGroupIds,
		"_VDC":     fetchResoucesVdcIds,
		"_NCLOUD":  fetchResourcesOfNClouds,
		"_PHCLOUD": fetchResourcesOfPHClouds,
	}
	re := regexp.MustCompile(checkUrl)
	for suffix, fetchFunc := range suffixes {
		if matchAndLog(suffix, checkUrl, reqURL, funcp, fetchFunc) {
			return true
		}
	}
	if re.MatchString(reqURL) {
		logger.Infof("findOperationAndResources nothing checkUrl:%s, reqURL:%s, funcp:%v", checkUrl, reqURL, funcp)
		return true
	}
	return false
}

/* Ended by AICoder, pid:affd159a00a3494cbdec4cb48ef80cfb */

/* Started by AICoder, pid:4d237ee1b0c34e9281a3936ddcde0b0a */
func PostOperateCodeToSM() error {
	regFilePath, err := getRegFilePath()
	if err != nil {
		return err
	}

	fileContent, err := readRegFile(regFilePath)
	if err != nil {
		return err
	}

	err = smRegOperateCode(fileContent)
	if err != nil {
		return err
	}

	logger.Infof("PostOperateCodeToSM Init %s To SM successfully", regFilePath)

	err = unmarshalAuthCheckModel(fileContent)
	if err != nil {
		return err
	}

	err = appendTenantURLCodes()
	if err != nil {
		return err
	}

	logger.Infof("PostOperateCodeToSM FileContent to authcheck:", util.ToJSONStr(AuthCheckModel))
	return nil
}

// Helper functions

func getRegFilePath() (string, error) {
	scene := os.Getenv("scene")
	var regFilePath string
	switch scene {
	case common_scen.CCM_SCENE, common_scen.MCM_SCENE:
		regFilePath = "conf/operate_code_ccm.json"
	case common_scen.CIIA_SCENE:
		regFilePath = "conf/operate_code_ciia.json"
	case common_scen.TOB_SCENE:
		regFilePath = "conf/operate_code_toB.json"
	default:
		regFilePath = "conf/operate_code.json"
	}
	return util.GetFilePath(regFilePath), nil
}

func readRegFile(regFilePath string) ([]byte, error) {
	fileContent, err := util.ReadFile(regFilePath)
	if err != nil {
		logger.Errorf("PostOperateCodeToSM Open %s failed:%v", regFilePath, err)
		return nil, err
	}
	return fileContent, nil
}

func smRegOperateCode(fileContent []byte) error {
	err := SmRegOperateCode(fileContent)
	if err != nil {
		logger.Errorf("PostOperateCodeToSM SmRegOperateCode failed:", err.Error())
		return err
	}
	return nil
}

func unmarshalAuthCheckModel(fileContent []byte) error {
	err := json.Unmarshal(fileContent, &AuthCheckModel)
	if err != nil {
		logger.Errorf("PostOperateCodeToSM json.Unmarshal AuthCheckModel to authcheck failed:", err.Error())
		return err
	}
	return nil
}

func appendTenantURLCodes() error {
	absFilePath := util.GetFilePath("conf/tenant_url_operatecode.json")
	fileContent, err := util.ReadFile(absFilePath)
	if err != nil {
		logger.Errorf("PostOperateCodeToSM Open tanent_url_operatecode.json failed:", err)
		return err
	}

	var tenantURLCodes []*AuthINFOpModelDto
	err = json.Unmarshal(fileContent, &tenantURLCodes)
	if err != nil {
		logger.Errorf("PostOperateCodeToSM json.Unmarshal tenant_url_code to authcheck failed:", err.Error())
		return err
	}

	for _, tenantURLCode := range tenantURLCodes {
		AuthCheckModel.OperationList = append(AuthCheckModel.OperationList, tenantURLCode)
	}
	return nil
}

/* Ended by AICoder, pid:4d237ee1b0c34e9281a3936ddcde0b0a */

func RegHelperForVdclcm() error {
	absFilePath := util.GetFilePath("conf/operate_code_for_vdclcm-about.json")
	fileContent, err := util.ReadFile(absFilePath)
	if err != nil {
		logger.Errorf("[RegHelperForVdclcm] Open operate_code_for_vdclcm-about.json failed: %v", err.Error())
		return err
	}
	err = SmRegOperateCode(fileContent)
	if err != nil {
		logger.Errorf("[RegHelperForVdclcm] SmRegOperateCode failed: %v", err.Error())
		return err
	}
	logger.Info("[RegHelperForVdclcm] Init Vdclcm AboutMenu OperateCode To SM successfully")

	return nil
}
