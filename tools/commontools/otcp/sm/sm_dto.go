package otcpsm

var ServiceUrl = "" // "/api/v1.0/pvrm"

const (
	//鉴权接口
	OesSmURLINFAuth       = "/api/oauth2/v1/perms/user/operations"
	OesSmCheckAccessToken = "/api/oauth2/v1/access_token/validity"
	//注册操作码接口
	OesSmURLOpeModel = "/api/role/v1/operation_model"
	//分域接口
	OesSmURLUserDomainURL = "/api/oauth2/v1/perms/token/op_resources"
	OesSmURLAccessToken   = "/api/oauth2/v1/oauth/token"
	OesSmURLClientid      = "/api/oauth2/v1/clientid"

	OesAppManage = "/api/user-inner/v1/appmanage"
)

type UserDomainParamDto struct {
	AccessToken    string   `json:"access_token"`
	Operations     []string `json:"operations"`
	ResourceGroups []string `json:"resource_groups"`
	ServiceType    string   `json:"service_type"`
}

type GetAccessTokenReq struct {
	GrantType string `json:"grantType"`
	UserName  string `json:"userName"`
	Value     string `json:"value"`
}

type GetAccessTokenRsp struct {
	AccessToken string `json:"accessToken"`
	Expires     int    `json:"expires"`
}

type SmAuthOpResourcesRespDto struct {
	ResourceGroups []string `json:"resource_groups"`
}

// 接口鉴权结构体
type AuthRequestDto struct {
	AccessToken string   `json:"access_token"`
	Operations  []string `json:"operations"`
}

type AuthRespBodyDto struct {
	Operations []string `json:"operations"`
}

// 注册操作码body体
type RegisterOperationDto struct {
	ServiceType    string          `json:"service_type"`
	ServiceVersion string          `json:"service_version"`
	Override       bool            `json:"override"`
	OperationList  []*OperationDto `json:"operation_list"`
}

type OperationDto struct {
	NodeID      string   `json:"nodeid"`
	NodeName    []string `json:"nodename"`
	Leaf        bool     `json:"leaf"`
	Level       int      `json:"level"`
	Description []string `json:"description"`
	ParentID    string   `json:"parent_id"`
}

// 接口对应操作码结构体
type AuthOperationDto struct {
	OperationList []*AuthINFOpModelDto `json:"operation_list"`
}

type AuthINFOpModelDto struct {
	URLMap []string  `json:"urlMap"`
	NodeID string    `json:"nodeid"`
	ApiMap []ApiInfo `json:"apiMap"`
}

type ApiInfo struct {
	UrlPath       string `json:"urlPath,omitempty"`
	UrlPathParam  string `json:"urlPathParam,omitempty"`
	UrlQueryParam string `json:"urlQueryParam,omitempty"`
	ReqBody       string `json:"reqBody,omitempty"`
}

type CheckAccessTokenReq struct {
	AccessToken []string `json:"access_token"`
}

type CheckAccessTokenResp struct {
	Result []CheckAccessTokenResult `json:"result"`
}

type CheckAccessTokenResult struct {
	AccessToken       string `json:"access_token"`
	StatusCode        int    `json:"status_code"`
	StatusDescription string `json:"status_desc"`
	UserName          string `json:"user_name"`
}

type ClientIDReportReq struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	IP           string `json:"ip"`
	Port         int    `json:"port"`
	URI          string `json:"uri"`
	LogoutURI    string `json:"logout_uri"`
}

type CreateOauth2ClientReq struct {
	Name         string `json:"name"`
	ServerURL    string `json:"serverURL"`
	LogoutURL    string `json:"logoutURL"`
	ClientSecret string `json:"clientSecret"`
	Type         string `json:"type"`
}
type CreateOauth2ClientRsp struct {
	ID       string `json:"id"`
	ClientID string `json:"ClientID"`
}
