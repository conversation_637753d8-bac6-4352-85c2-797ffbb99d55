package license

import (
	"encoding/json"
	"errors"
	"fmt"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/httpservice"
)

func GetLicenseValue(id string) (*GetLicenseValueRsp, error) {
	url := fmt.Sprintf(urlFmtGetLicenseValue, id)
	body, _, statusCode, err := httpservice.Get(url, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("[GetLicenseValue] httpservice.Get failed: %v, url: %s, code:%d", err, url, statusCode)
		return nil, fmt.Errorf("get to licensevalue failed: %v", err)
	}
	logger.Infof("[GetLicenseValue] httpservice.Get url: %s, code:%d, body:%s", url, statusCode, string(body))
	licenseValue := &GetLicenseValueRsp{}
	err = json.Unmarshal(body, licenseValue)
	if err != nil {
		return nil, errors.New("get license value json.Unmarshal error," + err.<PERSON>rror())
	}
	return licenseValue, nil
}
