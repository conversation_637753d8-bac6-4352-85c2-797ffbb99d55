package configcenter

import (
	"cwsm/infra/constant"
	"encoding/json"
	"errors"
	"os"

	"zte.com.cn/cms/crmX/commontools/restful"

	"zte.com.cn/cms/crmX/commontools/service/httpservice"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:ad51083e1759f8a1429d09071057f168c680d620 */
var _ = Describe("TestRegisterToConfigCenter", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetConfigcenterConfigserverIpAndPort returns a valid address", func() {
		It("should successfully register and return the correct status code", func() {
			requestBody := []byte(`{"service":"test"}`)
			patcher.ApplyFunc(GetConfigcenterConfigserverIpAndPort, func() (string, bool, error) {
				return "***********:8080", false, nil
			})

			patcher.ApplyFunc(httpservice.Post, func(url string, headers map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 200, nil // Simulate a successful POST request
			})

			code, err := RegisterToConfigCenter(requestBody)
			Expect(err).To(BeNil())
			Expect(code).To(Equal(200))
		})
	})

	Context("When GetConfigcenterConfigserverIpAndPort returns an error", func() {
		It("should return the error and status code 0", func() {
			patcher.ApplyFunc(GetConfigcenterConfigserverIpAndPort, func() (string, bool, error) {
				return "", false, errors.New("failed to get config center ip and port")
			})

			code, err := RegisterToConfigCenter([]byte(`{"service":"test"}`))
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("failed to get config center ip and port"))
			Expect(code).To(Equal(0))
		})
	})

	Context("When HTTP POST fails", func() {
		It("should return the error and status code 400", func() {
			patcher.ApplyFunc(GetConfigcenterConfigserverIpAndPort, func() (string, bool, error) {
				return "***********:8080", false, nil
			})

			patcher.ApplyFunc(httpservice.Post, func(url string, headers map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New("http post error")
			})

			code, err := RegisterToConfigCenter([]byte(`{"service":"test"}`))
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("http post error"))
			Expect(code).To(Equal(400))
		})
	})
})

/* Ended by AICoder, pid:ad51083e1759f8a1429d09071057f168c680d620 */

/* Started by AICoder, pid:h29daebd32j2a471415d08b041dadd14c5609c60 */
var _ = Describe("TestGetConfigcenterConfigserverCfg", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetMsbIpPortFromEnv returns a valid address", func() {
		It("should return the IP and port correctly", func() {
			// Mock the function to return a valid IP and port
			patcher.ApplyFunc(GetMsbIpPortFromEnv, func() string {
				return "***********:8080"
			})

			// Mock the HTTP GET call
			patcher.ApplyFunc(httpservice.Get, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				response := ConfigResponses{
					Nodes: []Nodes{
						{IP: "********", Port: "8081"},
					},
				}
				data, _ := json.Marshal(response)
				return data, nil, 200, nil
			})

			ip, port, _, err := GetConfigcenterConfigserverCfg()
			Expect(err).To(BeNil())
			Expect(ip).To(Equal("********"))
			Expect(port).To(Equal("8081"))
		})
	})

	Context("When MsbIpPort is invalid", func() {
		It("should return an error if HTTP GET fails", func() {
			// Mock the function to return an invalid address
			patcher.ApplyFunc(GetMsbIpPortFromEnv, func() string {
				return "invalid_ip"
			})

			ip, port, _, err := GetConfigcenterConfigserverCfg()
			Expect(err).To(HaveOccurred())
			Expect(ip).To(BeEmpty())
			Expect(port).To(BeEmpty())
		})
	})

	Context("When HTTP GET returns an error", func() {
		It("should return an error", func() {
			patcher.ApplyFunc(GetMsbIpPortFromEnv, func() string {
				return "***********:8080"
			})

			patcher.ApplyFunc(httpservice.Get, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				return nil, nil, 0, errors.New("http get error")
			})

			ip, port, _, err := GetConfigcenterConfigserverCfg()
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("http get error"))
			Expect(ip).To(BeEmpty())
			Expect(port).To(BeEmpty())
		})
	})

	Context("When JSON unmarshal fails", func() {
		It("should return an error", func() {
			patcher.ApplyFunc(GetMsbIpPortFromEnv, func() string {
				return "***********:8080"
			})

			patcher.ApplyFunc(httpservice.Get, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				return []byte("invalid json"), nil, 200, nil
			})

			ip, port, _, err := GetConfigcenterConfigserverCfg()
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("json.Unmarshal error"))
			Expect(ip).To(BeEmpty())
			Expect(port).To(BeEmpty())
		})
	})

	Context("When there are no nodes in the response", func() {
		It("should return an error if no nodes are found", func() {
			patcher.ApplyFunc(GetMsbIpPortFromEnv, func() string {
				return "***********:8080"
			})

			patcher.ApplyFunc(httpservice.Get, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				response := ConfigResponses{
					Nodes: []Nodes{}, // No nodes
				}
				data, _ := json.Marshal(response)
				return data, nil, 200, nil
			})

			ip, port, _, err := GetConfigcenterConfigserverCfg()
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("no nodes config found in the response"))
			Expect(ip).To(BeEmpty())
			Expect(port).To(BeEmpty())
		})
	})
})

/* Ended by AICoder, pid:h29daebd32j2a471415d08b041dadd14c5609c60 */

/* Started by AICoder, pid:kdc026175b253e3141ee082f20c1d47e65e25034 */
var _ = Describe("TestGetConfigcenterConfigserverIpAndPort", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetConfigcenterConfigserverCfg returns successfully", func() {
		It("should return the correct IP and port for IPv4", func() {
			patcher.ApplyFunc(GetConfigcenterConfigserverCfg, func() (string, string, bool, error) {
				return "***********", "10081", false, nil
			})

			result, _, err := GetConfigcenterConfigserverIpAndPort()

			Expect(err).To(BeNil())
			Expect(result).To(Equal("***********:10081"))
		})

		It("should return the correct IP and port for IPv6", func() {
			patcher.ApplyFunc(GetConfigcenterConfigserverCfg, func() (string, string, bool, error) {
				return "2001:0db8:85a3:0000:0000:8a2e:0370:7334", "10081", false, nil
			})

			patcher.ApplyFunc(getIPType, func(ip string) string {
				return "IPv6"
			})

			result, _, err := GetConfigcenterConfigserverIpAndPort()

			Expect(err).To(BeNil())
			Expect(result).To(Equal("[2001:0db8:85a3:0000:0000:8a2e:0370:7334]:10081"))
		})
	})

	Context("When GetConfigcenterConfigserverCfg returns an error", func() {
		It("should log an error and return the error", func() {
			patcher.ApplyFunc(GetConfigcenterConfigserverCfg, func() (string, string, bool, error) {
				return "", "", false, errors.New("config fetch error")
			})

			result, _, err := GetConfigcenterConfigserverIpAndPort()

			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("config fetch error"))
			Expect(result).To(BeEmpty())
		})
	})

	Context("When IP is invalid", func() {
		It("should return an empty string for invalid IP", func() {
			patcher.ApplyFunc(GetConfigcenterConfigserverCfg, func() (string, string, bool, error) {
				return "invalid_ip", "10081", false, nil
			})

			patcher.ApplyFunc(getIPType, func(ip string) string {
				return "invalid ip"
			})

			result, _, err := GetConfigcenterConfigserverIpAndPort()

			Expect(err).To(BeNil())
			Expect(result).To(Equal("invalid_ip:10081")) // Assuming invalid IP doesn't cause error
		})
	})
})

/* Ended by AICoder, pid:kdc026175b253e3141ee082f20c1d47e65e25034 */

/* Started by AICoder, pid:y85ecq98eblcbd3141fd08a8403b5560e109bfee */
var _ = Describe("TestGetMsbIpPortFromEnv", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
		os.Unsetenv(constant.DirectorMsbIp)
		os.Unsetenv(constant.DirectorMsbPort)
		os.Unsetenv(constant.DirectorInnerIp)
	})

	Context("When environment variables are set correctly", func() {
		It("should return the correct IP and port for IPv4", func() {
			os.Setenv(constant.DirectorMsbIp, "***********")
			os.Setenv(constant.DirectorMsbPort, "10081")

			result := GetMsbIpPortFromEnv()

			Expect(result).To(Equal("***********:10081"))
		})

		It("should return the correct IP and port for IPv6", func() {
			os.Setenv(constant.DirectorMsbIp, "2001:0db8:85a3:0000:0000:8a2e:0370:7334")
			os.Setenv(constant.DirectorMsbPort, "10081")

			patcher.ApplyFunc(getIPType, func(ip string) string {
				return "IPv6"
			})

			result := GetMsbIpPortFromEnv()

			Expect(result).To(Equal("[2001:0db8:85a3:0000:0000:8a2e:0370:7334]:10081"))
		})
	})

	Context("When environment variables are not set", func() {
		It("should log an error and return an empty string for IP", func() {
			os.Setenv(constant.DirectorMsbPort, "8080") // Only port is set

			result := GetMsbIpPortFromEnv()

			Expect(result).To(BeEmpty())
		})

		It("should log an error and return an empty string for port", func() {
			os.Setenv(constant.DirectorMsbIp, "***********") // Only IP is set

			result := GetMsbIpPortFromEnv()

			Expect(result).To(Equal("***********:10081")) // Assuming default port is 10081 if not set
		})
	})

	Context("When IP is invalid", func() {
		It("should return an empty string for invalid IP", func() {
			os.Setenv(constant.DirectorMsbIp, "invalid_ip")
			os.Setenv(constant.DirectorMsbPort, "10081")

			result := GetMsbIpPortFromEnv()

			Expect(result).To(ContainSubstring("invalid_ip:10081")) // Assuming getIPType returns "invalid ip"
		})
	})
})

/* Ended by AICoder, pid:y85ecq98eblcbd3141fd08a8403b5560e109bfee */

/* Started by AICoder, pid:a81f9w3ef6v657b14e35081360d4663307e262c5 */
var _ = Describe("TestGetIPType", func() {
	Context("When given valid IPv4 addresses", func() {
		It("should return 'IPv4' for valid addresses", func() {
			Expect(getIPType("***********")).To(Equal("IPv4"))
			Expect(getIPType("0.0.0.0")).To(Equal("IPv4"))
			Expect(getIPType("***************")).To(Equal("IPv4"))
		})
	})

	Context("When given valid IPv6 addresses", func() {
		It("should return 'IPv6' for valid addresses", func() {
			Expect(getIPType("::1")).To(Equal("IPv6"))
			Expect(getIPType("2001:0db8:85a3:0000:0000:8a2e:0370:7334")).To(Equal("IPv6"))
			Expect(getIPType("fe80::1ff:fe23:4567:890a")).To(Equal("IPv6"))
		})
	})

	Context("When given invalid IP addresses", func() {
		It("should return 'invalid ip' for invalid addresses", func() {
			Expect(getIPType("invalid_ip")).To(Equal("invalid ip"))
			Expect(getIPType("192.168.1.300")).To(Equal("invalid ip"))   // Out of range
			Expect(getIPType("256.256.256.256")).To(Equal("invalid ip")) // Out of range
		})
	})

	Context("When given other unknown formats", func() {
		It("should return 'unknown ip type' for non-IP strings", func() {
			Expect(getIPType("")).To(Equal("invalid ip"))
			Expect(getIPType("random_string")).To(Equal("invalid ip"))
		})
	})
})

/* Ended by AICoder, pid:a81f9w3ef6v657b14e35081360d4663307e262c5 */
