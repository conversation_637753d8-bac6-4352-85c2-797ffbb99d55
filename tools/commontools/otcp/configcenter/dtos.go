package configcenter

type GroupDto struct {
	ID          int         `json:"id"`
	GroupID     string      `json:"groupId"`
	Visible     bool        `json:"visible"`
	DisplayName string      `json:"displayName"`
	Children    []*GroupDto `json:"children"`
}

type ItemDto struct {
	ID           int    `json:"id"`
	Key          string `json:"key"`
	Category     string `json:"category"`
	DisplayName  string `json:"displayName"`
	Description  string `json:"description"`
	Visible      bool   `json:"visible"`
	Required     bool   `json:"required"`
	CurrentValue string `json:"currentValue"`
	DisplayValue string `json:"displayValue"`
}

type ConfigResponse struct {
	Items  []*ItemDto  `json:"items"`
	Groups []*GroupDto `json:"groups"`
}

type ConfigResponses struct {
	Nodes     []Nodes `json:"nodes"`
	EnableSSL bool    `json:"enable_ssl"`
}

type Nodes struct {
	IP   string `json:"ip"`
	Port string `json:"port"`
}

type CMPProviderRsp struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type PsConfigRsp struct {
	Name            string            `json:"name"`
	PropertySources []PropertySources `json:"propertySources"`
}

type PropertySources struct {
	Name   string `json:"name"`
	Source Source `json:"source"`
}

type Source struct {
	AppKey    string `json:"app_key"`
	AppSecret string `json:"app_secret"`
	Password  string `json:"password"`
	SvcID     string `json:"svc-identification"`
	MgtID     string `json:"mgt-identification"`
}
