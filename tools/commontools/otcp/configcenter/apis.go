package configcenter

import (
	"cwsm/infra/constant"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"fmt"
	"net"
	"os"

	"zte.com.cn/cms/crmX/commontools/service/httpservice"
)

var (
	MsbIpPort                      = ""
	ConfigcenterConfigserverIpPort = ""
)

const (
	centerAddrPrefix     = "/api/configcenter-configserver/v1"
	registerFmt          = centerAddrPrefix + "/uploadAll/%s/prod/master"
	groupsAddr           = centerAddrPrefix + "/groups"
	itemsAddrFmt         = centerAddrPrefix + "/items/%d"
	getCmpIdUrl          = "/api/configcenter-configserver/v1/getProperty/rm-nbiId/em.nbiId"
	propertySourcesUrl   = "/api/configcenter-configserver/v1/propertySources/%s/prod/master"
	EMGroupID            = "rm-service"
	EMItemKey            = "em.displayname"
	getConfigcenterIdUrl = "/api/configcenter-configserver/v1/getProperty/%s/%s"
)

/* Started by AICoder, pid:b86cfefe2146b9914220095220a4981fedc8664c */
func RegisterToConfigCenter(requestBody []byte) (int, error) {

	ConfigcenterConfigserverIpPort, enableSSL, err := GetConfigcenterConfigserverIpAndPort()
	if err != nil {
		logger.Errorf("Register cwsm to configCenter error, error: %v", err)
		return 0, err
	}

	var url string
	if enableSSL {
		url = constant.HTTPSPrefix + ConfigcenterConfigserverIpPort + "/api/configcenter-configserver/v1/uploadAll/cwsm-cwsm/prod/master"
	} else {
		url = constant.HTTPPrefix + ConfigcenterConfigserverIpPort + "/api/configcenter-configserver/v1/uploadAll/cwsm-cwsm/prod/master"
	}

	_, _, code, err := httpservice.Post(url, httpservice.DefaultHeaders(), requestBody, nil)
	if err != nil {
		logger.Errorf("Register cwsm to configCenter error, error: %v", err)
		return 400, err
	}
	logger.Info("Register cwsm to configCenter url:%s", url)

	return code, nil
}

/* Ended by AICoder, pid:b86cfefe2146b9914220095220a4981fedc8664c */

/* Started by AICoder, pid:v69cfta216qe518148dd0b4570a0562359f432f8 */
func GetConfigcenterConfigserverCfg() (string, string, bool, error) {
	MsbIpPort := GetMsbIpPortFromEnv()
	url := constant.HTTPPrefix + MsbIpPort + "/api/microservices/v1/services/configcenter-configserver/version/v1?namespace=director"
	logger.Info("MsbIpPort url:%s", url)
	Configcenter, _, _, err := httpservice.Get(url, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Error("configcenter.ListGroupItems http GET failed, url: %s, error: %v", url, err)
		return "", "", false, err
	}

	var res ConfigResponses
	if err = json.Unmarshal(Configcenter, &res); err != nil {
		logger.Error("configcenter.ListGroups json Unmarshal error: %v, response body: %s", err, string(Configcenter))
		return "", "", false, fmt.Errorf("json.Unmarshal error: %v", err)
	}

	if len(res.Nodes) > 0 {
		ip := res.Nodes[0].IP
		port := res.Nodes[0].Port
		return ip, port, res.EnableSSL, nil
	}

	return "", "", false, fmt.Errorf("no nodes config found in the response")
}

/* Ended by AICoder, pid:v69cfta216qe518148dd0b4570a0562359f432f8 */

/* Started by AICoder, pid:q194104348w04da140ec0ad2f00e571406b5562d */
func GetConfigcenterConfigserverIpAndPort() (string, bool, error) {
	ip, port, enableSSL, err := GetConfigcenterConfigserverCfg()
	if err != nil {
		logger.Error("get configcenter-configserver ip and port failed: %v", err)
		return "", enableSSL, err
	}
	ipType := getIPType(ip)
	if ipType == "IPv6" {
		ipv6 := "[" + ip + "]"
		ConfigcenterConfigserverIpPort = ipv6 + ":" + port
		return ConfigcenterConfigserverIpPort, enableSSL, nil
	}
	ConfigcenterConfigserverIpPort = ip + ":" + port
	return ConfigcenterConfigserverIpPort, enableSSL, nil
}

/* Ended by AICoder, pid:q194104348w04da140ec0ad2f00e571406b5562d */

/* Started by AICoder, pid:9f115odd6cl96f3140d40a2ae00efb187666c3c9 */
func GetMsbIpPortFromEnv() string {
	ip := os.Getenv(constant.DirectorMsbIp)
	if ip == "" {
		logger.Error("get director inner ip %s failed", ip)
		return ""
	}
	port := constant.DirectorMsbPort
	ipType := getIPType(ip)
	if ipType == "IPv6" {
		ipv6 := "[" + ip + "]"
		MsbIpPort = ipv6 + ":" + port
		return MsbIpPort
	}
	MsbIpPort = ip + ":" + port
	return MsbIpPort
}

/* Ended by AICoder, pid:9f115odd6cl96f3140d40a2ae00efb187666c3c9 */

/* Started by AICoder, pid:aba4386946r4fbf14bab0a71702aad1b208311f4 */
func getIPType(ip string) string {
	netIP := net.ParseIP(ip)
	if netIP == nil {
		return "invalid ip"
	}
	if netIP.To4() != nil {
		return "IPv4"
	}
	if netIP.To16() != nil {
		return "IPv6"
	}
	return "unknown ip type"
}

/* Ended by AICoder, pid:aba4386946r4fbf14bab0a71702aad1b208311f4 */
