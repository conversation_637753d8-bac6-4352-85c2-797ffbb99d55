package resx

import (
	"encoding/json"
	"fmt"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/otcp/res"
	"cwsm/tools/commontools/service/httpservice"
)

func UpdateInstance(model string, id string, instance interface{}) ([]byte, error) {
	url := fmt.Sprintf("%s/%ss/%s", otcpres.ResPrefix, model, id)
	body, _, _, err := httpservice.Put(url, httpservice.DefaultHeaders(), instance, nil)
	if err != nil {
		logger.Errorf("UpdateInstance %s for model %s failed: %v", id, model, err)
		return nil, fmt.Errorf("update res instance failed: %v", err)
	}
	return body, nil
}

func UpdateInstances(model string, instances interface{}) error {
	url := fmt.Sprintf("%s/%ss/batch?rm:bestEffort=true", otcpres.ResPrefix, model)
	_, _, _, err := httpservice.Put(url, httpservice.DefaultHeaders(), instances, nil)
	if err != nil {
		logger.Errorf("UpdateInstances for model %s failed: %v", model, err)
		return fmt.Errorf("update res instances failed: %v", err)
	}
	return nil
}

func DeleteInstance(model string, id string) error {
	if len(id) == 0 {
		return nil
	}
	url := fmt.Sprintf("%s/%ss/%s", otcpres.ResPrefix, model, id)
	_, _, _, err := httpservice.Delete(url, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("delete res model %s instance httpservice.Delete failed: %v", model, err)
		return fmt.Errorf("delete res instance failed: %v", err)
	}
	return nil
}

func DeleteInstances(model string, ids []string) error {
	if len(ids) == 0 {
		return nil
	}
	url := fmt.Sprintf("%s/%ss/batch?rm:bestEffort=true", otcpres.ResPrefix, model)
	_, _, _, err := httpservice.DeleteWithBody(url, httpservice.DefaultHeaders(), ids, nil)
	if err != nil {
		logger.Errorf("delete res model %s instances httpservice.DeleteWithBody failed: %v", model, err)
		return fmt.Errorf("delete res instances failed: %v", err)
	}
	return nil
}

func CreateInstance(model string, instance interface{}) error {
	if !util.IsValidPointer(instance) {
		logger.Errorf("CreateInstance error: input instance is nil or non-pointer")
		return fmt.Errorf("create res instance %s failed: input instance is nil or non-pointer", model)
	}

	url := fmt.Sprintf("%s/%ss", otcpres.ResPrefix, model)
	_, _, _, err := httpservice.Post(url, httpservice.DefaultHeaders(), instance, nil)
	if err != nil {
		logger.Errorf("create res model %s instance httpservice.Post failed: %v", model, err)
		return fmt.Errorf("create res instance failed: %v", err)
	}
	return nil
}

func CreateInstances(model string, instances interface{}) error {
	url := fmt.Sprintf("%s/%ss/batch?rm:bestEffort=true", otcpres.ResPrefix, model)
	_, _, _, err := httpservice.Post(url, httpservice.DefaultHeaders(), instances, nil)
	if err != nil {
		logger.Errorf("create res model %s instances httpservice.Post failed: %v", model, err)
		return fmt.Errorf("create res instances failed: %v", err)
	}
	return nil
}

func GetInstance(model, id string, instance interface{}) error {
	if !util.IsValidPointer(instance) {
		logger.Errorf("GetResInstance error: input instance is nil or non-pointer")
		return fmt.Errorf("get res instance %s failed: input instance is nil or non-pointer", model)
	}
	url := fmt.Sprintf("%s/%ss/%s", otcpres.ResPrefix, model, id)
	body, _, _, err := httpservice.Get(url, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("GetResInstance %s http request failed: %v", id, err)
		return fmt.Errorf("get res instance %s http request failed: %v", id, err)
	}

	err = json.Unmarshal(body, instance)
	if err != nil {
		logger.Errorf("GetResInstance %s json unmarshal failed: %v", id, err)
		return fmt.Errorf("get res instance %s json unmarshal failed: %v", id, err)
	}
	return nil
}

func GetInstanceWithDn(model, id string, instance interface{}) error {
	if !util.IsValidPointer(instance) {
		logger.Errorf("GetResInstance error: input instance is nil or non-pointer")
		return fmt.Errorf("get res instance %s failed: input instance is nil or non-pointer", model)
	}
	url := fmt.Sprintf("%s/%ss/%s?queryDn=true", otcpres.ResPrefix, model, id)
	body, _, _, err := httpservice.Get(url, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("GetResInstanceWithDn %s http request failed: %v", id, err)
		return fmt.Errorf("get res instance %s http request failed: %v", id, err)
	}

	err = json.Unmarshal(body, instance)
	if err != nil {
		logger.Errorf("GetResInstanceWithDn %s json unmarshal failed: %v", id, err)
		return fmt.Errorf("get res instance %s json unmarshal failed: %v", id, err)
	}
	return nil
}

func GetInstances(model string, condition map[string][]string, instances interface{}) error {
	if !util.IsValidPointer(instances) {
		logger.Errorf("GetResInstances error: input instances is nil or non-pointer")
		return fmt.Errorf("get res instance %s failed: input instance is nil or non-pointer", model)
	}
	condStr := ""
	for k, conds := range condition {
		if len(conds) == 0 {
			return nil
		}
		condStr += "&" + k + "="
		for _, cond := range conds {
			condStr += cond + ","
		}
		condStr = condStr[0 : len(condStr)-1]
	}
	if len(condStr) > 0 {
		condStr = condStr[1:]
	}
	url := fmt.Sprintf("%s/%ss?%s", otcpres.ResPrefix, model, condStr)
	body, _, _, err := httpservice.Get(url, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("GetResInstances for model %s with condition %s failed: %v", model, condStr, err)
		return fmt.Errorf("get res instances for model %s failed: %v", model, err)
	}
	err = json.Unmarshal(body, instances)
	if err != nil {
		logger.Errorf("GetResInstances for model %s json unmarshal failed: %v", model, err)
		return fmt.Errorf("get res instances for model %s json error: %v", model, err)
	}
	return nil
}
