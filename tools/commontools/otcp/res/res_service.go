package otcpres

import (
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/httpservice"
	"encoding/json"
	"errors"
	"fmt"
)

func GetResCloudEnvInstance(modelName string, instanceId string) (*ResCloudEnvGetRSP, error) {
	body, _, _, err := httpservice.Get(ResPrefix+"/"+modelName+"s/"+instanceId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("get res instance failed," + err.Error())
	}
	rsp := &ResCloudEnvGetRSP{}
	err = json.Unmarshal(body, rsp)
	if err != nil {
		return nil, errors.New("get res instance json error," + err.Error())
	}
	return rsp, nil
}

func GetResCloudEnvInstances(modelName string, condition map[string][]string) ([]ResCloudEnvGetRSP, error) {
	body, err := GetResInstancesByCondition(modelName, condition)
	if err != nil {
		return nil, err
	}
	rsp := []ResCloudEnvGetRSP{}
	err = json.Unmarshal(body, &rsp)
	if err != nil {
		return nil, errors.New("get res instances json error," + err.Error())
	}
	return rsp, nil
}

func GetResDcInstances(modelName string, condition map[string][]string) ([]ResDcGetRSP, error) {
	body, err := GetResInstancesByCondition(modelName, condition)
	if err != nil {
		return nil, err
	}
	rsp := []ResDcGetRSP{}
	err = json.Unmarshal(body, &rsp)
	if err != nil {
		return nil, errors.New("get res instances json error," + err.Error())
	}
	return rsp, nil
}

func GetResVdcInstances(modelName string, condition map[string][]string) ([]ResVdcGetRSP, error) {
	body, err := GetResInstancesByCondition(modelName, condition)
	if err != nil {
		return nil, err
	}
	rsp := []ResVdcGetRSP{}
	err = json.Unmarshal(body, &rsp)
	if err != nil {
		return nil, errors.New("get res instances json error," + err.Error())
	}
	return rsp, nil
}

func GetResTenantInstance(modelName string, instanceId string) (*ResTenantGetRSP, error) {
	body, _, _, err := httpservice.Get(ResPrefix+"/"+modelName+"s/"+instanceId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("get res tenant instance failed," + err.Error())
	}
	rsp := &ResTenantGetRSP{}
	err = json.Unmarshal(body, rsp)
	if err != nil {
		return nil, errors.New("get res tenant instance json error," + err.Error())
	}
	return rsp, nil
}

func GetResBackupJobInstance(modelName string, instanceId string) (*ResFreezerBackupJob, error) {
	body, _, _, err := httpservice.Get(ResPrefix+"/"+modelName+"s/"+instanceId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("Get res job instance failed," + err.Error())
	}
	rsp := &ResFreezerBackupJob{}
	err = json.Unmarshal(body, rsp)
	if err != nil {
		return nil, errors.New("Unmarshal res instance body error," + err.Error())
	}
	return rsp, nil
}

func GetResInstancesByCondition(modelName string, condition map[string][]string) ([]byte, error) {
	condString := ""
	for k, conds := range condition {
		if len(conds) == 0 {
			return []byte{}, nil
		}
		condString += "&" + k + "="
		for _, cond := range conds {
			condString += cond + ","
		}
		condString = condString[0 : len(condString)-1]
	}
	if len(condString) > 0 {
		condString = condString[1:]
	}

	body, _, _, err := httpservice.Get(ResPrefix+"/"+modelName+"s/"+condString, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("get res instances failed," + condString + err.Error())
	}
	return body, nil
}

/* Started by AICoder, pid:9571b4a06ca947208e26b8dd3bf5356e */
func ComboQueryInstances(modelName string, andFilter, orFilter map[string][]string, urlPara map[string]string, returnFields []string, neednot4Add bool, neednot4Or bool, instances interface{}) error {
	url := buildURL(modelName, urlPara, returnFields)
	reqBody, err := createRequestBody(andFilter, orFilter, neednot4Add, neednot4Or)
	if err != nil {
		return err
	}

	resBody, _, _, err := httpservice.Post(url, httpservice.DefaultHeaders(), reqBody, nil)
	if err != nil {
		return fmt.Errorf("ComboQueryInstances model:%s from res failed:%v", modelName, err.Error())
	}

	err = unmarshalResponse(modelName, resBody, instances)
	if err != nil {
		return err
	}

	return nil
}

func buildURL(modelName string, urlPara map[string]string, returnFields []string) string {
	condString := buildConditionString(urlPara, returnFields)
	url := ResPrefix + "/conbinationQuery/" + modelName + "s"
	if len(condString) > 0 {
		url += "?" + condString[1:]
	}
	return url
}

func buildConditionString(urlPara map[string]string, returnFields []string) string {
	var condString = ""
	for k, v := range urlPara {
		condString += "&" + k + "=" + v
	}
	var includeAttrString = ""
	for _, field := range returnFields {
		includeAttrString += field + ","
	}
	if len(includeAttrString) > 0 {
		condString += "&includeAttr=" + includeAttrString
	}
	return condString
}

func createRequestBody(andFilter, orFilter map[string][]string, neednot4Add bool, neednot4Or bool) ([]byte, error) {
	andAttrs := buildAttributes(andFilter)
	orAttrs := buildAttributes(orFilter)
	andCondition := Condition{"and", andAttrs, neednot4Add}
	orCondition := Condition{"or", orAttrs, neednot4Or}
	comboQuery := ComboQueryREQ{"and", []Condition{andCondition, orCondition}}

	reqBody, err := json.Marshal(comboQuery)
	if err != nil {
		logger.Errorf("ComboQueryInstances Marshal conditions error: %v", err)
		return nil, err
	}
	return reqBody, nil
}

func buildAttributes(filter map[string][]string) []Attribute {
	attrs := []Attribute{}
	for key, con := range filter {
		if len(con) == 0 {
			continue
		}
		af := make(map[string][]string)
		af[key] = con
		attr := Attribute{af}
		attrs = append(attrs, attr)
	}
	return attrs
}

func unmarshalResponse(modelName string, resBody []byte, instances interface{}) error {
	logger.Debug("ComboQueryInstances Response body:%s", string(resBody))

	err := json.Unmarshal(resBody, instances)
	if err != nil {
		logger.Errorf("ComboQueryInstances model %s json unmarshal failed: %v", modelName, err)
		return fmt.Errorf("ComboQueryInstances model %s json unmarshal error: %v", modelName, err)
	}
	return nil
}

/* Ended by AICoder, pid:9571b4a06ca947208e26b8dd3bf5356e */
