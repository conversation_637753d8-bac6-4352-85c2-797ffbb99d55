package otcpres

const (
	ResPrefix = "/api/res/v1"
	ResEms    = ResModel + "/em"
	ResModel  = ResPrefix + "/models"
)

type I18n struct {
	EN_US string `json:"en_US"`
	ZH_CN string `json:"zh_CN"`
}

type DisplayName struct {
	I18n       I18n `json:"i18n"`
	FuzzyMatch int  `json:"fuzzyMatch"`
}

type Enum struct {
	Value       string      `json:"value"`
	DisplayName DisplayName `json:"displayName"`
}

type Moc struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	DisplayName I18n   `json:"displayName"`
	UsedBy      string `json:"usedBy"`
	Model       string `json:"model,omitempty"`
}

type Field struct {
	Name        string `json:"name"`
	DisplayName I18n   `json:"displayName"`
	ValueType   string `json:"type"`
	Defaults    string `json:"defaults"`
	Length      int    `json:"length"`
	Scale       int    `json:"scale"`
	Unique      bool   `json:"unique"`
	Nullable    bool   `json:"nullable"`
	Isstate     bool   `json:"isstate"`
	Enums       []Enum `json:"enums"`
	Moc         []Moc  `json:"moc"`
	Action      string `json:"action"`
}

func FieldFactory(enName, cnName string) Field {
	return Field{
		Name:        enName,
		DisplayName: I18n{EN_US: enName, ZH_CN: cnName},
		ValueType:   "string",
		Length:      10000,
		Unique:      false,
		Nullable:    true,
		Isstate:     false,
		Enums:       []Enum{},
		Moc:         []Moc{},
		Action:      "",
	}
}

func (f *Field) SimpleEqual(field *Field) bool {
	if field.ValueType != f.ValueType || field.Defaults != f.Defaults || field.Length != f.Length || field.DisplayName != f.DisplayName {
		return false
	}
	return true
}

type ResModelDto struct {
	Name        string  `json:"name"`
	DisplayName I18n    `json:"displayName"`
	Fields      []Field `json:"fields"`
	Moc         []Moc   `json:"moc"`
	BaseModel   string  `json:"baseModel"`
}

type ResModelCreateRSP struct {
	Errorcode int    `json:"errorcode"`
	Errormsg  string `json:"errormsg"`
}

type ResInstanceGetRSP struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	NbiId string `json:"nbiId"`
}

type ResInstanceCreateRSP struct {
	ErrorCode int    `json:"errorCode"`
	Level     int    `json:"level"`
	Labels    string `json:"labels"`
}

type ResCloudEnvGetRSP struct {
	CloudEnvId   string `json:"id"`
	CloudEnvName string `json:"name"`
	CloudEnvType string `json:"cloudType"`
	DcId         string `json:"dcId"`
	NbiId        string `json:"nbiId"`
}

type ResCloudEnvsGetRSP struct {
	CloundEnvs []ResCloudEnvGetRSP `json:"director.cloudenvs"`
}

type ResTenantGetRSP struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	NbiId string `json:"nbiId"`
}

type ResTenantsGetRSP struct {
	Tenants []ResTenantGetRSP `json:"director.tenants"`
}

type ResDcGetRSP struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	NbiId string `json:"nbiId"`
}

type ResDcsGetRSP struct {
	Dcs []ResDcGetRSP `json:"director.dcs"`
}

type ResVdcGetRSP struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	OrgId string `json:"orgId"`
}

type ResVdcsGetRSP struct {
	Vdcs []ResVdcGetRSP `json:"director.vdcs"`
}

type ComboQueryREQ struct {
	Andor      string      `json:"andor"`
	Conditions []Condition `json:"conditions"`
}

type Condition struct {
	Andor      string      `json:"andor"`
	Conditions []Attribute `json:"conditions"`
	NeedNot    bool        `json:"neednot"`
}

type Attribute struct {
	Attr map[string][]string `json:"attr"`
}

type ResFreezerBackupJob struct {
	JobName        string `json:"name"`
	BackupName     string `json:"backup_name"`
	NovaInstId     string `json:"nova_inst_id"`
	CloudEnvId     string `json:"cloudEnvId"`
	Mode           string `json:"mode"`
	Action         string `json:"action"`
	TimeCreated    int64  `json:"time_created"`
	JobId          string `json:"job_id,omitempty"`
	BackupId       string `json:"backup_id,omitempty"`
	ProjectId      string `json:"project_id"`
	UserId         string `json:"user_id"`
	UserName       string `json:"user_name,,omitempty"`
	JobSchedule    string `json:"job_schedule,omitempty"`
	ClientId       string `json:"client_id,omitempty"`
	SessionId      string `json:"session_id,omitempty"`
	SessionTag     int    `json:"session_tag,omitempty"`
	Description    string `json:"description,omitempty"`
	JobActions     string `json:"job_actions,omitempty"`
	BackupMetadata string `json:"backup_metadata,omitempty"`
	TotalNum       int64  `json:"TotalNum"`
}

// res modelName
const (
	ResModelNameCloudEnv     = "director.cloudenv"
	ResModelNameDc           = "director.dc"
	ResModelNameTenant       = "director.tenant"
	ResModeNameFreezerJob    = "director.freezerjob"
	ResModeNameFreezerbackup = "director.freezerbackupdata"
)
