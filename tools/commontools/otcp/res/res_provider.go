package otcpres

import (
	"encoding/json"
	"errors"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/httpservice"
)

func ResHealthCheck() error {
	_, _, _, err := httpservice.Get(ResEms, httpservice.DefaultHeaders(), nil)
	if err != nil {
		logger.Errorf("ResHealthCheck failed:", err.<PERSON>rror())
		return err
	}
	return nil
}

func CreateResModel(model *ResModelDto) (*ResModelCreateRSP, error) {
	body, _, _, err := httpservice.Post(ResModel, httpservice.DefaultHeaders(), model, nil)
	if err != nil {
		return nil, errors.New("create res model failed," + err.Error())
	}
	rsp := &ResModelCreateRSP{}
	err = json.Unmarshal(body, rsp)
	if err != nil {
		return nil, errors.New("create res model json error," + err.Error())
	}
	return rsp, nil
}

func GetResModel(modelName string) (*ResModelDto, error) {
	body, _, _, err := httpservice.Get(ResModel+"/"+modelName, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("get res model failed," + err.Error())
	}
	rsp := &ResModelDto{}
	err = json.Unmarshal(body, rsp)
	if err != nil {
		return nil, errors.New("get res model json error," + err.Error())
	}
	return rsp, nil
}

func DeleteResModel(modelName string) ([]byte, error) {
	body, _, _, err := httpservice.Delete(ResModel+"/"+modelName, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("delete res model failed," + err.Error())
	}
	return body, nil
}

func PutResModel(modelName string, body interface{}) ([]byte, error) {
	response, _, _, err := httpservice.Put(ResModel+"/"+modelName, httpservice.DefaultHeaders(), body, nil)
	if err != nil {
		logger.Errorf("PutResModel error: %v", err)
		return nil, errors.New("put res model failed: " + err.Error())
	}
	return response, nil
}

func PutMoc(mocName string, body interface{}) ([]byte, error) {
	response, _, _, err := httpservice.Put(ResPrefix+"/mocs/"+mocName, httpservice.DefaultHeaders(), body, nil)
	if err != nil {
		logger.Errorf("PutMoc error: %v", err)
		return nil, errors.New("put moc failed: " + err.Error())
	}
	return response, nil
}

func PostMoc(body interface{}) ([]byte, error) {
	response, _, _, err := httpservice.Post(ResPrefix+"/mocs", httpservice.DefaultHeaders(), body, nil)
	if err != nil {
		logger.Errorf("PostMoc error: %v", err)
		return nil, errors.New("post moc failed: " + err.Error())
	}
	return response, nil
}

func GetMoc(mocName string) (*Moc, error) {
	body, _, _, err := httpservice.Get(ResPrefix+"/mocs/"+mocName, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("get moc failed," + err.Error())
	}
	rsp := &Moc{}
	err = json.Unmarshal(body, rsp)
	if err != nil {
		return nil, errors.New("get moc json error," + err.Error())
	}
	return rsp, nil
}

func CreateResInstance(modelName string, bodyBytes []byte) (*ResInstanceCreateRSP, error) {
	body, _, _, err := httpservice.Post(ResPrefix+"/"+modelName, httpservice.DefaultHeaders(), bodyBytes, nil)
	if err != nil {
		return nil, errors.New("create res instance failed," + err.Error())
	}
	rsp := &ResInstanceCreateRSP{}
	err = json.Unmarshal(body, rsp)
	if err != nil {
		return nil, errors.New("create res instance json error," + err.Error())
	}
	return rsp, nil
}

func CreateResInstances(modelName string, bodyBytes []byte) ([]byte, error) {
	body, _, _, err := httpservice.Post(ResPrefix+"/"+modelName+"s/batch", httpservice.DefaultHeaders(), bodyBytes, nil)
	if err != nil {
		return nil, errors.New("create res instances failed," + err.Error())
	}
	return body, nil
}

func UpdateResInstance(modelName string, instanceId string, bodyBytes []byte) ([]byte, error) {
	body, _, _, err := httpservice.Put(ResPrefix+"/"+modelName+"/"+instanceId, httpservice.DefaultHeaders(), bodyBytes, nil)
	if err != nil {
		return nil, errors.New("update res instance failed," + err.Error())
	}
	return body, nil
}

func UpdateResInstances(modelName string, bodyBytes []byte) ([]byte, error) {
	body, _, _, err := httpservice.Put(ResPrefix+"/"+modelName+"s/batch", httpservice.DefaultHeaders(), bodyBytes, nil)
	if err != nil {
		return nil, errors.New("update res instance failed," + err.Error())
	}
	return body, nil
}

func GetResInstance(modelName string, instanceId string) (*ResInstanceGetRSP, error) {
	body, _, _, err := httpservice.Get(ResPrefix+"/"+modelName+"s/"+instanceId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("get res instance failed," + err.Error())
	}
	rsp := &ResInstanceGetRSP{}
	err = json.Unmarshal(body, rsp)
	if err != nil {
		return nil, errors.New("get res instance json error," + err.Error())
	}
	return rsp, nil
}

func GetResInstances(modelName string, condition map[string][]string) ([]ResInstanceGetRSP, error) {
	body, err := GetResInstancesByCondition(modelName, condition)
	if err != nil {
		return nil, err
	}
	var rsp []ResInstanceGetRSP
	err = json.Unmarshal(body, &rsp)
	if err != nil {
		return nil, errors.New("get res instance json error," + err.Error())
	}
	return rsp, nil
}

func DeleteResInstance(modelName string, instanceId string) ([]byte, error) {
	body, _, _, err := httpservice.Delete(ResPrefix+"/"+modelName+"s/"+instanceId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("delete res instance failed," + err.Error())
	}
	return body, nil
}

func DeleteResInstances(modelName string, ids []string) ([]byte, error) {
	body, _, _, err := httpservice.DeleteWithBody(ResPrefix+"/"+modelName+"s/batch", httpservice.DefaultHeaders(), ids, nil)
	if err != nil {
		return nil, errors.New("delete res instances failed," + err.Error())
	}
	return body, nil
}
