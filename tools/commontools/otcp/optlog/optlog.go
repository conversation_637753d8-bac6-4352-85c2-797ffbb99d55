package otcplog

import (
	"context"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz/storage/vims_info_redis"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strings"
	"syscall"
	"time"

	otcpcfg "gitlab.zte.com.cn/oes/dexadf/config"
	"gitlab.zte.com.cn/oes/dexadf/es"
	"gitlab.zte.com.cn/oes/dexadf/internal_control"
	"gitlab.zte.com.cn/oes/dexadf/kafka/kclient"
	"gitlab.zte.com.cn/oes/dexadf/kms"
	"gitlab.zte.com.cn/oes/dexadf/log"
	"gitlab.zte.com.cn/oes/dexadf/logagent"
	"gitlab.zte.com.cn/oes/dexadf/logstash"
	"gitlab.zte.com.cn/oes/dexadf/msb"
	httpclient "gitlab.zte.com.cn/oes/dexadf/rpc/http/client"
	"go.uber.org/fx"
	"gopkg.in/olivere/elastic.v6"
)

/* Started by AICoder, pid:333c14d45d504b539a6eface8fcc45f9 */
func RecordOperLog(request *http.Request, optObj *OptObjContent, oper, decription, sourceType, logSource, operErr string, rank int, operTime time.Time) error {
	// 检查token是否为空
	if request.Header.Get(globalcv.HeaderAccessToken) == "" &&
		request.Header.Get(globalcv.ProviderHeaderAccessToken) == "" {
		logger.Infof("no need to RecordOperLog, token is null")
		return nil
	}

	userName := getOperUserName(request)
	clientIp := getOperClientIp(request)
	recordTime := time.Now()
	operJson := getOperJson(oper, sourceType)
	decriptionInfoJson := getDecriptionInfoJson(decription)
	detailJson := getDetailJson(optObj)
	operRes := getOperRes(optObj)
	optObjType := GetOptObjType(sourceType)

	logMsg, ok := getLogMsg(userName, operJson, decriptionInfoJson, clientIp, detailJson, rank, operTime, recordTime)
	if !ok {
		return fmt.Errorf("Fail to create NewOperLogMessage, id: %d", logMsg.Id)
	}

	setLogMsg(logMsg, logSource, oper, optObj, operRes, optObjType)
	setOperErr(logMsg, operErr)
	recordLog(logMsg)

	return nil
}

/* Ended by AICoder, pid:333c14d45d504b539a6eface8fcc45f9 */

func getOperUserName(request *http.Request) string {
	userName := request.Header.Get(globalcv.HeaderOperateuser)
	if userName == "" {
		userName = "unknown"
	}
	return userName
}

func getOperClientIp(request *http.Request) string {
	var clientIp string
	xForwardedFor := request.Header.Get(globalcv.HeaderXForwardedFor)
	xForwards := strings.Split(xForwardedFor, ",")
	if len(xForwards) > 0 {
		tempIP := xForwards[0]
		if strings.HasPrefix(tempIP, "::ffff:") {
			tempIP = strings.TrimLeft(tempIP, "::ffff:")
		}
		clientIp = tempIP
	} else {
		clientIp = request.RemoteAddr
	}
	if clientIp == "" {
		clientIp = "127.0.0.1"
	}
	return clientIp
}

func getOperJson(oper, sourceType string) string {
	operJson := GetOperJson(oper, sourceType) //按照规范要求自行构建操作信息
	if operJson == "" {
		operJson = "unknown"
	}
	return operJson
}

func getDecriptionInfoJson(decription string) string {
	decriptionInfoJson := GetOperDescriptionInfo(decription) //按照规范要求自行构建descriptionInfo信息
	if decriptionInfoJson == "" {
		decriptionInfoJson = "unknown"
	}
	return decriptionInfoJson
}

func getDetailJson(optObj *OptObjContent) string {
	detailJson := GetOperDetail(optObj) //按照规范要求自行构建detail信息
	if detailJson == "" {
		detailJson = "unknown"
	}
	return detailJson
}

func getOperRes(optObj *OptObjContent) string {
	operRes := fmt.Sprintf("%s(%s)", optObj.ObjName, optObj.ObjID)
	return operRes
}

func getLogMsg(userName, operJson, decriptionInfoJson, clientIp, detailJson string, rank int, operTime, recordTime time.Time) (*logagent.OperLogMessage, bool) {
	logMsg, ok := logagent.NewOperLogMessage(userName, operJson, decriptionInfoJson, clientIp, detailJson, rank, operTime, recordTime) //logagent提供的构建操作日志的函数, NewSecLogMessage安全日志,NewSysLogMessageϵ系统日志
	return logMsg, ok
}

func setLogMsg(logMsg *logagent.OperLogMessage, logSource, oper string, optObj *OptObjContent, operRes string, optObjType map[string]string) {
	if logSource == "OpenPalette" {
		logMsg.SetAppModule("{\"en_US\":\"OpenPalette\", \"zh_CN\": \"容器云平台\"}")
	} else {
		logMsg.SetAppModule("{\"en_US\":\"Resource management\", \"zh_CN\": \"资源管理\"}")
	}
	logMsg.SetConnectMode("WEB")
	logMsg.SetOperateType(oper)
	logMsg.SetOperateResult(logagent.OPERLOG_SUSSESS)
	logMsg.SetOperateResource([]string{operRes})

	//logMsg.ExtFieldsV1 = GetExtensionMsg(sourceType, logSource, optObj)
	//logMsg.ExtFieldsV1 = make(map[string]interface{}, 0)
	logMsg.SetX_KI18N(OPTOBJTYPE, optObjType)     //扩展字段
	logMsg.SetX_K(PROJECTNAME, optObj.TenantName) //扩展字段
	logMsg.SetX_K(PROJECTID, optObj.TenantID)
	logMsg.SetX_K(ENVNAME, optObj.EnvName)
	logMsg.SetX_K(ENVID, optObj.EnvID)
	if optObj.EnvID != "" {
		if vim, err := vims_info_redis.GetByEnvId(optObj.EnvID); err == nil {
			logMsg.SetX_K(VIMID, vim.ID)
		}
	}
}

func setOperErr(logMsg *logagent.OperLogMessage, operErr string) {
	if operErr != "" {
		logMsg.SetOperateResult(logagent.OPERLOG_ERROR)
		failReason := GetFailReason(operErr)
		logMsg.SetFailReason(failReason)
	}
}

func recordLog(logMsg *logagent.OperLogMessage) {
	_ = logagent.RecordLog(logMsg) //logagent封装上报操作日志的接口
}

func Run() {
	path, _ := os.Getwd()
	pathFile := path + "/conf"
	log.AddLoggerConfFile(pathFile)
	app := fx.New(
		otcpcfg.NewConfigProvider(pathFile),
		kms.NewModule(),
		internal_control.NewModule(),
		httpclient.NewHttpClienModule(),
		msb.NewMsbModule(),
		kclient.NewModule(),
		logagent.NewModule(),
		es.NewModule(),
		logstash.NewModule(),
		fx.Provide(NewRetryOutResult),
	)
	app.Start(context.Background())
	defer app.Stop(context.Background())
}

func RunEx() {
	path, _ := os.Getwd()
	pathFile := path + "/conf"
	log.AddLoggerConfFile(pathFile)
	app := fx.New(
		otcpcfg.NewConfigProvider(pathFile),
		fx.Provide(NewRetryOutResult),
	)
	app.Start(context.Background())
	defer app.Stop(context.Background())
}

type MyRetrier struct {
	backoff elastic.Backoff
}

func (r *MyRetrier) Retry(ctx context.Context, retry int, req *http.Request, resp *http.Response, err error) (time.Duration, bool, error) {
	// Fail hard on a specific error

	if err == syscall.ECONNREFUSED {
		return 0, false, errors.New("Elasticsearch or network down")
	}

	// Stop after 5 retries
	if retry >= 5 {
		return 0, false, nil
	}

	// Let the backoff strategy decide how long to wait and whether to stop
	wait, stop := r.backoff.Next(retry)
	return wait, stop, nil
}

func NewMyRetrier() *MyRetrier {
	return &MyRetrier{
		backoff: elastic.NewExponentialBackoff(10*time.Millisecond, 8*time.Second),
	}
}

func NewRetryOutResult() es.ClientOptionFuncOutResult {
	return es.ClientOptionFuncOutResult{
		EsClientOptionFunc: elastic.SetRetrier(NewMyRetrier()),
	}
}
