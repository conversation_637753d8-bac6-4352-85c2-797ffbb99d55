package otcplog

import (
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/redisservice"
	"cwsm/tools/commontools/storage/rediz/storage/cloud_env_redis"
	"cwsm/tools/commontools/storage/rediz/storage/tenant_redis"
	"cwsm/tools/commontools/storage/rediz/storage/vdc_redis"
	"cwsm/tools/commontools/storage/rediz/storage/vims_info_redis"
	"fmt"
	"strconv"
	"strings"
	"time"
)

var OptLogModel map[string]OptLogInfo

type OptLogInfo struct {
	OptZh       string    `json:"optZh"`
	OptEn       string    `json:"optEn"`
	OptObjZh    string    `json:"optObjZh"`
	OptObjEn    string    `json:"optObjEn"`
	OptType     string    `json:"optType"`
	Rank        string    `json:"rank"`
	ObjNum      int       `json:"objNum"`
	ObjInfo     []*IdName `json:"objInfo"`
	EnvInfo     []*IdName `json:"envInfo"`
	TenantInfo  []*IdName `json:"tenantInfo"`
	VdcInfo     []*IdName `json:"vdcInfo"`
	StartTime   time.Time `json:"startTime"`
	OptResource []string  `json:"optResource"`
	LogSource   string    `json:"logSource"`
}

type IdName struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

func (log *OptLogInfo) getOptJson() string {
	operation := InternationalField{
		En_US: log.OptEn,
		Zh_CN: log.OptZh,
	}
	return util.ToJSONStr(operation)
}

/* Started by AICoder, pid:2a462394e9df4b6b8855fd3bf91d2d3a */
func (log *OptLogInfo) getOptDetail() string {
	detail := InternationalField{}
	var detailEn, detailZh []string

	detailEn = append(detailEn, log.getEnvDetails()...)
	detailZh = append(detailZh, log.getEnvDetailsZh()...)

	detailEn = append(detailEn, log.getTenantDetails()...)
	detailZh = append(detailZh, log.getTenantDetailsZh()...)

	detailEn = append(detailEn, log.getVdcDetails()...)
	detailZh = append(detailZh, log.getVdcDetailsZh()...)

	if log.ObjNum != 0 {
		detailEn = append(detailEn, "objNum: "+strconv.Itoa(log.ObjNum))
		detailZh = append(detailZh, "操作对象数："+strconv.Itoa(log.ObjNum))
	}

	detailEn = append(detailEn, log.getObjDetails()...)
	detailZh = append(detailZh, log.getObjDetailsZh()...)

	detail.En_US = strings.Join(detailEn, ", ")
	detail.Zh_CN = strings.Join(detailZh, "，")

	return util.ToJSONStr(detail)
}

func (log *OptLogInfo) getEnvDetails() []string {
	var detailEn []string
	for _, envLog := range log.EnvInfo {
		if envLog.Id != "" && envLog.Name == "" {
			env, err := cloud_env_redis.Get(envLog.Id)
			if err != nil {
				logger.Errorf("[SetOptDetail] get env: %s from redis failed: %v", envLog.Id, err)
				continue
			}
			envLog.Name = env.Name
		}
		detailEn = append(detailEn, "envName: "+envLog.Name+", envId: "+envLog.Id)
	}
	return detailEn
}

func (log *OptLogInfo) getEnvDetailsZh() []string {
	var detailZh []string
	for _, envLog := range log.EnvInfo {
		detailZh = append(detailZh, "云环境名："+envLog.Name+"，云环境ID："+envLog.Id)
	}
	return detailZh
}

func (log *OptLogInfo) getTenantDetails() []string {
	var detailEn []string
	for _, tenantLog := range log.TenantInfo {
		if tenantLog.Id != "" && tenantLog.Name == "" {
			tenant, err := tenant_redis.GetById(tenantLog.Id)
			if err != nil {
				logger.Errorf("[SetOptDetail] get tenant: %s from redis failed: %v", tenantLog.Id, err)
				continue
			}
			tenantLog.Name = tenant.Name
		}
		detailEn = append(detailEn, "tenantName: "+tenantLog.Name+", tenantId: "+tenantLog.Id)
	}
	return detailEn
}

func (log *OptLogInfo) getTenantDetailsZh() []string {
	var detailZh []string
	for _, tenantLog := range log.TenantInfo {
		detailZh = append(detailZh, "租户名："+tenantLog.Name+"，租户ID："+tenantLog.Id)
	}
	return detailZh
}

func (log *OptLogInfo) getVdcDetails() []string {
	var detailEn []string
	for _, vdcLog := range log.VdcInfo {
		if vdcLog.Id != "" && vdcLog.Name == "" {
			vdc, err := vdc_redis.GetVdc(vdcLog.Id)
			if err != nil {
				logger.Errorf("[SetOptDetail] get vdc: %s from redis failed: %v", vdcLog.Id, err)
				continue
			}
			vdcLog.Name = vdc.Name
		}
		detailEn = append(detailEn, "vdcName: "+vdcLog.Name+", vdcId: "+vdcLog.Id)
	}
	return detailEn
}

func (log *OptLogInfo) getVdcDetailsZh() []string {
	var detailZh []string
	for _, vdcLog := range log.VdcInfo {
		detailZh = append(detailZh, "VDC名："+vdcLog.Name+"，VDC ID："+vdcLog.Id)
	}
	return detailZh
}

func (log *OptLogInfo) getObjDetails() []string {
	var detailEn []string
	for _, obj := range log.ObjInfo {
		log.OptResource = append(log.OptResource, fmt.Sprintf("%s(%s)", obj.Name, obj.Id))
		detailEn = append(detailEn, "objName: "+obj.Name+", objId: "+obj.Id)
	}
	return detailEn
}

func (log *OptLogInfo) getObjDetailsZh() []string {
	var detailZh []string
	for _, obj := range log.ObjInfo {
		detailZh = append(detailZh, "操作对象名："+obj.Name+"，操作对象ID："+obj.Id)
	}
	return detailZh
}

/* Ended by AICoder, pid:2a462394e9df4b6b8855fd3bf91d2d3a */

func (log *OptLogInfo) GetOptObjType() map[string]string {
	optObjType := make(map[string]string, 2)
	optObjType["En_US"] = log.OptObjZh
	optObjType["Zh_CN"] = log.OptEn
	return optObjType
}

func (log *OptLogInfo) getFailedOptJson() string {
	operation := InternationalField{
		En_US: log.OptEn + " failed",
		Zh_CN: log.OptZh + "失败",
	}
	return util.ToJSONStr(operation)
}

func (log *OptLogInfo) GetTenantIdName() (string, string) {
	var ids, names []string
	for _, tenant := range log.TenantInfo {
		ids = append(ids, tenant.Id)
		names = append(names, tenant.Name)
	}
	return strings.Join(ids, ", "), strings.Join(names, ",")
}

func (log *OptLogInfo) GetEnvIdNameVim() (string, string, string) {
	var ids, names, vims []string
	for _, env := range log.EnvInfo {
		names = append(names, env.Name)
		ids = append(ids, env.Id)
		if env.Id != "" {
			if vim, err := vims_info_redis.GetByEnvId(env.Id); err == nil {
				vims = append(vims, vim.ID)
			}
		}
	}
	return strings.Join(ids, ", "), strings.Join(names, ","), strings.Join(vims, ", ")
}

type OptObjContent struct {
	EnvID          string `json:"envID"`
	EnvName        string `json:"envName"`
	ObjID          string `json:"objID"`
	ObjName        string `json:"objName"`
	ObjNum         int    `json:"objNum"`
	TenantID       string `json:"tenantID"`
	TenantName     string `json:"tenantName"`
	VdcID          string `json:"vdcID"`
	VdcName        string `json:"vdcName"`
	OptDescription string `json:"optDescription"`
}

type LogParams struct {
	TenantId   string
	TenantName string
	EnvId      string
	EnvName    string
	ObjId      string
	ObjName    string
	ErrMsg     string
}

func (oc *OptObjContent) FillOptLog(params *LogParams) {
	oc.EnvID = params.EnvId
	oc.TenantID = params.TenantId
	if len(params.TenantName) == 0 || len(params.EnvName) == 0 {
		tenant4Redis, err := redisservice.GetTenant4Redis(params.EnvId, params.TenantId)
		if err != nil {
			logger.Errorf("[FillOptLog] get tenant from redis failed: %v", err)
		}
		oc.TenantName = tenant4Redis.Name
		oc.EnvName = tenant4Redis.EnvName
	} else {
		oc.TenantName = params.TenantName
		oc.EnvName = params.EnvName
	}
	oc.ObjID = params.ObjId
	oc.ObjName = params.ObjName
}

type ActionLabel struct {
	Zh   string
	En   string
	Rank string
}

var Labels = map[string]ActionLabel{
	"server":                   {"修改虚机名称/类型", "Modify VM name/resourceType", OptRankImportant},
	"metadata":                 {"配置虚机", "Configure VM", OptRankVeryImportant},
	"os-start":                 {"开启虚机", "Start VM", OptRankNotice},
	"os-stop":                  {"关闭虚机", "Stop VM", OptRankVeryImportant},
	"os-getVNCConsole":         {"打开控制台", "Open VNC Console", OptRankNotice},
	"pause":                    {"暂停虚机", "Pause VM", OptRankVeryImportant},
	"unpause":                  {"取消暂停虚机", "Unpause VM", OptRankNotice},
	"reboot":                   {"重启虚机", "Reboot VM", OptRankVeryImportant},
	"suspend":                  {"挂起虚机", "Suspend VM", OptRankVeryImportant},
	"resume":                   {"恢复挂起虚机", "Resume VM", OptRankNotice},
	"shelve":                   {"归档虚机", "Shelve VM", OptRankNotice},
	"unshelve":                 {"激活虚机", "Unshelve VM", OptRankNotice},
	"resize":                   {"规格调整虚机", "Resize VM", OptRankImportant},
	"liveResize":               {"在线扩容", "Live resize VM", OptRankImportant},
	"rebuild":                  {"重建虚机", "Rebuild VM", OptRankVeryImportant},
	"createImage":              {"镜像快照", "VM snapshot VM", OptRankNotice},
	"createTecsSnapshot":       {"镜像快照", "create snapshot", OptRankNotice},
	"createIecsSnapshot":       {"虚机快照", "create snapshot", OptRankNotice},
	"migrate":                  {"冷迁移虚机", "Migrate VM", OptRankVeryImportant},
	"os-migrateLive":           {"热迁移虚机", "Live Migrate VM", OptRankVeryImportant},
	"enterBios":                {"BIOS引导", "Enter VM Bios", OptRankImportant},
	"root_disk_migrate":        {"系统盘迁移", "VM Root Disk Migrate", OptRankVeryImportant},
	"clone":                    {"克隆虚机", "Clone VM", OptRankNotice},
	"usbBind":                  {"虚机挂载USB", "usbBind VM", OptRankNotice},
	"usbUnbind":                {"虚机解挂USB", "usbUnbind VM", OptRankImportant},
	"cdAdd":                    {"虚机添加光驱", "cdAdd VM", OptRankImportant},
	"cdDelete":                 {"虚机删除光驱", "cdDelete VM", OptRankImportant},
	"cdAttachment":             {"虚机光驱绑定镜像", "cdAttachment VM", OptRankImportant},
	"cdDetachment":             {"虚机光驱卸载镜像", "cdDetachment VM", OptRankImportant},
	"cdUpdate":                 {"虚机光驱更新镜像", "cdUpdate VM", OptRankImportant},
	"iecs_export":              {"导出ICES虚机", "export IECS VM", OptRankImportant},
	"iecs_memory_dynamic":      {"动态内存调整", "Dynamic memory adjustment", OptRankImportant},
	"revertIecsSnapshot":       {"恢复虚机镜像快照", "revert iecs snapshot", OptRankImportant},
	"lock":                     {"锁定虚机", "Lock VM", OptRankVeryImportant},
	"unlock":                   {"解锁虚机", "Unlock VM", OptRankNotice},
	"volumeAttachment":         {"挂载云硬盘", "Attach volume on the VM", OptRankImportant},
	"volumeDetachment":         {"解挂云硬盘", "Detach volume from the VM", OptRankVeryImportant},
	"reset_volume_delete_flag": {"重置虚机连带删除云盘", "Reset VM and Delete Volume", OptRankVeryImportant},
	"evacuate":                 {"疏散虚机", "Isolate VM", OptRankVeryImportant},
	"iecs_forceshutdown":       {"虚机强制关机", "Force shut down VM", OptRankVeryImportant},

	"os-extend":              {"扩展云硬盘", "Extend volume", OptRankImportant},
	"os-retype":              {"离线迁移云硬盘", "Retype volume", OptRankImportant},
	"os-attach":              {"挂载云硬盘", "Attach volume", OptRankImportant},
	"os-force_detach":        {"卸载云硬盘", "Detach volume", OptRankImportant},
	"os-volume_upload_image": {"云硬盘转换为镜像", "Upload volume to image", OptRankImportant},
	"revert":                 {"回滚", "revert volume by snapshot", OptRankImportant},
}
