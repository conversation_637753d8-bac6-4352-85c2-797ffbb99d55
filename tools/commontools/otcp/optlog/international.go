package otcplog

import (
	"encoding/json"
	"strconv"
	"strings"
	"cwsm/tools/commontools/logger"
)

const (
	OPER_ADD_EN    = "Add"
	OPER_ADD_CN    = "增加"
	OPER_UPDATE_EN = "Update"
	OPER_UPDATE_CN = "修改"
	OPER_DELETE_EN = "Del"
	OPER_DELETE_CN = "删除"
	OPER_OTHER_EN  = "Other"
	OPER_OTHER_CN  = "其它类别"

	// About DcGroup management
	CREATE_DATA_CENTER_GROUP_EN      = "Create DC Group"
	CREATE_DATA_CENTER_GROUP_ZH      = "创建数据中心组"
	CREATE_DATA_CENTER_GROUP_FAIL_EN = "Create DC Group Failed"
	CREATE_DATA_CENTER_GROUP_FAIL_ZH = "创建数据中心组失败"
	UPDATE_DATA_CENTER_GROUP_EN      = "Update DC Group"
	UPDATE_DATA_CENTER_GROUP_ZH      = "更新数据中心组"
	UPDATE_DATA_CENTER_GROUP_FAIL_EN = "Update DC Group Failed"
	UPDATE_DATA_CENTER_GROUP_FAIL_ZH = "更新数据中心组失败"
	DELETE_DATA_CENTER_GROUP_EN      = "Delete DC Group"
	DELETE_DATA_CENTER_GROUP_ZH      = "删除数据中心组"
	DELETE_DATA_CENTER_GROUP_FAIL_EN = "Delete DC Group Failed"
	DELETE_DATA_CENTER_GROUP_FAIL_ZH = "删除数据中心组失败"

	DATA_CENTER_GROUP_EN = "DC Group"
	DATA_CENTER_GROUP_ZH = "数据中心组"

	// About DC management
	CREATE_DATA_CENTER_EN      = "Create Data Center"
	CREATE_DATA_CENTER_ZH      = "创建数据中心"
	CREATE_DATA_CENTER_FAIL_EN = "Create Data Center Failed"
	CREATE_DATA_CENTER_FAIL_ZH = "创建数据中心失败"
	UPDATE_DATA_CENTER_EN      = "Update Data Center"
	UPDATE_DATA_CENTER_ZH      = "更新数据中心"
	UPDATE_DATA_CENTER_FAIL_EN = "Update Data Center Failed"
	UPDATE_DATA_CENTER_FAIL_ZH = "更新数据中心失败"
	DELETE_DATA_CENTER_EN      = "Delete Data Center"
	DELETE_DATA_CENTER_ZH      = "删除数据中心"
	DELETE_DATA_CENTER_FAIL_EN = "Delete Data Center Failed"
	DELETE_DATA_CENTER_FAIL_ZH = "删除数据中心失败"

	DATA_CENTER_EN = "Data Center"
	DATA_CENTER_ZH = "数据中心"

	// About image
	IMAGE_EN      = "Image"
	IMAGE_CN      = "镜像"
	IMAGE_FILE_EN = "File"
	IMAGE_FILE_CN = "镜像文件"
	VDC_IMAGE_EN  = "VdcImage"
	VDC_IMAGE_CN  = "VDC镜像"

	// About ImageMapping
	IMAGE_MAPPING_EN                   = "ImageMapping"
	IMAGE_MAPPING_CN                   = "镜像映射"
	CREATE_IMAGE_MAPPING_EN            = "Create Image Mapping"
	CREATE_IMAGE_MAPPING_ZH            = "创建镜像映射"
	CREATE_IMAGE_MAPPING_FAIL_EN       = "Create Image Mapping Failed"
	CREATE_IMAGE_MAPPING_FAIL_ZH       = "创建镜像映射失败"
	BATCH_DELETE_IMAGE_MAPPING_EN      = "Batch Delete Image Mapping"
	BATCH_DELETE_IMAGE_MAPPING_ZH      = "批量删除镜像映射"
	BATCH_DELETE_IMAGE_MAPPING_FAIL_EN = "Batch Delete Image Mapping Failed"
	BATCH_DELETE_IMAGE_MAPPING_FAIL_ZH = "批量删除镜像映射失败"
	EDIT_IMAGE_MAPPING_EN              = "Edit Image Mapping"
	EDIT_IMAGE_MAPPING_ZH              = "编辑镜像映射"
	EDIT_IMAGE_MAPPING_FAIL_EN         = "Edit Image Mapping Failed"
	EDIT_IMAGE_MAPPING_FAIL_ZH         = "编辑镜像映射失败"
	ATTACH_IMAGES_EN                   = "Attach Images"
	ATTACH_IMAGES_ZH                   = "绑定镜像"
	ATTACH_IMAGES_FAIL_EN              = "Attach Images Failed"
	ATTACH_IMAGES_FAIL_ZH              = "绑定镜像失败"

	// About OpenPalette
	CONTAINER_CLOUD_EN = "Container Cloud"
	CONTAINER_CLOUD_ZH = "容器云"
	CONTAINER_NODE_EN  = "Container Node"
	CONTAINER_NODE_ZH  = "容器节点"

	// About Openstack
	CREATE_OPENSTACK_CLOUD_EN                             = "Create Openstack Cloud"
	CREATE_OPENSTACK_CLOUD_ZH                             = "创建[云环境]"
	CREATE_OPENSTACK_CLOUD_FAIL_EN                        = "Create Openstack Cloud Failed"
	CREATE_OPENSTACK_CLOUD_FAIL_ZH                        = "创建[云环境]失败"
	DELETE_OPENSTACK_CLOUD_EN                             = "Delete Openstack Cloud"
	DELETE_OPENSTACK_CLOUD_ZH                             = "删除[云环境]"
	DELETE_OPENSTACK_CLOUD_FAIL_EN                        = "Delete Openstack Cloud Failed"
	DELETE_OPENSTACK_CLOUD_FAIL_ZH                        = "删除[云环境]失败"
	MODIFY_OPENSTACK_CLOUD_INFO_EN                        = "Modify Openstack Cloud Info"
	MODIFY_OPENSTACK_CLOUD_INFO_ZH                        = "修改[云环境]基本信息"
	MODIFY_OPENSTACK_CLOUD_INFO_FAIL_EN                   = "Modify Openstack Cloud Info Failed"
	MODIFY_OPENSTACK_CLOUD_INFO_FAIL_ZH                   = "修改[云环境]基本信息失败"
	MODIFY_OPENSTACK_CLOUD_PASSWORD_EN                    = "Modify Openstack Cloud Password"
	MODIFY_OPENSTACK_CLOUD_PASSWORD_ZH                    = "修改[云环境]密码"
	MODIFY_OPENSTACK_CLOUD_PASSWORD_FAIL_EN               = "Modify Openstack Cloud Password Failed"
	MODIFY_OPENSTACK_CLOUD_PASSWORD_FAIL_ZH               = "修改[云环境]密码失败"
	MODIFY_OPENSTACK_CLOUD_LABELS_EN                      = "Modify Openstack Cloud Labels"
	MODIFY_OPENSTACK_CLOUD_LABELS_ZH                      = "修改[云环境]标签"
	MODIFY_OPENSTACK_CLOUD_LABELS_FAIL_EN                 = "Modify Openstack Cloud Labels Failed"
	MODIFY_OPENSTACK_CLOUD_LABELS_FAIL_ZH                 = "修改[云环境]标签失败"
	MODIFY_OPENSTACK_CLOUD_SSLAUTH_EN                     = "Modify Openstack Cloud Ssl Auth"
	MODIFY_OPENSTACK_CLOUD_SSLAUTH_ZH                     = "修改[云环境]双向认证"
	MODIFY_OPENSTACK_CLOUD_SSLAUTH_FAIL_EN                = "Modify Openstack Cloud Ssl Auth Failed"
	MODIFY_OPENSTACK_CLOUD_SSLAUTH_FAIL_ZH                = "修改[云环境]双向认证失败"
	MODIFY_OPENSTACK_CLOUD_POOL_PORTAL_URL_CONFIG_EN      = "Modify Openstack Cloud Pool Portal URL Config"
	MODIFY_OPENSTACK_CLOUD_POOL_PORTAL_URL_CONFIG_ZH      = "修改[云环境]资源池入口配置"
	MODIFY_OPENSTACK_CLOUD_POOL_PORTAL_URL_CONFIG_FAIL_EN = "Modify Openstack Cloud Pool Portal URL Config Failed"
	MODIFY_OPENSTACK_CLOUD_POOL_PORTAL_URL_CONFIG_FAIL_ZH = "修改[云环境]资源池入口配置失败"
	MODIFY_OPENSTACK_CLOUD_SPECIAL_VIM_ID_EN              = "Modify Openstack Cloud Special Vim ID"
	MODIFY_OPENSTACK_CLOUD_SPECIAL_VIM_ID_ZH              = "修改[云环境] VIM_ID"
	MODIFY_OPENSTACK_CLOUD_SPECIAL_VIM_ID_FAIL_EN         = "Modify Openstack Cloud Special Vim ID Failed"
	MODIFY_OPENSTACK_CLOUD_SPECIAL_VIM_ID_FAIL_ZH         = "修改[云环境] VIM_ID失败"
	MODIFY_OPENSTACK_CLOUD_VM_BACKUP_CONFIG_EN            = "Modify Openstack Cloud Vm Backup Config"
	MODIFY_OPENSTACK_CLOUD_VM_BACKUP_CONFIG_ZH            = "修改[云环境]虚机备份配置"
	MODIFY_OPENSTACK_CLOUD_VM_BACKUP_CONFIG_FAIL_EN       = "Modify Openstack Cloud Vm Backup Config Failed"
	MODIFY_OPENSTACK_CLOUD_VM_BACKUP_CONFIG_FAIL_ZH       = "修改[云环境]虚机备份配置失败"
	MODIFY_OPENSTACK_CLOUd_API_VERSION_EN                 = "Modify Openstack Cloud Api Version"
	MODIFY_OPENSTACK_CLOUd_API_VERSION_ZH                 = "修改[云环境] API版本"
	MODIFY_OPENSTACK_CLOUd_API_VERSION_FAIL_EN            = "Modify Openstack Cloud Api Version Failed"
	MODIFY_OPENSTACK_CLOUd_API_VERSION_FAIL_ZH            = "修改[云环境] API版本失败"
	MODIFY_OPENSTACK_CLOUD_HA_POLICY_EN                   = "Modify Openstack Cloud Ha Policy"
	MODIFY_OPENSTACK_CLOUD_HA_POLICY_ZH                   = "修改[云环境] HA策略"
	MODIFY_OPENSTACK_CLOUD_HA_POLICY_FAIL_EN              = "Modify Openstack Cloud Ha Policy Failed"
	MODIFY_OPENSTACK_CLOUD_HA_POLICY_FAIL_ZH              = "修改[云环境] HA策略失败"
	MODIFY_OPENSTACK_CLOUD_SSO_INFO_EN                    = "Modify Openstack Cloud SSO Info"
	MODIFY_OPENSTACK_CLOUD_SSO_INFO_ZH                    = "修改[云环境] SSO信息"
	MODIFY_OPENSTACK_CLOUD_SSO_INFO_FAIL_EN               = "Modify Openstack Cloud SSO Info Failed"
	MODIFY_OPENSTACK_CLOUD_SSO_INFO_FAIL_ZH               = "修改[云环境] SSO信息失败"

	//About Ass
	AS_GROUP_EN    = "Scaling Group"
	AS_GROUP_ZH    = "弹缩组"
	AS_CONFIG_EN   = "Scaling Config"
	AS_CONFIG_ZH   = "弹缩配置"
	AS_STRATEGY_EN = "Scaling Strategy"
	AS_STRATEGY_ZH = "弹缩策略"

	//About Tms
	RESOURCE_TAG_EN = "Resource Tag"
	RESOURCE_TAG_ZH = "标签资源"

	// About Docking System Configuration
	DOCKING_SYSTEM_CONFIGURATION_EN = "Docking System Configuration"
	DOCKING_SYSTEM_CONFIGURATION_ZH = "对接系统配置"

	// About Freezer
	FREEZER_JOB_EN         = "Freezer Job"
	FREEZER_JOB_ZH         = "Freezer 任务"
	FREEZER_BACKUP_DATA_EN = "Freezer Backup Data"
	FREEZER_BACKUP_DATA_ZH = "Freezer备份数据"

	UNKNOWN_EN = "unknown"
	UNKNOWN_CN = "未知"

	// About Image
	PATCH_PUBLISH_IMAGE_EN                = "Patch Publish Image"
	PATCH_PUBLISH_IMAGE_ZH                = "编辑发布镜像"
	PATCH_PUBLISH_IMAGE_FAIL_EN           = "Patch Publish Image Failed"
	PATCH_PUBLISH_IMAGE_FAIL_ZH           = "编辑发布镜像失败"
	DELETE_PUBLISH_IMAGE_EN               = "Delete Publish Image"
	DELETE_PUBLISH_IMAGE_ZH               = "删除发布镜像"
	DELETE_PUBLISH_IMAGE_FAIL_EN          = "Delete Publish Image Failed"
	DELETE_PUBLISH_IMAGE_FAIL_ZH          = "删除发布镜像失败"
	DOWNLOAD_PUBLISH_IMAGE_EN             = "Download Publish Image"
	DOWNLOAD_PUBLISH_IMAGE_ZH             = "下载发布镜像"
	DOWNLOAD_PUBLISH_IMAGE_FAIL_EN        = "Download Publish Image Failed"
	DOWNLOAD_PUBLISH_IMAGE_FAIL_ZH        = "下载发布镜像失败"
	RESUME_TRANSFER_PUBLISH_IMAGE_EN      = "Resume Transfer Publish Image"
	RESUME_TRANSFER_PUBLISH_IMAGE_ZH      = "重新发布镜像"
	RESUME_TRANSFER_PUBLISH_IMAGE_FAIL_EN = "Resume Transfer Publish Image Failed"
	RESUME_TRANSFER_PUBLISH_IMAGE_FAIL_ZH = "重新发布镜像失败"
	PUBLISH_IMAGE_EN                      = "Publish Image"
	PUBLISH_IMAGE_ZH                      = "发布镜像"
	PUBLISH_IMAGE_FAIL_EN                 = "Publish Image Failed"
	PUBLISH_IMAGE_FAIL_ZH                 = "发布镜像失败"

	// About File
	MODIFY_FILE_EN      = "Modify File"
	MODIFY_FILE_ZH      = "编辑镜像文件"
	MODIFY_FILE_FAIL_EN = "Modify File Failed"
	MODIFY_FILE_FAIL_ZH = "编辑镜像文件失败"
	DELETE_FILE_EN      = "Delete File"
	DELETE_FILE_ZH      = "删除镜像文件"
	DELETE_FILE_FAIL_EN = "Delete File Failed"
	DELETE_FILE_FAIL_ZH = "删除镜像文件失败"
	UPLOAD_FILE_EN      = "Upload File"
	UPLOAD_FILE_ZH      = "上传镜像文件"
	UPLOAD_FILE_FAIL_EN = "Upload File Failed"
	UPLOAD_FILE_FAIL_ZH = "上传镜像文件失败"

	// About Vdc Image
	ORG_USER_MODIFY_VDCIMAGE_EN      = "Org User Modify VdcImage"
	ORG_USER_MODIFY_VDCIMAGE_ZH      = "组织用户编辑VDC镜像"
	ORG_USER_MODIFY_VDCIMAGE_FAIL_EN = "Org User Modify VdcImage Failed"
	ORG_USER_MODIFY_VDCIMAGE_FAIL_ZH = "组织用户编辑VDC镜像失败"
	ORG_USER_CREATE_VDCIMAGE_EN      = "Org User Create VdcImage"
	ORG_USER_CREATE_VDCIMAGE_ZH      = "组织用户创建VDC镜像"
	ORG_USER_CREATE_VDCIMAGE_FAIL_EN = "Org User Create VdcImage Failed"
	ORG_USER_CREATE_VDCIMAGE_FAIL_ZH = "组织用户创建VDC镜像失败"
	ORG_USER_DELETE_VDCIMAGE_EN      = "Org User Delete VdcImage"
	ORG_USER_DELETE_VDCIMAGE_ZH      = "组织用户删除VDC镜像"
	ORG_USER_DELETE_VDCIMAGE_FAIL_EN = "Org User Delete VdcImage Failed"
	ORG_USER_DELETE_VDCIMAGE_FAIL_ZH = "组织用户删除VDC镜像失败"

	// About OpenPalette
	CREATE_CONTAINER_CLOUD_EN               = "Create Container Cloud"
	CREATE_CONTAINER_CLOUD_ZH               = "创建[容器云]"
	CREATE_CONTAINER_CLOUD_FAIL_EN          = "Create Container Cloud Failed"
	CREATE_CONTAINER_CLOUD_FAIL_ZH          = "创建[容器云]失败"
	DELETE_CONTAINER_CLOUD_EN               = "Delete Container Cloud"
	DELETE_CONTAINER_CLOUD_ZH               = "删除[容器云]"
	DELETE_CONTAINER_CLOUD_FAIL_EN          = "Delete Container Cloud Failed"
	DELETE_CONTAINER_CLOUD_FAIL_ZH          = "删除[容器云]失败"
	MODIFY_CONTAINER_CLOUD_INFO_EN          = "Modify Container Cloud Info"
	MODIFY_CONTAINER_CLOUD_INFO_ZH          = "修改[容器云]基本信息"
	MODIFY_CONTAINER_CLOUD_INFO_FAIL_EN     = "Modify Container Cloud Info Failed"
	MODIFY_CONTAINER_CLOUD_INFO_FAIL_ZH     = "修改[容器云]基本信息失败"
	MODIFY_CONTAINER_CLOUD_PASSWORD_EN      = "Modify Container Cloud Password"
	MODIFY_CONTAINER_CLOUD_PASSWORD_ZH      = "修改[容器云]密码"
	MODIFY_CONTAINER_CLOUD_PASSWORD_FAIL_EN = "Modify Container Cloud Password Failed"
	MODIFY_CONTAINER_CLOUD_PASSWORD_FAIL_ZH = "修改[容器云]密码失败"
	MODIFY_CONTAINER_CLOUD_LABELS_EN        = "Modify Container Cloud Labels"
	MODIFY_CONTAINER_CLOUD_LABELS_ZH        = "修改[容器云]标签"
	MODIFY_CONTAINER_CLOUD_LABELS_FAIL_EN   = "Modify Container Cloud Labels Failed"
	MODIFY_CONTAINER_CLOUD_LABELS_FAIL_ZH   = "修改[容器云]标签失败"
	MODIFY_CONTAINER_CLOUD_SSLAUTH_EN       = "Modify Container Cloud SSL Auth"
	MODIFY_CONTAINER_CLOUD_SSLAUTH_ZH       = "修改[容器云]双向认证"
	MODIFY_CONTAINER_CLOUD_SSLAUTH_FAIL_EN  = "Modify Container Cloud SSL Auth Failed"
	MODIFY_CONTAINER_CLOUD_SSLAUTH_FAIL_ZH  = "修改[容器云]双向认证失败"
	MODIFY_CONTAINER_NODE_LABELS_EN         = "Modify Container Node Labels"
	MODIFY_CONTAINER_NODE_LABELS_ZH         = "修改[容器云]节点标签"
	MODIFY_CONTAINER_NODE_LABELS_FAIL_EN    = "Modify Container Node Labels Failed"
	MODIFY_CONTAINER_NODE_LABELS_FAIL_ZH    = "修改[容器云]节点标签失败"

	CREATE_NETWORK_CLOUD_EN                             = "Create Network Cloud"
	CREATE_NETWORK_CLOUD_ZH                             = "创建[网络环境]"
	CREATE_NETWORK_CLOUD_FAIL_EN                        = "Create Network Cloud Failed"
	CREATE_NETWORK_CLOUD_FAIL_ZH                        = "创建[网络环境]失败"
	DELETE_NETWORK_CLOUD_EN                             = "Delete Network Cloud"
	DELETE_NETWORK_CLOUD_ZH                             = "删除[网络环境]"
	DELETE_NETWORK_CLOUD_FAIL_EN                        = "Delete Network Cloud Failed"
	DELETE_NETWORK_CLOUD_FAIL_ZH                        = "删除[网络环境]失败"
	MODIFY_NETWORK_CLOUD_INFO_EN                        = "Modify Network Cloud Info"
	MODIFY_NETWORK_CLOUD_INFO_ZH                        = "修改[网络环境]基本信息"
	MODIFY_NETWORK_CLOUD_INFO_FAIL_EN                   = "Modify Network Cloud Info Failed"
	MODIFY_NETWORK_CLOUD_INFO_FAIL_ZH                   = "修改[网络环境]基本信息失败"
	MODIFY_NETWORK_CLOUD_PASSWORD_EN                    = "Modify Network Cloud Password"
	MODIFY_NETWORK_CLOUD_PASSWORD_ZH                    = "修改[网络环境]密码"
	MODIFY_NETWORK_CLOUD_PASSWORD_FAIL_EN               = "Modify Network Cloud Password Failed"
	MODIFY_NETWORK_CLOUD_PASSWORD_FAIL_ZH               = "修改[网络环境]密码失败"
	MODIFY_NETWORK_CLOUD_LABELS_EN                      = "Modify Network Cloud Labels"
	MODIFY_NETWORK_CLOUD_LABELS_ZH                      = "修改[网络环境]标签"
	MODIFY_NETWORK_CLOUD_LABELS_FAIL_EN                 = "Modify Network Cloud Labels Failed"
	MODIFY_NETWORK_CLOUD_LABELS_FAIL_ZH                 = "修改[网络环境]标签失败"
	MODIFY_NETWORK_CLOUD_SSLAUTH_EN                     = "Modify Network Cloud Ssl Auth"
	MODIFY_NETWORK_CLOUD_SSLAUTH_ZH                     = "修改[网络环境]双向认证"
	MODIFY_NETWORK_CLOUD_SSLAUTH_FAIL_EN                = "Modify Network Cloud Ssl Auth Failed"
	MODIFY_NETWORK_CLOUD_SSLAUTH_FAIL_ZH                = "修改[网络环境]双向认证失败"
	MODIFY_NETWORK_CLOUD_POOL_PORTAL_URL_CONFIG_EN      = "Modify Network Cloud Pool Portal URL Config"
	MODIFY_NETWORK_CLOUD_POOL_PORTAL_URL_CONFIG_ZH      = "修改[网络环境]资源池入口配置"
	MODIFY_NETWORK_CLOUD_POOL_PORTAL_URL_CONFIG_FAIL_EN = "Modify Network Cloud Pool Portal URL Config Failed"
	MODIFY_NETWORK_CLOUD_POOL_PORTAL_URL_CONFIG_FAIL_ZH = "修改[网络环境]资源池入口配置失败"
	MODIFY_NETWORK_CLOUD_SPECIAL_VIM_ID_EN              = "Modify Network Cloud Special Vim ID"
	MODIFY_NETWORK_CLOUD_SPECIAL_VIM_ID_ZH              = "修改[网络环境] VIM_ID"
	MODIFY_NETWORK_CLOUD_SPECIAL_VIM_ID_FAIL_EN         = "Modify Network Cloud Special Vim ID Failed"
	MODIFY_NETWORK_CLOUD_SPECIAL_VIM_ID_FAIL_ZH         = "修改[网络环境] VIM_ID失败"
	MODIFY_NETWORK_CLOUD_VM_BACKUP_CONFIG_EN            = "Modify Network Cloud Vm Backup Config"
	MODIFY_NETWORK_CLOUD_VM_BACKUP_CONFIG_ZH            = "修改[网络环境]虚机备份配置"
	MODIFY_NETWORK_CLOUD_VM_BACKUP_CONFIG_FAIL_EN       = "Modify Network Cloud Vm Backup Config Failed"
	MODIFY_NETWORK_CLOUD_VM_BACKUP_CONFIG_FAIL_ZH       = "修改[网络环境]虚机备份配置失败"
	MODIFY_NETWORK_CLOUd_API_VERSION_EN                 = "Modify Network Cloud Api Version"
	MODIFY_NETWORK_CLOUd_API_VERSION_ZH                 = "修改[网络环境] API版本"
	MODIFY_NETWORK_CLOUd_API_VERSION_FAIL_EN            = "Modify Network Cloud Api Version Failed"
	MODIFY_NETWORK_CLOUd_API_VERSION_FAIL_ZH            = "修改[网络环境] API版本失败"
	MODIFY_NETWORK_CLOUD_HA_POLICY_EN                   = "Modify Network Cloud Ha Policy"
	MODIFY_NETWORK_CLOUD_HA_POLICY_ZH                   = "修改[网络环境] HA策略"
	MODIFY_NETWORK_CLOUD_HA_POLICY_FAIL_EN              = "Modify Network Cloud Ha Policy Failed"
	MODIFY_NETWORK_CLOUD_HA_POLICY_FAIL_ZH              = "修改[网络环境] HA策略失败"
	MODIFY_NETWORK_CLOUD_SSO_INFO_EN                    = "Modify Network Cloud SSO Info"
	MODIFY_NETWORK_CLOUD_SSO_INFO_ZH                    = "修改[网络环境] SSO信息"
	MODIFY_NETWORK_CLOUD_SSO_INFO_FAIL_EN               = "Modify Network Cloud SSO Info Failed"
	MODIFY_NETWORK_CLOUD_SSO_INFO_FAIL_ZH               = "修改[网络环境] SSO信息失败"

	CREATE_PHYSICAL_ENV_EN               = "Create Physical Env"
	CREATE_PHYSICAL_ENV_ZH               = "创建[物理环境]"
	CREATE_PHYSICAL_ENV_FAIL_EN          = "Create Physical Env Failed"
	CREATE_PHYSICAL_ENV_FAIL_ZH          = "创建[物理环境]失败"
	DELETE_PHYSICAL_ENV_EN               = "Delete Physical Env"
	DELETE_PHYSICAL_ENV_ZH               = "删除[物理环境]"
	DELETE_PHYSICAL_ENV_FAIL_EN          = "Delete Physical Env Failed"
	DELETE_PHYSICAL_ENV_FAIL_ZH          = "删除[物理环境]失败"
	MODIFY_PHYSICAL_ENV_INFO_EN          = "Modify Physical Env Info"
	MODIFY_PHYSICAL_ENV_INFO_ZH          = "修改[物理环境]基本信息"
	MODIFY_PHYSICAL_ENV_INFO_FAIL_EN     = "Modify Physical Env Info Failed"
	MODIFY_PHYSICAL_ENV_INFO_FAIL_ZH     = "修改[物理环境]基本信息失败"
	MODIFY_PHYSICAL_ENV_PASSWORD_EN      = "Modify Physical Env Password"
	MODIFY_PHYSICAL_ENV_PASSWORD_ZH      = "修改[物理环境]密码"
	MODIFY_PHYSICAL_ENV_PASSWORD_FAIL_EN = "Modify Physical Env Password Failed"
	MODIFY_PHYSICAL_ENV_PASSWORD_FAIL_ZH = "修改[物理环境]密码失败"
	MODIFY_PHYSICAL_ENV_SSLAUTH_EN       = "Modify Physical Env Ssl Auth"
	MODIFY_PHYSICAL_ENV_SSLAUTH_ZH       = "修改[物理环境]双向认证"
	MODIFY_PHYSICAL_ENV_SSLAUTH_FAIL_EN  = "Modify Physical Env Ssl Auth Failed"
	MODIFY_PHYSICAL_ENV_SSLAUTH_FAIL_ZH  = "修改[物理环境]双向认证失败"

	// About Overall Cloud Operations
	CREATE_CLOUD_EN                             = "Create %s"
	CREATE_CLOUD_ZH                             = "创建[%s]"
	CREATE_CLOUD_FAIL_EN                        = "Create %s Failed"
	CREATE_CLOUD_FAIL_ZH                        = "创建[%s]失败"
	DELETE_CLOUD_EN                             = "Delete %s"
	DELETE_CLOUD_ZH                             = "删除[%s]"
	DELETE_CLOUD_FAIL_EN                        = "Delete %s Failed"
	DELETE_CLOUD_FAIL_ZH                        = "删除[%s]失败"
	MODIFY_CLOUD_INFO_EN                        = "Modify %s Info"
	MODIFY_CLOUD_INFO_ZH                        = "修改[%s]基本信息"
	MODIFY_CLOUD_INFO_FAIL_EN                   = "Modify %s Info Failed"
	MODIFY_CLOUD_INFO_FAIL_ZH                   = "修改[%s]基本信息失败"
	MODIFY_CLOUD_PASSWORD_EN                    = "Modify %s Password"
	MODIFY_CLOUD_PASSWORD_ZH                    = "修改[%s]密码"
	MODIFY_CLOUD_PASSWORD_FAIL_EN               = "Modify %s Password Failed"
	MODIFY_CLOUD_PASSWORD_FAIL_ZH               = "修改[%s]密码失败"
	MODIFY_CLOUD_LABELS_EN                      = "Modify %s Labels"
	MODIFY_CLOUD_LABELS_ZH                      = "修改[%s]标签"
	MODIFY_CLOUD_LABELS_FAIL_EN                 = "Modify %s Labels Failed"
	MODIFY_CLOUD_LABELS_FAIL_ZH                 = "修改[%s]标签失败"
	MODIFY_CLOUD_SSLAUTH_EN                     = "Modify %s Ssl Auth"
	MODIFY_CLOUD_SSLAUTH_ZH                     = "修改[%s]双向认证"
	MODIFY_CLOUD_SSLAUTH_FAIL_EN                = "Modify %s Ssl Auth Failed"
	MODIFY_CLOUD_SSLAUTH_FAIL_ZH                = "修改[%s]双向认证失败"
	MODIFY_CLOUD_POOL_PORTAL_URL_CONFIG_EN      = "Modify %s Pool Portal URL Config"
	MODIFY_CLOUD_POOL_PORTAL_URL_CONFIG_ZH      = "修改[%s]资源池入口配置"
	MODIFY_CLOUD_POOL_PORTAL_URL_CONFIG_FAIL_EN = "Modify %s Pool Portal URL Config Failed"
	MODIFY_CLOUD_POOL_PORTAL_URL_CONFIG_FAIL_ZH = "修改[%s]资源池入口配置失败"
	MODIFY_CLOUD_SPECIAL_VIM_ID_EN              = "Modify %s Special Vim ID"
	MODIFY_CLOUD_SPECIAL_VIM_ID_ZH              = "修改[%s] VIM_ID"
	MODIFY_CLOUD_SPECIAL_VIM_ID_FAIL_EN         = "Modify %s Special Vim ID Failed"
	MODIFY_CLOUD_SPECIAL_VIM_ID_FAIL_ZH         = "修改[%s] VIM_ID失败"
	MODIFY_CLOUD_VM_BACKUP_CONFIG_EN            = "Modify %s Vm Backup Config"
	MODIFY_CLOUD_VM_BACKUP_CONFIG_ZH            = "修改[%s]虚机备份配置"
	MODIFY_CLOUD_VM_BACKUP_CONFIG_FAIL_EN       = "Modify %s Vm Backup Config Failed"
	MODIFY_CLOUD_VM_BACKUP_CONFIG_FAIL_ZH       = "修改[%s]虚机备份配置失败"
	MODIFY_CLOUD_API_VERSION_EN                 = "Modify %s Api Version"
	MODIFY_CLOUD_API_VERSION_ZH                 = "修改[%s] API版本"
	MODIFY_CLOUD_API_VERSION_FAIL_EN            = "Modify %s Api Version Failed"
	MODIFY_CLOUD_API_VERSION_FAIL_ZH            = "修改[%s] API版本失败"
	MODIFY_CLOUD_HA_POLICY_EN                   = "Modify %s Ha Policy"
	MODIFY_CLOUD_HA_POLICY_ZH                   = "修改[%s] HA策略"
	MODIFY_CLOUD_HA_POLICY_FAIL_EN              = "Modify %s Ha Policy Failed"
	MODIFY_CLOUD_HA_POLICY_FAIL_ZH              = "修改[%s] HA策略失败"
	MODIFY_CLOUD_SSO_INFO_EN                    = "Modify %s SSO Info"
	MODIFY_CLOUD_SSO_INFO_ZH                    = "修改[%s] SSO信息"
	MODIFY_CLOUD_SSO_INFO_FAIL_EN               = "Modify %s SSO Info Failed"
	MODIFY_CLOUD_SSO_INFO_FAIL_ZH               = "修改[%s] SSO信息失败"
	MODIFY_NODE_LABELS_EN                       = "Modify %s Node Labels"
	MODIFY_NODE_LABELS_ZH                       = "修改[%s]节点标签"
	MODIFY_NODE_LABELS_FAIL_EN                  = "Modify %s Node Labels Failed"
	MODIFY_NODE_LABELS_FAIL_ZH                  = "修改[%s]节点标签失败"

	// About Source Type
	OPENPALETTE_CLOUD    = "Container Cloud"
	OPENPALETTE_CLOUD_ZH = "容器云"
	OPENSTACK_CLOUD      = "Openstack Cloud"
	OPENSTACK_CLOUD_ZH   = "云环境"
	NETWORK_CLOUD        = "Network Cloud"
	NETWORK_CLOUD_ZH     = "网络环境"
	PHYSICAL_ENV         = "Physical Env"
	PHYSICAL_ENV_ZH      = "物理环境"

	// About Log Source
	OPENSTACK   = "Openstack"
	OPENPALETTE = "Openpalette"
	NETWORK     = "Network"
	PHYENV      = "Physic"

	// About Bridge
	BRIDGE_EN                        = "Bridge"
	BRIDGE_CN                        = "虚机备份"
	BACKUP_NOW_EN                    = "back up now"
	BACKUP_NOW_ZH                    = "立即备份"
	CREATE_BACKUP_TASK_EN            = "create backup task"
	CREATE_BACKUP_TASK_ZH            = "创建备份任务"
	DELETE_BACKUP_TASK_EN            = "delete backup task"
	DELETE_BACKUP_TASK_ZH            = "删除备份任务"
	UPDATE_BACKUP_TASK_EN            = "update backup task"
	UPDATE_BACKUP_TASK_ZH            = "修改备份任务"
	RESTORE_VM_EN                    = "restore vm"
	RESTORE_VM_ZH                    = "恢复虚机"
	DELETE_BACKUP_EN                 = "delete backup"
	DELETE_BACKUP_ZH                 = "删除备份"
	CREATE_AUTO_DELETION_STRATEGY_EN = "create auto deletion strategy"
	CREATE_AUTO_DELETION_STRATEGY_ZH = "创建自动删除策略"
	DELETE_AUTO_DELETION_STRATEGY_EN = "delete auto deletion strategy"
	DELETE_AUTO_DELETION_STRATEGY_ZH = "删除自动删除策略"
	EXPORT_BACKUP_STATISTICS_EN      = "export backup statistics"
	EXPORT_BACKUP_STATISTICS_ZH      = "导出备份统计"

	// About Resource Tag
	PREDEFINE_RESOURCE_TAG_EN             = "Create Predefine Resource Tag"
	PREDEFINE_RESOURCE_TAG_ZH             = "创建预定义标签资源"
	PREDEFINE_RESOURCE_TAG_FAIL_EN        = "Create Predefine Resource Tag Failed"
	PREDEFINE_RESOURCE_TAG_FAIL_ZH        = "创建预定义标签资源失败"
	DELETE_PREDEFINE_RESOURCE_TAG_EN      = "Delete Predefine Resource Tag"
	DELETE_PREDEFINE_RESOURCE_TAG_ZH      = "删除预定义标签资源"
	DELETE_PREDEFINE_RESOURCE_TAG_FAIL_EN = "Delete Predefine Resource Tag Failed"
	DELETE_PREDEFINE_RESOURCE_TAG_FAIL_ZH = "删除预定义标签资源失败"
	UPDATE_RESOURCE_TAG_EN                = "Update Resource Tag"
	UPDATE_RESOURCE_TAG_ZH                = "编辑标签资源"
	UPDATE_RESOURCE_TAG_FAIL_EN           = "Update Resource Tag Failed"
	UPDATE_RESOURCE_TAG_FAIL_ZH           = "编辑标签资源失败"

	// About As group
	CREATE_AS_GROUP_EN       = "Create Scaling Group"
	CREATE_AS_GROUP_ZH       = "创建弹缩组"
	CREATE_AS_GROUP_FAIL_EN  = "Create Scaling Group Failed"
	CREATE_AS_GROUP_FAIL_ZH  = "创建弹缩组失败"
	DELETE_AS_GROUP_EN       = "Delete Scaling Group"
	DELETE_AS_GROUP_ZH       = "删除弹缩组"
	DELETE_AS_GROUP_FAIL_EN  = "Delete Scaling Group Failed"
	DELETE_AS_GROUP_FAIL_ZH  = "删除弹缩组失败"
	OPERATE_AS_GROUP_EN      = "Start/Stop Scaling Group"
	OPERATE_AS_GROUP_ZH      = "启动/停止弹缩组"
	OPERATE_AS_GROUP_FAIL_EN = "Start/Stop Scaling Group Failed"
	OPERATE_AS_GROUP_FAIL_ZH = "启动/停止弹缩组失败"

	//About As config
	CREATE_AS_CONFIG_EN      = "Create Scaling Config"
	CREATE_AS_CONFIG_ZH      = "创建弹缩配置"
	CREATE_AS_CONFIG_FAIL_EN = "Create Scaling Config Failed"
	CREATE_AS_CONFIG_FAIL_ZH = "创建弹缩配置失败"
	DELETE_AS_CONFIG_EN      = "Delete Scaling Config"
	DELETE_AS_CONFIG_ZH      = "删除弹缩配置"
	DELETE_AS_CONFIG_FAIL_EN = "Delete Scaling Config Failed"
	DELETE_AS_CONFIG_FAIL_ZH = "删除弹缩配置失败"

	//About As strategy
	CREATE_AS_STRATEGY_EN      = "Create Scaling Strategy"
	CREATE_AS_STRATEGY_ZH      = "创建弹缩策略"
	CREATE_AS_STRATEGY_FAIL_EN = "Create Scaling Strategy Failed"
	CREATE_AS_STRATEGY_FAIL_ZH = "创建弹缩策略失败"
	DELETE_AS_STRATEGY_EN      = "Delete Scaling Strategy"
	DELETE_AS_STRATEGY_ZH      = "删除弹缩策略"
	DELETE_AS_STRATEGY_FAIL_EN = "Delete Scaling Strategy Failed"
	DELETE_AS_STRATEGY_FAIL_ZH = "删除弹缩策略失败"
	UPDATE_AS_STRATEGY_EN      = "Update Scaling Strategy"
	UPDATE_AS_STRATEGY_ZH      = "修改弹缩策略"
	UPDATE_AS_STRATEGY_FAIL_EN = "Update Scaling Strategy Failed"
	UPDATE_AS_STRATEGY_FAIL_ZH = "修改弹缩策略失败"

	//About North Docking System
	CREATE_DOCKING_SYSTEM_CONFIGURATION_EN      = "Create Docking System Configuration"
	CREATE_DOCKING_SYSTEM_CONFIGURATION_ZH      = "创建对接系统配置"
	CREATE_DOCKING_SYSTEM_CONFIGURATION_FAIL_EN = "Create Docking System Configuration Failed"
	CREATE_DOCKING_SYSTEM_CONFIGURATION_FAIL_ZH = "创建对接系统配置失败"

	UPDATE_DOCKING_SYSTEM_CONFIGURATION_EN      = "Update Docking System Configuration"
	UPDATE_DOCKING_SYSTEM_CONFIGURATION_ZH      = "修改对接系统配置"
	UPDATE_DOCKING_SYSTEM_CONFIGURATION_FAIL_EN = "Update Docking System Configuration Failed"
	UPDATE_DOCKING_SYSTEM_CONFIGURATION_FAIL_ZH = "修改对接系统配置失败"

	DELETE_DOCKING_SYSTEM_CONFIGURATION_EN      = "Delete Docking System Configuration"
	DELETE_DOCKING_SYSTEM_CONFIGURATION_ZH      = "删除对接系统配置"
	DELETE_DOCKING_SYSTEM_CONFIGURATION_FAIL_EN = "Delete Docking System Configuration Failed"
	DELETE_DOCKING_SYSTEM_CONFIGURATION_FAIL_ZH = "删除对接系统配置失败"

	//About Resource north Config System
	RESOURCE_NORTH_CONFIG_EN = "Resource North Config"
	RESOURCE_NORTH_CONFIG_ZH   = "资源北向配置"

	CREATE_RESOURCE_NORTH_CONFIG_EN      = "Create Resource North Config"
	CREATE_RESOURCE_NORTH_CONFIG_ZH      = "创建资源北向配置"
	CREATE_RESOURCE_NORTH_CONFIG_FAIL_EN  = "Create Resource North Config Failed"
	CREATE_RESOURCE_NORTH_CONFIG_FAIL_ZH = "创建资源北向配置失败"

	MODIFY_RESOURCE_NORTH_CONFIG_EN      = "Modify Resource North Config"
	MODIFY_RESOURCE_NORTH_CONFIG_ZH      = "修改资源北向配置"
	MODIFY_RESOURCE_NORTH_CONFIG_FAIL_EN = "Modify Resource North Config Failed"
	MODIFY_RESOURCE_NORTH_CONFIG_FAIL_ZH = "修改资源北向配置失败"

	//About Freezer
	CREATE_FREEZER_JOB_EN             = "Create Freezer Job"
	CREATE_FREEZER_JOB_ZH             = "创建Freezer任务"
	CREATE_FREEZER_JOB_FAIL_EN        = "Create Freezer Job Failed"
	CREATE_FREEZER_JOB_FAIL_ZH        = "创建Freezer任务失败"
	OPERATE_FREEZER_JOB_EN            = "Operate Freezer Job"
	OPERATE_FREEZER_JOB_ZH            = "操作Freezer任务"
	OPERATE_FREEZER_JOB_FAIL_EN       = "Operate Freezer Job Failed"
	OPERATE_FREEZER_JOB_FAIL_ZH       = "操作Freezer任务失败"
	DELETE_FREEZER_JOB_EN             = "Delete Freezer Job"
	DELETE_FREEZER_JOB_ZH             = "删除Freezer任务"
	DELETE_FREEZER_JOB_FAIL_EN        = "Delete Freezer Job Failed"
	DELETE_FREEZER_JOB_FAIL_ZH        = "删除Freezer任务失败"
	DELETE_FREEZER_BACKUPDATA_EN      = "Delete Freezer Backup Data"
	DELETE_FREEZER_BACKUPDATA_ZH      = "删除Freezer备份数据"
	DELETE_FREEZER_BACKUPDATA_FAIL_EN = "Delete Freezer Backup Data Failed"
	DELETE_FREEZER_BACKUPDATA_FAIL_ZH = "删除Freezer备份数据失败"

	UPDATE_FREEZER_BACKUP_JOB_EN      = "Update Freezer Backup Job"
	UPDATE_FREEZER_BACKUP_JOB_ZH      = "修改Freezer备份任务"
	UPDATE_FREEZER_BACKUP_JOB_FAIL_EN = "Update Freezer Backup Job Failed"
	UPDATE_FREEZER_BACKUP_JOB_FAIL_ZH = "修改Freezer备份任务失败"

	//About Ecs
	ECS_EN             = "ECS"
	ECS_ZH             = "弹性云服务器"
	CREATE_ECS_EN      = "Create ECS"
	CREATE_ECS_ZH      = "创建弹性云服务器"
	CREATE_ECS_FAIL_EN = "Create ECS Failed"
	CREATE_ECS_FAIL_ZH = "创建弹性云服务器失败"

	//About Template
	TEMPLATE_EN                   = "Vm Template"
	TEMPLATE_ZH                   = "虚机模板"
	CREATE_TEMPLATE_EN            = "Create Vm Template"
	CREATE_TEMPLATE_ZH            = "创建虚机模板"
	CREATE_TEMPLATE_FAIL_EN       = "Create Vm Template Failed"
	CREATE_TEMPLATE_FAIL_ZH       = "创建虚机模板失败"
	DELETE_TEMPLATE_EN            = "Delete Vm Template"
	DELETE_TEMPLATE_ZH            = "删除虚机模板"
	DELETE_TEMPLATE_FAIL_EN       = "Delete Vm Template Failed"
	DELETE_TEMPLATE_FAIL_ZH       = "删除虚机模板失败"
	UPDATE_TEMPLATE_EN            = "Update Vm Template"
	UPDATE_TEMPLATE_ZH            = "更新虚机模板"
	UPDATE_TEMPLATE_FAIL_EN       = "Update Vm Template Failed"
	UPDATE_TEMPLATE_FAIL_ZH       = "更新虚机模板失败"
	CREATE_VM_BY_TEMPLATE_EN      = "Create Vm by Template"
	CREATE_VM_BY_TEMPLATE_ZH      = "使用模板创建虚机"
	CREATE_VM_BY_TEMPLATE_FAIL_EN = "Create Vm by Template Failed"
	CREATE_VM_BY_TEMPLATE_FAIL_ZH = "使用模板创建虚机失败"

	BATCH_DELETE_TEMPLATES_EN      = "Batch Delete Templates"
	BATCH_DELETE_TEMPLATES_ZH      = "批量删除模板"
	BATCH_DELETE_TEMPLATES_FAIL_EN = "Batch Delete Templates Failed"
	BATCH_DELETE_TEMPLATES_FAIL_ZH = "批量删除模板失败"

	// About volume group
	VOLUME_GROUP_EN                   = "Volume Group"
	VOLUME_GROUP_ZH                   = "云硬盘组"
	CREATE_VOLUME_GROUP_EN            = "Create Volume Group"
	CREATE_VOLUME_GROUP_ZH            = "创建云硬盘组"
	CREATE_VOLUME_GROUP_FAIL_EN       = "Create Volume Group Failed"
	CREATE_VOLUME_GROUP_FAIL_ZH       = "创建云硬盘组失败"
	DELETE_VOLUME_GROUP_EN            = "Delete Volume Group"
	DELETE_VOLUME_GROUP_ZH            = "删除云硬盘组"
	DELETE_VOLUME_GROUP_FAIL_EN       = "Delete Volume Group Failed"
	DELETE_VOLUME_GROUP_FAIL_ZH       = "删除云硬盘组失败"
	BATCH_DELETE_VOLUME_GROUP_EN      = "Batch Delete Volume Group"
	BATCH_DELETE_VOLUME_GROUP_ZH      = "批量删除云硬盘组"
	BATCH_DELETE_VOLUME_GROUP_FAIL_EN = "Batch Delete Volume Group Failed"
	BATCH_DELETE_VOLUME_GROUP_FAIL_ZH = "批量删除云硬盘组失败"
	UPDATE_VOLUME_GROUP_EN            = "Update Volume Group"
	UPDATE_VOLUME_GROUP_ZH            = "更新云硬盘组"
	UPDATE_VOLUME_GROUP_FAIL_EN       = "Update Volume Group Failed"
	UPDATE_VOLUME_GROUP_FAIL_ZH       = "更新云硬盘组失败"

	SUBSCTIPTION_EN             = "Subscription"
	SUBSCTIPTION_ZH             = "订阅"
	CREATE_SUBSCRIPTION_EN      = "Create Subscription"
	CREATE_SUBSCRIPTION_ZH      = "创建订阅"
	CREATE_SUBSCRIPTION_FAIL_EN = "Create Subscription Failed"
	CREATE_SUBSCRIPTION_FAIL_ZH = "创建订阅失败"
	DELETE_SUBSCRIPTION_EN      = "Delete Subscription"
	DELETE_SUBSCRIPTION_ZH      = "删除订阅"
	DELETE_SUBSCRIPTION_FAIL_EN = "Delete Subscription Failed"
	DELETE_SUBSCRIPTION_FAIL_ZH = "删除订阅失败"

	FTP_EN          = "FTP"
	FTP_ZH          = "FTP"
	SET_FTP_EN      = "Set FTP"
	SET_FTP_ZH      = "设置FTP"
	SET_FTP_FAIL_EN = "Set FTP Failed"
	SET_FTP_FAIL_ZH = "设置FTP失败"

	// About volume group snapshot
	VOLUME_GROUP_SNAPSHOT_EN                   = "Volume Group Snapshot"
	VOLUME_GROUP_SNAPSHOT_ZH                   = "云硬盘组快照"
	CREATE_VOLUME_GROUP_SNAPSHOT_EN            = "Create Volume Group Snapshot"
	CREATE_VOLUME_GROUP_SNAPSHOT_ZH            = "创建云硬盘组快照"
	CREATE_VOLUME_GROUP_SNAPSHOT_FAIL_EN       = "Create Volume Group Snapshot Failed"
	CREATE_VOLUME_GROUP_SNAPSHOT_FAIL_ZH       = "创建云硬盘组快照失败"
	DELETE_VOLUME_GROUP_SNAPSHOT_EN            = "Delete Volume Group Snapshot"
	DELETE_VOLUME_GROUP_SNAPSHOT_ZH            = "删除云硬盘组快照"
	DELETE_VOLUME_GROUP_SNAPSHOT_FAIL_EN       = "Delete Volume Group Snapshot Failed"
	DELETE_VOLUME_GROUP_SNAPSHOT_FAIL_ZH       = "删除云硬盘组快照失败"
	BATCH_DELETE_VOLUME_GROUP_SNAPSHOT_EN      = "Batch Delete Volume Snapshot Group"
	BATCH_DELETE_VOLUME_GROUP_SNAPSHOT_ZH      = "批量删除云硬盘组快照"
	BATCH_DELETE_VOLUME_GROUP_SNAPSHOT_FAIL_EN = "Batch Delete Volume Group Snapshot Failed"
	BATCH_DELETE_VOLUME_GROUP_SNAPSHOT_FAIL_ZH = "批量删除云硬盘组快照失败"
	UPDATE_VOLUME_GROUP_SNAPSHOT_EN            = "Update Volume Group Snapshot"
	UPDATE_VOLUME_GROUP_SNAPSHOT_ZH            = "更新云硬盘组快照"
	UPDATE_VOLUME_GROUP_SNAPSHOT_FAIL_EN       = "Update Volume Group Snapshot Failed"
	UPDATE_VOLUME_GROUP_SNAPSHOT_FAIL_ZH       = "更新云硬盘组快照失败"

	//About Backup
	BACKUP_EN                            = "Cloud Backup"
	BACKUP_ZH                            = "云备份"
	CREATE_BACKUP_JOB_EN                 = "Create Backup Job"
	CREATE_BACKUP_JOB_ZH                 = "创建备份任务"
	CREATE_BACKUP_JOB_FAIL_EN            = "Create Backup Job Failed"
	CREATE_BACKUP_JOB_FAIL_ZH            = "创建备份任务失败"
	DELETE_BACKUP_JOB_EN                 = "Delete Backup Job"
	DELETE_BACKUP_JOB_ZH                 = "删除备份任务"
	DELETE_BACKUP_JOB_FAIL_EN            = "Delete Backup Job Failed"
	DELETE_BACKUP_JOB_FAIL_ZH            = "删除备份任务失败"
	BACKUP_STRATEGY_EN                   = "Backup Strategy"
	BACKUP_STRATEGY_ZH                   = "备份策略"
	CREATE_BACKUP_STRATEGY_EN            = "Create Backup Strategy"
	CREATE_BACKUP_STRATEGY_ZH            = "创建备份策略"
	CREATE_BACKUP_STRATEGY_FAIL_EN       = "Create Backup Strategy Failed"
	CREATE_BACKUP_STRATEGY_FAIL_ZH       = "创建备份策略失败"
	UPDATE_BACKUP_STRATEGY_EN            = "Update Backup Strategy"
	UPDATE_BACKUP_STRATEGY_ZH            = "更新备份策略"
	UPDATE_BACKUP_STRATEGY_FAIL_EN       = "Update Backup Strategy Failed"
	UPDATE_BACKUP_STRATEGY_FAIL_ZH       = "更新备份策略失败"
	DELETE_BACKUP_STRATEGY_EN            = "Delete Backup Strategy"
	DELETE_BACKUP_STRATEGY_ZH            = "删除备份策略"
	DELETE_BACKUP_STRATEGY_FAIL_EN       = "Delete Backup Strategy Failed"
	DELETE_BACKUP_STRATEGY_FAIL_ZH       = "删除备份策略失败"
	BATCH_DELETE_BACKUP_STRATEGY_EN      = "Batch Delete Backup Strategy"
	BATCH_DELETE_BACKUP_STRATEGY_ZH      = "批量删除备份策略"
	BATCH_DELETE_BACKUP_STRATEGY_FAIL_EN = "Batch Delete Backup Strategy Failed"
	BATCH_DELETE_BACKUP_STRATEGY_FAIL_ZH = "批量删除备份策略失败"

	OPTOBJTYPE  = "x_ki18n_dir_log_optObjType"
	ENVNAME     = "x_k_dir_log_envName"
	ENVID       = "x_k_dir_log_envId"
	PROJECTNAME = "x_k_dir_log_projectName"
	PROJECTID   = "x_k_dir_log_projectId"
	VIMID       = "x_k_dir_log_vimId"
	LOGSOURCE   = "x_k_dir_log_logSource"
)

var (
	OperateCNMap = map[string]string{
		OPER_ADD_EN:    OPER_ADD_CN,
		OPER_UPDATE_EN: OPER_UPDATE_CN,
		OPER_DELETE_EN: OPER_DELETE_CN,
		OPER_OTHER_EN:  OPER_OTHER_CN,
	}

	OperateObjCNMap = map[string]string{
		IMAGE_EN:                        IMAGE_CN,
		IMAGE_FILE_EN:                   IMAGE_FILE_CN,
		VDC_IMAGE_EN:                    VDC_IMAGE_CN,
		CONTAINER_CLOUD_EN:              CONTAINER_CLOUD_ZH,
		NETWORK_CLOUD:                   NETWORK_CLOUD_ZH,
		PHYSICAL_ENV:                    PHYSICAL_ENV_ZH,
		CONTAINER_NODE_EN:               CONTAINER_NODE_ZH,
		AS_GROUP_EN:                     AS_GROUP_ZH,
		AS_CONFIG_EN:                    AS_CONFIG_ZH,
		AS_STRATEGY_EN:                  AS_STRATEGY_ZH,
		RESOURCE_TAG_EN:                 RESOURCE_TAG_ZH,
		DOCKING_SYSTEM_CONFIGURATION_EN: DOCKING_SYSTEM_CONFIGURATION_ZH,
		BRIDGE_EN:                       BRIDGE_CN,
		FREEZER_JOB_EN:                  FREEZER_JOB_ZH,
		FREEZER_BACKUP_DATA_EN:          FREEZER_BACKUP_DATA_ZH,
		OPENSTACK_CLOUD:                 OPENSTACK_CLOUD_ZH,
		DATA_CENTER_EN:           DATA_CENTER_ZH,
		DATA_CENTER_GROUP_EN:     DATA_CENTER_GROUP_ZH,
		TEMPLATE_EN:              TEMPLATE_ZH,
		SUBSCTIPTION_EN:          SUBSCTIPTION_ZH,
		FTP_EN:                   FTP_ZH,
		VOLUME_GROUP_EN:          VOLUME_GROUP_ZH,
		VOLUME_GROUP_SNAPSHOT_EN: VOLUME_GROUP_SNAPSHOT_ZH,
		BACKUP_EN:                BACKUP_ZH,
		BACKUP_STRATEGY_EN:       BACKUP_STRATEGY_ZH,
		ECS_EN:                   ECS_ZH,
		RESOURCE_NORTH_CONFIG_EN:          RESOURCE_NORTH_CONFIG_ZH,
	}

	OperateObjDescripyionMap = map[string]string{
		// About DC Group
		CREATE_DATA_CENTER_GROUP_EN: CREATE_DATA_CENTER_GROUP_ZH,
		UPDATE_DATA_CENTER_GROUP_EN: UPDATE_DATA_CENTER_GROUP_ZH,
		DELETE_DATA_CENTER_GROUP_EN: DELETE_DATA_CENTER_GROUP_ZH,

		// About DC
		CREATE_DATA_CENTER_EN: CREATE_DATA_CENTER_ZH,
		UPDATE_DATA_CENTER_EN: UPDATE_DATA_CENTER_ZH,
		DELETE_DATA_CENTER_EN: DELETE_DATA_CENTER_ZH,

		// About Image
		PATCH_PUBLISH_IMAGE_EN:           PATCH_PUBLISH_IMAGE_ZH,
		DELETE_PUBLISH_IMAGE_EN:          DELETE_PUBLISH_IMAGE_ZH,
		DOWNLOAD_PUBLISH_IMAGE_EN:        DOWNLOAD_PUBLISH_IMAGE_ZH,
		RESUME_TRANSFER_PUBLISH_IMAGE_EN: RESUME_TRANSFER_PUBLISH_IMAGE_ZH,
		PUBLISH_IMAGE_EN:                 PUBLISH_IMAGE_ZH,

		// About File
		MODIFY_FILE_EN: MODIFY_FILE_ZH,
		DELETE_FILE_EN: DELETE_FILE_ZH,
		UPLOAD_FILE_EN: UPLOAD_FILE_ZH,

		// About Vdc Image
		ORG_USER_MODIFY_VDCIMAGE_EN: ORG_USER_MODIFY_VDCIMAGE_ZH,
		ORG_USER_CREATE_VDCIMAGE_EN: ORG_USER_CREATE_VDCIMAGE_ZH,
		ORG_USER_DELETE_VDCIMAGE_EN: ORG_USER_DELETE_VDCIMAGE_ZH,

		// About Openstack
		CREATE_OPENSTACK_CLOUD_EN:                        CREATE_OPENSTACK_CLOUD_ZH,
		DELETE_OPENSTACK_CLOUD_EN:                        DELETE_OPENSTACK_CLOUD_ZH,
		MODIFY_OPENSTACK_CLOUd_API_VERSION_EN:            MODIFY_OPENSTACK_CLOUd_API_VERSION_ZH,
		MODIFY_OPENSTACK_CLOUD_INFO_EN:                   MODIFY_OPENSTACK_CLOUD_INFO_ZH,
		MODIFY_OPENSTACK_CLOUD_HA_POLICY_EN:              MODIFY_OPENSTACK_CLOUD_HA_POLICY_ZH,
		MODIFY_OPENSTACK_CLOUD_LABELS_EN:                 MODIFY_OPENSTACK_CLOUD_LABELS_ZH,
		MODIFY_OPENSTACK_CLOUD_PASSWORD_EN:               MODIFY_OPENSTACK_CLOUD_PASSWORD_ZH,
		MODIFY_OPENSTACK_CLOUD_POOL_PORTAL_URL_CONFIG_EN: MODIFY_OPENSTACK_CLOUD_POOL_PORTAL_URL_CONFIG_ZH,
		MODIFY_OPENSTACK_CLOUD_SPECIAL_VIM_ID_EN:         MODIFY_OPENSTACK_CLOUD_SPECIAL_VIM_ID_ZH,
		MODIFY_OPENSTACK_CLOUD_SSLAUTH_EN:                MODIFY_OPENSTACK_CLOUD_SSLAUTH_ZH,
		MODIFY_OPENSTACK_CLOUD_SSO_INFO_EN:               MODIFY_OPENSTACK_CLOUD_SSO_INFO_ZH,
		MODIFY_OPENSTACK_CLOUD_VM_BACKUP_CONFIG_EN:       MODIFY_OPENSTACK_CLOUD_VM_BACKUP_CONFIG_ZH,

		// About OpenPalette
		CREATE_CONTAINER_CLOUD_EN:          CREATE_CONTAINER_CLOUD_ZH,
		DELETE_CONTAINER_CLOUD_EN:          DELETE_CONTAINER_CLOUD_ZH,
		MODIFY_CONTAINER_CLOUD_INFO_EN:     MODIFY_CONTAINER_CLOUD_INFO_ZH,
		MODIFY_CONTAINER_CLOUD_PASSWORD_EN: MODIFY_CONTAINER_CLOUD_PASSWORD_ZH,
		MODIFY_CONTAINER_CLOUD_LABELS_EN:   MODIFY_CONTAINER_CLOUD_LABELS_ZH,
		MODIFY_CONTAINER_CLOUD_SSLAUTH_EN:  MODIFY_CONTAINER_CLOUD_SSLAUTH_ZH,
		MODIFY_CONTAINER_NODE_LABELS_EN:    MODIFY_CONTAINER_NODE_LABELS_ZH,

		// About Network Cloud
		CREATE_NETWORK_CLOUD_EN:                        CREATE_NETWORK_CLOUD_ZH,
		DELETE_NETWORK_CLOUD_EN:                        DELETE_NETWORK_CLOUD_ZH,
		MODIFY_NETWORK_CLOUD_INFO_EN:                   MODIFY_NETWORK_CLOUD_INFO_ZH,
		MODIFY_NETWORK_CLOUD_LABELS_EN:                 MODIFY_NETWORK_CLOUD_LABELS_ZH,
		MODIFY_NETWORK_CLOUd_API_VERSION_EN:            MODIFY_NETWORK_CLOUd_API_VERSION_ZH,
		MODIFY_NETWORK_CLOUD_HA_POLICY_EN:              MODIFY_NETWORK_CLOUD_HA_POLICY_ZH,
		MODIFY_NETWORK_CLOUD_PASSWORD_EN:               MODIFY_NETWORK_CLOUD_PASSWORD_ZH,
		MODIFY_NETWORK_CLOUD_POOL_PORTAL_URL_CONFIG_EN: MODIFY_NETWORK_CLOUD_POOL_PORTAL_URL_CONFIG_ZH,
		MODIFY_NETWORK_CLOUD_SPECIAL_VIM_ID_EN:         MODIFY_NETWORK_CLOUD_SPECIAL_VIM_ID_ZH,
		MODIFY_NETWORK_CLOUD_SSLAUTH_EN:                MODIFY_NETWORK_CLOUD_SSLAUTH_ZH,
		MODIFY_NETWORK_CLOUD_SSO_INFO_EN:               MODIFY_NETWORK_CLOUD_SSO_INFO_ZH,
		MODIFY_NETWORK_CLOUD_VM_BACKUP_CONFIG_EN:       MODIFY_NETWORK_CLOUD_VM_BACKUP_CONFIG_ZH,

		// About Physical Network Cloud
		CREATE_PHYSICAL_ENV_EN:          CREATE_PHYSICAL_ENV_ZH,
		DELETE_PHYSICAL_ENV_EN:          DELETE_PHYSICAL_ENV_ZH,
		MODIFY_PHYSICAL_ENV_INFO_EN:     MODIFY_PHYSICAL_ENV_INFO_ZH,
		MODIFY_PHYSICAL_ENV_PASSWORD_EN: MODIFY_PHYSICAL_ENV_PASSWORD_ZH,
		MODIFY_PHYSICAL_ENV_SSLAUTH_EN:  MODIFY_PHYSICAL_ENV_SSLAUTH_ZH,

		// About Bridge
		BACKUP_NOW_EN:                    BACKUP_NOW_ZH,
		CREATE_BACKUP_TASK_EN:            CREATE_BACKUP_TASK_ZH,
		DELETE_BACKUP_TASK_EN:            DELETE_BACKUP_TASK_ZH,
		UPDATE_BACKUP_TASK_EN:            UPDATE_BACKUP_TASK_ZH,
		RESTORE_VM_EN:                    RESTORE_VM_ZH,
		DELETE_BACKUP_EN:                 DELETE_BACKUP_ZH,
		CREATE_AUTO_DELETION_STRATEGY_EN: CREATE_AUTO_DELETION_STRATEGY_ZH,
		DELETE_AUTO_DELETION_STRATEGY_EN: DELETE_AUTO_DELETION_STRATEGY_ZH,
		EXPORT_BACKUP_STATISTICS_EN:      EXPORT_BACKUP_STATISTICS_ZH,

		// About ass
		CREATE_AS_GROUP_EN:    CREATE_AS_GROUP_ZH,
		DELETE_AS_GROUP_EN:    DELETE_AS_GROUP_ZH,
		OPERATE_AS_GROUP_EN:   OPERATE_AS_GROUP_ZH,
		CREATE_AS_CONFIG_EN:   CREATE_AS_CONFIG_ZH,
		DELETE_AS_CONFIG_EN:   DELETE_AS_CONFIG_ZH,
		CREATE_AS_STRATEGY_EN: CREATE_AS_STRATEGY_ZH,
		DELETE_AS_STRATEGY_EN: DELETE_AS_STRATEGY_ZH,
		UPDATE_AS_STRATEGY_EN: UPDATE_AS_STRATEGY_ZH,

		// About tms
		UPDATE_RESOURCE_TAG_EN:           UPDATE_RESOURCE_TAG_ZH,
		PREDEFINE_RESOURCE_TAG_EN:        PREDEFINE_RESOURCE_TAG_ZH,
		DELETE_PREDEFINE_RESOURCE_TAG_EN: DELETE_PREDEFINE_RESOURCE_TAG_ZH,

		// About Docking System Configuration
		CREATE_DOCKING_SYSTEM_CONFIGURATION_EN: CREATE_DOCKING_SYSTEM_CONFIGURATION_ZH,
		UPDATE_DOCKING_SYSTEM_CONFIGURATION_EN: UPDATE_DOCKING_SYSTEM_CONFIGURATION_ZH,
		DELETE_DOCKING_SYSTEM_CONFIGURATION_EN: DELETE_DOCKING_SYSTEM_CONFIGURATION_ZH,

		// About Freezer
		CREATE_FREEZER_JOB_EN:        CREATE_FREEZER_JOB_ZH,
		OPERATE_FREEZER_JOB_EN:       OPERATE_FREEZER_JOB_ZH,
		DELETE_FREEZER_JOB_EN:        DELETE_FREEZER_JOB_ZH,
		DELETE_FREEZER_BACKUPDATA_EN: DELETE_FREEZER_BACKUPDATA_ZH,
		UPDATE_FREEZER_BACKUP_JOB_EN: UPDATE_FREEZER_BACKUP_JOB_ZH,

		// About Template
		CREATE_TEMPLATE_EN:        CREATE_TEMPLATE_ZH,
		DELETE_TEMPLATE_EN:        DELETE_TEMPLATE_ZH,
		UPDATE_TEMPLATE_EN:        UPDATE_TEMPLATE_ZH,
		BATCH_DELETE_TEMPLATES_EN: BATCH_DELETE_TEMPLATES_ZH,

		// About Nfvo
		CREATE_SUBSCRIPTION_EN: CREATE_SUBSCRIPTION_ZH,
		DELETE_SUBSCRIPTION_EN: DELETE_SUBSCRIPTION_ZH,
		SET_FTP_EN:             SET_FTP_ZH,

		CREATE_VOLUME_GROUP_EN:       CREATE_VOLUME_GROUP_ZH,
		DELETE_VOLUME_GROUP_EN:       DELETE_VOLUME_GROUP_ZH,
		BATCH_DELETE_VOLUME_GROUP_EN: BATCH_DELETE_VOLUME_GROUP_ZH,
		UPDATE_VOLUME_GROUP_EN:       UPDATE_VOLUME_GROUP_ZH,

		CREATE_VOLUME_GROUP_SNAPSHOT_EN:       CREATE_VOLUME_GROUP_SNAPSHOT_ZH,
		DELETE_VOLUME_GROUP_SNAPSHOT_EN:       DELETE_VOLUME_GROUP_SNAPSHOT_ZH,
		BATCH_DELETE_VOLUME_GROUP_SNAPSHOT_EN: BATCH_DELETE_VOLUME_GROUP_SNAPSHOT_ZH,
		UPDATE_VOLUME_GROUP_SNAPSHOT_EN:       UPDATE_VOLUME_GROUP_SNAPSHOT_ZH,

		// About Backup
		CREATE_BACKUP_JOB_EN: CREATE_BACKUP_JOB_ZH,
		DELETE_BACKUP_JOB_EN: DELETE_BACKUP_JOB_ZH,

		// About Backup Strategy
		CREATE_BACKUP_STRATEGY_EN:       CREATE_BACKUP_STRATEGY_ZH,
		DELETE_BACKUP_STRATEGY_EN:       DELETE_BACKUP_STRATEGY_ZH,
		UPDATE_BACKUP_STRATEGY_EN:       UPDATE_BACKUP_STRATEGY_ZH,
		BATCH_DELETE_BACKUP_STRATEGY_EN: BATCH_DELETE_BACKUP_STRATEGY_ZH,

		// About Meo Config
		CREATE_RESOURCE_NORTH_CONFIG_EN: CREATE_RESOURCE_NORTH_CONFIG_ZH,
		MODIFY_RESOURCE_NORTH_CONFIG_EN: MODIFY_RESOURCE_NORTH_CONFIG_ZH,

		// About Ecs
		CREATE_ECS_EN: CREATE_ECS_ZH,
	}

	FailReasonCNMap = map[string]string{
		// About DC Group
		CREATE_DATA_CENTER_GROUP_FAIL_EN: CREATE_DATA_CENTER_GROUP_FAIL_ZH,
		UPDATE_DATA_CENTER_GROUP_FAIL_EN: UPDATE_DATA_CENTER_GROUP_FAIL_ZH,
		DELETE_DATA_CENTER_GROUP_FAIL_EN: DELETE_DATA_CENTER_GROUP_FAIL_ZH,

		// About DC
		CREATE_DATA_CENTER_FAIL_EN: CREATE_DATA_CENTER_FAIL_ZH,
		UPDATE_DATA_CENTER_FAIL_EN: UPDATE_DATA_CENTER_FAIL_ZH,
		DELETE_DATA_CENTER_FAIL_EN: DELETE_DATA_CENTER_FAIL_ZH,

		// About Image
		PATCH_PUBLISH_IMAGE_FAIL_EN:           PATCH_PUBLISH_IMAGE_FAIL_ZH,
		DELETE_PUBLISH_IMAGE_FAIL_EN:          DELETE_PUBLISH_IMAGE_FAIL_ZH,
		DOWNLOAD_PUBLISH_IMAGE_FAIL_EN:        DOWNLOAD_PUBLISH_IMAGE_FAIL_ZH,
		RESUME_TRANSFER_PUBLISH_IMAGE_FAIL_EN: RESUME_TRANSFER_PUBLISH_IMAGE_FAIL_ZH,
		PUBLISH_IMAGE_FAIL_EN:                 PUBLISH_IMAGE_FAIL_ZH,

		// About File
		MODIFY_FILE_FAIL_EN: MODIFY_FILE_FAIL_ZH,
		DELETE_FILE_FAIL_EN: DELETE_FILE_FAIL_ZH,
		UPLOAD_FILE_FAIL_EN: UPLOAD_FILE_FAIL_ZH,

		// About Vdc Image
		ORG_USER_MODIFY_VDCIMAGE_FAIL_EN: ORG_USER_MODIFY_VDCIMAGE_FAIL_ZH,
		ORG_USER_CREATE_VDCIMAGE_FAIL_EN: ORG_USER_CREATE_VDCIMAGE_FAIL_ZH,
		ORG_USER_DELETE_VDCIMAGE_FAIL_EN: ORG_USER_DELETE_VDCIMAGE_FAIL_ZH,

		// About Openstack
		CREATE_OPENSTACK_CLOUD_FAIL_EN:                        CREATE_OPENSTACK_CLOUD_FAIL_ZH,
		DELETE_OPENSTACK_CLOUD_FAIL_EN:                        DELETE_OPENSTACK_CLOUD_FAIL_ZH,
		MODIFY_OPENSTACK_CLOUd_API_VERSION_FAIL_EN:            MODIFY_OPENSTACK_CLOUd_API_VERSION_FAIL_ZH,
		MODIFY_OPENSTACK_CLOUD_INFO_FAIL_EN:                   MODIFY_OPENSTACK_CLOUD_INFO_FAIL_ZH,
		MODIFY_OPENSTACK_CLOUD_HA_POLICY_FAIL_EN:              MODIFY_OPENSTACK_CLOUD_HA_POLICY_FAIL_ZH,
		MODIFY_OPENSTACK_CLOUD_LABELS_FAIL_EN:                 MODIFY_OPENSTACK_CLOUD_LABELS_FAIL_ZH,
		MODIFY_OPENSTACK_CLOUD_PASSWORD_FAIL_EN:               MODIFY_OPENSTACK_CLOUD_PASSWORD_FAIL_ZH,
		MODIFY_OPENSTACK_CLOUD_POOL_PORTAL_URL_CONFIG_FAIL_EN: MODIFY_OPENSTACK_CLOUD_POOL_PORTAL_URL_CONFIG_FAIL_ZH,
		MODIFY_OPENSTACK_CLOUD_SPECIAL_VIM_ID_FAIL_EN:         MODIFY_OPENSTACK_CLOUD_SPECIAL_VIM_ID_FAIL_ZH,
		MODIFY_OPENSTACK_CLOUD_SSLAUTH_FAIL_EN:                MODIFY_OPENSTACK_CLOUD_SSLAUTH_FAIL_ZH,
		MODIFY_OPENSTACK_CLOUD_SSO_INFO_FAIL_EN:               MODIFY_OPENSTACK_CLOUD_SSO_INFO_FAIL_ZH,
		MODIFY_OPENSTACK_CLOUD_VM_BACKUP_CONFIG_FAIL_EN:       MODIFY_OPENSTACK_CLOUD_VM_BACKUP_CONFIG_FAIL_ZH,

		// About OpenPalette
		CREATE_CONTAINER_CLOUD_FAIL_EN:          CREATE_CONTAINER_CLOUD_FAIL_ZH,
		DELETE_CONTAINER_CLOUD_FAIL_EN:          DELETE_CONTAINER_CLOUD_FAIL_ZH,
		MODIFY_CONTAINER_CLOUD_INFO_FAIL_EN:     MODIFY_CONTAINER_CLOUD_INFO_FAIL_ZH,
		MODIFY_CONTAINER_CLOUD_PASSWORD_FAIL_EN: MODIFY_CONTAINER_CLOUD_PASSWORD_FAIL_ZH,
		MODIFY_CONTAINER_CLOUD_LABELS_FAIL_EN:   MODIFY_CONTAINER_CLOUD_LABELS_FAIL_ZH,
		MODIFY_CONTAINER_CLOUD_SSLAUTH_FAIL_EN:  MODIFY_CONTAINER_CLOUD_SSLAUTH_FAIL_ZH,
		MODIFY_CONTAINER_NODE_LABELS_FAIL_EN:    MODIFY_CONTAINER_NODE_LABELS_FAIL_ZH,

		// About Network Cloud
		CREATE_NETWORK_CLOUD_FAIL_EN:                        CREATE_NETWORK_CLOUD_FAIL_ZH,
		DELETE_NETWORK_CLOUD_FAIL_EN:                        DELETE_NETWORK_CLOUD_FAIL_ZH,
		MODIFY_NETWORK_CLOUD_INFO_FAIL_EN:                   MODIFY_NETWORK_CLOUD_INFO_FAIL_ZH,
		MODIFY_NETWORK_CLOUD_VM_BACKUP_CONFIG_FAIL_EN:       MODIFY_NETWORK_CLOUD_VM_BACKUP_CONFIG_FAIL_ZH,
		MODIFY_NETWORK_CLOUD_SSO_INFO_FAIL_EN:               MODIFY_NETWORK_CLOUD_SSO_INFO_FAIL_ZH,
		MODIFY_NETWORK_CLOUD_SSLAUTH_FAIL_EN:                MODIFY_NETWORK_CLOUD_SSLAUTH_FAIL_ZH,
		MODIFY_NETWORK_CLOUD_SPECIAL_VIM_ID_FAIL_EN:         MODIFY_NETWORK_CLOUD_SPECIAL_VIM_ID_FAIL_ZH,
		MODIFY_NETWORK_CLOUD_PASSWORD_FAIL_EN:               MODIFY_NETWORK_CLOUD_PASSWORD_FAIL_ZH,
		MODIFY_NETWORK_CLOUD_POOL_PORTAL_URL_CONFIG_FAIL_EN: MODIFY_NETWORK_CLOUD_POOL_PORTAL_URL_CONFIG_FAIL_ZH,
		MODIFY_NETWORK_CLOUD_HA_POLICY_FAIL_EN:              MODIFY_NETWORK_CLOUD_HA_POLICY_FAIL_ZH,
		MODIFY_NETWORK_CLOUd_API_VERSION_FAIL_EN:            MODIFY_NETWORK_CLOUd_API_VERSION_FAIL_ZH,
		MODIFY_NETWORK_CLOUD_LABELS_FAIL_EN:                 MODIFY_NETWORK_CLOUD_LABELS_FAIL_ZH,

		// About Physical Env
		CREATE_PHYSICAL_ENV_FAIL_EN:          CREATE_PHYSICAL_ENV_FAIL_ZH,
		DELETE_PHYSICAL_ENV_FAIL_EN:          DELETE_PHYSICAL_ENV_FAIL_ZH,
		MODIFY_PHYSICAL_ENV_INFO_FAIL_EN:     MODIFY_PHYSICAL_ENV_INFO_FAIL_ZH,
		MODIFY_PHYSICAL_ENV_PASSWORD_FAIL_EN: MODIFY_PHYSICAL_ENV_PASSWORD_FAIL_ZH,
		MODIFY_PHYSICAL_ENV_SSLAUTH_FAIL_EN:  MODIFY_PHYSICAL_ENV_SSLAUTH_FAIL_ZH,

		// About Ass
		CREATE_AS_GROUP_FAIL_EN:    CREATE_AS_GROUP_FAIL_ZH,
		DELETE_AS_GROUP_FAIL_EN:    DELETE_AS_GROUP_FAIL_ZH,
		OPERATE_AS_GROUP_FAIL_EN:   OPERATE_AS_GROUP_FAIL_ZH,
		CREATE_AS_CONFIG_FAIL_EN:   CREATE_AS_CONFIG_FAIL_ZH,
		DELETE_AS_CONFIG_FAIL_EN:   DELETE_AS_CONFIG_FAIL_ZH,
		CREATE_AS_STRATEGY_FAIL_EN: CREATE_AS_STRATEGY_FAIL_ZH,
		DELETE_AS_STRATEGY_FAIL_EN: DELETE_AS_STRATEGY_FAIL_ZH,
		UPDATE_AS_STRATEGY_FAIL_EN: UPDATE_AS_STRATEGY_FAIL_ZH,

		//About Tms
		UPDATE_RESOURCE_TAG_FAIL_EN:           UPDATE_RESOURCE_TAG_FAIL_ZH,
		PREDEFINE_RESOURCE_TAG_FAIL_EN:        PREDEFINE_RESOURCE_TAG_FAIL_ZH,
		DELETE_PREDEFINE_RESOURCE_TAG_FAIL_EN: DELETE_PREDEFINE_RESOURCE_TAG_FAIL_ZH,

		// About Docking System Configuration
		CREATE_DOCKING_SYSTEM_CONFIGURATION_FAIL_EN: CREATE_DOCKING_SYSTEM_CONFIGURATION_FAIL_ZH,
		UPDATE_DOCKING_SYSTEM_CONFIGURATION_FAIL_EN: UPDATE_DOCKING_SYSTEM_CONFIGURATION_FAIL_ZH,
		DELETE_DOCKING_SYSTEM_CONFIGURATION_FAIL_EN: DELETE_DOCKING_SYSTEM_CONFIGURATION_FAIL_ZH,

		// About Freezer
		CREATE_FREEZER_JOB_FAIL_EN:        CREATE_FREEZER_JOB_FAIL_ZH,
		DELETE_FREEZER_JOB_FAIL_EN:        DELETE_FREEZER_JOB_FAIL_ZH,
		OPERATE_FREEZER_JOB_FAIL_EN:       OPERATE_FREEZER_JOB_FAIL_ZH,
		DELETE_FREEZER_BACKUPDATA_FAIL_EN: DELETE_FREEZER_BACKUPDATA_FAIL_ZH,
		UPDATE_FREEZER_BACKUP_JOB_FAIL_EN: UPDATE_FREEZER_BACKUP_JOB_FAIL_ZH,

		// About Template
		CREATE_TEMPLATE_FAIL_EN: CREATE_TEMPLATE_FAIL_ZH,
		DELETE_TEMPLATE_FAIL_EN: DELETE_TEMPLATE_FAIL_ZH,
		UPDATE_TEMPLATE_FAIL_EN: UPDATE_TEMPLATE_FAIL_ZH,

		CREATE_VOLUME_GROUP_FAIL_EN:       CREATE_VOLUME_GROUP_FAIL_ZH,
		DELETE_VOLUME_GROUP_FAIL_EN:       BATCH_DELETE_VOLUME_GROUP_FAIL_ZH,
		BATCH_DELETE_VOLUME_GROUP_FAIL_EN: BATCH_DELETE_VOLUME_GROUP_FAIL_ZH,
		UPDATE_VOLUME_GROUP_FAIL_EN:       UPDATE_VOLUME_GROUP_FAIL_ZH,

		CREATE_VOLUME_GROUP_SNAPSHOT_FAIL_EN:       CREATE_VOLUME_GROUP_SNAPSHOT_FAIL_ZH,
		DELETE_VOLUME_GROUP_SNAPSHOT_FAIL_EN:       DELETE_VOLUME_GROUP_SNAPSHOT_FAIL_ZH,
		BATCH_DELETE_VOLUME_GROUP_SNAPSHOT_FAIL_EN: BATCH_DELETE_VOLUME_GROUP_SNAPSHOT_FAIL_ZH,
		UPDATE_VOLUME_GROUP_SNAPSHOT_FAIL_EN:       UPDATE_VOLUME_GROUP_SNAPSHOT_FAIL_ZH,

		// About Backup
		CREATE_BACKUP_JOB_FAIL_EN: CREATE_BACKUP_JOB_FAIL_ZH,
		DELETE_BACKUP_JOB_FAIL_EN: DELETE_BACKUP_JOB_FAIL_ZH,

		// About nfvo
		CREATE_SUBSCRIPTION_FAIL_EN: CREATE_SUBSCRIPTION_FAIL_ZH,
		DELETE_SUBSCRIPTION_FAIL_EN: DELETE_SUBSCRIPTION_FAIL_ZH,
		SET_FTP_FAIL_EN:             SET_FTP_FAIL_ZH,

		// About Meo Config
		CREATE_RESOURCE_NORTH_CONFIG_FAIL_EN: CREATE_RESOURCE_NORTH_CONFIG_FAIL_ZH,
		MODIFY_RESOURCE_NORTH_CONFIG_FAIL_EN: MODIFY_RESOURCE_NORTH_CONFIG_FAIL_ZH,

		// About Ecs
		CREATE_ECS_FAIL_EN: CREATE_ECS_FAIL_ZH,
	}
)

type InternationalField struct {
	En_US string `json:"en_US"`
	Zh_CN string `json:"zh_CN"`
}

func GetOperJson(oper, resourceType string) string {
	opretion := InternationalField{}
	if strings.EqualFold(oper, "Other") {
		opretion.En_US = "operate " + resourceType
	} else {
		opretion.En_US = oper + " " + resourceType
		if _, ok := OperateObjCNMap[resourceType]; !ok {
			logger.Errorf("Invalid resourceType: %s", resourceType)
			return ""
		}
	}
	if _, ok := OperateCNMap[oper]; !ok {
		logger.Errorf("Invalid operate: %s", oper)
		return ""
	}
	if strings.EqualFold(oper, "Other") {
		opretion.Zh_CN = "操作" + OperateObjCNMap[resourceType]
	} else {
		opretion.Zh_CN = OperateCNMap[oper] + OperateObjCNMap[resourceType]
	}
	o, _ := json.Marshal(opretion)
	return string(o)
}

func GetOperDescriptionInfo(decription string) string {
	decriptionInfo := InternationalField{}
	decriptionInfo.En_US = decription
	if _, ok := OperateObjDescripyionMap[decription]; !ok {
		logger.Errorf("Invalid decriptionInfo: %s", decription)
		return ""
	}
	decriptionInfo.Zh_CN = OperateObjDescripyionMap[decription]
	d, _ := json.Marshal(decriptionInfo)
	return string(d)
}

func GetOperDetail(optObj *OptObjContent) string {
	detail := InternationalField{}
	var detailStr []string
	if optObj.EnvName != "" || optObj.EnvID != "" {
		detailStr = append(detailStr, "envName:"+optObj.EnvName+", envId:"+optObj.EnvID)
	}

	if optObj.TenantName != "" || optObj.TenantID != "" {
		detailStr = append(detailStr, "tenantName:"+optObj.TenantName+", tenantId:"+optObj.TenantID)
	}

	if optObj.ObjName != "" || optObj.ObjID != "" {
		detailStr = append(detailStr, "objName:"+optObj.ObjName+", objId:"+optObj.ObjID)
	}

	if optObj.ObjNum != 0 {
		detailStr = append(detailStr, "objNum: "+strconv.Itoa(optObj.ObjNum))
	}

	if optObj.VdcID != "" || optObj.VdcName != "" {
		detailStr = append(detailStr, "objName:"+optObj.VdcName+", objId:"+optObj.VdcID)
	}

	detail.En_US = strings.Join(detailStr, ", ")
	detail.Zh_CN = strings.Join(detailStr, ", ")
	d, _ := json.Marshal(detail)
	return string(d)
}
func GetOperateResource(resourceType, name string) []string {
	res := make([]string, 0, 1)
	if _, ok := OperateObjCNMap[resourceType]; !ok {
		logger.Errorf("Invalid resourceType: %s", resourceType)
		return []string{}
	}
	res = append(res, OperateObjCNMap[resourceType]+": "+name)
	return res
}

func GetFailReason(reason string) string {
	failReason := InternationalField{}
	failReason.En_US = reason
	if _, ok := FailReasonCNMap[reason]; !ok {
		logger.Errorf("Invalid fail reason: %s", reason)
		return ""
	}
	failReason.Zh_CN = FailReasonCNMap[reason]
	r, _ := json.Marshal(failReason)
	return string(r)
}

func GetExtensionMsg(resourceType, logSource string, optObj *OptObjContent) map[string]interface{} {
	extension := make(map[string]interface{}, 7)
	objType := InternationalField{}
	if _, ok := OperateObjCNMap[resourceType]; !ok {
		logger.Errorf("Invalid resourceType: %s", resourceType)
		objType.En_US = UNKNOWN_EN
		objType.Zh_CN = UNKNOWN_CN
	} else {
		objType.En_US = resourceType
		objType.Zh_CN = OperateObjCNMap[resourceType]
	}
	r, _ := json.Marshal(objType)
	extension[OPTOBJTYPE] = r
	extension[ENVNAME] = optObj.EnvName
	extension[ENVID] = optObj.EnvID
	extension[PROJECTNAME] = optObj.TenantName
	extension[PROJECTID] = optObj.TenantID
	extension[LOGSOURCE] = logSource

	return extension
}

func GetOptObjType(resourceType string) map[string]string {
	optObjType := make(map[string]string, 2)
	if _, ok := OperateObjCNMap[resourceType]; !ok {
		logger.Errorf("Invalid resourceType: %s", resourceType)
		optObjType["En_US"] = UNKNOWN_EN
		optObjType["Zh_CN"] = UNKNOWN_CN
	} else {
		optObjType["en_US"] = resourceType
		optObjType["zh_CN"] = OperateObjCNMap[resourceType]
	}

	return optObjType
}
