package otcplog

import (
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/beego/beego/v2/server/web/context"
	"gitlab.zte.com.cn/oes/dexadf/logagent"
)

const (
	OptRankNormal        = "normal"
	OptRankNotice        = "notice"
	OptRankImportant     = "important"
	OptRankVeryImportant = "very important"
)

func InitOptLog() {
	absFilePath := util.GetFilePath("conf/operate_log.json")
	fileContent, err := util.ReadFile(absFilePath)
	if err != nil {
		logger.Errorf("InitOptLog Open operate_log.json failed:", err)
		return
	}

	err = json.Unmarshal(fileContent, &OptLogModel)
	if err != nil {
		logger.Errorf("InitOptLog json.Unmarshal OptLogModel failed:", err.Error())
		return
	}
}

func OptLogBeforeFilter(ctx *context.Context) {
	reqURL := ctx.Request.URL.Path + "_" + strings.ToUpper(ctx.Request.Method)
	for k, v := range OptLogModel {
		if ok, err := regexp.MatchString(k, reqURL); err == nil && ok {
			logInfo := v
			logInfo.StartTime = time.Now()
			SetOptLogToRequest(&logInfo, ctx.Request)
			return
		}
	}
	logger.Debugf("OptLogBeforeFilter: failed to get %s form OptLogInfo", reqURL)
}

func OptLogAfterFilter(ctx *context.Context) {
	if ctx.Request.Header.Get(globalcv.HeaderAccessToken) == "" &&
		ctx.Request.Header.Get(globalcv.ProviderHeaderAccessToken) == "" {
		logger.Debugf("no need to Record OptLog, token is null")
		return
	}

	logInfo := GetOptLogFromRequest(ctx.Request)
	if logInfo == nil {
		logger.Debugf("[OptLogAfterFilter] can not get logInfo from request")
		return
	}

	if len(logInfo.ObjInfo) == 0 {
		logger.Error("[OptLogAfterFilter] Obj Info is empty")
		return
	}

	logMsg, ok := logagent.NewOperLogMessage(getUserName(ctx.Request), logInfo.getOptJson(), logInfo.getOptJson(),
		getClientIp(ctx.Request), logInfo.getOptDetail(), getRankLevel(logInfo.Rank), logInfo.StartTime, time.Now())
	if !ok {
		logger.Errorf("Fail to create NewOperLogMessage, id: %d", logMsg.Id)
		return
	}

	if logInfo.LogSource == "OpenPalette" {
		logMsg.SetAppModule("{\"en_US\":\"OpenPalette\", \"zh_CN\": \"容器云平台\"}")
	} else {
		logMsg.SetAppModule("{\"en_US\":\"Resource management\", \"zh_CN\": \"资源管理\"}")
	}

	logMsg.SetConnectMode("WEB")
	logMsg.SetOperateType(logInfo.OptType)
	logMsg.SetOperateResource(logInfo.OptResource)
	logMsg.SetX_KI18N(OPTOBJTYPE, logInfo.GetOptObjType())

	tenantId, tenantName := logInfo.GetTenantIdName()
	logMsg.SetX_K(PROJECTID, tenantId)
	logMsg.SetX_K(PROJECTNAME, tenantName)

	envId, envName, vim := logInfo.GetEnvIdNameVim()
	logMsg.SetX_K(ENVNAME, envName)
	logMsg.SetX_K(ENVID, envId)
	logMsg.SetX_K(VIMID, vim)

	if ctx.ResponseWriter.Status >= 300 {
		logMsg.SetOperateResult(logagent.OPERLOG_ERROR)
		logMsg.SetFailReason(logInfo.getFailedOptJson())
	} else {
		logMsg.SetOperateResult(logagent.OPERLOG_SUSSESS)
	}
	_ = logagent.RecordLog(logMsg)
}

func GetOptLogFromRequest(request *http.Request) *OptLogInfo {
	optLog := &OptLogInfo{}
	if err := json.Unmarshal([]byte(request.Header.Get("logInfo")), optLog); err != nil {
		logger.Debugf("[GetOptLogFromRequest] failed to Unmarshal logInfo form OptLogInfo")
	}
	return optLog
}

func SetOptLogToRequest(optLog *OptLogInfo, request *http.Request) {
	request.Header.Set("logInfo", util.ToJSONStr(optLog))
}

func getRankLevel(rank string) int {
	switch rank {
	case "normal":
		return logagent.OPERLOG_RANK_NORMAL
	case "notice":
		return logagent.OPERLOG_RANK_NOTICE
	case "important":
		return logagent.OPERLOG_RANK_IMPORTANT
	case "very important":
		return logagent.OPERLOG_RANK_VERYIMPORTANT
	default:
		return logagent.OPERLOG_RANK_NORMAL
	}
}

func getUserName(request *http.Request) string {
	userName := request.Header.Get(globalcv.HeaderOperateuser)
	if userName == "" {
		userName = "unknown"
	}
	return userName
}

func getClientIp(request *http.Request) string {
	var clientIp string
	xForwardedFor := request.Header.Get(globalcv.HeaderXForwardedFor)
	xForwards := strings.Split(xForwardedFor, ",")
	if len(xForwards) > 0 {
		tempIP := xForwards[0]
		if strings.HasPrefix(tempIP, "::ffff:") {
			tempIP = strings.TrimLeft(tempIP, "::ffff:")
		}
		clientIp = tempIP
	} else {
		clientIp = request.RemoteAddr
	}
	if clientIp == "" {
		clientIp = "127.0.0.1"
	}
	return clientIp
}
