package msbservice

import (
	"os"
	"cwsm/tools/commontools/configure"
	"cwsm/tools/commontools/devconfig"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/opeanpalette/microservice"
)

func InitMSB(msbConf configure.MSBDto) {
	if os.Getenv(globalcv.GatewayHost) != "" {
		microservice.MsbIP = os.Getenv(globalcv.GatewayHost)
	} else {
		microservice.MsbIP = msbConf.MsbIP
	}
	microservice.MsbInnerPort = msbConf.MsbInnerPort
	microservice.MsbIP4Openpalett = os.Getenv(OpenpaletteMSBIP)
	microservice.MsbPort4Openpalett = os.Getenv(OpenpaletteMSBPort)
	microservice.NameSpace4Openpalett = os.Getenv(OpenpaletteNameSpace)

	//配置微服务内部调用ip地址，统一为msb内部调用地址
	globalcv.MsbURLPath = globalcv.HTTPPrefix + microservice.MsbIP + ":" + microservice.MsbInnerPort
	logger.Infof("msbURLPath: %s, msbIP4Openpalett: %s, msbPort4Openpalett: %s",
		globalcv.MsbURLPath, microservice.MsbIP4Openpalett, microservice.MsbPort4Openpalett)
}

func InitDevMsb(devConf configure.DevConfDto) {
	devconfig.DevMsbIP = devConf.MsbIP
	devconfig.DevMsbInnerPort = devConf.MsbInnerPort
	devconfig.DevMsbURLPath = "http://" + devconfig.DevMsbIP + ":" + devconfig.DevMsbInnerPort
}

func InitMicroServicesFromMsb() (map[string]globalcv.MsbRestClientInfoDto, error) {
	serviceURLPortMaps := make(map[string]globalcv.MsbRestClientInfoDto)
	for serviceName, port := range ServiceDefaultPortMap {
		msbInfo := globalcv.MsbRestClientInfoDto{}
		msbRes, err := microservice.GetMicroService(serviceName)
		if err != nil {
			logger.Errorf("InitMicroServicesFromMsb GetMicroService Service:%s failed:%s", serviceName, err)
			return serviceURLPortMaps, err
		}
		msbInfo.Port = port
		msbInfo.NameSpace = "director"
		msbInfo.ServiceName = msbRes.ServiceName
		msbInfo.URL = msbRes.URL + "/"

		if len(msbRes.Nodes) > 0 {
			msbInfo.Port = msbRes.Nodes[0].Port
		}
		if msbRes.NameSpace != "" {
			msbInfo.NameSpace = msbRes.NameSpace
		}
		logger.Debug("Query Micro Services urlPath:%s, msbInfo:%#v", msbInfo.URL, msbInfo)
		serviceURLPortMaps[msbInfo.URL] = msbInfo
	}
	return serviceURLPortMaps, nil
}
