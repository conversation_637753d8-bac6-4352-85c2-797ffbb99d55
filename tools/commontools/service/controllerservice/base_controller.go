package controllerservice

import (
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/infa/i18nutil"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"errors"
	"net/http"
	"strings"

	"github.com/beego/beego/v2/server/web"
)

type Response struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
}

type Controller struct {
	web.Controller
}

type ErrorResult struct {
	Code i18nutil.I18nKey `json:"code"`
	Args []string         `json:"args"`

	error `json:"err"`
}

func NewErrorResult(code string, err error, args ...string) *ErrorResult {
	return &ErrorResult{Code: i18nutil.I18nKey(code), error: err, Args: args}
}

func (c *Controller) GetAuthResourceIDs() []string {
	var authIDs []string
	authResourceIDs := c.Ctx.Input.GetData(globalcv.ResoucesIdsKey)
	if authResourceIDs != nil {
		authIDs, _ = authResourceIDs.([]string)
	}
	return authIDs
}

func (c *Controller) ReturnErrResult(code int, message string) {
	c.Ctx.Output.SetStatus(code)
	c.Data["json"] = &Response{
		Status:  code,
		Message: message,
	}
	c.ServeJSON()
}

// ReturnErrResultWithTr err must non-nil
func (c *Controller) ReturnErrResultWithTr(code int, err error) {
	rsp := &Response{
		Status:  code,
		Message: err.Error(),
	}

	errorResult := &ErrorResult{}
	switch {
	case errors.As(err, &errorResult), errors.As(err, errorResult):
		lang := i18nutil.EN
		langSplit := strings.Split(c.GetLangHeader(), "-") // zh-CN, en-US
		if len(langSplit) > 1 && util.IsContain4String([]string{i18nutil.ZH, i18nutil.EN}, langSplit[0]) {
			lang = langSplit[0]
		}
		rsp.Message = i18nutil.TrWithArgs(lang, errorResult.Code, errorResult.Args...)
	}

	c.Ctx.Output.SetStatus(code)
	c.Data["json"] = rsp
	c.ServeJSON()
}

func (c *Controller) GetLangHeader() string {
	langHeader := c.Ctx.Input.Header("language-option")
	return langHeader
}

// 返回前端信息。res参数为可选，status<300,并且带res则直接返回res
func (c *Controller) ReturnResult(message string, res ...interface{}) {
	//logger.Infof("ReturnResult message:", message)
	//结果状态码
	//	if _, ok := constants.ResponseStatusMap[message]; ok {
	//		c.Ctx.Output.Status = constants.ResponseStatusMap[message]
	//	}
	//未定义返回值,默认状态为500
	if c.Ctx.Output.Status == 0 {
		c.Ctx.Output.SetStatus(globalcv.HTTPCodeServerError500)
	}
	//操作成功,并且带res则直接返回res
	if len(res) > 0 && c.Ctx.Output.Status < 300 {
		c.Data["json"] = res[0]
	} else {
		tips := message
		//		if _, ok := constants.ErrorTipMap[message]; ok {
		//			//返回结果的错误提示 国际化
		//			tips = i18nutil.I18nMessage(constants.ErrorTipMap[message])
		//		}
		c.Data["json"] = &Response{
			Status:  c.Ctx.Output.Status,
			Message: tips,
		}
	}
	c.ServeJSON()
}

func (c *Controller) ServeJSON() {
	c.Ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
	return
}

func (c *Controller) GetQueryParam(key string) string {
	return util.TrivialValidator(c.Ctx.Request.URL.Query().Get(key)).(string)
}

func (c *Controller) GetPathParam(key string) string {
	if key != "" && key[0] != ':' {
		key = ":" + key
	}
	return util.TrivialValidator(c.Ctx.Input.Param(key)).(string)
}

func (c *Controller) GetRequestBody() []byte {
	return util.TrivialValidator(c.Ctx.Input.RequestBody).([]byte)
}

func (c *Controller) Get() {
	c.httpNotFound()
}

func (c *Controller) Post() {
	c.httpNotFound()
}

func (c *Controller) Delete() {
	c.httpNotFound()
}

func (c *Controller) Put() {
	c.httpNotFound()
}

func (c *Controller) Patch() {
	c.httpNotFound()
}

func (c *Controller) httpNotFound() {
	c.ApiResponse(http.StatusNotFound, map[string]string{"result": "Not found"})
}

func (c *Controller) SetDefaultHeader() {
	c.Ctx.ResponseWriter.Header().Set("Content-Type", "application/json;charset=UTF-8")
}

func (c *Controller) ApiResponse(codeStatus int, data interface{}) {
	c.SetDefaultHeader()
	c.Ctx.Output.SetStatus(codeStatus)
	if data == nil {
		c.Data["json"] = map[string]string{}
	} else {
		c.Data["json"] = data
	}
	c.ServeJSON()
	if codeStatus >= 300 {
		logger.Debug("HttpResponse Method:%s, Url:%s, Code:%d, Body:%+v",
			c.Ctx.Request.Method, c.Ctx.Request.URL.Path, codeStatus, c.Data["json"])
	}
}
