package controllerservice

import (
	"encoding/json"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
)

type CommController struct {
	Controller
}

type SwitchLogLevelREQ struct {
	LogType string `json:"logType"`
	Mode    string `json:"mode"`
}

func (c *CommController) CheckHealth() {
	c.Data["json"] = "ok"
	c.ServeJ<PERSON>()
}

func (c *CommController) SwitchLogLevel() {
	params := &SwitchLogLevelREQ{}
	err := json.Unmarshal(c.Ctx.Input.RequestBody, params)
	if err != nil {
		logger.Errorf("SwitchLogLevel failed, request: %v, Unmarshal request body error: %v", string(c.Ctx.Input.RequestBody), err)
		c.Return<PERSON>rrResult(globalcv.HTTPCodeBadRequest400, "Unmarshal request body error")
		return
	}
	err = logger.SetLogLevel(params.LogType)
	if err != nil {
		logger.Errorf("SwitchLogLevel failed, request: %v, SetLogLevel error: %v", string(c.Ctx.Input.RequestBody), err)
		c.ReturnErrResult(globalcv.HTTPCodeBadRequest400, "SetLogLevel error")
		return
	}
	c.Ctx.Output.Status = globalcv.HTTPCodeSuccess200
	c.ServeJSON()
}
