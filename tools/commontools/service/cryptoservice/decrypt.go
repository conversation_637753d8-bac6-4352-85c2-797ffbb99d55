package cryptoservice

import (
	"cwsm/tools/commontools/infa/util/aes"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/opeanpalette/kms"
	"fmt"
	"strings"
)

func DecryptInstance(secretKeyID string, ciphertext string) string {
	secretKeyID, ciphertext = strings.TrimSpace(secretKeyID), strings.TrimSpace(ciphertext)
	if len(secretKeyID) == 0 {
		logger.Warnf("DecryptInstance secrete id is empty")
		return decryptByAes(ciphertext, "", "", "")
	}
	keyDetail, err := kms.GetSecretKeyDetail(secretKeyID)
	if err != nil {
		logger.Warnf("DecryptInstance GetSecretKeyDetail failed: %v", err)
		return ciphertext
	}
	return decryptByAes(ciphertext, keyDetail.Key, keyDetail.Nonce, keyDetail.Algorithm)
}

func DecryptKms(secretKeyID string, ciphertext string) (string, error) {
	keyDetail, err := kms.GetSecretKeyDetail(secretKeyID)
	if err != nil {
		logger.Warnf("DecryptInstance GetSecretKeyDetail failed: %v", err)
		return ciphertext, fmt.Errorf("DecryptInstance GetSecretKeyDetail failed: %v", err)
	}
	return decryptByAesWithError(ciphertext, keyDetail.Key, keyDetail.Nonce, keyDetail.Algorithm)
}

func decryptByAes(ciphertext, key, iv, algorithm string) string {
	plaintext, _ := decryptByAesWithError(ciphertext, key, iv, algorithm)
	return plaintext
}
func decryptByAesWithError(ciphertext, key, iv, algorithm string) (string, error) {
	if strings.ToUpper(algorithm) == encryptMethodAES256GCM {
		plaintext, err := aes.GCMDecrypt(ciphertext, key, iv)
		if err != nil {
			logger.Errorf("decryptPassword aes.GCMDecrypt failed: %v", err)
			return plaintext, fmt.Errorf("decryptPassword aes.GCMDecrypt failed: %v", err)
		}
		return plaintext, nil
	}
	plaintext, err := aes.CBCDecrypt(ciphertext, key)
	if err != nil {
		logger.Errorf("decryptPassword aes.CBCDecrypt failed: %v", err)
		return plaintext, fmt.Errorf("decryptPassword aes.CBCDecrypt failed: %v", err)
	}
	return plaintext, nil
}

func Decrypt4Ams(secretKeyID string, ciphertext string) (string, error) {
	if strings.Contains(secretKeyID, "KEY_") {
		return aes.CBCDecrypt(ciphertext, "")
	}
	return DecryptKms(secretKeyID, ciphertext)
}
