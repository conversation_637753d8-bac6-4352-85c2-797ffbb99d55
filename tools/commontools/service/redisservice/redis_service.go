package redisservice

import (
	"encoding/json"
	"fmt"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage/cloud_env_redis"
)

func GetTenant4Redis(cloudEnvId, tenantId string) (*Tenant4Redis, error) {
	key := fmt.Sprintf(RedisKeyName4Tenant, cloudEnvId)
	tenant4RedisStr, err := rediz.HGet(key, tenantId)
	tenant4Redis := &Tenant4Redis{}
	if err != nil {
		logger.Errorf("[GetTenant4Redis] rediz.HGet error: %v, key:%s, field:%s", err, key, tenantId)
		return tenant4Redis, fmt.Errorf("get tenant4Redis rediz.HGet error:%v", err)
	}

	if err := json.Unmarshal([]byte(tenant4RedisStr), tenant4Redis); err != nil {
		logger.Errorf("[GetTenant4Redis] json.Unmarshal error: %v", err)
		return tenant4Redis, fmt.Errorf("Fail to get tenant4Redis due to json.Unmarshal error: %v", err)
	}

	return tenant4Redis, nil
}

func GetCloudEnv4Redis(cloudEnvId string) (*CloudEnv4Redis, error) {
	cloudEnvDao, err := cloud_env_redis.Get(cloudEnvId)
	if err != nil {
		logger.Errorf("[GetCloudEnv4Redis] cloud_env_redis.Get cloud %s error: %v", cloudEnvId, err)
		return nil, fmt.Errorf("get cloudEnv4Redis cloud_env_redis.Get error:%v", err)
	}

	cloudEnv4Redis := &CloudEnv4Redis{*cloudEnvDao}

	return cloudEnv4Redis, nil
}
