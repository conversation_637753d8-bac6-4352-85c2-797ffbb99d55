package redisservice

import (
	"cwsm/tools/commontools/authentication"
	"cwsm/tools/commontools/storage/rediz/storage/cloud_env_redis"
)

// REDIS model name
const (
	RedisKeyName4Tenant   = "%s_tenant_Last_vrm"
	RedisKeyName4CloudEnv = "%s_env"
)

type Tenant4Redis struct {
	Id        string     `json:"id"`
	Name      string     `json:"name"`
	DomainID  string     `json:"domain_id"`
	EvnID     string     `json:"evnId"`
	EnvName   string     `json:"envName"`
	UserRoles []UserRole `json:"userRoles"`
	VdcId     string     `json:"vdcId"`
	VdcName   string     `json:"vdcName"`
	Enabled   bool       `json:"enabled"`
}

type CloudEnv4Redis struct {
	cloud_env_redis.CloudEnvRedisDao
}

type ResourcePolicy struct {
	Name         string   `json:"name"`
	ResourceType []string `json:"resourceType"`
}

type UserRole struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Roles []Role `json:"roles"`
}

type Role struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Links Link   `json:"links"`
}

type Link struct {
	Self     string `json:"self"`
	Previous string `json:"previous"`
	Next     string `json:"next"`
}

func (cloudEnv4Redis *CloudEnv4Redis) ToOsClient(tenantName string) *authentication.OsClient {
	osClient := &authentication.OsClient{}
	for _, endpoint := range cloudEnv4Redis.Endpoints {
		if endpoint.Name == "public-auth" {
			osClient.SslAuthentication = endpoint.SslAuthentication
			osClient.Uri = endpoint.URL
			osClient.UserName = endpoint.UserName
			osClient.Version = endpoint.Version
			osClient.Scope = endpoint.Scope
			osClient.Password = endpoint.KmsPassword

			break
		}
	}
	osClient.TenantName = tenantName

	return osClient
}
