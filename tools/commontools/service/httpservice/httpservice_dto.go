package httpservice

type Header struct {
	<PERSON><PERSON><PERSON><PERSON> string `json:"headerKey"`
	HeaderVal string `json:"headerVal"`
}

func DefaultLangHeader(lang string) []Header {
	if len(lang) > 0 {
		return []Header{{HeaderKey: "language-option", HeaderVal: lang}, {HeaderKey: "Accept-Language", HeaderVal: lang}}
	}
	return []Header{}
}

func DefaultHeaders() map[string]string {
	return map[string]string{"Accept": "application/json", "Content-Type": "application/json", "Connection": "close"}
}

func DefaultHeadersWithLang(lang string) map[string]string {
	return DefaultHeadersWithExtra(DefaultLangHeader(lang)...)
}

func DefaultHeadersWithExtra(headers ...Header) map[string]string {
	defaultHeaders := DefaultHeaders()
	for _, h := range headers {
		defaultHeaders[h.<PERSON><PERSON><PERSON><PERSON>] = h.<PERSON><PERSON>al
	}
	return defaultHeaders
}

func PatchHeaders() map[string]string {
	return map[string]string{"Accept": "application/json", "Content-Type": "application/openstack-images-v2.1-json-patch"}
}

func EmptyHeader() map[string]string {
	return map[string]string{}
}
