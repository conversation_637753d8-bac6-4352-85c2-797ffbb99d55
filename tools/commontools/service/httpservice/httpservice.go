package httpservice

import (
	"cwsm/tools/commontools/devconfig"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"

	"zte.com.cn/cms/crmX/commontools-base/restful"

	"github.com/beego/beego/v2/server/web/context"
)

func Get(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte,
	map[string][]string, int, error) {
	url := setURL(urlpath)
	//开发模式替换header信息
	headers := setDevHeader(reqHeaders)
	//logger.Infof(url, fmt.Sprintf("%s", headers))
	return restful.GetMethod(url, headers, auth)
}

func GetWithAuth(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth, authParam ...string) ([]byte,
	map[string][]string, int, error) {
	url := setURL(urlpath)
	//开发模式替换header信息
	headers := setDevHeader(reqHeaders)
	//logger.Infof(url, fmt.Sprintf("%s", headers))
	return restful.GetMethodNoRedirect(url, headers, auth, authParam...)
}

func GetWithIo(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) (io.ReadCloser,
	map[string][]string, int, int64, error) {
	url := setURL(urlpath)
	//开发模式替换header信息
	headers := setDevHeader(reqHeaders)
	//logger.Infof(url, fmt.Sprintf("%s", headers))
	return restful.GetMethodWithIo(url, headers, auth)
}

func AsyncGet(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth, beegoOutput *context.BeegoOutput) error {
	url := setURL(urlpath)
	//开发模式替换header信息
	headers := setDevHeader(reqHeaders)
	return restful.AsyncGetMethod(url, headers, auth, beegoOutput)
}

func Post(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
	map[string][]string, int, error) {
	url := setURL(urlpath)
	//开发模式替换header信息
	headers := setDevHeader(reqHeaders)
	//logger.Infof(url, fmt.Sprintf("%s", headers))
	switch body.(type) {
	case []byte:
		return restful.PostMethod(url, headers, body.([]byte), auth)
	}
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		logger.Errorf("body marshal fail:", err)
		return nil, nil, 500, err
	}
	return restful.PostMethod(url, headers, bodyBytes, auth)
}

func PostMultipart(urlpath string, reqHeaders map[string]string, body io.Reader, auth *restful.SSLAuth) ([]byte,
	map[string][]string, int, error) {
	url := setURL(urlpath)
	//开发模式替换header信息
	headers := setDevHeader(reqHeaders)
	//logger.Infof(url, fmt.Sprintf("%s", headers))
	return restful.PostMultipartMethod(url, headers, body, auth)
}

func PostWithTimeout(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth, timeout int64) ([]byte,
	map[string][]string, int, error) {
	url := setURL(urlpath)
	//开发模式替换header信息
	headers := setDevHeader(reqHeaders)
	//logger.Infof(url, fmt.Sprintf("%s", headers))
	switch body.(type) {
	case []byte:
		return restful.PostMethodWithTimeout(url, headers, body.([]byte), auth, timeout)
	}
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		logger.Errorf("body marshal fail:", err)
		return nil, nil, 500, err
	}
	return restful.PostMethodWithTimeout(url, headers, bodyBytes, auth, timeout)
}

func Put(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) (
	[]byte, map[string][]string, int, error) {
	url := setURL(urlpath)
	headers := setDevHeader(reqHeaders)
	switch body.(type) {
	case []byte:
		return restful.PutMethod(url, headers, body.([]byte), auth)
	}
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		logger.Errorf("body marshal fail:", err)
		return nil, nil, 500, err
	}
	return restful.PutMethod(url, headers, bodyBytes, auth)
}

func PutStream(urlpath string, reqHeaders map[string]string, body io.ReadCloser, auth *restful.SSLAuth) (
	[]byte, map[string][]string, int, error) {
	url := setURL(urlpath)
	headers := setDevHeader(reqHeaders)
	return restful.PutStreamMethod(url, headers, body, auth)
}

func Patch(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) (
	[]byte, map[string][]string, int, error) {
	url := setURL(urlpath)
	headers := setDevHeader(reqHeaders)
	switch body.(type) {
	case []byte:
		return restful.PatchMethod(url, headers, body.([]byte), auth)
	}
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		logger.Errorf("body marshal fail:", err)
		return nil, nil, 500, err
	}
	return restful.PatchMethod(url, headers, bodyBytes, auth)
}

func Delete(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string,
	int, error) {
	url := setURL(urlpath)
	//开发模式替换header信息
	headers := setDevHeader(reqHeaders)
	//logger.Infof(url, fmt.Sprintf("%s", headers))
	return restful.DeleteMethod(url, headers, auth)
}

func DeleteWithBody(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
	map[string][]string, int, error) {
	url := setURL(urlpath)
	//开发模式替换header信息
	headers := setDevHeader(reqHeaders)
	//logger.Infof(url, fmt.Sprintf("%s", headers))
	switch body.(type) {
	case []byte:
		return restful.DeleteWithBodyMethod(url, headers, body.([]byte), auth)
	}
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		logger.Errorf("body marshal fail:", err)
		return nil, nil, 500, err
	}
	return restful.DeleteWithBodyMethod(url, headers, bodyBytes, auth)
}

func UniversalExec(method, urlPath string, reqBody []byte, reqHeaders map[string]string, auth *restful.SSLAuth) (
	resBody []byte, status int, resHeaders map[string][]string) {
	switch method {
	case http.MethodPost:
		resBody, resHeaders, status, _ = Post(urlPath, reqHeaders, reqBody, auth)
	case http.MethodDelete:
		if reqBody != nil {
			resBody, resHeaders, status, _ = DeleteWithBody(urlPath, reqHeaders, reqBody, auth)
		} else {
			resBody, resHeaders, status, _ = Delete(urlPath, reqHeaders, auth)
		}
	case http.MethodPut:
		resBody, resHeaders, status, _ = Put(urlPath, reqHeaders, reqBody, auth)
	case http.MethodPatch:
		resBody, resHeaders, status, _ = Patch(urlPath, reqHeaders, reqBody, auth)
	case http.MethodGet:
		resBody, resHeaders, status, _ = Get(urlPath, reqHeaders, auth)
	default:
		resBody = []byte(fmt.Sprintf(`{"error": "unsupported method %s"}`, method))
		status = http.StatusMethodNotAllowed
	}
	return
}

func setURL(urlpath string) string {
	//开发模式的配置（可忽略）
	if devconfig.IsDev {
		if strings.Contains(urlpath, "?") {
			return devconfig.DevMsbURLPath + urlpath + "&csrftoken=" + devconfig.DevHeader.CsrfToken
		}
		return devconfig.DevMsbURLPath + urlpath + "?csrftoken=" + devconfig.DevHeader.CsrfToken
	}
	if strings.HasPrefix(urlpath, "http") {
		return urlpath
	}
	//OTCP(oes)平台的微服务，接口走单独的端口
	for otcpServiceURL, msbInfo := range globalcv.ServiceURLMaps {
		if strings.HasPrefix(urlpath, otcpServiceURL) {
			caastype := os.Getenv("caas_type")
			if caastype != "" {
				restURL := globalcv.HTTPPrefix + msbInfo.ServiceName + "." + msbInfo.NameSpace + ":" + msbInfo.Port + urlpath
				logger.Debug("ResHealthCheck tsm setURL oes request url:%s", restURL)
				return restURL
			} else {
				restURL := globalcv.HTTPPrefix + msbInfo.ServiceName + "-" + msbInfo.NameSpace + ":" + msbInfo.Port + urlpath
				logger.Debug("ResHealthCheck csm setURL oes request url:%s", restURL)
				return restURL
			}

		}
	}
	return globalcv.MsbURLPath + urlpath
}

// 开发模式替换header信息
func setDevHeader(reqHeaders map[string]string) map[string]string {
	if devconfig.IsDev {
		headers := map[string]string{}
		headers["User-Agent"] = devconfig.DevHeader.UserAgent
		if devconfig.DevHeader.AuthCode != "" {
			headers["Z-AUTH-CODE"] = devconfig.DevHeader.AuthCode
		}
		if devconfig.DevHeader.Cookie != "" {
			headers["Cookie"] = devconfig.DevHeader.Cookie
		}
		return headers
	}
	reqHeaders["User-Agent"] = devconfig.DevHeader.UserAgent
	return reqHeaders
}
