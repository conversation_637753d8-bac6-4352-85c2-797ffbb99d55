package httpservice

import (
	"cwsm/tools/commontools/devconfig"
	"cwsm/tools/commontools/globalcv"
	"os"

	"github.com/onsi/ginkgo"
	"github.com/onsi/gomega"
)

var _ = ginkgo.Describe("setURL function", func() {
	ginkgo.Context("when in development mode", func() {
		ginkgo.BeforeEach(func() {
			devconfig.IsDev = true
			devconfig.DevMsbURLPath = "http://dev.example.com/"
			devconfig.DevHeader.CsrfToken = "dev-token"
		})

		ginkgo.It("should append csrftoken to URL without query parameters", func() {
			url := setURL("api/v1/resource")
			gomega.Expect(url).To(gomega.Equal("http://dev.example.com/api/v1/resource?csrftoken=dev-token"))
		})

		ginkgo.It("should append csrftoken to URL with existing query parameters", func() {
			url := setURL("api/v1/resource?existing_param=value")
			gomega.Expect(url).To(gomega.Equal("http://dev.example.com/api/v1/resource?existing_param=value&csrftoken=dev-token"))
		})
	})

	ginkgo.Context("when not in development mode", func() {
		ginkgo.BeforeEach(func() {
			devconfig.IsDev = false
		})

		ginkgo.It("should return the same URL if it starts with http", func() {
			url := setURL("http://example.com/api/v1/resource")
			gomega.Expect(url).To(gomega.Equal("http://example.com/api/v1/resource"))
		})

		ginkgo.Context("when URL belongs to OTCP service", func() {
			ginkgo.BeforeEach(func() {
				globalcv.ServiceURLMaps = map[string]globalcv.MsbRestClientInfoDto{
					"otcpService": {
						ServiceName: "otcpServiceName",
						NameSpace:   "namespace",
						Port:        "8080",
						URL:         "",
					},
				}
			})

			ginkgo.It("should construct the correct OTCP service URL when caastype is set", func() {
				os.Setenv("caas_type", "some_value")
				defer os.Unsetenv("caas_type")

				url := setURL("otcpService/api/v1/resource")
				gomega.Expect(url).To(gomega.Equal("http://otcpServiceName.namespace:8080otcpService/api/v1/resource"))
			})

			ginkgo.It("should construct the correct OTCP service URL when caastype is not set", func() {
				url := setURL("otcpService/api/v1/resource")
				gomega.Expect(url).To(gomega.Equal("http://otcpServiceName-namespace:8080otcpService/api/v1/resource"))
			})
		})

		ginkgo.It("should return the default URL path for other cases", func() {
			url := setURL("api/v1/resource")
			gomega.Expect(url).To(gomega.Equal(globalcv.MsbURLPath + "api/v1/resource"))
		})
	})
})
