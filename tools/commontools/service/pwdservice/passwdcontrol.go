package pwdservice

import (
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/infa/util/file"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/cryptoservice"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"strings"
	"sync"

	"github.com/fsnotify/fsnotify"
)

type InternalControlListener interface {
	OnUpdate(confResult *ConfResult) error
}

type InternalControlService struct {
	listeners    sync.Map
	dirListeners sync.Map
}

var fileContents = make(map[string]string)

var BasePath = "/usr/share/director/"

var notifyAll = make(chan struct{})

/*
*
注册监听器(单实例时使用)
confName: 环境变量名 例如：OPENPALETTE_PG_PASSWORD
listener: 监听器
*/
func (s *InternalControlService) RegisterInternalControlListener(confName string, listener InternalControlListener) {
	logger.Infof("Add a new listener, confName: ", confName)
	s.listeners.Store(confName, listener)
}

/*
*
注册监听器（多实例情况下使用）
dir: 子目录名 例如：ftp
confName: 环境变量名 例如：PENPALETTE_FTPSERVICECONFIG_USERPASSWORD
listener: 监听器
*/
func (s *InternalControlService) RegisterInternalControlListenerByDir(dir string, fileName string, listener InternalControlListener) {
	logger.Infof("Add a new listener to dirListeners, dir: " + dir + " ,fileName: " + fileName)
	s.dirListeners.Store(dir+"/"+fileName, listener)
}

/*
*
获取单实例密码
confName: 环境变量名 例如：OPENPALETTE_PG_PASSWORD
*/
func (s *InternalControlService) GetInitialParam(confName string) (*ConfResult, error) { //slice
	logger.Infof("GetInitialParam, confName: ", confName)
	var confResult *ConfResult
	confResults, err := getConfsFromFile(confName)

	if err != nil || len(confResults) == 0 {
		logger.Infof("Read conf from file failed! Try read conf from os")
		confResult = getConfFromEnv(confName)
	} else {
		confResult = &confResults[0]
	}

	if confResult == nil {
		return nil, fmt.Errorf("File or env not exists! confName: " + confName)
	}

	return confResult, nil
}

/*
*
获取实例密码（多实例情况下使用）
dir: 子目录名 例如：ftp
confName: 环境变量名 例如：PENPALETTE_FTPSERVICECONFIG_USERPASSWORD
*/
func (s *InternalControlService) GetInitialParamByDir(dir string, fileName string) (*ConfResult, error) { //slice

	logger.Infof("GetInitialParamByDir, dir: " + dir + " ,fileName: " + fileName)

	//  1，校验输入参数
	if dir == "" || fileName == "" {
		return nil, errors.New("dir or fileName is empty")
	}

	//2, 尝试查找密码文件
	filePath := BasePath + dir + "/" + fileName
	confResult, err := GetConfFromFile(filePath, fileName)
	if confResult != nil && err == nil {
		confResult.Dir = dir
		return confResult, nil
	}

	//3, 当密码文件不存在，则从环境变量中读取密码
	logger.Infof("Read from file " + filePath + " failed! try to read password from os!")
	envName := getEnvNameByDir(dir + "/" + fileName)
	confResult = getConfFromEnv(envName)
	if confResult != nil {
		confResult.ConfName = envName
		return confResult, nil
	}

	//4, 如果环境变量中也没有密码，则返回错误
	return nil, fmt.Errorf("Env " + envName + " is not exists!")
}

/*
*
使用协程处理事件
*/
func (s *InternalControlService) Run() {

	//创建一个监控服务
	watch, err := fsnotify.NewWatcher()
	if err != nil {
		logger.Errorf("Create file system watcher service failed! ", err)
		return
	}

	//循环添加对子目录的监控
	dirs, err := ioutil.ReadDir(BasePath)
	if err != nil || dirs == nil || len(dirs) == 0 {
		logger.Infof("Base path " + BasePath + " is not exist or empty!")
		return
	}
	for _, dir := range dirs {
		if dir.IsDir() {
			err = watch.Add(BasePath + dir.Name())
			if err != nil {
				logger.Errorf("watch Add failed : ", BasePath+dir.Name())
			}
			logger.Infof("Add directory to watch list : ", BasePath+dir.Name())

			// 存储文件的内容
			files, err := ioutil.ReadDir(BasePath + dir.Name())
			if err != nil || files == nil || len(files) == 0 {
				logger.Infof("Dir " + BasePath + dir.Name() + " is not exists or empty!")
				continue
			}
			for _, file := range files {
				//filePath := BasePath + dir.Name() + "/" + file.Name()
				filePath := filepath.Clean(filepath.Join(BasePath, dir.Name(), file.Name()))
				fileBytes, err := ioutil.ReadFile(filepath.Clean(filePath))
				if err != nil {
					logger.Errorf("ReadFile failed : ", filePath)
				}
				if fileBytes != nil {
					fileContents[filePath] = string(fileBytes)
				}
			}
		}
	}

	logger.Infof("Start watching file system!")
	//我们启一个goroutine来处理监控对象的事件
	go func() {
		for {
			select {
			case ev := <-watch.Events:
				{
					logger.Infof("File changed: ", ev.Name, ", Event type: ", ev.Op)
					s.handle()
				}
			case err := <-watch.Errors:
				{
					logger.Errorf("error : ", err)
				}
			case <-notifyAll:
				{
					s.notifyAllListeners()
				}
			}
		}
	}()
}

/*
*
循环遍历密码文件，找到变动的文件，通知到对应监听器
*/
func (s *InternalControlService) handle() {

	filePaths := scanFiles(BasePath)
	for _, filePath := range filePaths {
		if isFileChanged(filePath) {
			logger.Infof("Attention! File [", filePath, "] changed")
			s.handleEvent1(filePath)
			s.handleEvent2(filePath)
		}
	}
}

/*
*
根据文件名找到对应监听器，然后发送通知消息（用于单实例情况下）
*/
func (s *InternalControlService) handleEvent1(filePath string) {
	filePath = strings.Replace(filePath, "\\", "/", -1)
	paths := strings.Split(filePath, "/")
	fileName := paths[len(paths)-1]

	//从文件中获取配置
	confResult, err := GetConfFromFile(filePath, fileName)
	if err != nil || confResult == nil {
		logger.Infof("Read conf from file " + filePath + " failed! Try read conf from os")
		confResult = getConfFromEnv(fileName)
		if confResult == nil {
			logger.Infof("Read env " + fileName + " failed!")
			return
		}
	}
	// 通知到指定监听器
	s.listeners.Range(func(key, value interface{}) bool {
		confNamePattern := key.(string)
		if ok, err := regexp.MatchString(confNamePattern, fileName); ok {
			if err != nil {
				logger.Errorf("regexp.MatchString failed")
			}
			notify(confNamePattern, value.(InternalControlListener), confResult)
		}
		return true
	})
}

// 调用监听器通知方法
func notify(listenerKey string, listener InternalControlListener, result *ConfResult) {

	listenerName := reflect.TypeOf(listener)

	defer func() {
		if p := recover(); p != nil {
			logger.Warnf("Notify listener ", listenerName, " panic! key: ", listenerKey)
		}
	}()

	err := listener.OnUpdate(result)
	if err != nil {
		logger.Warnf("Notify listener ", listenerName, " failed! key: ", listenerKey, err)
	} else {
		logger.Infof("Notify listener ", listenerName, " failed! key: ", listenerKey)
	}
}

// 从环境变量中读取密码
func getConfFromEnv(confName string) *ConfResult {
	confValue := strings.TrimSpace(os.Getenv(confName))
	if confValue == "" {
		return nil
	}

	// 构造返回结果
	result := new(ConfResult)
	result.ConfValues = make(map[string]string)
	result.ConfValues["password"] = confValue
	result.ConfName = confName

	return result
}

/*
*
从文件目录中读取多个密码
*/
/* Started by AICoder, pid:6bbaabf7adef4a0485585ae29cb23860 */
func getConfsFromFile(confNamePattern string) (confResults []ConfResult, err error) {
	dirs, err := ioutil.ReadDir(BasePath)
	if err != nil {
		logger.Infof("Read from %s failed! ", BasePath)
		return nil, err
	}

	for _, dir := range dirs {
		if isValidDirectory(dir) {
			confResults, err = processDirectory(dir, confNamePattern, confResults)
			if err != nil {
				logger.Infof(fmt.Sprintf("Dir %s is not exist or empty!", dir.Name()))
				continue
			}
		}
	}
	return confResults, nil
}

func isValidDirectory(dir os.FileInfo) bool {
	return dir.IsDir() && (dir.Name() == "ftp" || dir.Name() == "es" || dir.Name() == "pg" || dir.Name() == "redis" || dir.Name() == "pgcache" || dir.Name() == "ldap")
}

func processDirectory(dir os.FileInfo, confNamePattern string, confResults []ConfResult) ([]ConfResult, error) {
	files, err := ioutil.ReadDir(filepath.Join(BasePath, dir.Name()))
	if err != nil {
		return confResults, err
	}

	for _, file := range files {
		if match, _ := regexp.MatchString(confNamePattern, file.Name()); match {
			confResult, err := getConfFromFile(dir, file)
			if err != nil {
				logger.Infof("Read from file " + file.Name() + " failed!")
				continue
			}
			confResults = append(confResults, *confResult)
		}
	}
	return confResults, nil
}

func getConfFromFile(dir os.FileInfo, file os.FileInfo) (*ConfResult, error) {
	filePath := filepath.Join(BasePath, dir.Name(), file.Name())
	return GetConfFromFile(filePath, file.Name())
}

/* Ended by AICoder, pid:6bbaabf7adef4a0485585ae29cb23860 */

/*
*
从环境变量中读取多个密码
*/
func getConfsFromEnv(confNamePattern string) ([]ConfResult, error) {
	var confResults []ConfResult
	envs := os.Environ()

	for _, env := range envs {
		s := strings.SplitN(env, "=", 2)
		if len(s) == 2 {
			envKey := s[0]
			if ok, err := regexp.MatchString(confNamePattern, envKey); ok {
				if err != nil {
					logger.Errorf("regexp.MatchString failed")
				}
				confResult := getConfFromEnv(envKey)
				if confResult != nil {
					confResults = append(confResults, *confResult)
				}
			}
		}
	}

	return confResults, nil
}

func isFileChanged(filePath string) bool {
	fileBytes, err := ioutil.ReadFile(filepath.Clean(filePath))

	if err != nil || len(fileBytes) == 0 {
		logger.Infof("filePath:" + filePath + " is empty!")
		fileContents[filePath] = ""
		return true
	}

	fileContent := string(fileBytes)
	filePath = strings.Replace(filePath, "\\", "/", -1)
	if fileContent != fileContents[filePath] {
		logger.Debug("filePath:", filePath)
		logger.Debug("new fileContent:", fileContent)
		logger.Debug("old fileContent:", fileContents[filePath])
		fileContents[filePath] = fileContent
		return true
	}

	return false
}

/*
*
根据文件名找到对应监听器，然后发送通知消息（用于多实例情况下）
*/
func (s *InternalControlService) handleEvent2(filePath string) {

	paths := strings.Split(filePath, "/")
	fileName := paths[len(paths)-1]

	s.dirListeners.Range(func(key, value interface{}) bool {
		dir := key.(string)
		if ok := strings.HasSuffix(filePath, dir); ok {

			//从文件中获取密码
			confResult, err := GetConfFromFile(filePath, fileName)
			if err != nil || confResult == nil {
				logger.Infof("Read conf from file " + filePath + " failed! Try read conf from os")

				envName := getEnvNameByDir(dir)
				confResult = getConfFromEnv(envName)
				if confResult == nil {
					logger.Infof("Read env " + fileName + " failed!")
				}
			}

			// 通知到指定监听器
			if confResult != nil {
				notify(dir, value.(InternalControlListener), confResult)
			}
		}

		return true
	})
}

func getEnvNameByDir(dir string) string {

	if ok := strings.HasPrefix(dir, "/"); ok {
		dir = dir[1:]
	}
	paths := strings.SplitN(dir, "/", 2)
	if len(paths) == 2 {
		if paths[0] == "ftp" || paths[0] == "es" || paths[0] == "pg" || paths[0] == "redis" || paths[0] == "pgcache" || paths[0] == "ldap" {
			return paths[1]
		}
		return paths[1] + "_" + paths[0]
	}

	return ""
}

func scanFiles(basePath string) (filePaths []string) {
	dirs, err := ioutil.ReadDir(basePath)
	if err != nil {
		logger.Infof("Read from " + basePath + " failed!")
		return nil
	}

	for _, dir := range dirs {
		if dir.IsDir() {
			files, err := ioutil.ReadDir(basePath + dir.Name())
			if err != nil {
				logger.Infof(fmt.Sprintf("Dir %s is not exist or empty!", dir.Name()))
			}
			for _, file := range files {
				filePath := basePath + dir.Name() + "/" + file.Name()
				filePaths = append(filePaths, filePath)
			}
		}
	}

	return filePaths
}

/*
*
通知所有监听器
此方法仅允许模块内部访问
*/
func (s *InternalControlService) notifyAllListeners() {

	s.listeners.Range(func(key, value interface{}) bool {

		fileName := key.(string)
		fileListener := value.(InternalControlListener)
		result, err := s.GetInitialParam(fileName)
		if err == nil {
			notify(fileName, fileListener, result)
		} else {
			logger.Warnf("GetInitialParam failed! fileName:", fileName, err)
		}

		return true
	})

	s.dirListeners.Range(func(key, value interface{}) bool {

		dirName := key.(string)
		dirListener := value.(InternalControlListener)

		filePath := strings.SplitN(dirName, "/", 2)
		if len(filePath) == 2 {
			dir := filePath[0]
			fileName := filePath[1]
			result, err := s.GetInitialParamByDir(dir, fileName)
			if err == nil {
				notify(dirName, dirListener, result)
			} else {
				logger.Warnf("GetInitialParamByDir failed! dir: "+dir+", fileName:", fileName, err)
			}
		}

		return true
	})
}

// 从文件中获取密码
func GetConfFromFile(filePath string, fileName string) (*ConfResult, error) {

	//读取文件内容
	ciphertext, err := file.ReadTextFromFile(filePath)
	if err != nil {
		logger.Infof("Read file %s failed! : ", filePath)
		return nil, err
	}
	if ciphertext == "" {
		logger.Infof("error : file is empty!")
		return nil, errors.New("file is empty")
	}
	// 尝试读取秘钥ID
	lastIndex := strings.LastIndex(filePath, "/")
	secretKeyIDPath := filePath[0:lastIndex+1] + globalcv.SecreteKeyIDFileName
	keyID, _ := file.ReadTextFromFile(secretKeyIDPath)

	// 解密
	password := cryptoservice.DecryptInstance(keyID, ciphertext)

	// 构造返回结果
	result := new(ConfResult)
	result.ConfValues = map[string]string{}
	result.ConfValues["password"] = password
	result.ConfName = fileName

	return result, nil
}
