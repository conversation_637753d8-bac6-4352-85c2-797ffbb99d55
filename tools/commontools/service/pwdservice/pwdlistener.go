package pwdservice

import (
	"cwsm/tools/commontools/db/dbutil"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
	"errors"
)

var listenerService = InternalControlService{}
var pgLisenter, redisListener, ftpListener = &DirectorListener{}, &DirectorListener{}, &DirectorListener{}

type DirectorListener struct {
}

// 监听文件变动后会调用这个方法
func (p *DirectorListener) OnUpdate(confResult *ConfResult) error {
	pass, exist := confResult.GetPassword()
	if !exist {
		logger.Errorf("password for ", confResult.ConfName, " not exist ")
		return errors.New("password for " + confResult.ConfName + " not exist")
	}
	logger.Infof("password for " + confResult.ConfName + " changed")

	if confResult.ConfName == RedisConfName {
		RedisPassword = pass
	} else if confResult.ConfName == PgConfName {
		DbPassword = pass
		if globalcv.DbPassword != DbPassword {
			globalcv.DbPassword = DbPassword
			ReConnectDB()
		}
	} else if confResult.ConfName == FtpConfName {
		FtpPassword = pass
	}
	return nil
}

func StartListenPassword() {
	pgConf, err := listenerService.GetInitialParam(PgConfName)
	if err != nil {
		logger.Infof("initialParam for pg fail, err :", err.Error())
	} else {
		listenerService.RegisterInternalControlListener(pgConf.ConfName, pgLisenter)
		if pass, exist := pgConf.GetPassword(); exist {
			DbPassword = pass
		}
	}
	listenerService.Run()
}

func ReConnectDB() {
	//关闭连接
	closed := dbutil.CloseDbClient(globalcv.DbType, globalcv.DbName)
	if !closed {
		logger.Errorf("close db conn failed: ", globalcv.DbName)
	}

	err := dbutil.InitDbClient()
	if err != nil {
		logger.Errorf("conn db failed: ", globalcv.DbName)
	}
}
