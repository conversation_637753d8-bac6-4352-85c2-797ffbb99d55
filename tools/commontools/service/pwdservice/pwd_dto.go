package pwdservice

import (
	"os"
)

const (
	RedisConfName = "OPENPALETTE_REDIS_PASSWORD"
	PgConfName    = "OPENPALETTE_PG_PASSWORD"
	FtpConfName   = "OPENPALETTE_FTPSERVICECONFIG_USERPASSWORD"
)

var DbPassword string = os.Getenv(PgConfName)
var RedisPassword string = os.Getenv(RedisConfName)
var FtpPassword string = os.Getenv(FtpConfName)

type ConfResult struct {
	ConfName   string            //环境变量名
	Dir        string            //目录名
	ConfValues map[string]string //密码值
}

func (confResult *ConfResult) GetPassword() (string, bool) {
	value, exist := confResult.ConfValues["password"]
	return value, exist
}
