package security

import (
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
	otcpsm "cwsm/tools/commontools/otcp/sm"
	"fmt"

	beego "github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
)

var UserCheck = func(ctx *context.Context) {
	UserName := ctx.Request.Header.Get("X-Auth-Username")
	PassWord := ctx.Request.Header.Get("X-Auth-Password")
	request := &otcpsm.GetAccessTokenReq{
		UserName:  UserName,
		GrantType: "password",
		Value:     PassWord}
	token, pathErr := otcpsm.GetToken(request)
	if pathErr != nil || token == "" {
		logger.Errorf("[UserCheck] userCheckFailed: wrong userinfo: %v", pathErr)
		panicWhenErrorAndCode(ctx, fmt.<PERSON><PERSON><PERSON>("wrong userinfo in header"), globalcv.HTTPCodeUnAuthorized401)
	}
	ctx.Input.SetData(globalcv.HeaderAccessToken, token)
}

func panicWhenErrorAndCode(ctx *context.Context, err error, code int) {
	ctx.Output.SetStatus(code)
	response := struct {
		Status  int    `json:"status"`
		Message string `json:"message"`
	}{
		Status:  ctx.Output.Status,
		Message: err.Error(),
	}
	_ = ctx.Output.JSON(response, false, false)
	panic(beego.ErrAbort)
}
