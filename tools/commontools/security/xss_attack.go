package security

import (
	"bytes"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/service/controllerservice"
	"encoding/json"
	"regexp"
	"strconv"
	"strings"

	beego "github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
	"github.com/pkg/errors"
)

var HandleXSSAttackREQ = func(ctx *context.Context) {
	if err := XSScheckInput(ctx.Input); err != nil {
		logger.Warnf("", err.Error())
		ctx.Output.SetStatus(globalcv.HTTPCodeBadRequest400)
		_ = ctx.Output.JSON(err.Error(), false, false)
		panic(beego.ErrAbort)
	}
}

var HandleXSSAttackRSP = func(ctx *context.Context) {
	if data := XSSFilter(ctx.Input.Data()["json"]); len(data) > 0 {
		data = dealChineseErrMsg(ctx, data)
		_ = ctx.Output.Body(data)
	}
}

func dealChineseErrMsg(ctx *context.Context, data []byte) []byte {
	newData := make([]byte, len(data))
	copy(newData, data)
	langHeader := ctx.Input.Header("language-option")
	if langHeader != "zh-CN" {
		return newData
	}
	rsp := &controllerservice.Response{}
	if err := json.Unmarshal(data, rsp); err != nil {
		return newData
	}
	if rsp.Status < 300 || len(rsp.Message) == 0 {
		return newData
	}
	if msg, err := strconv.Unquote(`"` + rsp.Message + `"`); err == nil {
		rsp.Message = msg
	}
	newData, _ = json.Marshal(rsp)
	return newData
}

var (
	xssCheckString = []string{
		`<script>(.*?)</script>`,
		`<img.*?on.*?=.*?>`,
		`<script(.*?)>`,
		`eval\\((.*?)\\)`,
		`e-xpression\\((.*?)\\)`,
		`expression\\((.*?)\\)`,
		`javascript:`,
		`vbscript:`,
		`onload(.*?)=`,
		`<%.*?java.*?%>`,
		`<jsp:.*?>.*?</jsp:.*?>`,
		`<meta.*?>`,
	}
	xssCheckExpr = make([]*regexp.Regexp, 0, len(xssCheckString))

	exprOK     bool = false
	excludeUrl map[string]bool
)

func init() {
	for _, str := range xssCheckString {
		expr, err := regexp.Compile(str)
		if err != nil {
			logger.Errorf("init XXS regExpr ( ", str, " ) fail")
			return
		}
		xssCheckExpr = append(xssCheckExpr, expr)
	}
	logger.Debug("init XXS regExpr OK")
	exprOK = true
}

// register url for not XSS check
func RegisterExcludeUrl(url string) {
	excludeUrl[url] = true
}

// replace XSS string to ""
func XSSFilter(body interface{}) []byte {
	bs, err := marshalInner(body)
	if err != nil {
		return []byte{}
	}

	str := string(bs)
	for _, reg := range xssCheckExpr {
		str = reg.ReplaceAllString(str, "")
	}

	str = strings.Replace(strings.Replace(str, "<", "＜", -1), ">", "＞", -1)

	logger.Debug("XSS filter done")
	return []byte(str)
}

func XSScheckInput(input *context.BeegoInput) error {
	logger.Debug("url check XSS begin, url:", input.URI())
	if checkUrlExcluded(input.URI()) {
		logger.Debug("url check XSS, exclude url:", input.URI())
		return nil
	}

	if checkXssString(string(input.RequestBody)) {
		return errors.New("Illegal type's parameter exist in request")
	}

	if checkXssHeaders(input.Context.Request.Header) {
		return errors.New("Illegal type's parameter exist in request")
	}

	if checkXssString(input.URI()) {
		return errors.New("Illegal type's parameter exist in request")
	}
	logger.Debug("url check XSS OK, url:", input.URI())
	return nil
}

func checkUrlExcluded(url string) bool {
	for exURL := range excludeUrl {
		if strings.Contains(url, exURL) {
			return true
		}
	}
	return false
}

func checkXssString(body string) bool {
	if exprOK == false {
		return true
	}
	return isXssFound(body)
}

func checkXssHeaders(headers map[string][]string) bool {
	if exprOK == false {
		return true
	}

	for key, values := range headers {
		if isXssFound(key) {
			return true
		}
		for _, value := range values {
			if isXssFound(value) {
				return true
			}
		}
	}
	return false
}

func isXssFound(str string) bool {
	for i, reg := range xssCheckExpr {
		if reg.Match([]byte(str)) {
			logger.Warnf("XSS check fail:", str, " matched to ", xssCheckString[i])
			return true
		}
	}
	return false
}

// 不转义序列化
func marshalInner(data interface{}) ([]byte, error) {
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	if err := jsonEncoder.Encode(data); err != nil {
		return nil, err
	}

	return bf.Bytes(), nil
}
