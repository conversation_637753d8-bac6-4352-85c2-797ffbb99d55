package security

import (
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
	"errors"
	"fmt"
	"net/http"
	"regexp"

	beego "github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
)

var includedUUIDParams map[string]struct{}

func SetUUIDParamsInURL(params map[string]struct{}) {
	includedUUIDParams = params
}

var HandleSQLInjection = func(ctx *context.Context) {
	pathUUIDs := listPathUUIDs(ctx)
	pathErr := raiseErrIfExistsInvalidUUID(pathUUIDs)
	if pathErr != nil {
		logger.Warnf("HandleSQLInjection: illegal path parameter: %v", pathErr)
		panicWhenError(ctx, errors.New("illegal path parameter exists in url"))
	}

	queryUUIDs := listQueryUUIDs(ctx)
	queryErr := raiseErrIfExistsInvalidUUID(queryUUIDs)
	if queryErr != nil {
		logger.Warnf("HandleSQLInjection: illegal query parameter: %v", queryErr)
		panicWhenError(ctx, errors.New("illegal query parameter exists in url"))
	}
}

func listPathUUIDs(ctx *context.Context) map[string]string {
	uuids := map[string]string{}
	params := ctx.Input.Params()
	for key, val := range params {
		if _, ok := includedUUIDParams[key]; ok {
			uuids[key] = val
		}
	}
	return uuids
}

func listQueryUUIDs(ctx *context.Context) map[string]string {
	uuids := map[string]string{}
	params := ctx.Request.URL.Query()
	for key, val := range params {
		if _, ok := includedUUIDParams[key]; ok {
			if len(val) == 1 {
				uuids[key] = val[0]
			}
		}
	}
	return uuids
}

func raiseErrIfExistsInvalidUUID(uuids map[string]string) error {
	if len(uuids) == 0 {
		return nil
	}

	regexpUUID := regexp.MustCompile(globalcv.RegExpStrUUID)
	for key, val := range uuids {
		if !regexpUUID.MatchString(val) {
			return fmt.Errorf("%s %s is not a valid uuid", key, val)
		}
	}
	return nil
}

func panicWhenError(ctx *context.Context, err error) {
	ctx.Output.SetStatus(http.StatusBadRequest)
	response := struct {
		Status  int    `json:"status"`
		Message string `json:"message"`
	}{
		Status:  ctx.Output.Status,
		Message: err.Error(),
	}
	_ = ctx.Output.JSON(response, false, false)
	panic(beego.ErrAbort)
}
