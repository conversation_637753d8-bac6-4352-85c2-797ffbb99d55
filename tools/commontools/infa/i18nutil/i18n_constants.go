package i18nutil

import (
	"fmt"
	"strings"
)

type I18nKey string

type I18nFormat struct {
	ZhMsg string
	EnMsg string
}

func (f I18nFormat) needArgs(lang string) bool {
	msg := ""
	if lang == ZH {
		msg = f.ZhMsg
	} else if lang == EN {
		msg = f.EnMsg
	}
	count := strings.Count(msg, "%s")
	return count > 0
}

type I18nMap map[I18nKey]I18nFormat

func (m I18nMap) Zh() []byte {
	return m.buildLangData(ZH)
}

func (m I18nMap) En() []byte {
	return m.buildLangData(EN)
}

func (m I18nMap) buildLangData(lang string) []byte {
	str := ""
	for k, format := range m {
		switch lang {
		case EN:
			str += fmt.Sprintf("%s=%s\n", k, format.EnMsg)
		case ZH:
			str += fmt.Sprintf("%s=%s\n", k, format.ZhMsg)

		}
	}
	return []byte(str)
}

const (
	EN = "en"
	ZH = "zh"
)

// common
const (
	InvalidBody          = "InvalidBody"
	InvalidFieldInBody   = "InvalidFieldInBody"
	OpenstackError       = "OpenstackError"
	CreateFailed         = "CreateFailed"
	DeleteFailed         = "DeleteFailed"
	UpdateFailed         = "UpdateFailed"
	OperateFailed        = "OperateFailed"
	QueryFailed          = "QueryFailed"
	JsonUnmarshalFailed  = "JsonUnmarshalFailed"
	UrlIncorrect         = "UrlIncorrect"
	AuthorizationFailed  = "AuthorizationFailed"
	UUIDInvalid          = "UUIDInvalid"
	BuildConditionFailed = "BuildConditionFailed"
	GetCloudEnvsFailed   = "GetCloudEnvsFailed"
	CloudEnvNotFound     = "CloudEnvNotFound"
	CloudEnvAbnormal     = "CloudEnvAbnormal"
	GetTenantsFailed     = "GetTenantsFailed"
	GetVdcsFailed        = "GetVdcsFailed"
	GetAccessInfoFailed  = "GetAccessInfoFailed"
	JobBuildFailed       = "JobBuildFailed"
	PasswordDeCodeFailed = "PassWordDeCodeFailed"
	PasswordEnCodeFailed = "PasswordEnCodeFailed"
	NameExceed           = "NameExceed"
	NameDuplicate        = "NameDuplicate"
	DescExceed           = "DescExceed"
	PageParamsError      = "PageParamsError"
)

// image
const (
	GetSubOrgsFailed          = "GetSubOrgsFailed"
	GetFilesFailed            = "GetFilesFailed"
	GetOrgFilesFailed         = "GetOrgFilesFailed"
	GetPublicOrgFilesFailed   = "GetCommonOrgFilesFailed"
	FileNotFound              = "FileNotFound"
	CreateFileFailed          = "CreateFileFailed"
	DeleteFileFailed          = "DeleteFileFailed"
	UpdateFileFailed          = "UpdateFileFailed"
	IncorrectFileSuffix       = "IncorrectFileSuffix"
	GetImagesFailed           = "GetImagesFailed"
	GetOrgImagesFailed        = "GetOrgImagesFailed"
	GetPublicOrgImagesFailed  = "GetPublicOrgImagesFailed"
	GetImagesCountFailed      = "GetImagesCountFailed"
	ListUnuseImagesFailed     = "ListUnuseImagesFailed"
	ImageNotFound             = "ImageNotFound"
	ImageInvalid              = "ImageInvalid"
	PublishImageFailed        = "PublishImageFailed"
	DeleteImageFailed         = "DeleteImageFailed"
	UpdateImageFailed         = "UpdateImageFailed"
	ConvertImageDetailFailed  = "ConvertImageDetailFailed"
	ImageCannotResumeTransfer = "ImageCannotResumeTransfer"
	FTPServerNotAvailable     = "FTPServerNotAvailable"
	FileHasBeenPublishedEnvs  = "FileHasBeenPublishedEnvs"
	GetOsTypeVersionFailed    = "GetOsTypeVersionFailed"
	GetVdcImagesFailed        = "GetVdcImagesFailed"
	VdcImagesNotFound         = "VdcImagesNotFound"
	CreateVdcImageFailed      = "CreateVdcImageFailed"
	DeleteVdcImageFailed      = "DeleteVdcImageFailed"
	UpdateVdcImageFailed      = "UpdateVdcImageFailed"
)

// cloudfuze
const (
	GetMsbFailed = "GetMsbFailed"
)

var i18nMap = I18nMap{
	InvalidBody:          I18nFormat{ZhMsg: "非法请求", EnMsg: "Requested body is probably invalid"},
	InvalidFieldInBody:   I18nFormat{ZhMsg: "无效字段【%s】存在于请求体中", EnMsg: "Invalid field [%s] in request body"},
	OpenstackError:       I18nFormat{ZhMsg: "%s", EnMsg: "%s"},
	CreateFailed:         I18nFormat{ZhMsg: "创建资源失败, %s", EnMsg: "Create resource failed, %s"},
	DeleteFailed:         I18nFormat{ZhMsg: "删除资源失败, %s", EnMsg: "Delete resource failed, %s"},
	UpdateFailed:         I18nFormat{ZhMsg: "编辑资源失败, %s", EnMsg: "Update resource failed, %s"},
	OperateFailed:        I18nFormat{ZhMsg: "操作资源失败, %s", EnMsg: "Operate resource failed, %s"},
	QueryFailed:          I18nFormat{ZhMsg: "查询资源失败, %s", EnMsg: "Query resource failed, %s"},
	JsonUnmarshalFailed:  I18nFormat{ZhMsg: "反序列化失败", EnMsg: "JSON deserialization failed"},
	UrlIncorrect:         I18nFormat{ZhMsg: "不正确的请求URL", EnMsg: "Incorrect request url"},
	AuthorizationFailed:  I18nFormat{ZhMsg: "请求未被授权", EnMsg: "Request not authorized"},
	UUIDInvalid:          I18nFormat{ZhMsg: "请求体中包含无效的UUID", EnMsg: "The request body contains invalid UUID"},
	BuildConditionFailed: I18nFormat{ZhMsg: "构建数据库查询条件失败", EnMsg: "Failed to build database query conditions"},
	GetCloudEnvsFailed:   I18nFormat{ZhMsg: "查询云环境信息失败", EnMsg: "Failed to query cloud environment information"},
	CloudEnvNotFound:     I18nFormat{ZhMsg: "云环境不存在", EnMsg: "Cloud not found"},
	CloudEnvAbnormal:     I18nFormat{ZhMsg: "云环境异常", EnMsg: "Abnormal cloud environment"},
	GetTenantsFailed:     I18nFormat{ZhMsg: "查询租户信息失败", EnMsg: "Failed to query tenant information"},
	GetVdcsFailed:        I18nFormat{ZhMsg: "查询VDC失败", EnMsg: "Failed to query VDC"},
	GetAccessInfoFailed:  I18nFormat{ZhMsg: "获取openstack访问令牌失败", EnMsg: "Failed to get openstack access token"},
	JobBuildFailed:       I18nFormat{ZhMsg: "构建异步任务失败", EnMsg: "Failed to build asynchronous task"},
	PasswordDeCodeFailed: I18nFormat{ZhMsg: "密码解密失败", EnMsg: "Password decryption failed"},
	PasswordEnCodeFailed: I18nFormat{ZhMsg: "密码加密失败", EnMsg: "Password encryption failed"},
	NameExceed:           I18nFormat{ZhMsg: "名字长度超过%s", EnMsg: "Name length exceeds %s"},
	NameDuplicate:        I18nFormat{ZhMsg: "名字重复", EnMsg: "Duplicate name"},
	DescExceed:           I18nFormat{ZhMsg: "描述长度超过%s", EnMsg: "Description length exceeds %s"},
	PageParamsError:      I18nFormat{ZhMsg: "分页参数错误", EnMsg: "Paging parameter error"},

	GetSubOrgsFailed:          I18nFormat{ZhMsg: "获取组织下的子组织列表失败", EnMsg: "Failed to obtain the list of sub organizations under the organization"},
	GetFilesFailed:            I18nFormat{ZhMsg: "查询镜像文件失败", EnMsg: "Failed to query image files"},
	GetOrgFilesFailed:         I18nFormat{ZhMsg: "查询组织下的镜像文件失败", EnMsg: "Failed to query image file under organization"},
	GetPublicOrgFilesFailed:   I18nFormat{ZhMsg: "查询组织下的公共镜像文件失败", EnMsg: "Failed to query public image files under organization"},
	FileNotFound:              I18nFormat{ZhMsg: "镜像文件不存在", EnMsg: "Image file not found"},
	CreateFileFailed:          I18nFormat{ZhMsg: "创建镜像文件失败", EnMsg: "Failed to create image files"},
	DeleteFileFailed:          I18nFormat{ZhMsg: "删除镜像文件失败", EnMsg: "Failed to delete image files"},
	UpdateFileFailed:          I18nFormat{ZhMsg: "更新镜像文件失败", EnMsg: "Failed to update image files"},
	IncorrectFileSuffix:       I18nFormat{ZhMsg: "不正确镜像文件名后缀", EnMsg: "Incorrect image file name suffix"},
	GetImagesFailed:           I18nFormat{ZhMsg: "查询镜像失败", EnMsg: "Failed to query images"},
	GetOrgImagesFailed:        I18nFormat{ZhMsg: "查询组织下镜像失败", EnMsg: "Failed to query images under organization"},
	GetPublicOrgImagesFailed:  I18nFormat{ZhMsg: "查询组织下公共镜像失败", EnMsg: "Failed to query public images under organization"},
	GetImagesCountFailed:      I18nFormat{ZhMsg: "查询镜像数量失败", EnMsg: "Failed to count images"},
	ListUnuseImagesFailed:     I18nFormat{ZhMsg: "查询未使用镜像失败", EnMsg: "Failed to query unused images"},
	ImageNotFound:             I18nFormat{ZhMsg: "镜像不存在", EnMsg: "Image not found"},
	ImageInvalid:              I18nFormat{ZhMsg: "镜像受保护或者镜像不存在", EnMsg: "Image is protected or not found"},
	PublishImageFailed:        I18nFormat{ZhMsg: "发布镜像失败", EnMsg: "Failed to publish image"},
	DeleteImageFailed:         I18nFormat{ZhMsg: "删除镜像失败", EnMsg: "Failed to delete image"},
	UpdateImageFailed:         I18nFormat{ZhMsg: "更新镜像失败", EnMsg: "Failed to update image"},
	ConvertImageDetailFailed:  I18nFormat{ZhMsg: "转换镜像详情失败", EnMsg: "Failed to convert image details"},
	ImageCannotResumeTransfer: I18nFormat{ZhMsg: "镜像无法继续传输", EnMsg: "Image cannot resume transfer"},
	FTPServerNotAvailable:     I18nFormat{ZhMsg: "FTP服务不可用", EnMsg: "FTP server is not available"},
	FileHasBeenPublishedEnvs:  I18nFormat{ZhMsg: "镜像文件已经发布到云上", EnMsg: "Image file has been published cloud environment"},
	GetOsTypeVersionFailed:    I18nFormat{ZhMsg: "获取操作系统类型及版本失败", EnMsg: "Failed to obtain type and version of the system"},
	GetVdcImagesFailed:        I18nFormat{ZhMsg: "查询VDC下的镜像失败", EnMsg: "Failed to query VDC images"},
	VdcImagesNotFound:         I18nFormat{ZhMsg: "VDC镜像不存在", EnMsg: "VDC image not found"},
	CreateVdcImageFailed:      I18nFormat{ZhMsg: "创建VDC镜像失败", EnMsg: "Failed to create VDC image"},
	DeleteVdcImageFailed:      I18nFormat{ZhMsg: "删除VDC镜像失败", EnMsg: "Failed to delete VDC image"},
	UpdateVdcImageFailed:      I18nFormat{ZhMsg: "更新VDC镜像失败", EnMsg: "Failed to update VDC image"},

	GetMsbFailed: I18nFormat{ZhMsg: "查询msb端口失败", EnMsg: "failed to get msb port"},
}
