package i18nutil

import (
	"cwsm/tools/commontools/logger"
	"os"
	"path"

	"github.com/beego/i18n"
)

func InitLang() {
	if checkI18nDataLoad() {
		return
	}
	defer cleanTempFile()
	err := i18n.SetMessageData(ZH, i18nMap.Zh())
	err = i18n.SetMessageData(EN, i18nMap.En())
	if err != nil {
		logger.Errorf("[InitLang] failed: %s", err.Error())
	}
}

func checkI18nDataLoad() bool {
	zh := i18n.Tr(ZH, InvalidBody)
	en := i18n.Tr(EN, InvalidBody)
	return zh == i18nMap[InvalidBody].ZhMsg && en == i18nMap[InvalidBody].EnMsg
}

func cleanTempFile() {
	p := path.Join(os.TempDir(), "goconfig")
	if err := os.RemoveAll(p); err != nil {
		logger.Errorf("[cleanTempFile] failed: %s", err.<PERSON>rror())
	}
	logger.Infof("i18n init finished")
}

func TrWithArgs(lang string, k I18nKey, args ...string) string {
	switch lang {
	case ZH, EN:
		if format, ok := i18nMap[k]; ok {
			if format.needArgs(lang) {
				return i18n.Tr(lang, string(k), args)
			} else {
				return i18n.Tr(lang, string(k))
			}
		}
	}
	return string(k)
}
