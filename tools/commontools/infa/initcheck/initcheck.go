package initcheck

import (
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/infa/task"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/migration"
	otcpres "cwsm/tools/commontools/otcp/res"
	otcpsm "cwsm/tools/commontools/otcp/sm"
	"cwsm/tools/commontools/service/msbservice"
	"errors"
)

func BeforeCheck(migrationIF migration.MigrationProcessIF) error {
	err := BeforeCheck4Otcp()
	if err != nil {
		return errors.New("BeforeCheck BeforeCheck4Otcp failed:%s" + err.Error())
	}

	err = BeforeCheck4Migration(migrationIF)
	if err != nil {
		return errors.New("BeforeCheck BeforeCheck4Migration failed:%s" + err.Error())
	}

	return nil
}

func BeforeCheck4Otcp() error {
	if !util.WaitForTimes(30, 5, CheckOTCPMsbInfo) {
		return errors.New("BeforeCheck CheckOTCPMsbInfo failed")
	}

	if !util.WaitForTimes(30, 5, CheckOesRm) {
		return errors.New("BeforeCheck CheckOesRm failed")
	}

	if !util.WaitForTimes(30, 5, CheckRegisterOesSm) {
		return errors.New("BeforeCheck CheckRegisterOesSm failed")
	}

	return nil
}

func BeforeCheck4Migration(migrationIF migration.MigrationProcessIF) error {
	err := migration.StartMigration(migrationIF)
	if err != nil {
		return errors.New("BeforeCheck migration failed:%s" + err.Error())
	}
	return nil
}

func CheckOTCPMsbInfo() bool {
	logger.Infof("CheckOTCPMsbInfo begin")
	serviceURLPortMaps, err := msbservice.InitMicroServicesFromMsb()
	if err != nil {
		logger.Errorf("CheckOTCPMsbInfo InitMicroServicesFromMsb failed:%s" + err.Error())
	}
	globalcv.ServiceURLMaps = serviceURLPortMaps
	logger.Infof("DependCheck curl msb services:", util.ToJSONStr(serviceURLPortMaps))
	return len(serviceURLPortMaps) == len(msbservice.ServiceDefaultPortMap)
}

func CheckOesRm() bool {
	logger.Infof("CheckOesRm begin")
	err := otcpres.ResHealthCheck()
	if err != nil {
		logger.Errorf("CheckOesRm ResHealthCheck failed:%s" + err.Error())
		return false
	}
	return true
}

func CheckRegisterOesSm() bool {
	logger.Infof("CheckRegisterOesSm begin")
	err := otcpsm.PostOperateCodeToSM()
	if err != nil {
		logger.Errorf("CheckRegisterOesSm PostOperateCodeToSM failed:%s" + err.Error())
		return false
	}
	return true
}

func CrashCheckAndRecovery() bool {
	if task.AllRunnerAsync != nil && len(task.AllRunnerAsync) >= 0 {
		return true
	}

	// when the executor reaches here, it means that the program has been interrupted by some unexpected forces;
	err := task.RecoverAsyncTasks()
	if err != nil {
		logger.Error("[CrashCheckAndRecovery] err: %v", err.Error())
		return false
	}
	return true
}
