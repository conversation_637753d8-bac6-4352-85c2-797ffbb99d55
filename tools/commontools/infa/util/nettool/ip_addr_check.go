package nettool

import (
	"fmt"
	"math/big"
	"net"
	"regexp"
	"strconv"
	"strings"
	"cwsm/tools/commontools/logger"
)

const (
	httpRegex = "(http|https)://"
	ipv4Regex = "((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)"
	ipv6Regex = "([\\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)" +
		"|::([\\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)" +
		"|([\\da-fA-F]{1,4}:):([\\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d" +
		"|[01]?\\d\\d?)|([\\da-fA-F]{1,4}:){2}:([\\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]" +
		"|2[0-4]\\d|[01]?\\d\\d?)|([\\da-fA-F]{1,4}:){3}:([\\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\\d" +
		"|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)" +
		"|([\\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)" +
		"|([\\da-fA-F]{1,4}:){7}[\\da-fA-F]{1,4}|:((:[\\da-fA-F]{1,4}){1,6}|:)" +
		"|[\\da-fA-F]{1,4}:((:[\\da-fA-F]{1,4}){1,5}|:)|([\\da-fA-F]{1,4}:){2}((:[\\da-fA-F]{1,4}){1,4}|:)" +
		"|([\\da-fA-F]{1,4}:){3}((:[\\da-fA-F]{1,4}){1,3}|:)|([\\da-fA-F]{1,4}:){4}((:[\\da-fA-F]{1,4}){1,2}|:)" +
		"|([\\da-fA-F]{1,4}:){5}:([\\da-fA-F]{1,4})?|([\\da-fA-F]{1,4}:){6}:"
	dnRegex   = "[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+.?"
	portRegex = "([1-5]?\\d{1,4}|6[1-5][1-5][1-3][1-5])"

	ipv4AddrRegex = "^" + httpRegex + "(" + ipv4Regex + "):" + portRegex + "$"
	ipv6AddrRegex = "^" + httpRegex + "(\\[(" + ipv6Regex + ")\\]):" + portRegex + "$"
	dnAddrRegex   = "^" + httpRegex + "(" + dnRegex + "):" + portRegex + "$"
)

type URLType string

const (
	URLTypeIPV4 URLType = "ipv4"
	URLTypeIPV6 URLType = "ipv6"
	URLTypeDN   URLType = "dn"
)

func IsValidAddrType(address string, urlType URLType) bool {
	address = strings.Trim(address, " ")
	var addrRegex string
	switch urlType {
	case URLTypeIPV4:
		addrRegex = ipv4AddrRegex
	case URLTypeIPV6:
		addrRegex = ipv6AddrRegex
	case URLTypeDN:
		addrRegex = dnAddrRegex
	default:
		return false
	}
	return regexMatch(address, addrRegex)
}

func IsValidIPType(ip string, urlType URLType) bool {
	var ipRegex string
	switch urlType {
	case URLTypeIPV4:
		ipRegex = ipv4Regex
	case URLTypeIPV6:
		ipRegex = ipv6Regex
	case URLTypeDN:
		ipRegex = dnRegex
	default:
		return false
	}
	return regexMatch(ip, ipRegex)
}

func IsValidAddr(address string) bool {
	for _, exp := range []string{ipv4AddrRegex, ipv6AddrRegex, dnAddrRegex} {
		if regexMatch(address, exp) {
			return true
		}
	}
	return false
}

func ExtractAddressIP(address string) string {
	addrRegexes := [][]string{{ipv4AddrRegex, ipv4Regex}, {ipv6AddrRegex, ipv6Regex}, {dnAddrRegex, dnRegex}}
	var pattern string
	for _, regs := range addrRegexes {
		if regexMatch(address, regs[0]) {
			pattern = regs[1]
			break
		}
	}
	re, _ := regexp.Compile(pattern)
	return re.FindString(address)
}

func regexMatch(str, pattern string) bool {
	re, err := regexp.Compile(pattern)
	if err != nil {
		logger.Errorf("regexMatch string %s doesn't match with pattern %s", str, pattern)
		return false
	}
	if re.MatchString(str) {
		logger.Debugf("string %s matches pattern %s", str, pattern)
		return true
	}
	return false
}

func IpFormatter(inputIp string) string {
	ipTmp := inputIp
	if IsValidIPType(inputIp, URLTypeIPV6) {
		ipTmp = fmt.Sprintf("[%s]", inputIp)
	}
	return ipTmp
}

// GetIPAndPort get ip and port from address
//
// address must be in the form of `ip:port`
func GetIPAndPort(addr string) (string, string) {
	idx := strings.LastIndex(addr, ":")
	if idx > 0 {
		return addr[:idx], addr[idx+1:]
	}
	return addr, ""
}

func AddressToIP(addr string) string {

	return ""
}

func UniformIPV6Address(ipv6Addr string) string {
	_, port := GetIPAndPort(ipv6Addr)

	index := strings.Index(ipv6Addr, "//")
	protocol := ipv6Addr[:index]

	ip := ExtractAddressIP(ipv6Addr)
	ipv6Ip := net.ParseIP(ip)

	ipv6URL := protocol + "//[" + ipv6Ip.String() + "]:" + port
	return ipv6URL
}

func IPv6toBigInt(IPv6Address net.IP) *big.Int {
	IPv6Int := big.NewInt(0)
	IPv6Int.SetBytes(IPv6Address.To16())
	return IPv6Int
}
func Ipv4toInt(ip string) int64 {
	ret := big.NewInt(0)
	ret.SetBytes(net.ParseIP(ip).To4())
	return ret.Int64()
}

func Ip2DecimalStr(ip string) string {
	if IsValidIPType(ip, URLTypeIPV4) {
		return strconv.FormatInt(Ipv4toInt(ip), 10)
	}
	if IsValidIPType(ip, URLTypeIPV6) {
		return IPv6toBigInt(net.ParseIP(ip)).String()
	}
	return ""
}
