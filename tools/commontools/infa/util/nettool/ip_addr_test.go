package nettool

import (
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"net"
	"testing"
	"cwsm/tools/commontools/logger"
)

const (
	IPV6_ADDR_FULL           = "https://[fe80:0000:92e2:0000:0000:0000:0000:3478]:443"
	IPV6_ADDR_SIMPLIFIED     = "https://[fe80:0000:92e2::3478]:443"
	IPV6_ADDR_IGNORE_CAPITAL = "https://[FE80:0000:92E2::3478]:443"
)

func TestRsaApiTest(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "ipv6 authentication test")
}

var _ = Describe("IP authentication test", func() {
	Describe("IPV6 test", func() {
		It("should identify the three identical ipv6 addresses", func() {

			ip, port := GetIPAndPort(IPV6_ADDR_FULL)
			logger.Infof("IP: %s; Port: %s", ip, port)

			isvalidType := IsValidIPType(IPV6_ADDR_SIMPLIFIED, URLTypeIPV6)
			Expect(true).To(Equal(isvalidType))

			isvalidURL := IsValidAddr(IPV6_ADDR_FULL)
			Expect(true).To(Equal(isvalidURL))

			ipv6_full_ip := ExtractAddressIP(IPV6_ADDR_FULL)
			logger.Infof("IP extracted: %s", ipv6_full_ip)
			ipv6_full_parsed := net.ParseIP(ipv6_full_ip)
			if len(ipv6_full_parsed) == 0 {
				logger.Errorf("invalid ipv6_full address")
			} else {
				logger.Infof("valid ipv6_full address: %s", ipv6_full_parsed)
			}

			ipv6_simplified_ip := ExtractAddressIP(IPV6_ADDR_SIMPLIFIED)
			logger.Infof("IP extracted: %s", ipv6_simplified_ip)
			ipv6_simplified_parsed := net.ParseIP(ipv6_simplified_ip)
			if len(ipv6_simplified_parsed) == 0 {
				logger.Errorf("invalid ipv6_simplified address")
			} else {
				logger.Infof("valid ipv6_simplified address: %s", ipv6_simplified_parsed)
			}

			ipv6_ignore_capital_ip := ExtractAddressIP(IPV6_ADDR_IGNORE_CAPITAL)
			logger.Infof("IP extracted: %s", ipv6_ignore_capital_ip)
			ipv6_ignore_capital_parsed := net.ParseIP(ipv6_ignore_capital_ip)
			if len(ipv6_ignore_capital_parsed) == 0 {
				logger.Errorf("invalid ipv6_ignore_capital address")
			} else {
				logger.Infof("valid ipv6_ignore_capital address: %s", ipv6_ignore_capital_parsed)
			}

			Expect(true).To(Equal(ipv6_full_parsed.String() == ipv6_simplified_parsed.String()))
			Expect(true).To(Equal(ipv6_simplified_parsed.String() == ipv6_ignore_capital_parsed.String()))
			Expect(true).To(Equal(ipv6_full_parsed.String() == ipv6_ignore_capital_parsed.String()))
			logger.Infof("the three ipv6 addresses are identical")

		})

	})
})
