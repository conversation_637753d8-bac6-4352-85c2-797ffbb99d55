package compare

import (
	"time"
)

type VMList struct {
	VMsInfo []*VMInfo `json:"vmInfo"`
}
type Flavor struct {
	ID           string `json:"id"`
	OriginalName string `json:"original_name"`
	Vcpus        int    `json:"vcpus"`
	RAM          int    `json:"ram"`
	Disk         int    `json:"disk"`
}

type Addresses struct {
	Vlan216 []*Vlan216 `json:"vlan216"`
}

type Vlan216 struct {
	Version            int    `json:"version"`
	Addr               string `json:"addr"`
	OSEXTIPSMACMacAddr string `json:"OS-EXT-IPS-MAC:mac_addr"`
	OSEXTIPSType       string `json:"OS-EXT-IPS:type"`
}

type CPUPinning struct {
	Num0 int `json:"0"`
	Num1 int `json:"1"`
}
type CPUTopology struct {
	Cores   int `json:"cores"`
	Threads int `json:"threads"`
	Sockets int `json:"sockets"`
}
type NumaTopology struct {
	ID              string       `json:"id"`
	Cpuset          []int        `json:"cpuset"`
	CPUPinning      *CPUPinning  `json:"cpu_pinning"`
	CPUTopology     *CPUTopology `json:"cpu_topology"`
	Memory          int          `json:"memory"`
	Pagesize        int          `json:"pagesize"`
	CPUPolicy       string       `json:"cpu_policy"`
	CPUThreadPolicy interface{}  `json:"cpu_thread_policy"`
	NamePolicy      interface{}  `json:"name_policy"`
}
type ParentDn struct {
	DirectorHost   []string `json:"director.host"`
	DirectorTenant []string `json:"director.tenant"`
}
type VMInfo struct {
	Name           string          `json:"name"`
	Dc             string          `json:"dc"`
	CloudEnv       string          `json:"cloudEnv"`
	EnvType        string          `json:"envType"`
	Az             string          `json:"az"`
	HaNames        []string        `json:"haNames"`
	Host           string          `json:"host"`
	HostNameShow   string          `json:"hostNameShow"`
	Image          string          `json:"image"`
	ImageID        interface{}     `json:"imageId"`
	VolumeIds      []string        `json:"volumeIds"`
	IP             []string        `json:"ip"`
	State          interface{}     `json:"state"`
	PowerState     string          `json:"powerState"`
	TenantID       string          `json:"tenantId"`
	TenantName     string          `json:"tenantName"`
	UserScopedVMID string          `json:"userScopedVmId"`
	CloudEnvID     string          `json:"cloudEnvId"`
	Flavor         *Flavor         `json:"flavor"`
	Addresses      *Addresses      `json:"addresses"`
	ID             string          `json:"id"`
	VdcID          string          `json:"vdcId"`
	VdcName        string          `json:"vdcName"`
	Created        time.Time       `json:"created"`
	Updated        time.Time       `json:"updated"`
	Status         string          `json:"status"`
	DomainID       string          `json:"domainId"`
	ComputerType   string          `json:"computerType"`
	DataStore      []interface{}   `json:"dataStore"`
	Ports          []interface{}   `json:"ports"`
	NumaTopology   []*NumaTopology `json:"numa_topology"`
	VolumeNames    []string        `json:"volumeNames"`
	VMState        string          `json:"vmState"`
	Locked         string          `json:"locked"`
	Fault          interface{}     `json:"fault"`
	HostID         string          `json:"hostId"`
	OsType         interface{}     `json:"os_type"`
	ParentDn       *ParentDn       `json:"parentDn"`
}

var Vm01Bytes []byte = []byte(`{"vmInfo":[{"name":"trest","dc":"DC_SH","cloudEnv":"bms","envType":"openstack","az":"nova",
"haNames":["HA30"],"host":"host-2025-201--17-30-9-160","hostNameShow":"host-2025-201--17-30-9-160",
"image":"{\"id\":\"c025a451-b037-401d-9786-fd9a281db28c\",\"links\":[{\"rel\":\"bookmark\",\"href\":\"https://************:8774/cf28abf4d3494d77b57c8de161cd4634/images/c025a451-b037-401d-9786-fd9a281db28c\"}]}",
"imageId":"c025a451-b037-401d-9786-fd9a281db28c","volumeIds":[],"ip":["2021:15::236"],"state":"","powerState":"running",
"tenantId":"63e9735bed1c46028312112198095c43","tenantName":"luo1admin","userScopedVmId":"caed06f2-b0ac-4cdf-9b4d-dd4ee74855a4",
"cloudEnvId":"358fe8f2-2d12-4a87-8c01-4acccd680983","flavor":{"id":"","original_name":"flavor02","vcpus":2,"ram":1024,"disk":5},
"addresses":{"vlan216":[{"version":6,"addr":"2021:15::236","OS-EXT-IPS-MAC:mac_addr":"fa:16:3e:13:6e:f8","OS-EXT-IPS:type":"fixed"}]},
"id":"caed06f2-b0ac-4cdf-9b4d-dd4ee74855a4","vdcId":"","vdcName":"","created":"2022-06-14T06:29:23Z","updated":"2022-06-14T06:29:56Z",
"status":"active","domainId":"320dba35-1422-4aea-9b84-a94840fecdd2","computerType":"vm","dataStore":[],"metadata":{},"ports":[],
"numa_topology":[{"id":"","cpuset":[0,1],"cpu_pinning":{"0":25,"1":5},"cpu_topology":{"cores":1,"threads":2,"sockets":1},
"memory":1024,"pagesize":1048576,"cpu_policy":"dedicated","cpu_thread_policy":"","name_policy":""}],"volumeNames":[],
"vmState":"active","locked":"false","fault":"","hostId":"52b2a7a5-5113-332f-9dee-bc10d6714668","os_type":"","nics_info":[],
"parentDn":{"director.host":["52b2a7a5-5113-332f-9dee-bc10d6714668"],"director.tenant":["63e9735bed1c46028312112198095c43"]}}]}`)

var Vm01BytesVariant []byte = []byte(`{"vmInfo":[{"dc":"DC_SH","envType":"openstack","name":"trest","cloudEnv":"bms","az":"nova",
"haNames":["HA30"],"host":"host-2025-201--17-30-9-160","hostNameShow":"host-2025-201--17-30-9-160","image":"{\"id\":\"c025a451-b037-401d-9786-fd9a281db28c\",\"links\":[{\"rel\":\"bookmark\",\"href\":\"https://************:8774/cf28abf4d3494d77b57c8de161cd4634/images/c025a451-b037-401d-9786-fd9a281db28c\"}]}",
"imageId":"c025a451-b037-401d-9786-fd9a281db28c","volumeIds":[],"ip":["2021:15::236"],"state":"","powerState":"running",
"tenantId":"63e9735bed1c46028312112198095c43","tenantName":"luo1admin","userScopedVmId":"caed06f2-b0ac-4cdf-9b4d-dd4ee74855a4",
"cloudEnvId":"358fe8f2-2d12-4a87-8c01-4acccd680983","flavor":{"id":"","original_name":"flavor02","vcpus":2,"ram":1024,"disk":5},
"addresses":{"vlan216":[{"version":6,"addr":"2021:15::236","OS-EXT-IPS-MAC:mac_addr":"fa:16:3e:13:6e:f8","OS-EXT-IPS:type":"fixed"}]},
"id":"caed06f2-b0ac-4cdf-9b4d-dd4ee74855a4","vdcId":"","vdcName":"","created":"2022-06-14T06:29:23Z","updated":"2022-06-14T06:29:56Z",
"status":"active","domainId":"320dba35-1422-4aea-9b84-a94840fecdd2","computerType":"vm","dataStore":[],"metadata":{},"ports":[],
"numa_topology":[{"id":"","cpuset":[0,1],"cpu_pinning":{"0":25,"1":5},"cpu_topology":{"cores":1,"threads":2,"sockets":1},
"memory":1024,"pagesize":1048576,"cpu_policy":"dedicated","cpu_thread_policy":"","name_policy":""}],"volumeNames":[],
"vmState":"active","locked":"false","fault":"","hostId":"52b2a7a5-5113-332f-9dee-bc10d6714668","os_type":"","nics_info":[],
"parentDn":{"director.host":["52b2a7a5-5113-332f-9dee-bc10d6714668"],"director.tenant":["63e9735bed1c46028312112198095c43"]}}]}`)

var Vm01BytesUpdated []byte = []byte(`{"vmInfo":[{"name":"trest","dc":"DC_SH","cloudEnv":"bms","envType":"openstack","az":"nova",
"haNames":["HA30"],"host":"host-2025-201--17-30-9-160","hostNameShow":"host-2025-201--17-30-9-160","image":"{\"id\":\"c025a451-b037-401d-9786-fd9a281db28c\",\"links\":[{\"rel\":\"bookmark\",\"href\":\"https://************:8774/cf28abf4d3494d77b57c8de161cd4634/images/c025a451-b037-401d-9786-fd9a281db28c\"}]}",
"imageId":"c025a451-b037-401d-9786-fd9a281db28c","volumeIds":[],"ip":["2021:15::236"],"state":"","powerState":"running",
"tenantId":"63e9735bed1c46028312112198095c43","tenantName":"luo1admin","userScopedVmId":"caed06f2-b0ac-4cdf-9b4d-dd4ee74855a4",
"cloudEnvId":"358fe8f2-2d12-4a87-8c01-4acccd680983","flavor":{"id":"","original_name":"flavor02","vcpus":2,"ram":1024,"disk":5},
"addresses":{"vlan216":[{"version":6,"addr":"2021:15::236","OS-EXT-IPS-MAC:mac_addr":"fa:16:3e:13:6e:f8","OS-EXT-IPS:type":"error"}]},
"id":"caed06f2-b0ac-4cdf-9b4d-dd4ee74855a4","vdcId":"","vdcName":"","created":"2022-06-14T06:29:23Z","updated":"2022-06-14T06:29:56Z",
"status":"error","domainId":"320dba35-1422-4aea-9b84-a94840fecdd2","computerType":"vm","dataStore":[],"metadata":{},"ports":[],
"numa_topology":[{"id":"","cpuset":[0,1],"cpu_pinning":{"0":25,"1":5},"cpu_topology":{"cores":2,"threads":4,"sockets":1},
"memory":1024,"pagesize":1048576,"cpu_policy":"dedicated","cpu_thread_policy":"","name_policy":""}],"volumeNames":[],
"vmState":"active","locked":"false","fault":"","hostId":"52b2a7a5-5113-332f-9dee-bc10d6714668","os_type":"","nics_info":[],
"parentDn":{"director.host":["52b2a7a5-5113-332f-9dee-bc10d6714668"],"director.tenant":["63e9735bed1c46028312112198095c43"]}}]}`)

var Vm02Bytes []byte = []byte(`{"vmInfo":[{"name":"testfeng","dc":"DC_SH","cloudEnv":"bms","envType":"openstack","az":"nova",
"haNames":["HA30"],"host":"host-2025-201--17-30-9-159","hostNameShow":"host-2025-201--17-30-9-159","image":"\"\"","imageId":"",
"volumeIds":["72ce06cd-5bc3-43af-8c00-c5df39c86231"],"ip":[""],"state":"","powerState":"running","tenantId":"cf28abf4d3494d77b57c8de161cd4634",
"tenantName":"admin","userScopedVmId":"dc8d7d70-57f3-484c-b169-ffac78ca4aa7","cloudEnvId":"358fe8f2-2d12-4a87-8c01-4acccd680983",
"flavor":{"id":"","original_name":"flavor02","vcpus":2,"ram":1024,"disk":5},"addresses":{},"id":"dc8d7d70-57f3-484c-b169-ffac78ca4aa7",
"vdcId":"","vdcName":"","created":"2022-06-17T06:48:06Z","updated":"2022-06-17T06:49:32Z","status":"active","domainId":"320dba35-1422-4aea-9b84-a94840fecdd2",
"computerType":"vm","dataStore":[],"metadata":{},"ports":[],"numa_topology":[{"id":"","cpuset":[0,1],"cpu_pinning":{"0":3,"1":23},
"cpu_topology":{"cores":1,"threads":2,"sockets":1},"memory":1024,"pagesize":1048576,"cpu_policy":"dedicated","cpu_thread_policy":"",
"name_policy":""}],"volumeNames":["testfeng"],"vmState":"active","locked":"false","fault":"","hostId":"9d6ceea7-4355-3a6c-b8a0-47fc49171a22",
"os_type":"","nics_info":[],"parentDn":{"director.host":["9d6ceea7-4355-3a6c-b8a0-47fc49171a22"],"director.tenant":["cf28abf4d3494d77b57c8de161cd4634"]}}]}`)

var Vm02BytesUpdated []byte = []byte(`{"vmInfo":[{"name":"testfeng","dc":"DC_SH","cloudEnv":"bms","envType":"openstack","az":"nova",
"haNames":["HA30"],"host":"host-2025-201--17-30-9-159","hostNameShow":"host-2025-201--17-30-9-159","image":"\"\"","imageId":"",
"volumeIds":["72ce06cd-5bc3-43af-8c00-c5df39c86231"],"ip":[""],"state":"","powerState":"pending","tenantId":"cf28abf4d3494d77b57c8de161cd4634",
"tenantName":"admin","userScopedVmId":"dc8d7d70-57f3-484c-b169-ffac78ca4aa7","cloudEnvId":"358fe8f2-2d12-4a87-8c01-4acccd680983",
"flavor":{"id":"","original_name":"flavor02","vcpus":2,"ram":2048,"disk":10},"addresses":{},"id":"dc8d7d70-57f3-484c-b169-ffac78ca4aa7",
"vdcId":"","vdcName":"","created":"2022-06-17T06:48:06Z","updated":"2022-06-17T06:49:32Z","status":"active","domainId":"320dba35-1422-4aea-9b84-a94840fecdd2",
"computerType":"vm","dataStore":[],"metadata":{},"ports":[],"numa_topology":[{"id":"","cpuset":[0,1],"cpu_pinning":{"0":3,"1":23},
"cpu_topology":{"cores":1,"threads":2,"sockets":1},"memory":1024,"pagesize":1048576,"cpu_policy":"dedicated","cpu_thread_policy":"",
"name_policy":""}],"volumeNames":["testfeng"],"vmState":"active","locked":"false","fault":"","hostId":"9d6ceea7-4355-3a6c-b8a0-47fc49171a22",
"os_type":"","nics_info":[],"parentDn":{"director.host":["9d6ceea7-4355-3a6c-b8a0-47fc49171a22"],"director.tenant":["cf28abf4d3494d77b57c8de161cd4634"]}}]}`)
