package compare_api

import (
	"cwsm/tools/commontools/infa/util/universal_compare/compare_dtos"
	"cwsm/tools/commontools/infa/util/universal_compare/compare_implement"
	"cwsm/tools/commontools/logger"
	"fmt"
	"reflect"
	"runtime"
)

/* ===================================================================================================================================

Read me:
A generally applicable function of Comparison and Disparities Extraction for any two input data objects with unknown types;
As for the bottom implementation, It has two different branches separately driven by two distinct comparing algorithms
- Recursive and Iterative(DFS);

Brief User Spec:
1. If you intend to operate batch comparison for any type of struct kind data, you should point out a primary key that is unique;
Otherwise, the algorithm will search for possible one like "id" or "ID" and make it effective when picking out differences of
the two bunch of data list;

2. If you need the detailed information of the comparison result, set the NeedDetails variable as true; Then the algorithm will
yield corresponding details:
- for any single pair of struct-formed objects, all the different fields at the first level of the struct will be picked
out in the form of a field name list;

- for multiple pairs of struct-formed objects, differences like objects to be added, deleted and updated according to the
primary key will be picked out, and for each pair having the same primary key you can acquire the changed fields if necessary;

- for multiple pairs of struct-formed objects, if you have designated any primary key or there are ones that can be found,
the sequence of the objects in each list does not have any influence on the comparing result;

- for struct-formed objects, if you want to make any fields ineffective for the comparison, you need to give the specified
field names and their parent field or struct names one by one, so that the algorithm will overlook those fields; the parent
field names are needed for the sake of possible duplicate names in nested struct;

- for single or multiple pairs of bare slices or nested slices, the algorithm will only tell if they are equal or not
without any detailed information even you intend to secure it.

- for single pair of maps, the detailed comparison result will include all the different keys only when using iterative manner,
whereas recursive approach does not support this feature because of the tight time span of development.

3. When assigning the compareApproach variable with 1, you are pointing the Iterative approach,
  When assigning with 2, Recursive approach; If you don't give any value on the variable or the given value is invalid,
  the algorithm will automatically choose one depending on the memory occupation status;

4. It is recommended to use the iterative manner for its better performance in practical scenarios, since it occupies
less resources in terms of both time and space consumption.

Created by 10318237-ZQ_Kang in the July of 2022;

=================================================================================================================================== */

var defaultPrimaryKeys = []string{"ID", "Id", "id"}
var compareActor compare_implement.CompareCollection

/* Started by AICoder, pid:f86bc6e75cac4e1d89d0ce4e65aff771 */
func CompareOldNew(oldObj, newObj interface{}, compConfig *compare_dtos.CompareConfig) (bool, *compare_dtos.CompareDetail, error) {
	logger.Infof("Enter the major API portal...")

	result := &compare_dtos.CompareDetail{
		AddedObjPKeys:   []string{},
		AddedObjects:    []interface{}{},
		DeletedObjPKeys: []string{},
		DeletedObjects:  []interface{}{},
		UpdatedObjPKeys: []string{},
		UpdatedObjects:  []*compare_dtos.UpdateDetail{},
	}
	isEqual := true
	var disparities []string
	determineCompareActor(compConfig.CompareApproach)

	switch reflect.TypeOf(oldObj).Kind() {
	case reflect.Slice, reflect.Array:
		isEqual, disparities = compareSlices(oldObj, newObj, result, compConfig)
		if !isEqual {
			result.UpdatedObjPKeys = append(result.UpdatedObjPKeys, disparities...)
		}

	case reflect.Struct:
		err := disposeStructList([]interface{}{oldObj}, []interface{}{newObj}, result, compConfig)
		if err != nil {
			logger.Errorf("****** [CompareOldNew] dispose single struct obj, error: %v", err.Error())
			return false, result, err
		}

	default:
		isEqual, disparities = compareSingleObject(oldObj, newObj, compConfig)
		if !isEqual {
			result.UpdatedObjPKeys = append(result.UpdatedObjPKeys, disparities...)
		}
	}

	return isEqual && len(result.AddedObjPKeys) == 0 && len(result.DeletedObjPKeys) == 0 && len(result.UpdatedObjPKeys) == 0 && len(result.UpdatedObjects) == 0, result, nil
}

func compareSlices(oldObj, newObj interface{}, result *compare_dtos.CompareDetail, compConfig *compare_dtos.CompareConfig) (bool, []string) {
	oldObjValue := reflect.ValueOf(oldObj)
	newObjValue := reflect.ValueOf(newObj)

	oldObjs := make([]interface{}, 0)
	newObjs := make([]interface{}, 0)

	oldLen := oldObjValue.Len()
	newLen := newObjValue.Len()

	for i := 0; i < oldLen; i++ {
		value := oldObjValue.Index(i).Interface()
		oldVal := reflect.ValueOf(value)
		if oldVal.Kind() == reflect.Ptr {
			oldObjs = append(oldObjs, oldVal.Elem().Interface())
		} else {
			oldObjs = append(oldObjs, value)
		}
	}
	for i := 0; i < newLen; i++ {
		value := newObjValue.Index(i).Interface()
		newVal := reflect.ValueOf(value)
		if newVal.Kind() == reflect.Ptr {
			newObjs = append(newObjs, newVal.Elem().Interface())
		} else {
			newObjs = append(newObjs, value)
		}
	}

	var kind reflect.Kind
	if oldLen > 0 {
		kind = reflect.ValueOf(oldObjs[0]).Kind()
	} else if newLen > 0 {
		kind = reflect.ValueOf(newObjs[0]).Kind()
	} else {
		return false, []string{}
	}

	switch kind {
	case reflect.Struct:
		err := disposeStructList(oldObjs, newObjs, result, compConfig)
		if err != nil {
			logger.Errorf("****** [CompareOldNew] dispose struct list, error: %v", err.Error())
			return false, []string{}
		}
		return true, []string{}

	default:
		return compareActor.Equals(oldObjs, newObjs, compConfig.NeedDetails, compConfig.IgnoredFields)
	}
}

func compareSingleObject(oldObj, newObj interface{}, compConfig *compare_dtos.CompareConfig) (bool, []string) {
	return compareActor.Equals(oldObj, newObj, compConfig.NeedDetails, compConfig.IgnoredFields)
}

/* Ended by AICoder, pid:f86bc6e75cac4e1d89d0ce4e65aff771 */

/* Started by AICoder, pid:371e283382b142ab843b9c51b8262fd3 */
func disposeStructList(oldObjs, newObjs []interface{}, result *compare_dtos.CompareDetail, compConfig *compare_dtos.CompareConfig) error {
	if err := validateInputs(oldObjs, newObjs); err != nil {
		return err
	}

	primaryKey := getPrimaryKey(oldObjs, compConfig)
	logger.Infof("****** [CompareOldNew] Primary Key: %s", primaryKey)

	if len(oldObjs) == 0 {
		addNewObjects(newObjs, primaryKey, result)
		return nil
	}

	if len(newObjs) == 0 {
		deleteOldObjects(oldObjs, primaryKey, result)
		return nil
	}

	if isSingleElementComparison(compConfig, oldObjs, newObjs) {
		return compareSingleElements(oldObjs[0], newObjs[0], compConfig, result)
	}

	return compareMultipleElements(oldObjs, newObjs, primaryKey, compConfig, result)
}

func validateInputs(oldObjs, newObjs []interface{}) error {
	if len(oldObjs) == 0 && len(newObjs) == 0 {
		return fmt.Errorf("empty object list for both old and new input parameters")
	}
	return nil
}

func getPrimaryKey(oldObjs []interface{}, compConfig *compare_dtos.CompareConfig) string {
	primaryKey := compConfig.PrimaryKey
	if len(primaryKey) == 0 {
		primaryKey = findPossiblePrimaryKey(oldObjs[0])
	}
	return primaryKey
}

func addNewObjects(newObjs []interface{}, primaryKey string, result *compare_dtos.CompareDetail) {
	for _, obj := range newObjs {
		value := reflect.ValueOf(obj)
		if len(primaryKey) > 0 {
			result.AddedObjPKeys = append(result.AddedObjPKeys, value.FieldByName(primaryKey).String())
		}
		result.AddedObjects = append(result.AddedObjects, obj)
	}
}

func deleteOldObjects(oldObjs []interface{}, primaryKey string, result *compare_dtos.CompareDetail) {
	for _, obj := range oldObjs {
		value := reflect.ValueOf(obj)
		if len(primaryKey) > 0 {
			result.DeletedObjPKeys = append(result.DeletedObjPKeys, value.FieldByName(primaryKey).String())
		}
		result.DeletedObjects = append(result.DeletedObjects, obj)
	}
}

func isSingleElementComparison(compConfig *compare_dtos.CompareConfig, oldObjs, newObjs []interface{}) bool {
	return len(compConfig.PrimaryKey) == 0 && len(oldObjs) == 1 && len(newObjs) == 1
}

func compareSingleElements(oldObj, newObj interface{}, compConfig *compare_dtos.CompareConfig, result *compare_dtos.CompareDetail) error {
	identical, disparities := compareActor.Equals(oldObj, newObj, compConfig.NeedDetails, compConfig.IgnoredFields)
	if !identical {
		result.UpdatedObjects = append(result.UpdatedObjects, &compare_dtos.UpdateDetail{Content: newObj, ChangedFields: disparities})
	}
	return nil
}

func compareMultipleElements(oldObjs, newObjs []interface{}, primaryKey string, compConfig *compare_dtos.CompareConfig, result *compare_dtos.CompareDetail) error {
	newObjMap := make(map[string]interface{})
	for i := range newObjs {
		newVal := reflect.ValueOf(newObjs[i])
		if newVal.Kind() != reflect.Struct {
			return fmt.Errorf("all elements in the new object list must be homogeneous")
		}
		newObjMap[newVal.FieldByName(primaryKey).String()] = newObjs[i]
	}

	for i := range oldObjs {
		oldVal := reflect.ValueOf(oldObjs[i])
		pKeyValue := oldVal.FieldByName(primaryKey).String()
		logger.Infof("****** [CompareOldNew] Primary Key Value: %s", pKeyValue)

		if newObj, ok := newObjMap[pKeyValue]; ok {
			identical, disparities := compareActor.Equals(oldObjs[i], newObj, compConfig.NeedDetails, compConfig.IgnoredFields)
			if !identical {
				result.UpdatedObjPKeys = append(result.UpdatedObjPKeys, pKeyValue)
				result.UpdatedObjects = append(result.UpdatedObjects, &compare_dtos.UpdateDetail{Content: newObj, ChangedFields: disparities})
			}
			delete(newObjMap, pKeyValue)
		} else {
			result.DeletedObjPKeys = append(result.DeletedObjPKeys, pKeyValue)
			result.DeletedObjects = append(result.DeletedObjects, oldObjs[i])
		}
	}

	for pKey, obj := range newObjMap {
		result.AddedObjPKeys = append(result.AddedObjPKeys, pKey)
		result.AddedObjects = append(result.AddedObjects, obj)
	}

	return nil
}

/* Ended by AICoder, pid:371e283382b142ab843b9c51b8262fd3 */

func findPossiblePrimaryKey(obj interface{}) string {
	if !reflect.ValueOf(obj).IsValid() {
		return compare_dtos.EmptyPlaceholder
	}

	for _, defaultPKey := range defaultPrimaryKeys {
		if reflect.ValueOf(obj).FieldByName(defaultPKey).IsValid() {
			return defaultPKey
		}
	}

	return compare_dtos.EmptyPlaceholder
}

func determineCompareActor(designatedManner int) {
	if designatedManner < 0 || designatedManner > 2 {
		designatedManner = 0
	}

	if designatedManner == compare_dtos.IterativeManner {
		compareActor = new(compare_implement.CompareIteratively)
	} else if designatedManner == compare_dtos.RecursiveManner {
		compareActor = new(compare_implement.CompareRecursively)
	} else {
		memStatus := &runtime.MemStats{}
		runtime.ReadMemStats(memStatus)
		occupiedRatio := float32(memStatus.HeapAlloc/memStatus.HeapSys) * 100
		if occupiedRatio < 65 {
			compareActor = new(compare_implement.CompareIteratively)
		} else {
			compareActor = new(compare_implement.CompareRecursively)
		}
	}
}
