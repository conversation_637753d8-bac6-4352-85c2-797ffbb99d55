package sort

import (
	"fmt"
	"math"
	"reflect"
)

func HeapSort(value reflect.Value) {
	value = reflect.ValueOf(value.Interface())
	length := value.Len()
	for i := length/2 - 1; i >= 0; i-- {
		sink(value, length, i)
	}
	for i := length - 1; i > 0; i-- {
		swap(value, i, 0)
		sink(value, i, 0)
	}
}

/* Started by AICoder, pid:8dd42c7ddacb42b5ab94e231ac9059df */
func isLess(x, y reflect.Value) bool {
	switch x.Type().Kind() {
	case reflect.Bool:
		return !x.Bool() && y.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return x.Int() < y.Int()
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return x.Uint() < y.Uint()
	case reflect.Float32, reflect.Float64:
		return x.Float() < y.Float() || math.IsNaN(x.Float()) && !math.IsNaN(y.Float())
	case reflect.Complex64, reflect.Complex128:
		return isLessComplex(x.Complex(), y.Complex())
	case reflect.String:
		return x.String() < y.String()
	case reflect.Ptr:
		return isLess(x.Elem(), y.Elem())
	default:
		return fmt.Sprintf("%+v", x.Interface()) < fmt.Sprintf("%+v", y.Interface())
	}
}

func isLessComplex(cx, cy complex128) bool {
	rx, ix, ry, iy := real(cx), imag(cx), real(cy), imag(cy)
	if rx == ry || (math.IsNaN(rx) && math.IsNaN(ry)) {
		return ix < iy || math.IsNaN(ix) && !math.IsNaN(iy)
	}
	return rx < ry || math.IsNaN(rx) && !math.IsNaN(ry)
}

/* Ended by AICoder, pid:8dd42c7ddacb42b5ab94e231ac9059df */

func sink(value reflect.Value, length, root int) {
	largest := root
	left, right := 2*root+1, 2*root+2
	if left < length && isLess(value.Index(root), value.Index(left)) {
		largest = left
	}
	if right < length && isLess(value.Index(largest), value.Index(right)) {
		largest = right
	}
	if largest != root {
		swap(value, root, largest)
		sink(value, length, largest)
	}
}

func swap(value reflect.Value, i, j int) {
	vi := reflect.New(value.Index(i).Type())
	vi.Elem().Set(value.Index(i))
	value.Index(i).Set(value.Index(j))
	value.Index(j).Set(vi.Elem())
}
