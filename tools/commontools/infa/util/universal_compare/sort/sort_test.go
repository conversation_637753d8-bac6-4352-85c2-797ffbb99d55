package sort

import (
	"fmt"
	"reflect"
	"testing"
	"time"
)

var nick Professor = Professor{Name: "nick <PERSON>vis<PERSON>", Friends: []string{"<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "petr", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"}}
var padmini Professor = Professor{Name: "<PERSON><PERSON><PERSON>", Friends: []string{"<PERSON>", "<PERSON>", "<PERSON>"}}
var y<PERSON> = Professor{Name: "<PERSON>", Friends: []string{"<PERSON><PERSON>", "<PERSON>", "<PERSON>"}}
var ucsd university = university{
	Professors: []Professor{padmini, yuri, nick},
	Schools:    map[string][]string{"Jacobs": {"MAE", "BIOE", "ECE", "CSE"}, "Math": {"Mathematics", "Physics", "Biology"}},
	Classes: [][]string{{"Intro to programming", "MATLAB"}, {"Data Structure", "Data Science"}, {},
		{"Algorithm", "Machine Learning", "Artificial Intelligence"}},
}

// var wei student = student{Name: "wei", Advisors: []*Professor{&nick, &padmini},
// 	Courses: []string{"Algorithms", "bio-inspired robot", "Computational Mechanics"}}

type university struct {
	Professors []Professor
	Schools    map[string][]string
	Classes    [][]string
}

type Professor struct {
	Name    string
	Friends []string
}

// type principalInvesigator struct {
// 	Professor
// 	Titles []string
// }

// type student struct {
// 	Name     string
// 	Advisors []*Professor
// 	Courses  []string
// }

// func (s student) String() string {
// 	professors := ""
// 	for i, p := range s.Advisors {
// 		if i != len(s.Advisors)-1 {
// 			professors += fmt.Sprintf("&%#v, ", *p)
// 		} else {
// 			professors += fmt.Sprintf("&%#v", *p)
// 		}
// 	}
// 	return fmt.Sprintf("sorts.student{Name:%s, Advisors:[]*sorts.professor{%s}, Courses:%#v}",
// 		s.Name, professors, s.Courses)
// }

func TestRecursiveSort(t *testing.T) {
	tests := []struct {
		name string
		in   reflect.Value
		want string
	}{
		{
			name: "sort map",
			in: reflect.ValueOf(map[string][]string{
				"lives":   {"ccv", "ccb", "no"},
				"peoples": {"nope", "ab"},
				"life":    {"ss", "yes", "Ks"},
				"people":  {"yeah", "Ye"},
			}),
			want: `map[life:[Ks ss yes] lives:[ccb ccv no] people:[Ye yeah] peoples:[ab nope]]`,
		},
		{
			name: "sort slice",
			in:   reflect.ValueOf([]string{"lifeisgood", "lifeiswonderful", "GoodLife", "Goodlife", "goodlife", "vv", "VV"}),
			want: `[GoodLife Goodlife VV goodlife lifeisgood lifeiswonderful vv]`,
		},
		{
			name: "sort nested slice",
			in:   reflect.ValueOf([][][]string{{{"KaKa", "WkWk"}, {"MeMo", "xyxy"}}, {{"JoJo", "ojoj"}}, {{"zz", "mimi"}, {"MoMo"}}}),
			want: `[[[JoJo ojoj]] [[KaKa WkWk] [MeMo xyxy]] [[MoMo] [mimi zz]]]`,
		},
		{
			name: "sort struct",
			in:   reflect.ValueOf(nick),
			want: `{Name:nick Gravish Friends:[Huan J.S. Chen Padmini Yuri petr]}`,
		},
		{
			name: "sort nested struct",
			in:   reflect.ValueOf(ucsd),
			want: `{Professors:[{Name:Padmini Rangemani Friends:[Bo Li Nick Gravish Yuri Bazilevis]} ` +
				`{Name:Yuri Bazilevis Friends:[Bo Li Nick Gravish Petr]} ` +
				`{Name:nick Gravish Friends:[Huan J.S. Chen Padmini Yuri petr]}] ` +
				`Schools:map[Jacobs:[BIOE CSE ECE MAE] Math:[Biology Mathematics Physics]] ` +
				`Classes:[[Algorithm Artificial Intelligence Machine Learning] ` +
				`[Data Science Data Structure] [Intro to programming MATLAB] []]}`,
		},
		// {
		// 	name: "sort struct with anonymous field",
		// 	in:   reflect.ValueOf(principalInvesigator{Professor: nick, Titles: []string{"scientist", "AP", "P.E.", "P.I."}}),
		// 	want: `{Professor:{Name:nick Gravish Friends:[Huan J.S. Chen Padmini Yuri petr]} Titles:[AP P.E. P.I. scientist]}`,
		// },
		// {
		// 	name: "sort struct with pointer field",
		// 	in:   reflect.ValueOf(wei),
		// 	want: `sorts.student{Name:wei, Advisors:[]*sorts.professor{&sorts.Professor{Name:"Padmini Rangemani", ` +
		// 		`Friends:[]string{"Bo Li", "Nick Gravish", "Yuri Bazilevis"}}, &sorts.Professor{Name:"nick Gravish", ` +
		// 		`Friends:[]string{"Huan", "J.S. Chen", "Padmini", "Yuri", "petr"}}}, Courses:[]string{"Algorithms", ` +
		// 		`"Computational Mechanics", "bio-inspired robot"}}`,
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			RecursiveSort(tt.in)
			out := fmt.Sprintf("%+v", tt.in.Interface())
			if tt.want != out {
				t.Errorf("\n%s failed!\nExpected: %s\nActual: %s", tt.name, tt.want, out)
			}
		})

		time.Sleep(1 * time.Second)

		t.Run(tt.name, func(t *testing.T) {
			IterativeSort(tt.in)
			out := fmt.Sprintf("%+v", tt.in.Interface())
			if tt.want != out {
				t.Errorf("\n%s failed!\nExpected: %s\nActual: %s", tt.name, tt.want, out)
			}
		})
	}
}
