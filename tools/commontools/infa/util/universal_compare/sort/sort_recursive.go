package sort

import (
	"reflect"
)

func RecursiveSort(value reflect.Value) {
	if !value.IsValid() || !value.CanInterface() {
		return
	}

	value = reflect.ValueOf(value.Interface())
	switch value.Type().Kind() {
	case reflect.Slice, reflect.Array:
		switch value.Type().Elem().Kind() {
		case reflect.String, reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
			reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr,
			reflect.Bool, reflect.Float32, reflect.Float64, reflect.Complex64, reflect.Complex128:
		default:
			for i := 0; i < value.Len(); i++ {
				RecursiveSort(value.Index(i))
			}
		}
		HeapSort(value)
	case reflect.Struct:
		for i := 0; i < value.NumField(); i++ {
			RecursiveSort(value.Field(i))
		}
	// In Go 1.12+, you can just print a map value and it will be sorted by key automatically.
	case reflect.Map:
		for _, key := range value.MapKeys() {
			RecursiveSort(value.MapIndex(key))
		}
	case reflect.Ptr:
		RecursiveSort(value.Elem())
	default:
	}
}
