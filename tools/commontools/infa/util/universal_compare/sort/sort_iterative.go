package sort

import (
	"reflect"
	"cwsm/tools/commontools/infa/util/my_stack"
)

type SortCell struct {
	Value   reflect.Value
	Visited bool
}

func IterativeSort(value reflect.Value) {
	stack := my_stack.NewStack()
	stack.Push(SortCell{Value: value, Visited: false})

	for !stack.IsEmpty() {
		cell := stack.Peek().(SortCell)
		if !cell.Value.IsValid() || !cell.Value.CanInterface() {
			stack.Pop()
			continue
		}
		realVal := reflect.ValueOf(cell.Value.Interface())

		switch realVal.Kind() {
		case reflect.Slice, reflect.Array:
			if cell.Visited {
				HeapSort(realVal)
				stack.Pop()
				continue
			}
			cell.Visited = true
			stack.Pop()
			stack.Push(cell)
			for i := 0; i < realVal.Len(); i++ {
				stack.Push(SortCell{Value: realVal.Index(i), Visited: false})
			}
		case reflect.Struct:
			stack.Pop()
			for i := 0; i < realVal.NumField(); i++ {
				stack.Push(SortCell{Value: realVal.Field(i), Visited: false})
			}
		case reflect.Map:
			stack.Pop()
			for _, key := range realVal.MapKeys() {
				stack.Push(SortCell{Value: realVal.MapIndex(key), Visited: false})
			}
		case reflect.Ptr:
			stack.Pop()
			stack.Push(SortCell{Value: realVal.Elem(), Visited: false})
		default:
			stack.Pop()
		}
	}
}
