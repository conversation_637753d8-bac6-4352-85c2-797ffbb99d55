package compare_implement

import (
	"cwsm/tools/commontools/infa/util/my_stack"
	"cwsm/tools/commontools/infa/util/universal_compare/compare_dtos"
	"cwsm/tools/commontools/infa/util/universal_compare/sort"
	"cwsm/tools/commontools/logger"
	"fmt"
	"math"
	"reflect"
	"strings"
)

// Brief Introduction of the iterative Algorithm: the Variant of N-ary Tree Post-order Traversal via iterative DFS (Depth First Search);
// Time Complexity: O(n * logn), where n represents the total number of the columns in the data object;
// Space Complexity: O(n) on heap and O(logn) on stack; Overall SC: O(n), where n represents the total number of the columns in the data object;

type CompCell struct {
	Value     reflect.Value
	FieldName string
	CompLevel int
	Visited   bool
}

func (c *CompareIteratively) Equals(x, y interface{}, needDetails bool, ignoredFields []*compare_dtos.IgnoredField) (bool, []string) {
	logger.Infof("Enter Equals - Iterative...")
	vx, vy := reflect.ValueOf(x), reflect.ValueOf(y)
	tx, ty := reflect.TypeOf(x), reflect.TypeOf(y)

	ignoreMap := make(map[string]string)
	for _, pair := range ignoredFields {
		ignoreMap[pair.FieldName] = pair.StructName
	}

	if tx.String() != ty.String() {
		logger.Infof("*********tx.String() - %s != ty.String() - %s**********", tx.String(), ty.String())
		return false, []string{}
	}

	return compareIteratively(vx, vy, needDetails, ignoreMap)
}

/* Started by AICoder, pid:481b38c96a564fc28dc83b8627c465bc */
func postProcess(stackX, stackY *my_stack.MyStack) []string {
	var disparities []string

	if !stackX.IsEmpty() {
		cellX := stackX.Pop().(CompCell)
		if cellX.CompLevel <= 1 {
			disparities = append(disparities, cellX.FieldName)
		}
	}

	if !stackY.IsEmpty() {
		cellY := stackY.Pop().(CompCell)
		if cellY.CompLevel <= 1 {
			disparities = append(disparities, cellY.FieldName)
		}
	}

	return disparities
}

/* Started by AICoder, pid:2917c72f4f0f4fb885f26630e50bcb6d */
func traverseDataStructTree(stackX, stackY *my_stack.MyStack, needDetails bool, ignoredMap map[string]string) (bool, []string) {
	var disparities []string
	var currBranchFlagEqual, globalFlagEqual = true, true
	for !stackX.IsEmpty() && !stackY.IsEmpty() {
		if !needDetails && !globalFlagEqual {
			return false, []string{}
		}
		cellX, x, cellY, y, cmpDepth := peekStacks(stackX, stackY)
		if !x.IsValid() && !y.IsValid() {
			popBothStacks(stackX, stackY)
			continue
		}
		if isInvalidComparison(x, y) {
			currBranchFlagEqual, globalFlagEqual = false, false
			popBothStacks(stackX, stackY)
			continue
		}
		globalFlagEqual, currBranchFlagEqual, disparities = handleTypeComparison(x, y, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities,
			cellX.FieldName, stackX, stackY, ignoredMap, cellX, cellY, needDetails)
		if !isComplexType(x.Type().Kind()) {
			popBothStacks(stackX, stackY)
		}
	}
	return globalFlagEqual, disparities
}

func peekStacks(stackX, stackY *my_stack.MyStack) (CompCell, reflect.Value, CompCell, reflect.Value, int) {
	cellX := stackX.Peek().(CompCell)
	cellY := stackY.Peek().(CompCell)
	return cellX, cellX.Value, cellY, cellY.Value, cellX.CompLevel
}

func popBothStacks(stackX, stackY *my_stack.MyStack) {
	stackX.Pop()
	stackY.Pop()
}

func isInvalidComparison(x, y reflect.Value) bool {
	return !x.IsValid() || !y.IsValid() || x.Type().Kind() == reflect.Invalid || y.Type().Kind() == reflect.Invalid || (x.Type().Kind() != y.Type().Kind())
}

func isComplexType(kind reflect.Kind) bool {
	switch kind {
	case reflect.Struct, reflect.Array, reflect.Slice, reflect.Map, reflect.Ptr:
		return true
	default:
		return false
	}
}

/* Ended by AICoder, pid:2917c72f4f0f4fb885f26630e50bcb6d */

/* Started by AICoder, pid:2f49cb69933a47baa8a060a14aa96ce7 */
func handleTypeComparison(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string, stackX, stackY *my_stack.MyStack, ignoredMap map[string]string, cellX, cellY CompCell, needDetails bool) (bool, bool, []string) {
	switch x.Type().Kind() {
	case reflect.Invalid:
		logger.Errorf("****** invalid field: %s", fieldName)
		currBranchFlagEqual = false
		globalFlagEqual = false
	case reflect.Bool:
		currBranchFlagEqual, globalFlagEqual, disparities = handleBool(x, y, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, fieldName)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		currBranchFlagEqual, globalFlagEqual, disparities = handleInt(x, y, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, fieldName)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		currBranchFlagEqual, globalFlagEqual, disparities = handleUint(x, y, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, fieldName)
	case reflect.Float32, reflect.Float64:
		currBranchFlagEqual, globalFlagEqual, disparities = handleFloat(x, y, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, fieldName)
	case reflect.Complex64, reflect.Complex128:
		currBranchFlagEqual, globalFlagEqual, disparities = handleComplex(x, y, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, fieldName)
	case reflect.String:
		currBranchFlagEqual, globalFlagEqual, disparities = handleString(x, y, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, fieldName)
	case reflect.Struct:
		currBranchFlagEqual, globalFlagEqual, disparities = handleStruct(x, y, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, fieldName, stackX, stackY, ignoredMap, cellX, cellY, needDetails)
	case reflect.Array, reflect.Slice:
		currBranchFlagEqual, globalFlagEqual, disparities = handleArraySlice(x, y, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, fieldName, stackX, stackY, cellX, cellY)
	case reflect.Map:
		currBranchFlagEqual, globalFlagEqual, disparities = handleMap(x, y, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, fieldName, stackX, stackY, ignoredMap, cellX, cellY, needDetails)
	case reflect.Ptr:
		handlePtr(x, y, cmpDepth, fieldName, stackX, stackY)
	default:
		currBranchFlagEqual, globalFlagEqual, disparities = handleTypeComparisonDefault(x, y, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, fieldName)
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

/* Ended by AICoder, pid:2f49cb69933a47baa8a060a14aa96ce7 */

func handleTypeComparisonDefault(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string) (bool, bool, []string) {
	sx, sy := fmt.Sprintf("%+v", x.Interface()), fmt.Sprintf("%+v", y.Interface())
	if sx != sy {
		currBranchFlagEqual = false
		globalFlagEqual = false
		if cmpDepth <= 1 {
			disparities = append(disparities, fieldName)
			currBranchFlagEqual = true
		}
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

func handleBool(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string) (bool, bool, []string) {
	if x.Bool() != y.Bool() {
		currBranchFlagEqual = false
		globalFlagEqual = false
		if cmpDepth <= 1 {
			disparities = append(disparities, fieldName)
			currBranchFlagEqual = true
		}
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

func handleInt(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string) (bool, bool, []string) {
	if x.Int() != y.Int() {
		currBranchFlagEqual = false
		globalFlagEqual = false
		if cmpDepth <= 1 {
			disparities = append(disparities, fieldName)
			currBranchFlagEqual = true
		}
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

func handleUint(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string) (bool, bool, []string) {
	if x.Uint() != y.Uint() {
		currBranchFlagEqual = false
		globalFlagEqual = false
		if cmpDepth <= 1 {
			disparities = append(disparities, fieldName)
			currBranchFlagEqual = true
		}
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

func handleFloat(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string) (bool, bool, []string) {
	fx, fy := x.Float(), y.Float()
	if math.IsNaN(fx) && !math.IsNaN(fy) || !math.IsNaN(fx) && math.IsNaN(fy) || fx-fy < compare_dtos.Epsilon {
		currBranchFlagEqual = false
		globalFlagEqual = false
		if cmpDepth <= 1 {
			disparities = append(disparities, fieldName)
			currBranchFlagEqual = true
		}
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

/* Started by AICoder, pid:0b9f23daf29e4160b68c3ac23bb2bdfe */
func handleComplex(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string) (bool, bool, []string) {
	cx, cy := x.Complex(), y.Complex()
	rx, ix, ry, iy := real(cx), imag(cx), real(cy), imag(cy)
	if checkNaNDisparities(rx, ry, ix, iy) {
		globalFlagEqual, currBranchFlagEqual, disparities = handleNaNDisparity(cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, fieldName)
	}
	if checkValueDisparities(rx, ry, ix, iy) {
		currBranchFlagEqual, globalFlagEqual = false, false
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

func checkNaNDisparities(rx, ry, ix, iy float64) bool {
	return (math.IsNaN(rx) != math.IsNaN(ry)) || (math.IsNaN(ix) != math.IsNaN(iy))
}

func handleNaNDisparity(cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string) (bool, bool, []string) {
	currBranchFlagEqual, globalFlagEqual = false, false
	if cmpDepth <= 1 {
		disparities = append(disparities, fieldName)
		currBranchFlagEqual = true
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

func checkValueDisparities(rx, ry, ix, iy float64) bool {
	return !(math.IsNaN(rx) && math.IsNaN(ry) && math.IsNaN(ix) && math.IsNaN(iy)) && (rx != ry || ix != iy)
}

/* Ended by AICoder, pid:0b9f23daf29e4160b68c3ac23bb2bdfe */

func handleString(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string) (bool, bool, []string) {
	if x.String() != y.String() {
		currBranchFlagEqual = false
		globalFlagEqual = false
		if cmpDepth <= 1 {
			disparities = append(disparities, fieldName)
			currBranchFlagEqual = true
		}
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

/* Started by AICoder, pid:d70cebd2553b480ab8892616822b6e2d */
func handleStruct(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string, stackX, stackY *my_stack.MyStack, ignoredMap map[string]string, cellX, cellY CompCell, needDetails bool) (bool, bool, []string) {
	if cellX.Value.NumField() != cellY.Value.NumField() {
		return handleDifferentFieldNumber(cellX, cellY, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, stackX, stackY)
	}
	if cellX.Visited {
		return handleVisited(cellX, cellY, cmpDepth, currBranchFlagEqual, globalFlagEqual, disparities, stackX, stackY)
	}
	cellX.Visited = true
	stackX.Pop()
	stackX.Push(cellX)
	if cmpDepth <= 1 {
		currBranchFlagEqual = true
	}
	index := 0
	handleFields(x, y, cmpDepth, stackX, stackY, ignoredMap, cellX, cellY, index)
	handleExtraFields(x, y, cmpDepth, globalFlagEqual, disparities, index)
	return globalFlagEqual, currBranchFlagEqual, disparities
}

func handleDifferentFieldNumber(cellX, cellY CompCell, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, stackX, stackY *my_stack.MyStack) (bool, bool, []string) {
	stackX.Pop()
	stackY.Pop()
	if cmpDepth <= 1 {
		disparities = append(disparities, cellX.FieldName)
		currBranchFlagEqual = true
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

func handleVisited(cellX, cellY CompCell, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, stackX, stackY *my_stack.MyStack) (bool, bool, []string) {
	stackX.Pop()
	stackY.Pop()
	if (cmpDepth == 0 && len(disparities) > 0) || (!currBranchFlagEqual && cmpDepth <= 1) {
		disparities = append(disparities, cellX.FieldName)
		currBranchFlagEqual = true
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

func handleFields(x, y reflect.Value, cmpDepth int, stackX, stackY *my_stack.MyStack, ignoredMap map[string]string, cellX, cellY CompCell, index int) {
	xLen, yLen := x.NumField(), y.NumField()

	for index < xLen && index < yLen {
		cmpTags := strings.Split(x.Type().Field(index).Tag.Get("cmp"), ",")
		if ignoredField, ok := ignoredMap[reflect.TypeOf(x.Interface()).Field(index).Name]; ok && ignoredField == cellX.FieldName {
			index++
			continue
		}
		for _, tag := range cmpTags {
			if tag != "ignore" {
				stackX.Push(CompCell{Value: x.Field(index), FieldName: reflect.TypeOf(x.Interface()).Field(index).Name, CompLevel: cmpDepth + 1, Visited: false})
				stackY.Push(CompCell{Value: y.Field(index), FieldName: reflect.TypeOf(y.Interface()).Field(index).Name, CompLevel: cmpDepth + 1, Visited: false})
			}
		}
		index++
	}
}

func handleExtraFields(x, y reflect.Value, cmpDepth int, globalFlagEqual bool, disparities []string, index int) {
	if index < x.NumField() || index < y.NumField() {
		globalFlagEqual = false
		if cmpDepth == 0 {
			for index < x.NumField() {
				disparities = append(disparities, reflect.TypeOf(x.Interface()).Field(index).Name)
				index++
			}
			for index < y.NumField() {
				disparities = append(disparities, reflect.TypeOf(y.Interface()).Field(index).Name)
				index++
			}
		}
	}
}

/* Ended by AICoder, pid:d70cebd2553b480ab8892616822b6e2d */

func handleArraySlice(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string, stackX, stackY *my_stack.MyStack, cellX, cellY CompCell) (bool, bool, []string) {
	if x.Len() != y.Len() {
		currBranchFlagEqual = false
		globalFlagEqual = false
		stackX.Pop()
		stackY.Pop()
		if cmpDepth <= 1 {
			disparities = append(disparities, cellX.FieldName)
			currBranchFlagEqual = true
		}
		return globalFlagEqual, currBranchFlagEqual, disparities
	}
	if cellX.Visited {
		stackX.Pop()
		stackY.Pop()
		if !currBranchFlagEqual && cmpDepth <= 1 {
			disparities = append(disparities, cellX.FieldName)
			currBranchFlagEqual = true
		}
		return globalFlagEqual, currBranchFlagEqual, disparities
	}

	sort.IterativeSort(cellX.Value)
	sort.IterativeSort(cellY.Value)

	cellX.Visited = true
	stackX.Pop()
	stackX.Push(cellX)
	stackY.Pop()
	stackY.Push(cellY)
	if cmpDepth <= 1 {
		currBranchFlagEqual = true
	}

	xLen, yLen := x.Len(), y.Len()
	index := 0
	for index < xLen && index < yLen {
		stackX.Push(CompCell{Value: x.Index(index), FieldName: "", CompLevel: cmpDepth + 1, Visited: false})
		stackY.Push(CompCell{Value: y.Index(index), FieldName: "", CompLevel: cmpDepth + 1, Visited: false})
		index++
	}

	if index < xLen || index < yLen {
		globalFlagEqual = false
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

func handleMap(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string, stackX, stackY *my_stack.MyStack, ignoredMap map[string]string, cellX, cellY CompCell, needDetails bool) (bool, bool, []string) {
	if cellX.Visited {
		stackX.Pop()
		stackY.Pop()
		if !currBranchFlagEqual && cmpDepth <= 1 {
			disparities = append(disparities, cellX.FieldName)
		}
		return globalFlagEqual, currBranchFlagEqual, disparities
	}
	cellX.Visited = true
	stackX.Pop()
	stackX.Push(cellX)
	if cmpDepth <= 1 {
		currBranchFlagEqual = true
	}
	keysX := x.MapKeys()
	usedKeys := make(map[string]bool)
	for _, key := range keysX {
		if _, ok := ignoredMap[key.String()]; !ok {
			stackX.Push(CompCell{Value: x.MapIndex(key), FieldName: key.String(), CompLevel: cmpDepth + 1, Visited: false})
			stackY.Push(CompCell{Value: y.MapIndex(key), FieldName: key.String(), CompLevel: cmpDepth + 1, Visited: false})
		}
		usedKeys[key.String()] = true
	}

	keysY := y.MapKeys()
	for _, key := range keysY {
		if used, ok := usedKeys[key.String()]; !ok && !used {
			globalFlagEqual = false
			if needDetails && cmpDepth == 0 {
				disparities = append(disparities, key.String())
			}
		}
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

func handlePtr(x, y reflect.Value, cmpDepth int, fieldName string, stackX, stackY *my_stack.MyStack) {
	stackX.Pop()
	stackX.Push(CompCell{Value: x.Elem(), FieldName: fieldName, CompLevel: cmpDepth, Visited: false})
	stackY.Pop()
	stackY.Push(CompCell{Value: y.Elem(), FieldName: fieldName, CompLevel: cmpDepth, Visited: false})
}

func compareIteratively(valX, valY reflect.Value, needDetails bool, ignoredMap map[string]string) (bool, []string) {
	logger.Infof("Enter compareByIterative...")
	stackX := my_stack.NewStack()
	stackX.Push(CompCell{Value: valX, FieldName: reflect.TypeOf(valX.Interface()).Name(), CompLevel: 0, Visited: false})

	stackY := my_stack.NewStack()
	stackY.Push(CompCell{Value: valY, FieldName: reflect.TypeOf(valY.Interface()).Name(), CompLevel: 0, Visited: false})

	globalFlagEqual, disparities := traverseDataStructTree(stackX, stackY, needDetails, ignoredMap)
	disparities = append(disparities, postProcess(stackX, stackY)...)

	return globalFlagEqual, disparities
}

/* Ended by AICoder, pid:481b38c96a564fc28dc83b8627c465bc */
