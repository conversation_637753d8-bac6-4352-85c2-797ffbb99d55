package compare_implement

import (
	"cwsm/tools/commontools/infa/util/universal_compare/compare_dtos"
	"cwsm/tools/commontools/infa/util/universal_compare/sort"
	"cwsm/tools/commontools/logger"
	"fmt"
	"math"
	"reflect"
	"strings"
)

// Brief introduction: N-ary Tree Post-order Traversal via Recursive manner;
// TC: O(n * logn), SC: O(n) on the heap and O(height * logn) on the stack, where n is the total amount of elements in the input object and height is the height of a single data object tree.

/* Started by AICoder, pid:1edc877760454645b163808d9c08a6cc */
func (c *CompareRecursively) Equals(x, y interface{}, needDetails bool, ignoredFields []*compare_dtos.IgnoredField) (bool, []string) {
	logger.Infof("Enter Equals - Recursive...")
	vx, vy := reflect.ValueOf(x), reflect.ValueOf(y)
	tx, ty := reflect.TypeOf(x), reflect.TypeOf(y)
	if tx.String() != ty.String() {
		return false, []string{}
	}

	ignoreMap := makeIgnoreMap(ignoredFields)
	var disparities []string
	result := compareRecursively(vx, vy, needDetails, ignoreMap, &disparities, 0, tx.Name())

	return result, disparities
}

func makeIgnoreMap(ignoredFields []*compare_dtos.IgnoredField) map[string]string {
	ignoreMap := make(map[string]string)
	for _, pair := range ignoredFields {
		ignoreMap[pair.FieldName] = pair.StructName
	}
	return ignoreMap
}

/* Ended by AICoder, pid:1edc877760454645b163808d9c08a6cc */

/* Started by AICoder, pid:247855b036a4410e848c8000f08020c0 */
func compareRecursively(x, y reflect.Value, needDetails bool, ignoredMap map[string]string, disparities *[]string, recursionDepth int, fieldName string) bool {
	if !x.IsValid() && !y.IsValid() {
		return true
	}
	if !x.IsValid() || !y.IsValid() {
		return false
	}

	switch x.Type().Kind() {
	case reflect.Invalid:
		logger.Infof("****** invalid field: %s", fieldName)
		return false
	case reflect.Bool, reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr,
		reflect.Float32, reflect.Float64, reflect.Complex64, reflect.Complex128, reflect.String:
		return basicTypesEqual(x, y)
	case reflect.Array, reflect.Slice:
		return compareSlices(x, y, needDetails, ignoredMap, disparities, recursionDepth, fieldName)
	case reflect.Struct:
		return compareStructs(x, y, needDetails, ignoredMap, disparities, recursionDepth, fieldName)
	case reflect.Map:
		return compareMaps(x, y, needDetails, ignoredMap, disparities, recursionDepth, fieldName)
	case reflect.Ptr:
		return comparePointers(x, y, needDetails, ignoredMap, disparities, recursionDepth, fieldName)
	default:
		return compareDefault(x, y)
	}
}

func compareSlices(x, y reflect.Value, needDetails bool, ignoredMap map[string]string, disparities *[]string, recursionDepth int, fieldName string) bool {
	sort.RecursiveSort(x)
	sort.RecursiveSort(y)

	xLen, yLen := x.Len(), y.Len()
	res := true
	index := 0

	for index < xLen && index < yLen {
		isEqual := compareRecursively(x.Index(index), y.Index(index), needDetails, ignoredMap, disparities, recursionDepth+1, "")
		if !isEqual {
			if !needDetails {
				return false
			}
			res = false
		}
		index++
	}

	if index < xLen || index < yLen {
		res = false
	}

	if !res && recursionDepth <= 1 {
		*disparities = append(*disparities, fieldName)
	}

	return res
}

/* Started by AICoder, pid:01af719822054f3b83ab9be444c91a65 */
func compareStructs(x, y reflect.Value, needDetails bool, ignoredMap map[string]string, disparities *[]string, recursionDepth int, fieldName string) bool {
	xLen, yLen := x.NumField(), y.NumField()
	index := 0
	res := true
	res = IgnoredField(x, y, xLen, yLen, needDetails, index, disparities, recursionDepth, res, ignoredMap, fieldName)

	res = handleDisparities(x, y, xLen, yLen, needDetails, index, disparities, recursionDepth, res)

	if !res && recursionDepth <= 1 {
		*disparities = append(*disparities, fieldName)
	}
	return res
}

func IgnoredField(x, y reflect.Value, xLen, yLen int, needDetails bool, index int, disparities *[]string, recursionDepth int, res bool, ignoredMap map[string]string, fieldName string) bool {
	for index < xLen && index < yLen {
		if ignoredField, ok := ignoredMap[reflect.TypeOf(x.Interface()).Field(index).Name]; ok && ignoredField == fieldName {
			index++
			continue
		}
		cmpTags := strings.Split(x.Type().Field(index).Tag.Get("cmp"), ",")
		for _, tag := range cmpTags {
			if tag != "ignore" {
				isEquals := compareRecursively(x.Field(index), y.Field(index), needDetails, ignoredMap, disparities, recursionDepth+1, reflect.TypeOf(x.Interface()).Field(index).Name)
				if !isEquals {
					if !needDetails {
						return false
					}
					res = false
				}
			}
		}
		index++
	}
	return res
}

func handleDisparities(x, y reflect.Value, xLen, yLen int, needDetails bool, index int, disparities *[]string, recursionDepth int, res bool) bool {
	if index < xLen || index < yLen {
		if needDetails && recursionDepth == 0 {
			for index < xLen {
				*disparities = append(*disparities, reflect.TypeOf(x.Interface()).Field(index).Name)
				index++
			}
			for index < yLen {
				*disparities = append(*disparities, reflect.TypeOf(y.Interface()).Field(index).Name)
				index++
			}
		}
		res = false
	}
	return res
}

/* Ended by AICoder, pid:01af719822054f3b83ab9be444c91a65 */

func compareMaps(x, y reflect.Value, needDetails bool, ignoredMap map[string]string, disparities *[]string, recursionDepth int, fieldName string) bool {
	usedKeys := make(map[string]bool)
	res := true
	index := 0

	keysX := x.MapKeys()
	for _, key := range keysX {
		isEqual := compareRecursively(x.MapIndex(key), y.MapIndex(key), needDetails, ignoredMap, disparities, recursionDepth+1, key.String())
		if !isEqual {
			if !needDetails {
				return false
			}
			res = false
		}
		usedKeys[key.String()] = true
		index++
	}

	keysY := y.MapKeys()
	for _, key := range keysY {
		if used, ok := usedKeys[key.String()]; !ok && !used {
			res = false
			if needDetails && recursionDepth == 0 {
				*disparities = append(*disparities, key.String())
			}
		}
	}
	return res
}

func comparePointers(x, y reflect.Value, needDetails bool, ignoredMap map[string]string, disparities *[]string, recursionDepth int, fieldName string) bool {
	return compareRecursively(x.Elem(), y.Elem(), needDetails, ignoredMap, disparities, recursionDepth, fieldName)
}

func compareDefault(x, y reflect.Value) bool {
	sx, sy := fmt.Sprintf("%+v", x.Interface()), fmt.Sprintf("%+v", y.Interface())
	return sx == sy
}

/* Ended by AICoder, pid:247855b036a4410e848c8000f08020c0 */

/* Started by AICoder, pid:1006d741297e4dc3b842d9cfd2451b93 */
func basicTypesEqual(x, y reflect.Value) bool {
	switch x.Type().Kind() {
	case reflect.Invalid:
		return false
	case reflect.Bool:
		return handleBteBool(x, y)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return handleBteInt(x, y)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return handleBteUint(x, y)
	case reflect.Float32, reflect.Float64:
		return handleBteFloat(x, y)
	case reflect.Complex64, reflect.Complex128:
		return handleBteComplex(x, y)
	case reflect.String:
		return handleBteString(x, y)
	default:
		return handleDefault(x, y)
	}
}

func handleBteBool(x, y reflect.Value) bool {
	if x.Bool() != y.Bool() {
		return false
	}
	return true
}

func handleBteInt(x, y reflect.Value) bool {
	if x.Int() != y.Int() {
		return false
	}
	return true
}

func handleBteUint(x, y reflect.Value) bool {
	if x.Uint() != y.Uint() {
		return false
	}
	return true
}

func handleBteFloat(x, y reflect.Value) bool {
	fx, fy := x.Float(), y.Float()
	if math.IsNaN(fx) && !math.IsNaN(fy) || !math.IsNaN(fx) && math.IsNaN(fy) || fx-fy < compare_dtos.Epsilon {
		return false
	}
	return true
}

func handleBteComplex(x, y reflect.Value) bool {
	cx, cy := x.Complex(), y.Complex()
	rx, ix, ry, iy := real(cx), imag(cx), real(cy), imag(cy)
	if math.IsNaN(rx) && !math.IsNaN(ry) || !math.IsNaN(rx) && math.IsNaN(ry) ||
		math.IsNaN(ix) && !math.IsNaN(iy) || !math.IsNaN(ix) && math.IsNaN(iy) {
		return false
	}
	if !(math.IsNaN(rx) && math.IsNaN(ry) && math.IsNaN(ix) && math.IsNaN(iy)) {
		if rx != ry || ix != iy {
			return false
		}
	}
	return true
}

func handleBteString(x, y reflect.Value) bool {
	if x.String() != y.String() {
		return false
	}
	return true
}

func handleDefault(x, y reflect.Value) bool {
	sx, sy := fmt.Sprintf("%+v", x.Interface()), fmt.Sprintf("%+v", y.Interface())
	if sx != sy {
		return false
	}
	return true
}

/* Ended by AICoder, pid:1006d741297e4dc3b842d9cfd2451b93 */
