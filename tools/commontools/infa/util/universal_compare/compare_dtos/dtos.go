package compare_dtos

const (
	Automatic = iota
	IterativeManner
	RecursiveManner
)

const (
	Epsilon          = 1e-5
	EmptyPlaceholder = ""
)

type CompareConfig struct {
	NeedDetails     bool
	PrimaryKey      string
	CompareApproach int
	IgnoredFields   []*IgnoredField
}

type IgnoredField struct {
	StructName string
	FieldName  string
}

type UpdateDetail struct {
	Content       interface{}
	ChangedFields []string
}

type CompareDetail struct {
	AddedObjPKeys   []string
	DeletedObjPKeys []string
	UpdatedObjPKeys []string
	AddedObjects    []interface{}
	DeletedObjects  []interface{}
	UpdatedObjects  []*UpdateDetail
}
