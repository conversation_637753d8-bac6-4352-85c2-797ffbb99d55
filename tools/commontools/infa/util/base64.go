package util

import "encoding/base64"

func Decode(base64String string) []byte {
	decoded, err := base64.StdEncoding.DecodeString(base64String)
	if err != nil {
		//ilog.Error("","", "Base64.Decode() error --- base64String: %s, error: $s", base64String, err.Error())
		return []byte{}
	}
	return decoded
}

func Encode(binaryData []byte) string {
	return base64.StdEncoding.EncodeToString(binaryData)
}

func EncodeString(str string) string {
	return Encode([]byte(str))
}
