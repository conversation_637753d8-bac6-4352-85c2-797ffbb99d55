package my_stack

import (
	"reflect"
	"sync"
)

type MyStack struct {
	list    []interface{}
	rwMutex sync.RWMutex
}

func NewStack() *MyStack {
	return &MyStack{list: make([]interface{}, 0)}
}

func (s *MyStack) Len() int {
	return len(s.list)
}

func (s *MyStack) IsEmpty() bool {
	return len(s.list) == 0
}

func (s *MyStack) Push(ele interface{}) {
	s.list = append(s.list, ele)
}

func (s *MyStack) Pop() interface{} {
	if len(s.list) == 0 {
		return nil
	}

	res := s.list[len(s.list)-1]
	s.list = s.list[:len(s.list)-1]
	return res
}

func (s *MyStack) Peek() interface{} {
	return s.list[len(s.list)-1]
}

func (s *MyStack) PushFirst(ele interface{}) {
	s.list = append([]interface{}{ele}, s.list...)
}

func (s *MyStack) PopFirst() interface{} {
	if len(s.list) == 0 {
		return nil
	}

	res := s.list[0]
	s.list = s.list[1:]
	return res
}

func (s *MyStack) Clean() {
	if len(s.list) == 0 {
		return
	}

	for i := range s.list {
		s.list[i] = reflect.ValueOf(nil)
	}

	s.list = make([]interface{}, 0)
}
