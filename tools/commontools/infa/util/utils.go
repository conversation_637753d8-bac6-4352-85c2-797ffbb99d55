package util

import (
	"bytes"
	"crypto/sha256"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/logger"
	"encoding/gob"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/gofrs/uuid"
)

func UUID() string {
	UUID, _ := uuid.NewV4()
	return UUID.String()
}

var TrivialValidator = func(param interface{}) interface{} {
	return param
}

func AreValidUUIDs(strs ...string) bool {
	regexUUID := regexp.MustCompile(globalcv.RegExpStrUUID)
	for _, str := range strs {
		if len(str) != 0 && !regexUUID.MatchString(str) {
			return false
		}
	}
	return true
}

func AreNotEmpty(strs ...string) bool {
	for _, s := range strs {
		if len(s) == 0 {
			return false
		}
	}
	return true
}

func ToJSONStr(value interface{}) string {
	b, err := json.Marshal(value)
	if err != nil {
		fmt.Println("Umarshal failed:", err)
		return ""
	}
	//logger.Infof(string(b))
	return string(b)
}

// 转string方法，orm库中有实现
func ToStr(value interface{}) string {
	if value == nil {
		return ""
	}
	//map类型的要特殊处理下转成json
	t := reflect.TypeOf(value)

	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	if t.Kind() == reflect.Struct || t.Kind() == reflect.Map || t.Kind() == reflect.Slice || t.Kind() == reflect.Array {
		jsonStr, err := json.Marshal(value)
		if err != nil {
			fmt.Println("Error marshalling value to JSON:", err.Error())
		}
		return string(jsonStr)
	}

	return fmt.Sprintf("%v", value)
}

func ReadFile(filePath string) ([]byte, error) {
	f, err := os.Open(path.Clean(filePath))
	if err != nil {
		return nil, err
	}

	defer f.Close()
	var fileContent []byte
	buf := make([]byte, 1024)
	for {
		n, err := f.Read(buf)
		if 0 == n || err != nil {
			break
		}
		fileContent = append(fileContent, buf[:n]...)
	}
	return fileContent, nil
}

// GetIPAndPort get ip and port from address
//
// address must be in the form of `ip:port`
func GetIPAndPort(addr string) (string, string) {
	idx := strings.LastIndex(addr, ":")
	if idx > 0 {
		return addr[:idx], addr[idx+1:]
	}
	return addr, ""
}

func AddressToIP(addr string) string {

	return ""
}

func GetCurrentPath() (string, error) {
	//执行程序所在的绝对路径
	file, err := exec.LookPath(os.Args[0])
	if err != nil {
		return "", err
	}
	//检测是否绝对路径，是直接返回，否添加当前工作路径到参数path前
	filePath, err := filepath.Abs(file)
	if err != nil {
		return "", err
	}
	index := strings.LastIndex(filePath, string(os.PathSeparator))
	ret := filePath[:index]
	return ret, nil
}

func GetFilePath(filePath string) string {
	var absFilePath string
	//检测是否为绝对地址，是直接返回，否添加当前工作路径到参数path前
	if path.IsAbs(filePath) {
		absFilePath = filePath
	} else {
		curPath, err := GetCurrentPath()
		if err != nil {
			fmt.Println("can't get curPath")
			return ""
		}
		absFilePath = fmt.Sprintf("%s%c%s", curPath, os.PathSeparator, filePath)
	}

	return absFilePath
}

func GetAbsPathFromRelativePath(path string) string {
	wd, err := os.Getwd()
	if err != nil {
		fmt.Println("get absolute path failed")
		return ""
	}
	return wd[:strings.LastIndex(wd, "pvrm")+4] + "/" + path
}

func GetAbsPathFromRelativePathForImage(path string) string {
	wd, err := os.Getwd()
	if err != nil {
		fmt.Println("get absolute path failed")
		return ""
	}
	return wd[:strings.LastIndex(wd, "image")+5] + "/" + path
}

// 一般用于业务中string类型的对象转成map
func StringToMap(value string) map[string]interface{} {
	var extra map[string]interface{}
	if value != "" {
		if err := json.Unmarshal([]byte(value), &extra); err != nil {
			fmt.Println(err.Error())
		}
	}
	return extra
}

// 把sclice/array转换成符号分割的字符串
func SliceCutToString(values interface{}, mark string) string {
	return strings.Replace(strings.Trim(fmt.Sprint(values), "[]"), " ", mark, -1)
}

func SliceCutToStringWithMark(values interface{}, mark string) string {
	return "'" + strings.Replace(strings.Trim(fmt.Sprint(values), "[]"), " ", "'"+mark+"'", -1) + "'"
}

// 判断类型
func TypeOf(v interface{}) string {
	return reflect.TypeOf(v).String()
}

// 判断obj是否在target中，target支持的类型arrary,slice,map
func Contains(obj interface{}, target interface{}) bool {
	targetValue := reflect.ValueOf(target)
	switch reflect.TypeOf(target).Kind() {
	case reflect.Slice, reflect.Array:
		for i := 0; i < targetValue.Len(); i++ {
			if targetValue.Index(i).Interface() == obj {
				return true
			}
		}
	case reflect.Map:
		if targetValue.MapIndex(reflect.ValueOf(obj)).IsValid() {
			return true
		}
	}
	return false
}

// 删除target切片中的obj，target支持的类型arrary,slice
func DeleteElemOfSlice(obj interface{}, target interface{}) interface{} {
	targetValue := reflect.ValueOf(target)
	switch reflect.TypeOf(target).Kind() {
	case reflect.Slice, reflect.Array:
		i := 0
		for ; i < targetValue.Len(); i++ {
			if targetValue.Index(i).Interface() == obj {
				break
			}
		}
		if i < targetValue.Len() {
			targetValue = reflect.AppendSlice(targetValue.Slice(0, i), targetValue.Slice(i+1, targetValue.Len()))
		}
	}
	return targetValue.Interface()
}

// map的key为struct的字段，如需要json标签的字段使用StructTagJsonToMap
func StructToMap(obj interface{}) map[string]interface{} {
	return StructTagToMap(obj, "")
}

// map的key为标签json字段的值
func StructTagJSONToMap(obj interface{}) map[string]interface{} {
	return StructTagToMap(obj, "json")
}

func StructTagToMap(obj interface{}, tag string) map[string]interface{} {
	t, v := ReflectTypeAndValueOf(obj)
	if t.Kind() != reflect.Struct {
		logger.Warnf("check type error not struct")
		return nil
	}
	var data = make(map[string]interface{})
	generateKey := func(field reflect.StructField, tag string) string {
		if tag == "" {
			return field.Name
		}
		return field.Tag.Get(tag)
	}
	for i := 0; i < t.NumField(); i++ {
		data[generateKey(t.Field(i), tag)] = v.Field(i).Interface()
	}
	return data
}

func ReflectTypeAndValueOf(obj interface{}) (reflect.Type, reflect.Value) {
	//获得类型对象（reflect.Type）
	//t.Name 类型名 例：[]orgDto{}
	//t.Kind 类型种类 例：Slice
	t := reflect.TypeOf(obj)
	//值的反射值对象
	v := reflect.ValueOf(obj)
	//指针类型处理
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	return t, v
}

// 判空公用方法
/* Started by AICoder, pid:2b9cf41d5ed641d9a90c98c701964714 */
func IsEmpty(actual interface{}) bool {
	if actual == nil {
		return true
	}
	value := reflect.ValueOf(actual)
	return isEmptyValue(value)
}

func isEmptyValue(value reflect.Value) bool {
	switch value.Kind() {
	case reflect.Slice, reflect.Chan, reflect.Map, reflect.String:
		return isEmptyLen(value)
	case reflect.Ptr:
		return isEmptyPtr(value)
	default:
		return false
	}
}

func isEmptyLen(value reflect.Value) bool {
	return value.Len() == 0
}

func isEmptyPtr(value reflect.Value) bool {
	if value.IsNil() {
		return true
	}
	elem := value.Elem()
	return (elem.Kind() == reflect.Slice || elem.Kind() == reflect.Array) && elem.Len() == 0
}

/* Ended by AICoder, pid:2b9cf41d5ed641d9a90c98c701964714 */

// 获取项目中extra的字段值
func GetExtraProperty(key string, extra map[string]interface{}) string {
	if _, ok := extra[key]; ok {
		return ToStr(extra[key])
	}
	return ""
}

// 获取extra字段中的某项
func GetExtraItem(extra string, key string) (interface{}, bool) {
	extraMap := map[string]interface{}{}
	err := json.Unmarshal([]byte(extra), &extraMap)
	if err != nil {
		fmt.Println("getExtraItem json.Marshal failed:", err)
		return nil, false
	}
	if value, ok := extraMap[key]; ok {
		return value, true
	}
	return nil, false
}

// 解析vmware来源的主机名
func ProcessHostNameFromVmware(host string) string {
	hostInfoArr := strings.Split(host, ":")
	return hostInfoArr[len(hostInfoArr)-1]
}

func IsContain(items []int, item int) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

func IsContain4String(items []string, item string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

func GetStructFieldName(structName interface{}) []string {
	t := reflect.TypeOf(structName)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		fmt.Println("Check type error not Struct")
		return nil
	}
	fieldNum := t.NumField()
	result := make([]string, 0, fieldNum)
	for i := 0; i < fieldNum; i++ {
		result = append(result, t.Field(i).Name)
	}
	return result
}

func GetCurrentUTCTime() string {
	return time.Now().UTC().Format(time.RFC3339)
}

func Format4PgTime(old string) string {
	tm2, err := time.Parse("2006-01-02T15:04:05.000000", old)
	if err != nil {
		fmt.Printf("Format4PgTime parse time error, time: %s, error: %v\n", old, err)
		return old
	}
	return tm2.Format(time.RFC3339)
}

func ConvertUnixTimeToRFC3339(sec int64) string {
	return time.Unix(sec, 0).UTC().Format(time.RFC3339)
}

func WaitForTimes(times int, durationSeconds int, check func() bool) bool {
	for i := 0; i < times; i++ {
		if check() {
			return true
		}
		time.Sleep(time.Duration(durationSeconds) * time.Second)
		continue
	}
	return false
}

func WaitForTimesEx(times int, durationSeconds int, check func(string) bool, param string) bool {
	for i := 0; i < times; i++ {
		if check(param) {
			return true
		}
		time.Sleep(time.Duration(durationSeconds) * time.Second)
		continue
	}
	return false
}

func StructPrintln(s string, b interface{}, level int) {
	if level >= 1 {
		body, _ := json.Marshal(b)
		fmt.Println(s + string(body))
	}
}

func SetOpAND(setA, setB map[interface{}]struct{}) []interface{} {
	var res []interface{}
	for a := range setA {
		if _, ok := setB[a]; ok {
			res = append(res, a)
		}
	}
	return res
}

func SetOpDIFF(setA, setB map[interface{}]struct{}) []interface{} {
	var res []interface{}
	for a := range setA {
		if _, ok := setB[a]; !ok {
			res = append(res, a)
		}
	}
	return res
}

// []string 去重
func RemoveRepeatedElement(arr []string) (newArr []string) {
	newArr = make([]string, 0)
	for i := 0; i < len(arr); i++ {
		repeat := false
		for j := i + 1; j < len(arr); j++ {
			if arr[i] == arr[j] {
				repeat = true
				break
			}
		}
		if !repeat {
			newArr = append(newArr, arr[i])
		}
	}
	return
}

// The function below is the optimized version of the one above [RemoveRepeatedElement] with much better time complexity of O(n) than O(n ^ 2);
// furthermore, it is an in-place algorithm, which means it doesn't need additional space for the output result;
func DeduplicateElements(arr []string) []string {
	history := make(map[string]bool)
	slow := 0
	fast := 0
	for fast < len(arr) {
		if _, ok := history[arr[fast]]; ok {
			fast++
		} else {
			history[arr[fast]] = true
			swap(&arr, slow, fast)
			slow++
			fast++
		}
	}
	return arr[:slow]
}

func swap(arr *[]string, left, right int) {
	tmp := (*arr)[left]
	(*arr)[left] = (*arr)[right]
	(*arr)[right] = tmp
}

// The function below is designed to support the flow of get-compare-update that is commonly used in partial updating scenarios
// on a struct-formed object by implementing the comparison between the old and the new one and then assigning all of the empty
// fields in the new object with the values extracted from the old one. It returns the new object with all fields assigned.
func CompareAndCollateForStructObjs(old, new interface{}) (interface{}, error) {
	x, y := reflect.ValueOf(old), reflect.ValueOf(new)
	if x.Kind() == reflect.Ptr {
		x = x.Elem()
	}
	if y.Kind() == reflect.Ptr {
		y = y.Elem()
	}
	if x.Kind() != reflect.Struct || y.Kind() != reflect.Struct {
		return nil, errors.New("both of the input objects must be struct kind")
	}

	xLen, yLen := x.NumField(), y.NumField()
	if xLen != yLen {
		return nil, errors.New("both of the input objects must be the instances of the same struct")
	}

	compareAndCollateForStructObjs(x, y)

	return y.Interface(), nil
}

func compareAndCollateForStructObjs(x, y reflect.Value) {
	valLen := x.NumField()
	for i := 0; i < valLen; i++ {
		oldVal := x.Field(i)
		reqVal := y.Field(i)
		switch oldVal.Kind() {
		case reflect.Struct:
			compareAndCollateForStructObjs(oldVal, reqVal)
		default:
			if reqVal.IsValid() && reqVal.IsZero() && reqVal.CanSet() {
				reqVal.Set(oldVal)
			}
		}
	}
}

func IsValidPointer(v interface{}) bool {
	rv := reflect.ValueOf(v)
	if rv.Kind() != reflect.Ptr || rv.IsNil() {
		return false
	}
	return true
}

func DeepCopy(dst, src interface{}) error {
	var buf bytes.Buffer
	if err := gob.NewEncoder(&buf).Encode(src); err != nil {
		return err
	}
	return gob.NewDecoder(bytes.NewBuffer(buf.Bytes())).Decode(dst)
}

func SHA256Encode(str string) string {
	hash := sha256.New()
	hash.Write([]byte(str))
	return hex.EncodeToString(hash.Sum(nil))
}
func IpToFormatString(addr string) string {
	result := strings.Replace(addr, ".", "-", -1)
	return strings.Replace(result, ":", "-", -1)
}

// kind 0 (number) 1 (lowercase) 2 (uppercase) 3 all
func RandomString(size int, kind int) string {
	ikind, kinds, rsbytes := kind, [][]int{{10, 48}, {26, 97}, {26, 65}}, make([]byte, size)
	isAll := kind > 2 || kind < 0
	rand.Seed(time.Now().UnixNano())
	for i := 0; i < size; i++ {
		if isAll {
			ikind = rand.Intn(3)
		}
		scope, base := kinds[ikind][0], kinds[ikind][1]
		rsbytes[i] = uint8(base + rand.Intn(scope))
	}
	return string(rsbytes)
}

func ToSlice(data interface{}) []interface{} {
	var ret []interface{}
	v := reflect.ValueOf(data)
	kind := v.Kind()
	switch kind {
	case reflect.Slice, reflect.Array:
		for i := 0; i < v.Len(); i++ {
			ret = append(ret, v.Index(i).Interface())
		}
	case reflect.Map:
		for _, key := range v.MapKeys() {
			ret = append(ret, v.MapIndex(key).Interface())
		}
	default:
		ret = append(ret, data)
	}
	return ret
}

func ToMap(data interface{}, key string) map[string]interface{} {
	var ret = make(map[string]interface{})
	slice := ToSlice(data)
	for idx, val := range slice {
		v := reflect.ValueOf(val)
		if v.Kind() == reflect.Ptr {
			v = v.Elem()
		}
		var k string
		switch v.Kind() {
		case reflect.Struct:
			k = v.FieldByName(key).String()
		default:
			k = fmt.Sprintf("%s%d", key, idx)
		}
		ret[k] = val
	}
	return ret
}

func IsLengthBetween(content string, begin int, end int) bool {
	if utf8.RuneCountInString(content) >= begin && utf8.RuneCountInString(content) <= end {
		return true
	}
	return false
}

type Tuple[T interface{}] struct {
	X, Y T
}

func ErrMsg(msg string) string {
	result, _ := json.Marshal(map[string]string{"error": msg})
	return string(result)
}

func IsInteger(s string) bool {
	_, err := strconv.Atoi(s)
	if err != nil {
		logger.Errorf("%s is not integer", s)
		return false
	}
	return true
}

func Intersect(m, n []string) (result []string) {
	for _, v := range m {
		if IsContain4String(n, v) {
			result = append(result, v)
		}
	}
	return
}
