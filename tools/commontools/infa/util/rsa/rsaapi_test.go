package rsa

import (
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"testing"
	"cwsm/tools/commontools/logger"
)

const (
	Prefix = "-----BEGIN PUBLIC KEY-----\n"
	Suffix = "\n-----<PERSON>ND PUBLIC KEY-----"
)

func TestRsaApiTest(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "rsa test")
}

var _ = Describe("Rsa encryption test", func() {
	Describe("public key format test", func() {
		It("should return normal encrypted result with a valid public key", func() {
			var pubPEMData = []byte(` 
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAis7FGtz58umYDjKuUjF+CjrEEY2cQopP
mrfIWyEHFXagtQU38vhCBWTHlKaPuGNtDTvZNMG8SqwOSt5lmY0F4WchgVcTPD8417V3LUKigh2f
Uymtu9ZeBrF5Tp/HQ7e0F+2vpp9PkQwYQYiGuJ/RCVyT/5VB14O2ygyhE2+bgRuI9wKAaeKOqzQI
xJk+HT4yKoaGOGsA6TanJxHMhSFBgggi48HDS4a+yp51fz/zIbs4CWe4QhJirbxhZZLOBB4TQ6/p
iM+c5GqIw7xoYfoJgow6wm5G4P3wZqKmPbeqY+zXyfKPuqDt78WZJ4ZTPnjiPYGEdEjPniOMqY5d
zsSr0wIDAQAB
`)

			input := Prefix + string(pubPEMData) + Suffix
			logger.Infof("[public key] formatted key: %s", input)

			result, err := RsaEncrypt([]byte(input), []byte("snmp2000ZX!"))
			if err != nil {
				logger.Errorf("[error] err: %s", err.Error())
			}
			logger.Infof("[encrypted] result: %s", string(result))
			Expect(true).To(Equal(err == nil))

		})

	})
})
