package rsa

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"cwsm/tools/commontools/director/iks"
	"cwsm/tools/commontools/logger"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"log"
)

const (
	iamProperties = "conf/transport.properties"
	iamKey        = "transport_key"
)

type EncryptedRequest struct {
	Cipher string `json:"cipher"`
}

type RSAPrivateKey struct {
	PrivateKeyPkcs1 string `json:"key_pkcs1_pem"`
	PrivateKeyPkcs8 string `json:"key_pkcs8"`
}

func RSAEncryptCipherBody(s string) (string, error) {
	enBase64A := base64.StdEncoding.EncodeToString([]byte(s))
	enRsa, err := RSAEncrypt([]byte(enBase64A))
	if err != nil {
		return "", err
	}
	enBase64B := base64.StdEncoding.EncodeToString(enRsa)
	return enBase64B, nil
}

//{"key":"-----BEGIN PUBLIC KEY-----\nMIIDIjANBgkqhkiG9w0BAQEFAAOCAw8AMIIDCgKCAwEAtcqNdqQZVj4b9PCdPsAD\nrFHVDdfdkZzHepzgFwA4usl+pN+7vZM9HBfdioi701Nn/FThD2gFkXirNPJSUYnB\nxydxHU8S9ilhCbHANTdkK6B6MkNqAl2CvXNUjlErirVotLSkFk5vhschvf/1DGpP\nWabLuQteej7elXR8WYi2ma/+xT1JJl71cH7xX9TUn0HdKWhozWUe2Oosafnw9ZNe\nEiwGGW0y+Goq2b9TknId0W/j20h7ZsxLY3FJDWyxJoGcmSgx7EiRYvt8QyIB5J9X\nEZpjxaVO5qh/GKmih1ThbFZDGs9ZBsx3rxhcwuy8AFAD7WNKPLbVScdE0s1EYTYo\nItcwF77p66aRflmeqp94X/0zrpnt+mLWf0Oy2ioj9V8APa7RnvSF2Kwb8pV4Exl2\n1saAbl5rAyVjVbPNmpzaE5reyQuRuxNv5hrR6k1OrYLElNJUTbaJFxGmZefdie72\nepfiDt6ZSCZMMwM75DMeTz6j5bfHd5Dd2cmTKDBd/aJK88wGxkiVVzNHqVFsKSPL\nbpH4apXaq42qoIWEGayYIvv45kWRipMIwdaInb9onstX0aSDG4c9Vl5JsJbeyEbC\nF6DIXOwQJ9LtYghyp23SsvNz6D6ILRdhhhCOCGP8JJFarABwprnJYhLI//4QYhcr\naAzpSpUCC2v87g2aBzWHwIATP0OFw0oPFLzBW3QWl+INdKjRZN46QqL6nFmVTiDW\nyaSTaeoRh4uf6deL8uQqVt5myxYtorGsA6zl+AOvilm74KTm4YjemChmfKShckau\ncO+kZWuN/URj+8ZskSrGOCwDdA8rZLQde+UK0JZt1iT2ivSC9o0sOBPoIMi17NoZ\nAXEHTTwXEUX7LpA1JE+3RrhMk3ituM0ACxBvsFU3xtQXTSfeXSv4+0qbkZNAoMBI\nZxMuQdM2yYqYXFd98b2PmA4+OEoLgLd9d4kBeJu1bLHP+1VzaU35mr/hnabY6IIW\nwMqrK52RfjoVsa53YAchwFYtdFt4FTYtzXf/V8h1TvLrAgMBAAE=\n-----END PUBLIC KEY-----","key_x509_encoded":"MIIDIjANBgkqhkiG9w0BAQEFAAOCAw8AMIIDCgKCAwEAtcqNdqQZVj4b9PCdPsADrFHVDdfdkZzHepzgFwA4usl+pN+7vZM9HBfdioi701Nn/FThD2gFkXirNPJSUYnBxydxHU8S9ilhCbHANTdkK6B6MkNqAl2CvXNUjlErirVotLSkFk5vhschvf/1DGpPWabLuQteej7elXR8WYi2ma/+xT1JJl71cH7xX9TUn0HdKWhozWUe2Oosafnw9ZNeEiwGGW0y+Goq2b9TknId0W/j20h7ZsxLY3FJDWyxJoGcmSgx7EiRYvt8QyIB5J9XEZpjxaVO5qh/GKmih1ThbFZDGs9ZBsx3rxhcwuy8AFAD7WNKPLbVScdE0s1EYTYoItcwF77p66aRflmeqp94X/0zrpnt+mLWf0Oy2ioj9V8APa7RnvSF2Kwb8pV4Exl21saAbl5rAyVjVbPNmpzaE5reyQuRuxNv5hrR6k1OrYLElNJUTbaJFxGmZefdie72epfiDt6ZSCZMMwM75DMeTz6j5bfHd5Dd2cmTKDBd/aJK88wGxkiVVzNHqVFsKSPLbpH4apXaq42qoIWEGayYIvv45kWRipMIwdaInb9onstX0aSDG4c9Vl5JsJbeyEbCF6DIXOwQJ9LtYghyp23SsvNz6D6ILRdhhhCOCGP8JJFarABwprnJYhLI//4QYhcraAzpSpUCC2v87g2aBzWHwIATP0OFw0oPFLzBW3QWl+INdKjRZN46QqL6nFmVTiDWyaSTaeoRh4uf6deL8uQqVt5myxYtorGsA6zl+AOvilm74KTm4YjemChmfKShckaucO+kZWuN/URj+8ZskSrGOCwDdA8rZLQde+UK0JZt1iT2ivSC9o0sOBPoIMi17NoZAXEHTTwXEUX7LpA1JE+3RrhMk3ituM0ACxBvsFU3xtQXTSfeXSv4+0qbkZNAoMBIZxMuQdM2yYqYXFd98b2PmA4+OEoLgLd9d4kBeJu1bLHP+1VzaU35mr/hnabY6IIWwMqrK52RfjoVsa53YAchwFYtdFt4FTYtzXf/V8h1TvLrAgMBAAE="}

func getPublicKey() (string, error) {
	encryptedKey, err := iks.GetEncryptedPublicKey()
	if err != nil {
		return "-----BEGIN PUBLIC KEY-----\nMIIDIjANBgkqhkiG9w0BAQEFAAOCAw8AMIIDCgKCAwEAtcqNdqQZVj4b9PCdPsAD\nrFHVDdfdkZzHepzgFwA4usl+pN+7vZM9HBfdioi701Nn/FThD2gFkXirNPJSUYnB\nxydxHU8S9ilhCbHANTdkK6B6MkNqAl2CvXNUjlErirVotLSkFk5vhschvf/1DGpP\nWabLuQteej7elXR8WYi2ma/+xT1JJl71cH7xX9TUn0HdKWhozWUe2Oosafnw9ZNe\nEiwGGW0y+Goq2b9TknId0W/j20h7ZsxLY3FJDWyxJoGcmSgx7EiRYvt8QyIB5J9X\nEZpjxaVO5qh/GKmih1ThbFZDGs9ZBsx3rxhcwuy8AFAD7WNKPLbVScdE0s1EYTYo\nItcwF77p66aRflmeqp94X/0zrpnt+mLWf0Oy2ioj9V8APa7RnvSF2Kwb8pV4Exl2\n1saAbl5rAyVjVbPNmpzaE5reyQuRuxNv5hrR6k1OrYLElNJUTbaJFxGmZefdie72\nepfiDt6ZSCZMMwM75DMeTz6j5bfHd5Dd2cmTKDBd/aJK88wGxkiVVzNHqVFsKSPL\nbpH4apXaq42qoIWEGayYIvv45kWRipMIwdaInb9onstX0aSDG4c9Vl5JsJbeyEbC\nF6DIXOwQJ9LtYghyp23SsvNz6D6ILRdhhhCOCGP8JJFarABwprnJYhLI//4QYhcr\naAzpSpUCC2v87g2aBzWHwIATP0OFw0oPFLzBW3QWl+INdKjRZN46QqL6nFmVTiDW\nyaSTaeoRh4uf6deL8uQqVt5myxYtorGsA6zl+AOvilm74KTm4YjemChmfKShckau\ncO+kZWuN/URj+8ZskSrGOCwDdA8rZLQde+UK0JZt1iT2ivSC9o0sOBPoIMi17NoZ\nAXEHTTwXEUX7LpA1JE+3RrhMk3ituM0ACxBvsFU3xtQXTSfeXSv4+0qbkZNAoMBI\nZxMuQdM2yYqYXFd98b2PmA4+OEoLgLd9d4kBeJu1bLHP+1VzaU35mr/hnabY6IIW\nwMqrK52RfjoVsa53YAchwFYtdFt4FTYtzXf/V8h1TvLrAgMBAAE=\n-----END PUBLIC KEY-----", nil
	}
	return encryptedKey.Key, nil
}

func RSAEncrypt(cipher []byte) ([]byte, error) {
	publicKey, err := getPublicKey()
	if err != nil {
		return []byte{}, err
	}
	text, err := RsaEncrypt([]byte(publicKey), cipher)
	if err != nil {
		return []byte{}, err
	}
	return text, nil
}

func rsaDecrypt(privateKey []byte, cipher []byte) ([]byte, error) {
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return []byte{}, errors.New("decode privateKey fail")
	}

	priv, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		log.Printf("rsaDecrypt:x509.ParsePKCS1PrivateKey error: %v\n", err)
		logger.Errorf("rsaDecrypt:x509.ParsePKCS1PrivateKey error:", err.Error())
		return []byte{}, errors.New("parse PKCS1 privateKey fail, err : " + err.Error())
	}

	passwd, err := rsa.DecryptPKCS1v15(rand.Reader, priv, cipher)
	if err != nil {
		logger.Errorf("rsaDecrypt:rsa.DecryptPKCS1v15 error:", err.Error())
		return []byte{}, errors.New("decrypt PKCS15 fail, err : " + err.Error())
	}
	return passwd, nil
}

func RsaEncrypt(publicKey []byte, cipher []byte) ([]byte, error) {
	block, _ := pem.Decode(publicKey)
	if block == nil {
		return []byte{}, errors.New("decode publicKey fail")
	}

	pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		log.Printf("rsaDecrypt:x509.ParsePKCS1PublicKey error: %v\n", err)
		logger.Errorf("rsaDecrypt:x509.ParsePKCS1PublicKey error:", err.Error())
		return []byte{}, errors.New("parse PKCS1 publicKey fail, err : " + err.Error())
	}
	pub := pubInterface.(*rsa.PublicKey)
	passwd, err := rsa.EncryptPKCS1v15(rand.Reader, pub, cipher)
	if err != nil {
		logger.Errorf("rsaDecrypt:rsa.EncryptPKCS1v15 error:", err.Error())
		return []byte{}, errors.New("decrypt PKCS15 fail, err : " + err.Error())
	}
	return passwd, nil
}
