package util

type Set struct {
	elements map[interface{}]struct{}
}

func NewSet(elements ...interface{}) *Set {
	s := &Set{}
	s.elements = make(map[interface{}]struct{})
	s.Add(elements...)
	return s
}

func (s *Set) Add(elements ...interface{}) {
	for _, element := range elements {
		s.elements[element] = struct{}{}
	}
}

func (s *Set) Del(elements ...interface{}) {
	for _, element := range elements {
		delete(s.elements, element)
	}
}

func (s *Set) Contains(element interface{}) bool {
	_, ok := s.elements[element]
	return ok
}

func (s *Set) Size() int {
	return len(s.elements)
}

func (s *Set) Equal(other *Set) bool {
	if s.Size() != other.Size() {
		return false
	}

	for key := range s.elements {
		if !other.Contains(key) {
			return false
		}
	}
	return true
}

func (s *Set) IsSubset(other *Set) bool {
	if s.Size() > other.Size() {
		return false
	}
	for key := range s.elements {
		if !other.Contains(key) {
			return false
		}
	}
	return true
}
