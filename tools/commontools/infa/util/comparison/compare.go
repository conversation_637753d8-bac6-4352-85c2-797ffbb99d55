package comparison

import (
	"cwsm/tools/commontools/infa/util/universal_compare/sort"
	"fmt"
	"math"
	"reflect"
)

func Equals(x, y interface{}) bool {
	vx, vy := reflect.ValueOf(x), reflect.ValueOf(y)
	tx, ty := reflect.TypeOf(x), reflect.TypeOf(y)
	if tx.String() != ty.String() {
		return false
	}
	return sortingEquals(vx, vy)
}

/* Started by AICoder, pid:afe42f4e2720492eb43e3a088258186d */
func sortingEquals(x, y reflect.Value) bool {
	if !x.IsValid() || !y.IsValid() {
		return false
	}
	switch x.Type().Kind() {
	case reflect.Bool, reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr,
		reflect.Float32, reflect.Float64, reflect.Complex64, reflect.Complex128, reflect.String:
		return equals(x, y)
	case reflect.Array, reflect.Slice:
		return handleArraySlice(x, y)
	case reflect.Map:
		return handleMap(x, y)
	case reflect.Struct:
		return handleStruct(x, y)
	case reflect.Ptr:
		return handlePtr(x, y)
	default:
		sx, sy := fmt.Sprintf("%+v", x.Interface()), fmt.Sprintf("%+v", y.Interface())
		return sx == sy
	}
}

func handleArraySlice(x, y reflect.Value) bool {
	if x.Len() != y.Len() {
		return false
	}
	sort.RecursiveSort(x)
	sort.RecursiveSort(y)
	return equals(x, y)
}

func handleMap(x, y reflect.Value) bool {
	if x.Len() != y.Len() {
		return false
	}
	for _, key := range x.MapKeys() {
		if !sortingEquals(x.MapIndex(key), y.MapIndex(key)) {
			return false
		}
	}
	return true
}

func handleStruct(x, y reflect.Value) bool {
	for i := 0; i < x.NumField(); i++ {
		if !sortingEquals(x.Field(i), y.Field(i)) {
			return false
		}
	}
	return true
}

func handlePtr(x, y reflect.Value) bool {
	if !x.IsNil() && !y.IsNil() {
		return sortingEquals(x.Elem(), y.Elem())
	} else if (x.IsNil() && !y.IsNil()) || (!x.IsNil() && y.IsNil()) {
		return false
	}
	return true
}

/* Ended by AICoder, pid:afe42f4e2720492eb43e3a088258186d */

/* Started by AICoder, pid:3379343582314b5ebbde3ee022f4c08a */
func equals(x, y reflect.Value) bool {
	if !x.IsValid() || !y.IsValid() {
		return false
	}
	switch x.Type().Kind() {
	case reflect.Bool:
		return equalsBool(x, y)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return equalsInt(x, y)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return equalsUint(x, y)
	case reflect.Float32, reflect.Float64:
		return equalsFloat(x, y)
	case reflect.Complex64, reflect.Complex128:
		return equalsComplex(x, y)
	case reflect.String:
		return equalsString(x, y)
	case reflect.Struct:
		return equalsStruct(x, y)
	case reflect.Array, reflect.Slice:
		return equalsArray(x, y)
	case reflect.Map:
		return equalsMap(x, y)
	case reflect.Ptr:
		return equalsPtr(x, y)
	default:
		return equalsDefault(x, y)
	}
}

func equalsBool(x, y reflect.Value) bool {
	if x.Bool() != y.Bool() {
		return false
	}
	return true
}

func equalsInt(x, y reflect.Value) bool {
	if x.Int() != y.Int() {
		return false
	}
	return true
}

func equalsUint(x, y reflect.Value) bool {
	if x.Uint() != y.Uint() {
		return false
	}
	return true
}

func equalsFloat(x, y reflect.Value) bool {
	fx, fy := x.Float(), y.Float()
	if math.IsNaN(fx) && !math.IsNaN(fy) || !math.IsNaN(fx) && math.IsNaN(fy) || fx-fy < epsilon {
		return false
	}
	return true
}

/* Started by AICoder, pid:ce027b577bb04d88aef54bc5bfeafdc9 */
func equalsComplex(x, y reflect.Value) bool {
	cx, cy := x.Complex(), y.Complex()
	rx, ix, ry, iy := real(cx), imag(cx), real(cy), imag(cy)
	if hasNaNDisparity(rx, ry, ix, iy) {
		return false
	}
	if !allAreNaN(rx, ry, ix, iy) && hasValueDisparity(rx, ry, ix, iy) {
		return false
	}
	return true
}

func hasNaNDisparity(rx, ry, ix, iy float64) bool {
	return (math.IsNaN(rx) && !math.IsNaN(ry)) || (!math.IsNaN(rx) && math.IsNaN(ry)) ||
		(math.IsNaN(ix) && !math.IsNaN(iy)) || (!math.IsNaN(ix) && math.IsNaN(iy))
}

func allAreNaN(rx, ry, ix, iy float64) bool {
	return math.IsNaN(rx) && math.IsNaN(ry) && math.IsNaN(ix) && math.IsNaN(iy)
}

func hasValueDisparity(rx, ry, ix, iy float64) bool {
	return rx != ry || ix != iy
}

/* Ended by AICoder, pid:ce027b577bb04d88aef54bc5bfeafdc9 */

func equalsString(x, y reflect.Value) bool {
	if x.String() != y.String() {
		return false
	}
	return true
}

func equalsStruct(x, y reflect.Value) bool {
	for i := 0; i < x.NumField(); i++ {
		if !equals(x.Field(i), y.Field(i)) {
			return false
		}
	}
	return true
}

func equalsArray(x, y reflect.Value) bool {
	if x.Len() != y.Len() {
		return false
	}
	for i := 0; i < x.Len(); i++ {
		if !equals(x.Index(i), y.Index(i)) {
			return false
		}
	}
	return true
}

func equalsMap(x, y reflect.Value) bool {
	if x.Len() != y.Len() {
		return false
	}
	keys := x.MapKeys()
	for _, key := range keys {
		if !equals(x.MapIndex(key), y.MapIndex(key)) {
			return false
		}
	}
	return true
}

func equalsPtr(x, y reflect.Value) bool {
	if !x.IsNil() && !y.IsNil() {
		if !equals(x.Elem(), y.Elem()) {
			return false
		}
	} else if (x.IsNil() && !y.IsNil()) || (!x.IsNil() && y.IsNil()) {
		return false
	}
	return true
}

func equalsDefault(x, y reflect.Value) bool {
	sx, sy := fmt.Sprintf("%+v", x.Interface()), fmt.Sprintf("%+v", y.Interface())
	if sx != sy {
		return false
	}
	return true
}

/* Ended by AICoder, pid:3379343582314b5ebbde3ee022f4c08a */
