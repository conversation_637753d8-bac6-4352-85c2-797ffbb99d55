package comparison

import (
	"fmt"
	"math"
	"reflect"
)

const epsilon = 1e-5

/* Started by AICoder, pid:f7e79200401e443ebf026ce06f029b0e */
func isLess(x, y reflect.Value) bool {
	switch x.Type().Kind() {
	case reflect.Bool:
		return !x.Bool() && y.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return x.Int() < y.Int()
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return x.Uint() < y.Uint()
	case reflect.Float32, reflect.Float64:
		return x.Float() < y.Float() || math.IsNaN(x.Float()) && !math.IsNaN(y.Float())
	case reflect.Complex64, reflect.Complex128:
		return isLessComplex(x.Complex(), y.Complex())
	case reflect.String:
		return x.String() < y.String()
	case reflect.Ptr:
		return isLessPtr(x, y)
	default:
		return fmt.Sprintf("%+v", x.Interface()) < fmt.Sprintf("%+v", y.Interface())
	}
}

func isLessComplex(cx, cy complex128) bool {
	rx, ix, ry, iy := real(cx), imag(cx), real(cy), imag(cy)
	if rx == ry || (math.IsNaN(rx) && math.IsNaN(ry)) {
		return ix < iy || math.IsNaN(ix) && !math.IsNaN(iy)
	}
	return rx < ry || math.IsNaN(rx) && !math.IsNaN(ry)
}

func isLessPtr(x, y reflect.Value) bool {
	if !x.IsNil() && !y.IsNil() {
		return isLess(x.Elem(), y.Elem())
	} else if !x.IsNil() && y.IsNil() {
		return true
	}
	return false
}

/* Ended by AICoder, pid:f7e79200401e443ebf026ce06f029b0e */

func sink(value reflect.Value, length, root int) {
	max := root
	le, ri := 2*root+1, 2*root+2
	if le < length && isLess(value.Index(root), value.Index(le)) {
		max = le
	}
	if ri < length && isLess(value.Index(max), value.Index(ri)) {
		max = ri
	}
	if max != root {
		swap(value, root, max)
		sink(value, length, max)
	}
}

func swap(value reflect.Value, i, j int) {
	vi := reflect.New(value.Index(i).Type())
	vi.Elem().Set(value.Index(i))
	value.Index(i).Set(value.Index(j))
	value.Index(j).Set(vi.Elem())
}
