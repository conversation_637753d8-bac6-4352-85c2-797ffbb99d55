package file

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
)

// 从文件中读取内容
func ReadTextFromFile(filePath string) (string, error) {
	bytes, err := ioutil.ReadFile(filepath.Clean(filePath))
	if err != nil {
		logger.Infof("Read file %s failed! : ", filePath)
		return "", fmt.Errorf("Read file " + filePath + " failed!")
	}

	fileContent := string(bytes)

	return fileContent, err
}

var MakeDirIfNotExists = func(dir string) error {
	if !DirExists(dir) {
		err := os.MkdirAll(dir, 0750)
		if err != nil {
			logger.Errorf("create dir failed, path : ", dir, ", err : ", err)
			return err
		}
	}
	return nil
}

var DirExists = func(dir string) bool {
	_, err := os.Stat(dir)
	if err == nil {
		return true
	}
	if os.IsNotExist(err) {
		return false
	}
	logger.Errorf("dir status failed, path : ", dir, ", err : ", err)
	return false
}

var ListFileNames = func(dir string, filterFunc func(os.FileInfo) bool) ([]string, error) {
	if !DirExists(dir) {
		return []string{}, nil
	}
	fileList, err := ioutil.ReadDir(dir)
	if err != nil {
		logger.Errorf("openDir failed: ", dir)
		return nil, err
	}

	var fileNames []string
	for _, file := range fileList {
		if filterFunc(file) {
			fileNames = append(fileNames, file.Name())
		}
	}

	return fileNames, nil
}

var ListFileInfos = func(dir string) ([]os.FileInfo, error) {
	fileList, err := ioutil.ReadDir(dir)
	if err != nil {
		logger.Errorf("openDir failed: ", dir)
	}
	return fileList, err
}

var CleanDir = func(dir string, filterFunc func(os.FileInfo) bool) error {
	fileList, err := ioutil.ReadDir(dir)
	if err != nil {
		logger.Errorf("openDir failed: ", dir)
		return err
	}

	for _, file := range fileList {
		if filterFunc(file) {
			if err = os.Remove(dir + file.Name()); err != nil {
				logger.Errorf("remove file failed: ", file.Name())
				return err
			}
		}
	}
	return nil
}

var DeleteFile = func(allFileName string) error {
	if err := os.Remove(allFileName); err != nil {
		logger.Errorf("remove file failed.", allFileName)
		return err
	}
	return nil
}

var RemoveAll = func(dirName string) error {
	if err := os.RemoveAll(dirName); err != nil {
		return err
	}
	return nil
}

func CsvFileFilter(fileInfo os.FileInfo) bool {
	return strings.HasSuffix(fileInfo.Name(), ".csv")
}

func ZipFileFilter(fileInfo os.FileInfo) bool {
	return strings.HasSuffix(fileInfo.Name(), ".zip")
}

func GetCmd(cmd string) string {
	return cmd
}

func GetArgs(args ...string) []string {
	return append([]string{}, args...)
}

func IsFileExist(url string) bool {
	_, err := os.Stat(url)
	if err != nil {
		if os.IsExist(err) {
			return true
		}
		return false
	}
	return true
}

func PraseFileContent(filepath string, target interface{}) error {
	var absFilePath = util.GetFilePath(filepath)
	fileContent, err := util.ReadFile(filepath)
	if err != nil {
		return fmt.Errorf("Open "+absFilePath+" failed,err:", err)
	}
	logger.Info("load " + filepath + " file successfully.")
	if err = json.Unmarshal(fileContent, target); err != nil {
		return fmt.Errorf("Unmarshal fileContent err %s", fileContent)
	}
	return nil
}
