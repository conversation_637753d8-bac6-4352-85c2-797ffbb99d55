package propertiesparser

import (
	"bufio"
	"io"
	"os"
	"path"
	"strings"
)

func ParseConfig(filePath string) map[string]string {
	dbMap := make(map[string]string)

	f, err := os.Open(path.Clean(filePath))
	if err != nil {
		return dbMap
	}
	defer f.Close()

	r := bufio.NewReader(f)
	for {
		b, _, err := r.ReadLine()
		if err != nil {
			if err == io.EOF {
				break
			}
			return dbMap
		}

		s := strings.TrimSpace(string(b))
		index := strings.Index(s, "=")
		if index < 0 {
			continue
		}

		key := strings.TrimSpace(s[:index])
		if len(key) == 0 {
			continue
		}

		value := strings.TrimSpace(s[index+1:])
		if len(value) == 0 {
			continue
		}
		dbMap[key] = value
	}
	return dbMap
}
