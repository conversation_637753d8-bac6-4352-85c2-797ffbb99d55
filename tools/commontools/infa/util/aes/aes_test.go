package aes

import (
	"cwsm/tools/commontools/director/iks"
	"encoding/hex"
	"encoding/json"
	"errors"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:z1364r1054b3dc2148eb0a38b04740625765c691 */
var _ = Describe("GetKeyAfterDecode function", func() {
	var patcher *gomonkey.Patches

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("when PostRsaDecrypt succeeds", func() {
		It("should return commonIV and commonKey256", func() {
			ivResponse := iks.RsaDecryptDecode{Decrypt_data: []byte("iv data")}
			keyResponse := iks.RsaDecryptDecode{Decrypt_data: []byte("key data")}

			// Mock the responses directly as byte slices
			patcher.ApplyFunc(iks.PostRsaDecrypt, func(requestData *iks.RsaDecrypt) ([]byte, error) {
				if requestData.Data == "decodeIV" {
					return ivResponse.Decrypt_data, nil // Return the byte slice directly
				}
				return keyResponse.Decrypt_data, nil // Return the byte slice directly
			})

			commonIV, commonKey256, err := GetKeyAfterDecode()
			Expect(err).To(BeNil())
			Expect(commonIV).To(Equal([]byte("key data")))
			Expect(commonKey256).To(Equal([]byte("key data")))
		})
	})

	Context("when PostRsaDecrypt fails for IV", func() {
		It("should return an error", func() {
			patcher.ApplyFunc(iks.PostRsaDecrypt, func(requestData *iks.RsaDecrypt) ([]byte, error) {
				return nil, errors.New("IV request failed")
			})

			commonIV, commonKey256, err := GetKeyAfterDecode()
			Expect(err).ToNot(BeNil())
			Expect(err.Error()).To(ContainSubstring("IV request failed"))
			Expect(commonIV).To(BeNil())
			Expect(commonKey256).To(BeNil())
		})
	})

	Context("when PostRsaDecrypt fails for Key", func() {
		It("should return an error", func() {
			ivResponse := iks.RsaDecryptDecode{Decrypt_data: []byte("iv data")}
			ivRespData, _ := json.Marshal(ivResponse)

			patcher.ApplyFunc(iks.PostRsaDecrypt, func(requestData *iks.RsaDecrypt) ([]byte, error) {
				if requestData.Data == "decodeIV" {
					return ivRespData, nil
				}
				return nil, errors.New("Key request failed")
			})

			commonIV, commonKey256, err := GetKeyAfterDecode()
			Expect(err).ToNot(BeNil())
			Expect(err.Error()).To(ContainSubstring("Key request failed"))
			Expect(commonIV).To(BeNil())
			Expect(commonKey256).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:z1364r1054b3dc2148eb0a38b04740625765c691 */

/* Started by AICoder, pid:sddb3ib505q33c9147c909b73001b08bad6167be */
var _ = Describe("CBCDecrypt function", func() {
	var patcher *gomonkey.Patches

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("when decryption succeeds", func() {
		It("should return the decrypted string", func() {
			// Mock GetKeyAfterDecode to return a valid IV and key
			patcher.ApplyFunc(GetKeyAfterDecode, func() ([]byte, []byte, error) {
				return []byte("1234567890123456"), []byte("1234567890123456"), nil // 16 bytes key and IV
			})

			// Setup test data
			encrypted := ""                                       // Example encrypted data in hex
			key := hex.EncodeToString([]byte("1234567890123456")) // Valid key in hex

			// Call the CBCDecrypt function
			decrypted, err := CBCDecrypt(encrypted, key)
			Expect(err).To(BeNil())
			//Expect(decrypted).ToNot(BeNil())
			Expect(decrypted).ToNot(Equal("expected plaintext")) // Replace with actual expected plaintext
		})
	})

	Context("when GetKeyAfterDecode fails", func() {
		It("should return an error", func() {
			// Mock GetKeyAfterDecode to return an error
			patcher.ApplyFunc(GetKeyAfterDecode, func() ([]byte, []byte, error) {
				return nil, nil, errors.New("GetKeyAfterDecode error")
			})

			encrypted := "6a0a3c5f7b8d6b6d1b6b3f029c6f3e5f" // Example encrypted data
			key := "key data"

			decrypted, err := CBCDecrypt(encrypted, key)
			Expect(err).ToNot(BeNil())
			Expect(err.Error()).To(ContainSubstring("GetKeyAfterDecode error"))
			Expect(decrypted).To(Equal(""))
		})
	})

	Context("when hex decoding fails", func() {
		It("should return an error for invalid encrypted data", func() {
			// Mock GetKeyAfterDecode to return a valid IV and key
			patcher.ApplyFunc(GetKeyAfterDecode, func() ([]byte, []byte, error) {
				return []byte("1234567890123456"), []byte("key data"), nil // Valid IV
			})

			encrypted := "invalid hex data" // Invalid hex data
			key := hex.EncodeToString([]byte("key data"))

			decrypted, err := CBCDecrypt(encrypted, key)
			Expect(err).ToNot(BeNil())
			Expect(err.Error()).To(ContainSubstring("CBCDecrypt decode password error"))
			Expect(decrypted).To(Equal(""))
		})
	})

	Context("when AES cipher creation fails", func() {
		It("should return an error for invalid key", func() {
			// Mock GetKeyAfterDecode to return valid IV but invalid key
			patcher.ApplyFunc(GetKeyAfterDecode, func() ([]byte, []byte, error) {
				return []byte("1234567890123456"), []byte("12345678"), nil // 8 bytes key, invalid for AES
			})

			encrypted := "6a0a3c5f7b8d6b6d1b6b3f029c6f3e5f" // Example encrypted data
			key := hex.EncodeToString([]byte("12345678"))   // Example of an invalid key

			decrypted, err := CBCDecrypt(encrypted, key)
			Expect(err).ToNot(BeNil())
			Expect(err.Error()).To(ContainSubstring("CBCDecrypt:aes.NewCipher error"))
			Expect(decrypted).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:sddb3ib505q33c9147c909b73001b08bad6167be */
