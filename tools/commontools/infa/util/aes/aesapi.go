package aes

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"cwsm/tools/commontools/director/iks"
	"cwsm/tools/commontools/logger"
	"encoding/hex"
	"fmt"
	"strings"
)

var (
	decodeIV  = "wGZYYSW49XfK8FHY64ANH7whPFPYleglPxrEUXsybyI="
	decode256 = "bo8lSaZ4YK1KYEDv7SHcEfbhhkJeNxgSQxtnsvMfRfr8HCretP+VF39/WJimwyUl"
)

func AesDecryptWithHex(crypted string, key []byte, iv []byte) (resultValue string, resultErr error) {
	defer func() {
		if r := recover(); r != nil {
			resultValue = ""
			resultErr = fmt.Errorf("failed to decrypt: %v", r)
		}
	}()

	input, err := hex.DecodeString(crypted)
	if err != nil {
		return "", err
	}
	output, err := AesDecrypt(input, key, iv)
	if err != nil {
		return "", err
	}
	return string(output), nil
}

// 先16进制解密，再Aes解密
func AesDecryptHex(pass64 string, key, iv []byte) string {
	input, err := hex.DecodeString(pass64)
	if err != nil {
		logger.Errorf("hex.DecodeString error: ", err)
		return ""
	}
	output, err := AesDecrypt(input, []byte(key), iv)
	if err != nil {
		logger.Errorf("AesDecrypt error: ", err)
		return ""
	}
	return string(output)
}

func AesEncrypt(origData, key []byte, iv []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()
	origData = PKCS5Padding(origData, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, iv)
	crypted := make([]byte, len(origData))
	blockMode.CryptBlocks(crypted, origData)
	return crypted, nil
}

func AesDecrypt(crypted, key []byte, iv []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockMode := cipher.NewCBCDecrypter(block, iv)
	origData := make([]byte, len(crypted))
	blockMode.CryptBlocks(origData, crypted)
	origData = PKCS5UnPadding(origData)
	return origData, nil
}

func PKCS5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func PKCS5UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

func AesDecryptUnPadByZero(crypted, key []byte, iv []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockMode := cipher.NewCBCDecrypter(block, iv)
	origData := make([]byte, len(crypted))
	blockMode.CryptBlocks(origData, crypted)
	origData = PKCS5UnPaddingUnPadByZero(origData)
	return origData, nil
}

func PKCS5UnPaddingUnPadByZero(origData []byte) []byte {
	var bs []byte
	for _, b := range origData {
		if b != 0 {
			bs = append(bs, b)
		}
	}
	return bs
}

func GCMEncrypt(decrypted, secretKey, iv string) (string, error) {
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("GCMEncrypt panic: %v", r)
		}
	}()

	gcm, plaintext, nonce, err := gcmPreprocess(decrypted, secretKey, iv, "encrypt")
	if err != nil {
		logger.Errorf("GCMEncrypt gcmPreprocess failed: %v", err)
		return "", fmt.Errorf("gcm encryption failed: %v", err)
	}

	return hex.EncodeToString(gcm.Seal(nil, nonce, plaintext, nil)), nil
}

func GCMDecrypt(encrypted, secretKey, iv string) (string, error) {
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("GCMDecrypt panic: %v", r)
		}
	}()

	gcm, ciphertext, nonce, err := gcmPreprocess(encrypted, secretKey, iv, "decrypt")
	if err != nil {
		logger.Errorf("GCMDecrypt gcmPreprocess failed: %v", err)
		return "", fmt.Errorf("gcm decryption failed: %v", err)
	}
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		logger.Errorf("GCMDecrypt gcm.Open failed: %v", err)
		return "", fmt.Errorf("gcm.Open failed: %v", err)
	}
	return string(plaintext), nil
}

func gcmPreprocess(src, key, iv, op string) (gcm cipher.AEAD, text, nonce []byte, err error) {
	src, key, iv = strings.TrimSpace(src), strings.TrimSpace(key), strings.TrimSpace(iv)
	var secretKey []byte
	if strings.ToLower(op) == "encrypt" {
		text = []byte(src)
	} else {
		if text, err = hex.DecodeString(src); err != nil {
			logger.Errorf("gcmPreprocess decode src text failed: %v", err)
			return
		}
	}
	if secretKey, err = hex.DecodeString(key); err != nil {
		logger.Errorf("gcmPreprocess decode secret key failed: %v", err)
		return
	}
	if nonce, err = hex.DecodeString(iv); err != nil {
		logger.Errorf("gcmPreprocess decode nonce failed: %v", err)
		return
	}

	var aesCipher cipher.Block
	aesCipher, err = aes.NewCipher(secretKey)
	if err != nil {
		logger.Errorf("gcmPreprocess NewCipher failed: %v", err)
		return
	}
	switch {
	case len(nonce) == 12:
		gcm, err = cipher.NewGCM(aesCipher)
		if err != nil {
			logger.Errorf("gcmPreprocess NewGCM failed: %v", err)
			return
		}
	default:
		gcm, err = cipher.NewGCMWithNonceSize(aesCipher, len(nonce))
		if err != nil {
			logger.Errorf("gcmPreprocess NewGCMWithNonceSize failed: %v", err)
			return
		}
	}
	return
}

/* Started by AICoder, pid:339few3e3el048d140a9080670e9324b1094b3f0 */
func CBCDecrypt(encrypted, key string) (decrypted string, err error) {
	defer func() {
		if r := recover(); r != nil {
			decrypted = ""
			err = fmt.Errorf("GCMDecrypt exception: %v", r)
		}
	}()

	encrypted, key = strings.TrimSpace(encrypted), strings.TrimSpace(key)
	if len(encrypted) == 0 {
		return encrypted, nil
	}
	var ciphertext, decryptKey []byte
	if ciphertext, err = hex.DecodeString(encrypted); err != nil {
		logger.Errorf("CBCDecrypt decode password error: %v", err)
		return "", fmt.Errorf("CBCDecrypt decode password error: %v", err)
	}

	commonIV, commonKey256, err := GetKeyAfterDecode()
	if err != nil {
		return "", fmt.Errorf("GetKeyAfterDecode error: %v", err)
	}

	if decryptKey, err = hex.DecodeString(key); err != nil {
		logger.Errorf("CBCDecrypt decode key error: %v", err)
		return "", fmt.Errorf("CBCDecrypt decode key error: %v", err)
	}
	if len(decryptKey) == 0 {
		decryptKey = commonKey256
	}
	c, err := aes.NewCipher(decryptKey)
	if err != nil {
		logger.Errorf("CBCDecrypt:aes.NewCipher error: %v", err)
		return "", fmt.Errorf("CBCDecrypt:aes.NewCipher error: %v", err)
	}

	decrypter := cipher.NewCBCDecrypter(c, commonIV)
	plaintext := make([]byte, len(ciphertext))
	decrypter.CryptBlocks(plaintext, ciphertext)
	plaintext = PKCS5UnPadding(plaintext)
	return string(plaintext), nil
}

/* Ended by AICoder, pid:339few3e3el048d140a9080670e9324b1094b3f0 */

/* Started by AICoder, pid:la9ffud8d5uc8d61438a0886e0aa5315ef93d703 */
func GetKeyAfterDecode() ([]byte, []byte, error) {
	requestDataIV := &iks.RsaDecrypt{Data: decodeIV}
	commonIV, err := iks.PostRsaDecrypt(requestDataIV)
	if err != nil {
		return nil, nil, err
	}
	requestDataKey := &iks.RsaDecrypt{Data: decode256}
	commonKey256, err := iks.PostRsaDecrypt(requestDataKey)
	if err != nil {
		return nil, nil, err
	}
	return commonIV, commonKey256, nil
}

/* Ended by AICoder, pid:la9ffud8d5uc8d61438a0886e0aa5315ef93d703 */
