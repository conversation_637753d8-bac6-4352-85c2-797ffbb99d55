package task

import (
	"errors"
	"fmt"
	"time"
)

// 超时错误
var ErrTimeout = errors.New("received timeout")

// 操作系统系统中断错误
var ErrInterrupt = errors.New("received interrupt")

var AllRunnerAsync = make(map[string]*RunnerAsync)

const (
	SyncInit    = "init"
	SyncDoing   = "doing"
	SyncFinish  = "finished"
	SyncFailed  = "failed"
	SyncTimeout = "timeout"
	SyncStop    = "stop"
)

const (
	StepInit      = "init"
	StepExecuting = "executing"
	StepFinish    = "finished"
	StepFailed    = "failed"
)

const (
	AsyncInit        = "init"
	AsyncDoing       = "doing"
	AsyncFinish      = "finished"
	AsyncError       = "error"
	AsyncTimeout     = "timeout"
	AsyncInterrupted = "interrupted"
)

// 创建要执行的任务
func createTaskAsync() func(args ...interface{}) error {
	return func(args ...interface{}) error {
		fmt.Printf("正在执行%s个任务\n", args)
		//模拟任务执行,sleep
		time.Sleep(2 * time.Second)
		return nil
	}
}
