package task

import (
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz/storage/job_redis"
	"encoding/json"
	"fmt"
	"time"
)

// 同步执行任务
type RunnerSync struct {
	//操作系统的信号检测
	interrupt chan int8
	//记录执行完成的状态
	complete chan error
	//超时检测
	timeout <-chan time.Time

	job_redis.RunnerSyncDetail
}

// new一个RunnerSync对象
func NewRunnerSync(name string, d time.Duration) *RunnerSync {
	return &RunnerSync{
		interrupt: make(chan int8),
		complete:  make(chan error),
		timeout:   time.After(d),
		RunnerSyncDetail: job_redis.RunnerSyncDetail{
			Name:       name,
			StepSize:   0,
			Status:     SyncInit,
			Path:       []string{},
			SyncSteps:  []*job_redis.SyncStep{},
			CreateTime: time.Now(),
		},
	}
}

// 添加一个任务
func (this *RunnerSync) Add(taskName string, execution func(args ...interface{}) error,
	startMsg string, endMsg string, args ...interface{}) {
	step := &job_redis.SyncStep{
		Name:       taskName,
		CreateTime: time.Now(),
		Params:     args,
		Status:     StepInit,
		StartMsg:   startMsg,
		EndMsg:     endMsg,
		Execution:  execution,
	}
	this.SyncSteps = append(this.SyncSteps, step)
	this.StepSize++
}

// 启动RunnerSync，监听错误信息
func (this *RunnerSync) Start() error {

	this.StartTime = time.Now()
	this.Status = SyncDoing
	//执行任务
	go func() {
		defer func() {
			if p := recover(); p != nil {
				logger.Errorf("RunnerSync name:%s panic: %v", this.Name, p)
				this.complete <- fmt.Errorf("RunnerSync name:%s panic: %v", this.Name, p)
			}
		}()
		err := this.Run()
		this.complete <- err
	}()

	select {
	//返回执行结果
	case err := <-this.complete:
		this.EndTime = time.Now()
		if err == nil {
			this.Progress = 100
			this.Status = SyncFinish
		} else if err == ErrInterrupt {
			this.Status = SyncStop
		} else {
			this.Status = SyncFailed
		}
		logger.Infof("RunnerSync task name:%s, resourceName:%s, status:%s, err:%v end",
			this.Name, this.ResourceName, this.Status, err)
		return err
		//超时返回
	case <-this.timeout:
		this.Status = SyncTimeout
		this.EndTime = time.Now()
		logger.Infof("RunnerSync task name:%s, resourceName:%s, status:%s time out",
			this.Name, this.ResourceName, this.Status)
		return ErrTimeout
	}
}

// 顺序执行所有的任务
func (this *RunnerSync) Run() error {
	for _, step := range this.SyncSteps {
		if this.gotInterrupt() {
			this.Status = SyncStop
			return ErrInterrupt
		}
		//执行任务
		this.Path = append(this.Path, step.StartMsg)
		step.Status = StepExecuting
		step.StartTime = time.Now()
		this.StopStep = step.Name
		err := step.Execution(step.Params...)
		step.EndTime = time.Now()
		if this.StepSize > 0 {
			this.Progress += 100 / this.StepSize
		}
		step.Errs = err
		if err != nil {
			step.Status = StepFailed
			return err
		}
		this.Path = append(this.Path, step.EndMsg)
		step.Status = StepFinish
	}
	return nil
}

// 判断是否接收到操作系统中断信号
func (this *RunnerSync) gotInterrupt() bool {
	select {
	case <-this.interrupt:
		return true
	default:
		return false
	}
}

func (this *RunnerSync) Stop() {
	this.interrupt <- 0
}

func (this *RunnerSync) printDetail() string {
	msg, _ := json.Marshal(this)
	return string(msg)
}
