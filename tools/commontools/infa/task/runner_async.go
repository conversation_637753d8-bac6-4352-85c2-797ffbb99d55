package task

import (
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz/storage/job_redis"
	"encoding/json"
	"fmt"
	"reflect"
	"sync"
	"time"
)

const (
	ImagePublishJobName = "imagePublishAsync"
	ImageAsyncJobName   = "imageAsync"
	FreezerAsyncJobName = "freezerJobAsync"
)

type RunnerAsync struct {
	//记录执行完成的状态
	complete chan error
	//超时检测
	timeout <-chan time.Time
	//等待所有任务完成的pv组
	waitGroup sync.WaitGroup
	//内部锁，防止并发写map问题
	lock sync.Mutex
	// maintain the status of redis service;
	redisStatus bool

	RunnerAsyncDetail
}

type RunnerAsyncDetail struct {
	ResourceName string            `json:"resourceName"`
	Tasks        []*RunnerSync     `json:"tasks"`
	TaskErr      map[string]string `json:"taskErr"`
	Uuid         string            `json:"uuid"`
	Size         int               `json:"size"`
	TaskStepSize int               `json:"taskStepSize"`
	Status       string            `json:"status"`
	Name         string            `json:"name"`
	CreatedTime  time.Time         `json:"createdTime"`
	StartTime    time.Time         `json:"startTime"`
	Progress     int               `json:"progress"`
	EndTime      time.Time         `json:"endTime"`
}

/* Started by AICoder, pid:5a43e389b7e647eca59ddfdae33cc2a7 */
func (runnerAsync *RunnerAsync) toRunnerAsyncDetail4Redis() *job_redis.RunnerAsyncDetail4Redis {
	rad := &job_redis.RunnerAsyncDetail4Redis{
		ResourceName: runnerAsync.ResourceName,
		TaskErr:      runnerAsync.TaskErr,
		Uuid:         runnerAsync.Uuid,
		Size:         runnerAsync.Size,
		TaskStepSize: runnerAsync.TaskStepSize,
		Status:       runnerAsync.Status,
		Name:         runnerAsync.Name,
		CreatedTime:  runnerAsync.CreatedTime,
		StartTime:    runnerAsync.StartTime,
		Progress:     runnerAsync.Progress,
		EndTime:      runnerAsync.EndTime,
	}

	// deep copy for the sync tasks;
	var tmpTasks []*job_redis.RunnerSyncDetail
	for _, rs := range runnerAsync.Tasks {
		tmpTask := convertRunnerSyncDetail(rs)
		tmpTasks = append(tmpTasks, tmpTask)
	}

	rad.Tasks = tmpTasks

	return rad
}

func convertRunnerSyncDetail(rs *RunnerSync) *job_redis.RunnerSyncDetail {
	tmpTask := &job_redis.RunnerSyncDetail{
		Name:         rs.Name,
		ResourceName: rs.ResourceName,
		StepSize:     rs.StepSize,
		Status:       rs.Status,
		Path:         rs.Path,
		StopStep:     rs.StopStep,
		CreateTime:   rs.CreateTime,
		StartTime:    rs.StartTime,
		EndTime:      rs.EndTime,
		Progress:     rs.Progress,
	}

	var tmpSss []*job_redis.SyncStep
	for _, sstep := range rs.SyncSteps {
		tmpSs := convertSyncStep(sstep)
		tmpSss = append(tmpSss, tmpSs)
	}
	tmpTask.SyncSteps = tmpSss

	return tmpTask
}

func convertSyncStep(sstep *job_redis.SyncStep) *job_redis.SyncStep {
	tmpSs := &job_redis.SyncStep{
		Execution:  sstep.Execution,
		Name:       sstep.Name,
		CreateTime: sstep.CreateTime,
		StartMsg:   sstep.StartMsg,
		EndMsg:     sstep.EndMsg,
		StartTime:  sstep.StartTime,
		EndTime:    sstep.EndTime,
		Status:     sstep.Status,
	}

	if sstep.Errs != nil {
		tmpSs.ErrMsg = sstep.Errs.Error()
	}

	var tmpParams []interface{}
	for _, param := range sstep.Params {
		tmpParam := sanitizeParameter(param)
		tmpParams = append(tmpParams, tmpParam)
	}
	tmpSs.Params = tmpParams

	return tmpSs
}

func sanitizeParameter(param interface{}) interface{} {
	switch reflect.ValueOf(param).Kind() {
	case reflect.Chan, reflect.Func, reflect.Invalid:
		return "place_holder_for_channel"
	default:
		return param
	}
}

/* Ended by AICoder, pid:5a43e389b7e647eca59ddfdae33cc2a7 */

// new一个Runner对象
func NewRunnerAsync(name string, d time.Duration) *RunnerAsync {
	logger.Info("[NewRunnerAsync] Enter...")
	ra := &RunnerAsync{
		complete:    make(chan error),
		timeout:     time.After(d),
		waitGroup:   sync.WaitGroup{},
		lock:        sync.Mutex{},
		redisStatus: IsRedisAvailable(),
		RunnerAsyncDetail: RunnerAsyncDetail{
			Tasks:       []*RunnerSync{},
			TaskErr:     map[string]string{},
			Uuid:        util.UUID(),
			Size:        0,
			Status:      AsyncInit,
			Name:        name,
			CreatedTime: time.Now(),
		},
	}

	AllRunnerAsync[ra.Uuid] = ra

	if ra.redisStatus {
		err := SetAsyncJob(ra)
		if err != nil {
			logger.Error(err.Error())
		}
	}

	return ra
}

// 添加一个任务
func (runnerAsync *RunnerAsync) Add(sync *RunnerSync) {
	runnerAsync.Tasks = append(runnerAsync.Tasks, sync)
	runnerAsync.TaskStepSize += sync.StepSize
	runnerAsync.Size++
}

// 启动Runner，监听错误信息
func (runnerAsync *RunnerAsync) Start(waitResult bool) error {
	if runnerAsync.StartTime.IsZero() {
		runnerAsync.StartTime = time.Now()
	}

	runnerAsync.Status = AsyncDoing
	if runnerAsync.redisStatus {
		err := SetAsyncJob(runnerAsync)
		if err != nil {
			logger.Errorf(err.Error())
		}
	}
	//并发执行任务
	go func() {
		runnerAsync.complete <- runnerAsync.Run()
	}()
	if waitResult {
		return runnerAsync.waittingResult()
	}
	go func() {
		runnerAsync.waittingResult()
	}()
	return nil

}

func (runnerAsync *RunnerAsync) waittingResult() error {
	defer func() {
		if runnerAsync.redisStatus {
			runnerAsync.lock.Lock()
			err := SetAsyncJob(runnerAsync)
			if err != nil {
				logger.Errorf(err.Error())
			}
			runnerAsync.lock.Unlock()
		}
	}()
	select {
	//返回执行结果,正常结束返回nil
	case err := <-runnerAsync.complete:
		runnerAsync.EndTime = time.Now()
		if err == nil {
			if len(runnerAsync.TaskErr) > 0 {
				runnerAsync.Status = AsyncError
				logger.Info("job id:%s, name:%s, resourceName:%s error all end, cost:%s", runnerAsync.Uuid, runnerAsync.Name,
					runnerAsync.ResourceName, runnerAsync.EndTime.Sub(runnerAsync.StartTime))
				return fmt.Errorf(runnerAsync.PrintErrors())
			}
			runnerAsync.Progress = 100
			runnerAsync.Status = AsyncFinish
		}
		logger.Info("job id:%s, name:%s, resourceName:%s finished all end, cost:%s", runnerAsync.Uuid, runnerAsync.Name,
			runnerAsync.ResourceName, runnerAsync.EndTime.Sub(runnerAsync.StartTime))
		return err
	//超时返回
	case <-runnerAsync.timeout:
		runnerAsync.EndTime = time.Now()
		runnerAsync.Status = AsyncTimeout
		logger.Info("job id:%s, name:%s, resourceName:%s timeout all end, cost:%s", runnerAsync.Uuid, runnerAsync.Name,
			runnerAsync.ResourceName, runnerAsync.EndTime.Sub(runnerAsync.StartTime))
		return ErrTimeout
	}

}

// 异步执行所有的任务
func (runnerAsync *RunnerAsync) Run() error {
	for _, taskFuc := range runnerAsync.Tasks {
		//pv锁加1
		runnerAsync.waitGroup.Add(1)

		go func(t *RunnerSync) {
			defer func() {
				if p := recover(); p != nil {
					logger.Errorf("RunnerAsync job id:%s, job name:%s, job resourceName:%s"+
						"final task name:%s, resourceName:%s, status:%s panic",
						runnerAsync.Uuid, runnerAsync.Name, runnerAsync.ResourceName, t.Name, t.ResourceName, t.Status)
					runnerAsync.lock.Lock()
					runnerAsync.TaskErr[t.Name] = fmt.Sprintf("RunnerAsync name:%s taskFuc:%s panic: %v",
						runnerAsync.Name, t.Name, p)
					runnerAsync.lock.Unlock()
				}
				if runnerAsync.redisStatus {
					runnerAsync.lock.Lock()
					err := SetAsyncJob(runnerAsync)
					if err != nil {
						logger.Errorf(err.Error())
					}
					runnerAsync.lock.Unlock()
				}
			}()
			err := t.Start()
			if err != nil {
				runnerAsync.lock.Lock()
				runnerAsync.TaskErr[t.Name] = err.Error()
				runnerAsync.lock.Unlock()
			} else {
				if runnerAsync.TaskStepSize > 0 {
					runnerAsync.Progress += t.StepSize * 100 / runnerAsync.TaskStepSize
				}
			}
			//pv组减1
			logger.Infof("RunnerAsync job id:%s, name:%s, resourceName:%s, "+
				"task name:%s, resourceName:%s, status:%s end",
				runnerAsync.Uuid, runnerAsync.Name, runnerAsync.ResourceName, t.Name, t.ResourceName, t.Status)
			runnerAsync.waitGroup.Done()
		}(taskFuc)
	}
	//等待所有任务完成
	runnerAsync.waitGroup.Wait()
	return nil
}

func (runnerAsync *RunnerAsync) PrintErrors() string {
	x := ""
	for key, value := range runnerAsync.TaskErr {
		x += fmt.Sprintf("task name:%s, errorMsg:%s \n", key, value)
	}
	return x
}

func (runnerAsync *RunnerAsync) PrintStringErrors() []string {
	x := []string{}
	for key, value := range runnerAsync.TaskErr {
		x = append(x, fmt.Sprintf("task name:%s, errorMsg:%s \n", key, value))
	}
	return x
}

func (runnerAsync *RunnerAsync) GetStatus() string {
	return runnerAsync.Status
}

func (runnerAsync *RunnerAsync) PrintDetail() string {
	msg, _ := json.Marshal(runnerAsync)
	return string(msg)
}

func (runnerAsync *RunnerAsync) GetTasks() []*RunnerSync {
	return runnerAsync.Tasks
}
