package job

import (
	"cwsm/tools/commontools/logger"
)

type ResourceExecutor interface {
	DoOperation(logId string, request interface{}) (*GenericDoOperationRsp, error)
}

type MonitorInnerJobHelper interface {
	GetExpectedJobStates() []string
	QueryInnerJob(logId string, jobId string, serviceName string) (*GenericJobRsp, error)
}

type MonitorResourceHelper interface {
	GetExpectedResourceStates() []string
	QueryResource(logId string, id string) (*GenericResourceRsp, error)
}

type resourceJobHandler struct {
	resourceExecutor      ResourceExecutor
	monitorInnerJobHelper MonitorInnerJobHelper
	monitorResourceHelper MonitorResourceHelper
}

func newDefaultResourceJobHandler(executor ResourceExecutor) *resourceJobHandler {
	return &resourceJobHandler{
		resourceExecutor:      executor,
		monitorInnerJobHelper: &emptyMonitorInnerJobHelper{},
		monitorResourceHelper: &emptyMonitorResourceHelper{},
	}
}

type emptyMonitorInnerJobHelper struct{}

func (h *emptyMonitorInnerJobHelper) GetExpectedJobStates() []string {
	return []string{}
}
func (h *emptyMonitorInnerJobHelper) QueryInnerJob(logId string, jobId string, serviceName string) (*GenericJobRsp, error) {
	return nil, nil
}

type emptyMonitorResourceHelper struct{}

func (e *emptyMonitorResourceHelper) GetExpectedResourceStates() []string {
	return []string{}
}
func (e *emptyMonitorResourceHelper) QueryResource(logId string, id string) (*GenericResourceRsp, error) {
	return nil, nil
}

// PostHandler callback after sync job completion.
type PostHandler interface {
	OnFail(logId string, sharedContext *ResourceJobGlobalContext) error
	OnSuccess(logId string, sharedContext *ResourceJobGlobalContext) error
}

type emptyPostHandler struct{}

func (handler *emptyPostHandler) OnFail(logId string, sharedContext *ResourceJobGlobalContext) error {
	logger.Errorf("[Default OnFail] [logId %s] operate resource %s failed: %s", logId, sharedContext.Name, sharedContext.ErrMsg)
	sharedContext.SetIsSuccess(false).SetIsFinish(true)
	return nil
}

func (handler *emptyPostHandler) OnSuccess(logId string, sharedContext *ResourceJobGlobalContext) error {
	logger.Infof("[Default OnSuccess] [logId %s] operate resource %s successful", logId, sharedContext.Id)
	sharedContext.SetIsSuccess(true).SetIsFinish(true)
	return nil
}

type proxyPostHandler struct {
	userPostHandler PostHandler
}

func (proxy *proxyPostHandler) OnFail(logId string, sharedContext *ResourceJobGlobalContext) error {
	logger.Errorf("[Proxy OnFail] [logId %s] operate resource %s failed: %s", logId, sharedContext.Name, sharedContext.ErrMsg)
	sharedContext.SetIsSuccess(false).SetIsFinish(true)
	return proxy.userPostHandler.OnFail(logId, sharedContext)
}

func (proxy *proxyPostHandler) OnSuccess(logId string, sharedContext *ResourceJobGlobalContext) error {
	logger.Infof("[Proxy OnSuccess] [logId %s] operate resource %s successful", logId, sharedContext.Id)
	sharedContext.SetIsSuccess(true).SetIsFinish(true)
	return proxy.userPostHandler.OnSuccess(logId, sharedContext)
}

func newProxyPostHandler(userPostHandler PostHandler) *proxyPostHandler {
	return &proxyPostHandler{userPostHandler: userPostHandler}
}
