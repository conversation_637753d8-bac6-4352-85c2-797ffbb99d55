package job

type MonitorSnapshotHelper struct {
	Op string
}

func (helper *MonitorSnapshotHelper) GetExpectedResourceStates() []string {
	if helper.Op == "delete" {
		return []string{ResourceState_NotFound}
	}
	return []string{ResourceState_Available}
}

type MonitorVolumeHelper struct {
	Op string
}

func (helper *MonitorVolumeHelper) GetExpectedResourceStates() []string {
	if helper.Op == "delete" {
		return []string{ResourceState_NotFound}
	}
	return []string{ResourceState_Available}
}

type MonitorVmHelper struct {
	Op string
}

func (helper *MonitorVmHelper) GetExpectedResourceStates() []string {
	if helper.Op == "delete" {
		return []string{ResourceState_NotFound}
	}
	return []string{ResourceState_Available}
}
