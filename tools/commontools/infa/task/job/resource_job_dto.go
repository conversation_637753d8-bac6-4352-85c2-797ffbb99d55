package job

import (
	"time"
)

type GenericDoOperationRsp struct {
	InnerJobId  string `json:"innerJobId"`
	ResourceId  string `json:"resourceId"`
	ServiceName string `json:"serviceName"`
}
type GenericJobRsp struct {
	JobId      string   `json:"jobId"`
	JobState   string   `json:"jobState"`
	ErrorMsg   string   `json:"errorMessage"`
	Success    []string `json:"success"`
	Failed     []string `json:"failed"`
	InstanceId string   `json:"instanceId"`
}

type GenericResourceRsp struct {
	ResourceId    string `json:"resourceId"`
	ResourceName  string `json:"resourceName"`
	ResourceState string `json:"resourceState"`
}

const (
	JobState_Timeout        = "timeout"
	JobState_Finished       = "finished"
	JobState_Error          = "error"
	ResourceState_Available = "available"
	ResourceState_Error     = "error"
	ResourceState_NotFound  = "not_found"
	EmptyPlaceHolder        = ""
)

var (
	defaultPollingInterval = 6 * time.Second
	defaultPollingTimes    = 300
)

type Object struct {
	Id   string `json:"objectId,omitempty"`
	Name string `json:"objectName,omitempty"`
}

type ResourceJobGlobalContext struct {
	*Object
	ResourceExecuteRequest interface{}
	PollingInterval        time.Duration
	PollingTimes           int

	DoOperationRsp     *GenericDoOperationRsp
	InnerJobRsp        *GenericJobRsp
	GenericResourceRsp *GenericResourceRsp
	ErrMsg             string
	IsSuccess          bool
	IsFinish           bool
}

func (r *ResourceJobGlobalContext) SetId(id string) *ResourceJobGlobalContext {
	if len(r.Id) == 0 {
		r.Id = id
	}
	return r
}
func (r *ResourceJobGlobalContext) SetName(name string) *ResourceJobGlobalContext {
	if len(r.Name) == 0 {
		r.Name = name
	}
	return r
}
func (r *ResourceJobGlobalContext) SetPollingInterval(interval time.Duration) *ResourceJobGlobalContext {
	r.PollingInterval = interval
	return r
}
func (r *ResourceJobGlobalContext) SetPollingTimes(times int) *ResourceJobGlobalContext {
	r.PollingTimes = times
	return r
}
func (r *ResourceJobGlobalContext) SetDoOperationRsp(rsp *GenericDoOperationRsp) *ResourceJobGlobalContext {
	r.DoOperationRsp = rsp
	return r
}
func (r *ResourceJobGlobalContext) SetInnerJobRsp(rsp *GenericJobRsp) *ResourceJobGlobalContext {
	r.InnerJobRsp = rsp
	return r
}
func (r *ResourceJobGlobalContext) SetGenericResourceRsp(rsp *GenericResourceRsp) *ResourceJobGlobalContext {
	r.GenericResourceRsp = rsp
	return r
}
func (r *ResourceJobGlobalContext) SetErrMsg(errMsg string) *ResourceJobGlobalContext {
	r.ErrMsg = errMsg
	return r
}
func (r *ResourceJobGlobalContext) SetIsSuccess(flag bool) *ResourceJobGlobalContext {
	r.IsSuccess = flag
	return r
}
func (r *ResourceJobGlobalContext) SetIsFinish(flag bool) *ResourceJobGlobalContext {
	r.IsFinish = flag
	return r
}

func NewResourceJobGlobalContext(obj *Object, request interface{}) *ResourceJobGlobalContext {
	return &ResourceJobGlobalContext{Object: obj, ResourceExecuteRequest: request, PollingInterval: defaultPollingInterval, PollingTimes: defaultPollingTimes}
}
