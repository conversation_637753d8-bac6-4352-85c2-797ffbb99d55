package job

import (
	"cwsm/tools/commontools/infa/task"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"fmt"
	"time"
)

var UT = false

type ResourceSyncJob struct {
	PostHandler

	SharedContext *ResourceJobGlobalContext
	jobHandler    *resourceJobHandler
}

// NewResourceSyncJob one value of *resourceSyncJob match one goroutine.
// to ensure concurrency security, the value of *resourceSyncJob should not be shared in a concurrent environment.
func NewResourceSyncJob(obj *Object, request interface{}, executor ResourceExecutor) *ResourceSyncJob {
	return &ResourceSyncJob{
		PostHandler:   &emptyPostHandler{},
		SharedContext: NewResourceJobGlobalContext(obj, request),
		jobHandler:    newDefaultResourceJobHandler(executor),
	}
}

func (sync *ResourceSyncJob) EditPollingConfig(pollingInterval time.Duration, pollingTimes int) *ResourceSyncJob {
	sync.SharedContext.SetPollingInterval(pollingInterval).SetPollingTimes(pollingTimes)
	return sync
}

func (sync *ResourceSyncJob) RegisterMonitorJob(jobHelper MonitorInnerJobHelper) *ResourceSyncJob {
	sync.jobHandler.monitorInnerJobHelper = jobHelper
	return sync
}

func (sync *ResourceSyncJob) RegisterMonitorResource(resourceHelper MonitorResourceHelper) *ResourceSyncJob {
	sync.jobHandler.monitorResourceHelper = resourceHelper
	return sync
}

func (sync *ResourceSyncJob) RegisterPostHandler(userHandler PostHandler) *ResourceSyncJob {
	sync.PostHandler = newProxyPostHandler(userHandler)
	return sync
}

func (sync *ResourceSyncJob) buildSyncJob(logId string, taskName string) *task.RunnerSync {
	syncJobPrefix := fmt.Sprintf("%s_%s", taskName, sync.getResourceIdentification())
	syncJob := task.NewRunnerSync(fmt.Sprintf("%s_SyncJob", syncJobPrefix), sync.computeSyncJobTimeout())

	syncJob.Add(fmt.Sprintf("%s_DoOperation", syncJobPrefix), buildDoOperationFn(logId, sync),
		fmt.Sprintf("%s_DoOperation_started", syncJobPrefix), fmt.Sprintf("%s_DoOperation_ended", syncJobPrefix), sync.SharedContext, sync.SharedContext.Object)

	syncJob.Add(fmt.Sprintf("%s_MonitorInnerJob", syncJobPrefix), buildMonitorInnerJobFn(logId, sync),
		fmt.Sprintf("%s_MonitorInnerJob_started", syncJobPrefix), fmt.Sprintf("%s_MonitorInnerJob_ended", syncJobPrefix), sync.SharedContext, sync.SharedContext.Object)

	syncJob.Add(fmt.Sprintf("%s_MonitorResource", syncJobPrefix), buildMonitorResourceFn(logId, sync),
		fmt.Sprintf("%s_MonitorResource_started", syncJobPrefix), fmt.Sprintf("%s_MonitorResource_ended", syncJobPrefix), sync.SharedContext, sync.SharedContext.Object)

	syncJob.Add(fmt.Sprintf("%s_SuccessNotify", syncJobPrefix), func(args ...interface{}) error {
		if err := sync.OnSuccess(logId, sync.SharedContext); err != nil {
			logger.Errorf("[SuccessNotify] [logId %s] notify failed: %s", logId, err.Error())
		}
		return nil
	}, fmt.Sprintf("%s_SuccessNotify_started", syncJobPrefix), fmt.Sprintf("%s_SuccessNotify_ended", syncJobPrefix))
	return syncJob
}

func (sync *ResourceSyncJob) computeSyncJobTimeout() time.Duration {
	var interval float64 = 1
	if seconds := sync.SharedContext.PollingInterval.Seconds(); seconds > 1 {
		interval = seconds
	}
	long := (int(interval) * sync.SharedContext.PollingTimes) * 2
	return time.Duration(long) * time.Second
}

func (sync *ResourceSyncJob) getResourceIdentification() string {
	id := sync.SharedContext.Id
	name := sync.SharedContext.Name
	if len(id) == 0 {
		id = "-"
	}
	if len(name) == 0 {
		name = "-"
	}
	return fmt.Sprintf("%s(%s)", name, id)
}

func buildDoOperationFn(logId string, r *ResourceSyncJob) func(arg ...interface{}) error {
	return func(arg ...interface{}) error {
		param := arg[0].(*ResourceJobGlobalContext)
		rsp, err := r.jobHandler.resourceExecutor.DoOperation(logId, param.ResourceExecuteRequest)
		if err != nil {
			r.SharedContext.SetErrMsg(err.Error())
			if err := r.OnFail(logId, r.SharedContext); err != nil {
				logger.Errorf("[FailNotify] [logId %s] notify failed: %s", err.Error())
			}
			return err
		}
		r.SharedContext.SetId(rsp.ResourceId).SetDoOperationRsp(rsp)
		return nil
	}
}

func buildMonitorInnerJobFn(logId string, r *ResourceSyncJob) func(arg ...interface{}) error {
	return func(arg ...interface{}) error {
		expectedJobStates := r.jobHandler.monitorInnerJobHelper.GetExpectedJobStates()
		if len(expectedJobStates) == 0 {
			logger.Warnf("[MonitorResource] [logId %s] expected inner job states is empty, skip monitor inner job state", logId)
			return nil
		}
		param := arg[0].(*ResourceJobGlobalContext)
		consecutiveFailCount := 0
		defer func() {
			if len(r.SharedContext.ErrMsg) > 0 {
				if err := r.OnFail(logId, r.SharedContext); err != nil {
					logger.Errorf("[FailNotify] [logId %s] notify failed: %s", err.Error())
				}
			}
		}()

		innerJobId := param.DoOperationRsp.InnerJobId
		if len(innerJobId) == 0 {
			return dealErr(r.SharedContext, fmt.Errorf("inner job id is empty"))
		}
		ticker := time.NewTicker(r.SharedContext.PollingInterval)
		defer ticker.Stop()
		for i := 0; i < r.SharedContext.PollingTimes; i++ {
			select {
			case <-ticker.C:
				jobInfo, err := r.jobHandler.monitorInnerJobHelper.QueryInnerJob(logId, innerJobId, param.DoOperationRsp.ServiceName)
				if err != nil {
					consecutiveFailCount++
					if consecutiveFailCount >= 3 {
						return dealErr(r.SharedContext, fmt.Errorf("get inner job %s failed: %s", innerJobId, err.Error()))
					} else {
						continue
					}
				}
				consecutiveFailCount = 0
				if jobInfo.JobState == JobState_Error {
					return dealErr(r.SharedContext, fmt.Errorf("inner job %s ended with error status: %s", innerJobId, jobInfo.ErrorMsg))
				}

				if util.IsContain4String(expectedJobStates, jobInfo.JobState) {
					logger.Infof("[MonitorInnerJob] [logId %s] hit job %s target status: %s", logId, innerJobId, jobInfo.JobState)
					r.SharedContext.SetId(jobInfo.InstanceId).SetInnerJobRsp(jobInfo)
					return nil
				}
			}
		}
		return dealErr(r.SharedContext, fmt.Errorf("monitor job %s ended with timeout", innerJobId))
	}
}

func buildMonitorResourceFn(logId string, r *ResourceSyncJob) func(arg ...interface{}) error {
	return func(arg ...interface{}) error {
		expectedResourceStates := r.jobHandler.monitorResourceHelper.GetExpectedResourceStates()
		if len(expectedResourceStates) == 0 {
			logger.Warnf("[MonitorResource] [logId %s] expected resource states is empty, skip monitor resource state", logId)
			return nil
		}
		param := arg[0].(*ResourceJobGlobalContext)
		consecutiveFailCount := 0
		defer func() {
			if len(r.SharedContext.ErrMsg) > 0 {
				if err := r.OnFail(logId, r.SharedContext); err != nil {
					logger.Errorf("[FailNotify] [logId %s] notify failed: %s", err.Error())
				}
			}
		}()

		resourceId := param.Object.Id
		if len(resourceId) == 0 {
			return dealErr(r.SharedContext, fmt.Errorf("resource id is empty"))
		}
		ticker := time.NewTicker(r.SharedContext.PollingInterval)
		defer ticker.Stop()
		for i := 0; i < r.SharedContext.PollingTimes; i++ {
			select {
			case <-ticker.C:
				resourceRsp, err := r.jobHandler.monitorResourceHelper.QueryResource(logId, resourceId)
				if err != nil {
					consecutiveFailCount++
					if consecutiveFailCount >= 3 {
						return dealErr(r.SharedContext, fmt.Errorf("get resource %s failed: %s", resourceId, err.Error()))
					} else {
						continue
					}
				}
				consecutiveFailCount = 0
				if resourceRsp.ResourceState == ResourceState_Error {
					return dealErr(r.SharedContext, fmt.Errorf("resource %s ended with error status", resourceId))
				}
				if util.IsContain4String(expectedResourceStates, resourceRsp.ResourceState) {
					logger.Infof("[MonitorResource] [logId %s] hit resource %s target status: %s", logId, resourceId, resourceRsp.ResourceState)
					r.SharedContext.SetName(resourceRsp.ResourceName).SetGenericResourceRsp(resourceRsp)
					return nil
				}
			}
		}
		return dealErr(r.SharedContext, fmt.Errorf("monitor resource %s ended with timeout", resourceId))
	}
}

func dealErr(sharedContext *ResourceJobGlobalContext, err error) error {
	sharedContext.SetErrMsg(err.Error())
	return err
}

type ResourceAsyncJob struct {
	logId            string
	ResourceSyncJobs []*ResourceSyncJob
	TaskName         string
}

func NewResourceAsyncJob(taskName string) *ResourceAsyncJob {
	return &ResourceAsyncJob{logId: util.UUID(), TaskName: taskName}
}

func (async *ResourceAsyncJob) BuildRunnerAsync() *task.RunnerAsync {
	runnerAsync := task.NewRunnerAsync(async.TaskName, async.computeAsyncJobTimeout())
	for _, job := range async.ResourceSyncJobs {
		runnerAsync.Add(job.buildSyncJob(async.logId, async.TaskName))
	}
	return runnerAsync
}

func (async *ResourceAsyncJob) Start() (jobId string) {
	runnerAsync := async.BuildRunnerAsync()
	if err := runnerAsync.Start(false); err != nil {
		logger.Errorf("[RunnerAsyncStart] [logId %s] start failed: %s", async.logId, err.Error())
	}
	return runnerAsync.Uuid
}

func (async *ResourceAsyncJob) computeAsyncJobTimeout() time.Duration {
	headJob := async.ResourceSyncJobs[0]
	return 2 * headJob.computeSyncJobTimeout()
}

func (async *ResourceAsyncJob) CollectFinalSuccessResources() []*Object {
	objs := make([]*Object, 0, len(async.ResourceSyncJobs))
	for _, job := range async.ResourceSyncJobs {
		if job.SharedContext.IsSuccess {
			objs = append(objs, job.SharedContext.Object)
		}
	}
	return objs
}

func (async *ResourceAsyncJob) CheckFinished() bool {
	for _, job := range async.ResourceSyncJobs {
		if !job.SharedContext.IsFinish {
			return false
		}
	}
	return true
}

func (async *ResourceAsyncJob) WaitFinished() error {
	times := (async.computeAsyncJobTimeout().Seconds() / defaultPollingInterval.Seconds()) + 1
	result := util.WaitForTimes(int(times), int(defaultPollingInterval.Seconds()), async.CheckFinished)
	if !result {
		return fmt.Errorf("job timeout")
	}
	return nil
}

func (async *ResourceAsyncJob) Add(job *ResourceSyncJob) *ResourceAsyncJob {
	if UT {
		job.EditPollingConfig(time.Millisecond, 3)
	}
	async.ResourceSyncJobs = append(async.ResourceSyncJobs, job)
	return async
}
