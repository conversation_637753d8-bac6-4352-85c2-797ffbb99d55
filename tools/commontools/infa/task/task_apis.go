package task

import (
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz/storage/job_redis"
	"fmt"
)

func GetAsyncJob(id, jobName string) (*job_redis.RunnerAsyncDetail4Redis, error) {
	if !job_redis.IsAvailable() {
		rad4Redis := AllRunnerAsync[id].toRunnerAsyncDetail4Redis()
		if len(rad4Redis.Uuid) == 0 {
			return &job_redis.RunnerAsyncDetail4Redis{}, fmt.Errorf("[GetAsyncJob] the job (id: %s) does not exist", id)
		}
		return rad4Redis, nil
	}

	if len(jobName) == 0 {
		return job_redis.GetAsyncJobById(id)
	}

	_, rad4Redis, err := job_redis.GetAsyncJobByIdAndName(id, jobName)
	if err != nil {
		return rad4Redis, fmt.Errorf("[<PERSON><PERSON><PERSON><PERSON><PERSON>] get async job by id: %s, and name: %s, failed: %v", id, jobName, err.Error())
	}

	return rad4Redis, nil
}

func GetAsyncJobListByJobName(name string) ([]*job_redis.RunnerAsyncDetail4Redis, error) {
	if !job_redis.IsAvailable() {
		jobs := []*job_redis.RunnerAsyncDetail4Redis{}
		for _, job := range AllRunnerAsync {
			if job.Name == name {
				jobs = append(jobs, job.toRunnerAsyncDetail4Redis())
			}
		}
		logger.Info("[GetAsyncJobListByJobName] jobs: %s", util.ToJSONStr(jobs))
		return jobs, nil
	}

	return job_redis.GetAsyncJobListByName(name)
}

func GetAllAsyncJobsFromRedis() ([]*job_redis.RunnerAsyncDetail4Redis, error) {
	if !job_redis.IsAvailable() {
		return []*job_redis.RunnerAsyncDetail4Redis{}, fmt.Errorf("[GetAllAsyncJobsFromRedis] redis service is unavailable")
	}

	return job_redis.GetAllAsyncJobs()
}

func SetAsyncJob(ra *RunnerAsync) error {
	return job_redis.SetJobInfo(ra.Uuid, ra.toRunnerAsyncDetail4Redis())
}

func DeleteAsyncJob(id string) error {
	delete(AllRunnerAsync, id)

	if job_redis.IsAvailable() {
		success := job_redis.DeleteJobInfo(id)
		if success {
			return nil
		}
	}

	return fmt.Errorf("[DeleteAsyncJob] delete job (id: %s) from redis, failed", id)
}

func IsRedisAvailable() bool {
	return job_redis.IsAvailable()
}

func RecoverAsyncTasks() error {
	if AllRunnerAsync == nil {
		AllRunnerAsync = make(map[string]*RunnerAsync)
	}

	if !job_redis.IsAvailable() {
		return fmt.Errorf("[RecoverAsyncTasks] redis service is unavailable")
	}

	ra4RedisDetailCollection, err := GetAllAsyncJobsFromRedis()
	if err != nil {
		return fmt.Errorf("[RecoverAyncTasks] failed: %v", err.Error())
	}

	for _, ra4RedisDetail := range ra4RedisDetailCollection {
		if len(ra4RedisDetail.Status) == 0 || ra4RedisDetail.Status == AsyncDoing {
			ra4RedisDetail.Status = AsyncInterrupted
			err = job_redis.SetJobInfo(ra4RedisDetail.Uuid, ra4RedisDetail)
			if err != nil {
				logger.Error("[RecoverAsyncTasks] failed: %v", err.Error())
			}
		}
	}

	return err
}
