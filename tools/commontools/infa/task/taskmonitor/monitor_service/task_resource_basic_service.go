package monitor_service

import (
	"fmt"
	"time"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
)

type TaskInitializer interface {
	InitInternalJobSettings() *JobSettings
}

type TaskExecutor interface {
	DoOperation(input interface{}, args ...interface{}) (*JobResourcesCell, error)
}

type TaskExtraProcessor interface {
	PreProcess(resourceCell *ResourceInfoCell, args ...interface{}) error
	PostProcess(resourceCell *ResourceInfoCell, args ...interface{}) error
}

type JobMonitor interface {
	GetJobStateWithResourceInfo(jobId string) (*GenericJobRsp, *Object, error)
}

type LocateResource interface {
	ListResources() (interface{}, error)
	IsMatchedResource(resourceInfo, opReq4Compare interface{}) (*Object, error)
}

type ResourceMonitor interface {
	GetResource(resourceId string, args ...interface{}) (interface{}, error)
	ExtractCurrentResourceState(resourceInfo interface{}, args ...interface{}) (string, error)
}

func monitorJob(jobId string, targetStates []string, interval time.Duration, maxStrokes int,
	executor func(args ...interface{}) (*GenericJobRsp, *Object, error)) (*Object, error) {
	expected := make(map[string]bool)
	for _, s := range targetStates {
		expected[s] = false
	}
	_, expectFinished := expected[JobState_Finished]

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for i := 0; i < maxStrokes; i++ {
		select {
		case _ = <-ticker.C:
			jobRsp, object, err := executor(jobId)
			if err != nil {
				logger.Errorf("[WaitForTargetJobStates] get job info, failed: %v", err.Error())
				continue
			}

			if jobRsp.JobState == JobState_Error {
				logger.Errorf("[WaitForTargetJobStates] job ended with error state")
				return &Object{}, fmt.Errorf("job %s ended with error state: %s", jobId, jobRsp.ErrorMsg)
			}

			if !expectFinished && jobRsp.JobState == JobState_Finished {
				logger.Infof("[WaitForTargetJobStates] job %s ended with no expected states: %s", jobId, targetStates)
				return &Object{}, fmt.Errorf("job %s ended with no expected states: %s", jobId, targetStates)
			}
			if _, ok := expected[jobRsp.JobState]; ok {
				logger.Infof("hit target job state: %s", jobRsp.JobState)
				return object, nil
			}
		}
	}
	totalSeconds := float64(maxStrokes) * interval.Seconds()
	logger.Errorf("[WaitForTargetJobStates] timeout after %1f seconds", totalSeconds)

	return &Object{}, fmt.Errorf("timeout after %1f seconds", totalSeconds)
}

func monitorResource(actor ResourceMonitor, resourceId string, targetStates []string, interval time.Duration, maxStrokes int,
	fn func(args ...interface{}) (interface{}, error), args ...interface{}) error {
	targetStatesMap := make(map[string]bool)
	for _, state := range targetStates {
		targetStatesMap[state] = false
	}
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for index := 0; index < maxStrokes; index++ {
		select {
		case _ = <-ticker.C:
			resourceRsp, err := fn(resourceId)
			_, ok := targetStatesMap[ResourceState_NotFound]
			if resourceRsp == nil && !ok {
				continue
			}
			if (resourceRsp == nil && ok) || (err != nil && ok) {
				logger.Infof("hit target resource state: %s", ResourceState_NotFound)
				return nil
			}

			currentState, err := actor.ExtractCurrentResourceState(resourceRsp, args)
			if err != nil {
				logger.Errorf("[Wait4TargetResourceStates] extract current resource state: %s, failed: %v", util.ToJSONStr(resourceRsp), err.Error())
			}

			_, ok = targetStatesMap[currentState]
			if !ok && currentState == ResourceState_Error {
				return fmt.Errorf("resource: %s ending up with error status", resourceId)
			}
			if ok {
				logger.Infof("hit target resource state: %s", currentState)
				return nil
			}
		}
	}

	return fmt.Errorf("operation timeout for resource: %s", resourceId)
}
