package monitor_service

import (
	"fmt"
	"strings"
	"time"
	"cwsm/tools/commontools/infa/task"
	"cwsm/tools/commontools/logger"
)

/* ===================================================================================================================
				The Unified AOP Framework for Resource Operations based on Async-Sync Jobs

Read me:

The files in this directory are designed for executing and monitoring the status of jobs or resources til they reach
desired ones or end up with error. The author would say that it is a great paradigm of AOP (Aspect Oriented Programming)
framework that will really facilitate our daily development job.
Here it is the unified portal of it.

It has four input parameters filled up by developers according to their demands:

	- the first one represents the exterior name of the whole task.

	- the second one in the form of time.Duration indicates the overall time span of the task.

	- the map at the third position is the most important parameter of this framework storing pairs of internal sub-task actor
and its corresponding information related to this sub-task and the resource be operated respectively. Each actor in the map
must implement the mandatory interface - TaskInitializer, which determines some basic settings for each sub-task, and any one
or several alternative interfaces exposed in different aspects other than the TaskInitializer. The internal structure of
the TaskResourceInfo object means in every sub-task module one actor can do one operation stroke to one piece or a bunch of resources.
During operating stage, actors will transform into different actors dynamically and automatically, which depicts the concept of Proxy
though it is static proxy. All the fields defined in the task and resource info struct are very intuitive so that any developer
can understand the meaning of them immediately after seeing them.

	- the last boolean parameter determines whether the framework should return the job id immediately after the commencement of sub-tasks or
wait til all of the sub-tasks have their results.


Regarding the major flow of each sub-task in this framework:

	- It comprises four major parts - sub-tasks implementation and tracking, resources determination and monitoring, with each aspect
being optionally included determined by whether its corresponding interface is implemented or not under specific conditions or scenarios.
Furthermore, they are arranged in a fixed but reasonable sequence - jobs first and resources last.

	- As for the do-operation stage, if you are going to both work on and track multiple resources like creating a kind of resource in batch,
you must figure out the relationship between each of the resource object in your TaskUnifiedRequest and the actual resource being operated
by implementing the methods in the interface LocateResource. if you are not, the operation request should be positioned at the ResourceUniqueRequest
in resource info since it relies on only one individual resource object.

	- As for the job monitoring part, the system will poll the job api and compare the current job state with the given target states; if you
have already got a job id in previous and just need to watch this job, then assign the id to the field TaskIndex.

	- As for the resource tracking part, the system will poll the resource api and compare the current state of the designated field with its target states;


User specification:

Step 1: Prepare your own APIs for the features defined in each group of interfaces like getting job and/or resource by id. In terms of the return parameters of these APIs,
it is recommended to include an error return;

Step 2: Define your own actor struct and let the actor struct as a receiver implementing the interfaces exposed in different aspects according
to your own needs.

Step 3: In using this architecture, just new() your own actors and construct an Actor-TaskResourceInfo map with them. Then call this function
with your own settings.

Summary:

Comparing with traditional manner of applying the async-sync job work initiated and compiled in any possible places but having similar work flow for
resource operation - tracking related tasks, you don't need to do much duplicated works since the major parts of the methods have already been constructed.

Note that If you want some special features for extension, the flexible design of this framework can also make this come true.
- In terms of extra data, there are two approaches available for you to deliver additional arguments into the system -
assigning additional args in the resource cell (refer the use of this manner to ServerMonitor) or defining extra fields in your own actor
(refer to EvsMonitor).

- Regarding extra flows, let your actor implement the methods declared in the interface TaskExtraProcessor (also take EvsMonitor as an example).

In closing, have fun in using this small but effective, flexible and convenient architecture to save your precious time and energy!!!

(in 12/2022, designed by 10318237, and will be implemented by you.)
=====================================================================================================================*/

func ExecuteAndMonitorTasks(taskName string, taskSpan time.Duration, taskActorResourceMap map[*TaskActor]*TaskResourceInfo, waitTilEnd bool) (string, error) {
	if len(taskActorResourceMap) == 0 {
		return EmptyPlaceHolder, fmt.Errorf("empty task content")
	}

	asyncJobsMonitor := task.NewRunnerAsync(taskName, taskSpan)

	var resourceList []string
	for taskActor, info := range taskActorResourceMap {

		actor := taskActor.Actor
		taskInfo := info.TaskInfo
		syncJobSettings := actor.InitInternalJobSettings()
		syncJobResourceMonitor := task.NewRunnerSync(fmt.Sprintf("%s_SyncJob", taskName), syncJobSettings.SyncJobDuration)
		syncJobResourceMonitor.ResourceName = strings.Join(resourceList, " | ")

		for _, resource := range info.ResourcesInfo {

			var jobSubTitle string
			if len(resource.ID) == 0 {
				resourceList = append(resourceList, resource.Name)
				jobSubTitle = fmt.Sprintf("%s_%s", taskName, resource.Name)
			} else {
				resourceList = append(resourceList, resource.ID)
				jobSubTitle = fmt.Sprintf("%s_%s", taskName, resource.ID)
			}

			var jobIdChan chan string
			var objectChan chan *Object

			operationReq := resource.ResourceUniqueRequest
			if operationReq == nil && taskInfo != nil {
				operationReq = info.TaskInfo.TaskUnifiedRequest
			}

			if _, ok := actor.(TaskExecutor); ok {
				taskDoFn := func(taskArgs ...interface{}) error {
					opRsp, err := actor.(TaskExecutor).DoOperation(operationReq, taskArgs)
					if opRsp != nil && len(opRsp.JobID) > 0 {
						jobIdChan = make(chan string, 1)
						jobIdChan <- opRsp.JobID
					}
					if opRsp != nil && opRsp.Object != nil && len(opRsp.Object.ID) > 0 {
						objectChan = make(chan *Object, 1)
						objectChan <- opRsp.Object
					}
					return err
				}
				syncJobResourceMonitor.ResourceName = resource.Name

				syncJobResourceMonitor.Add(fmt.Sprintf("%s_DoOperation", jobSubTitle), taskDoFn, fmt.Sprintf("do_%s_started", jobSubTitle),
					fmt.Sprintf("do_%s_finished", jobSubTitle), resource.AdditionalArg)
			}

			if _, ok := actor.(JobMonitor); ok && taskInfo != nil {
				jobMonitorFn := func(jobArgs ...interface{}) error {
					if jobIdChan != nil {
						defer close(jobIdChan)
						jobArgs[1] = <-jobIdChan
					}
					object, err := taskInfo.Wait4TargetJobStates(actor.(JobMonitor), jobArgs[1].(string), syncJobSettings)
					if object != nil && len(object.ID) > 0 && objectChan == nil {
						objectChan = make(chan *Object, 1)
						objectChan <- object
					}
					return err
				}
				syncJobResourceMonitor.ResourceName = resource.Name

				syncJobResourceMonitor.Add(fmt.Sprintf("%s_JobMonitor", jobSubTitle), jobMonitorFn, fmt.Sprintf("%s_monitor_job_started", jobSubTitle),
					fmt.Sprintf("%s_monitor_job_finished", jobSubTitle), resource.AdditionalArg, taskInfo.TaskIndex)
			}

			_, ok1 := actor.(LocateResource)
			_, ok2 := actor.(ResourceMonitor)
			if len(resource.ID) == 0 && !ok1 && ok2 {
				logger.Warnf("warning on using manner: APIs related to resource locating must be implemented" +
					"since resource IDs are prerequisite condition for following steps")
			}

			if ok1 {
				locateResourceFn := func(locateResArgs ...interface{}) error {
					obj, err := resource.LocateResource(actor.(LocateResource), operationReq, locateResArgs[1].(time.Duration), locateResArgs[2].(int))
					resource.ID = obj.ID
					if len(obj.Name) > 0 {
						resource.Name = obj.Name
					}
					return err
				}
				syncJobResourceMonitor.ResourceName = resource.Name

				syncJobResourceMonitor.Add(fmt.Sprintf("%s_LocateResource", jobSubTitle), locateResourceFn, fmt.Sprintf("%s_resource_locate_started", jobSubTitle),
					fmt.Sprintf("%s_resource_locate_ended", jobSubTitle), resource.AdditionalArg, syncJobSettings.Interval, syncJobSettings.MaxStrokes)
			}

			if ok2 {
				finalStateMonitorFn := func(resourceArgs ...interface{}) error {
					if objectChan != nil {
						defer close(objectChan)
						tmpObj := <-objectChan
						resource.ID = tmpObj.ID
						if len(tmpObj.Name) > 0 {
							resource.Name = tmpObj.Name
						}
					}
					return resource.Wait4TargetResourceStates(actor.(ResourceMonitor), resourceArgs[1].(time.Duration), resourceArgs[2].(int))
				}
				syncJobResourceMonitor.ResourceName = resource.Name

				syncJobResourceMonitor.Add(fmt.Sprintf("%s_ResourceMonitor", jobSubTitle), finalStateMonitorFn, fmt.Sprintf("%s_resource_state_monitor_started", jobSubTitle),
					fmt.Sprintf("%s_resource_state_monitor_ended", jobSubTitle), resource.AdditionalArg, syncJobSettings.Interval, syncJobSettings.MaxStrokes)
			}
		}

		asyncJobsMonitor.Add(syncJobResourceMonitor)
	}
	asyncJobsMonitor.ResourceName = strings.Join(resourceList, " | ")

	if err := asyncJobsMonitor.Start(waitTilEnd); err != nil {
		logger.Errorf("[MonitorActionJobs] start %s jobs, failed: %v", taskName, err.Error())
		return asyncJobsMonitor.Uuid, fmt.Errorf("%v", err.Error())
	}

	return asyncJobsMonitor.Uuid, nil
}
