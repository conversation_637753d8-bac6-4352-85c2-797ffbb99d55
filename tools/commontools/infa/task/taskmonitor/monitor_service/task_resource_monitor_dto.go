package monitor_service

import (
	"fmt"
	"reflect"
	"time"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
)

const (
	JobState_Timeout        = "timeout"
	JobState_Finished       = "finished"
	JobState_Error          = "error"
	ResourceState_Available = "available"
	ResourceState_Error     = "error"
	ResourceState_NotFound  = "not_found"
	EmptyPlaceHolder        = ""
)

type TaskActor struct {
	Actor TaskInitializer
}

type TaskInfoCell struct {
	TaskIndex          string
	TaskUnifiedRequest interface{}
	TargetJobStates    []string
}

type JobSettings struct {
	SyncJobDuration time.Duration
	Interval        time.Duration
	MaxStrokes      int
}

type TaskResourceInfo struct {
	TaskInfo      *TaskInfoCell
	ResourcesInfo []*ResourceInfoCell
}

type ResourceInfoCell struct {
	*Object
	ResourceUniqueRequest interface{}
	TargetResourceStates  []string
	AdditionalArg         interface{}
}

type Object struct {
	ID   string `json:"objectId,omitempty"`
	Name string `json:"objectName,omitempty"`
}

type JobResourcesCell struct {
	JobID  string
	Object *Object
}

type GenericJobRsp struct {
	JobID    string   `json:"jobId"`
	JobState string   `json:"jobState"`
	Process  int      `json:"process"`
	ErrorMsg string   `json:"errorMessage"`
	Success  []string `json:"success"`
	Failed   []string `json:"failed"`
}

func (m *TaskInfoCell) Wait4TargetJobStates(actor JobMonitor, jobId string, settings *JobSettings) (*Object, error) {
	if len(jobId) == 0 || !util.AreValidUUIDs(jobId) {
		return &Object{}, fmt.Errorf("invalid job id: %s", jobId)
	}
	fn := func(args ...interface{}) (*GenericJobRsp, *Object, error) {
		return actor.(JobMonitor).GetJobStateWithResourceInfo(args[0].(string))
	}
	return monitorJob(jobId, m.TargetJobStates, settings.Interval, settings.MaxStrokes, fn)
}

func (r *ResourceInfoCell) LocateResource(actor LocateResource, request interface{}, interval time.Duration, maxStrokes int, args ...interface{}) (*Object, error) {
	ticker := time.NewTicker(2 * interval)
	for i := 0; i < maxStrokes; i++ {
		select {
		case _ = <-ticker.C:
			resources, err := actor.ListResources()
			if err != nil {
				logger.Errorf("[LocateResource] get resource list, failed: %v", err.Error())
				continue
			}
			if resources == nil {
				logger.Errorf("[LocateResource] empty resource list")
				continue
			}
			resourceList := reflect.ValueOf(resources)
			switch resourceList.Kind() {
			case reflect.Slice, reflect.Array:
				length := resourceList.Len()
				for i := 0; i < length; i++ {
					tmp := resourceList.Index(i)
					if tmp.Kind() == reflect.Ptr {
						tmp = tmp.Elem()
					}
					obj, err := actor.IsMatchedResource(tmp.Interface(), request)
					if err != nil {
						logger.Errorf("[LocateResource] identify resources, failed: %v", err.Error())
						continue
					}
					if obj != nil {
						return obj, nil
					}
				}
			default:
				return nil, fmt.Errorf("inappropriate resource list type, it must be a slice or array")
			}
		}
	}

	return nil, fmt.Errorf("timeout for locating resource")
}

func (r *ResourceInfoCell) Wait4TargetResourceStates(actor ResourceMonitor, interval time.Duration, maxStrokes int, args ...interface{}) error {
	if len(r.ID) == 0 {
		return fmt.Errorf("ended due to the failure of locating resource for %s", r.Name)
	}

	if extraProcess, ok := actor.(TaskExtraProcessor); ok {
		err := extraProcess.PreProcess(r, args)
		if err != nil {
			logger.Errorf("[Wait4TargetResourceStates] pre-process for resource: %s, failed: %v", r.ID, err.Error())
			return fmt.Errorf("%v", err.Error())
		}
		actor = extraProcess.(ResourceMonitor)
	}

	fn := func(args ...interface{}) (interface{}, error) {
		return actor.GetResource(args[0].(string))
	}
	err := monitorResource(actor, r.ID, r.TargetResourceStates, interval, maxStrokes, fn, args)
	if err != nil {
		logger.Errorf("[Wait4TargetResourceStates] monitor resource: %s, error: %v", r.ID, err.Error())
		return fmt.Errorf("%v", err.Error())
	}

	if extraProcess, ok := actor.(TaskExtraProcessor); ok {
		err := extraProcess.PostProcess(r, args)
		if err != nil {
			logger.Errorf("[Wait4TargetResourceStates] post-process for resource: %s, failed: %v", r.ID, err.Error())
			return fmt.Errorf("%v", err.Error())
		}
	}

	return nil
}
