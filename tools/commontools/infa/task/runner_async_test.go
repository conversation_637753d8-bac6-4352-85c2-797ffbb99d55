package task

import (
	"cwsm/tools/commontools/infa/util"
	"fmt"
	"os"
	"runtime"
	"time"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("task", func() {
	Describe("asynctask", func() {
		It("async should ok", func() {
			runtime.GOMAXPROCS(runtime.NumCPU())
			ok := make(chan string)

			go func() {
			loop:
				select {
				case x := <-ok:
					fmt.Println("allTask end" + x)
					return
				default:
					for key, value := range AllRunnerAsync {
						fmt.Println(key + value.PrintDetail())
					}
					time.Sleep(1 * time.Second)
					goto loop
				}
			}()

			//创建runner对象，设置超时时间
			runner := NewRunnerAsync("testAsync", 18*time.Second)
			//添加运行的任务

			task1 := NewRunnerSync("sync1", 8*time.Second)
			task1.Add("sync1_1", createTaskAsync(), "sync1_1 start", "sync1_1 end 10%", "sync1_1110", "sync1_111")
			task1.Add("sync1_2", createTaskAsync(), "sync1_2 start", "sync1_2 end 100%", 222221, 222222)

			task2 := NewRunnerSync("sync2", 8*time.Second)
			task2.Add("sync2_1", createTaskAsync(), "sync2_111", "sync2_1 start", "sync2_1 end 10%")
			task2.Add("sync2_2", createTaskAsync(), "sync2_222", "sync2_2 start", "sync2_2 end 100%")

			runner.Add(task1)
			runner.Add(task2)

			fmt.Println("异步执行任务")
			//开始执行任务
			if err := runner.Start(false); err != nil {
				switch err {
				case ErrTimeout:
					fmt.Println("执行超时")
					os.Exit(1)
				case ErrInterrupt:
					fmt.Println("任务被中断")
					os.Exit(2)
				}
			}
			for key, value := range AllRunnerAsync {
				fmt.Println(key + value.PrintDetail())
			}
			util.WaitForTimes(60, 5, func() bool { return runner.Status == AsyncFinish })
			Expect(runner.Status).To(Equal(AsyncFinish))

		})
		// It("async should timeout", func() {
		// 	runtime.GOMAXPROCS(runtime.NumCPU())
		// 	ok := make(chan string)

		// 	go func() {
		// 	loop:
		// 		select {
		// 		case x := <-ok:
		// 			fmt.Println("allTask end" + x)
		// 			return
		// 		default:
		// 			for key, value := range AllRunnerAsync {
		// 				fmt.Println(key + value.PrintDetail())
		// 			}
		// 			time.Sleep(1 * time.Second)
		// 			goto loop
		// 		}
		// 	}()

		// 	//创建runner对象，设置超时时间
		// 	runner := NewRunnerAsync("testAsync", 1*time.Second)
		// 	//添加运行的任务

		// 	task1 := NewRunnerSync("sync1", 8*time.Second)
		// 	task1.Add("sync1_1", createTaskAsync(), "sync1_111", "sync1_1 start", "sync1_1 end 10%")
		// 	task1.Add("sync1_2", createTaskAsync(), "sync1_222", "sync1_2 start", "sync1_2 end 100%")

		// 	task2 := NewRunnerSync("sync2", 8*time.Second)
		// 	task2.Add("sync2_1", createTaskAsync(), "sync2_111", "sync2_1 start", "sync2_1 end 10%")
		// 	task2.Add("sync2_2", createTaskAsync(), "sync2_222", "sync2_2 start", "sync2_2 end 100%")

		// 	runner.Add(task1)
		// 	runner.Add(task2)

		// 	fmt.Println("异步执行任务")
		// 	//开始执行任务
		// 	if err := runner.Start(true); err != nil {
		// 		switch err {
		// 		case ErrTimeout:
		// 			fmt.Println("执行超时")
		// 		case ErrInterrupt:
		// 			fmt.Println("任务被中断")
		// 		}
		// 	}
		// 	for key, value := range AllRunnerAsync {
		// 		fmt.Println(key + value.PrintDetail())
		// 	}

		// 	Expect(runner.Status).To(Equal(AsyncTimeout))

		// })
	})
})
