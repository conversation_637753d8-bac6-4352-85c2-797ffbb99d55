package table_operator

import (
	"encoding/json"
	"errors"
	"reflect"
	"strings"
	"time"
	"cwsm/tools/commontools/db/dbutil"
	"cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
)

const BatchSize = 500

func And(old *dbcore.Condition, new *dbcore.Condition) *dbcore.Condition {
	if old == nil {
		return new
	}
	return old.And(new)
}

func Or(old *dbcore.Condition, new *dbcore.Condition) *dbcore.Condition {
	if old == nil {
		return new
	}
	return old.Or(new)

}

func ExecSQL(sqlDescrip string) error {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when exec sql: ", sqlDescrip)
		return errors.New("db client is nil")
	}

	if !client.Exec(sqlDescrip) {
		return errors.New("exec sql: " + sqlDescrip + " failed")
	}

	return nil
}

func TruncateTable(tableName string) error {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when truncate table: ", tableName)
		return errors.New("db client is nil")
	}

	if !client.Delete(tableName, nil) {
		return errors.New("truncate table failed")
	}

	return nil
}

func DropTable(tableName string) error {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when drop table: ", tableName)
		return errors.New("db client is nil")
	}

	if !client.DestroyTable(tableName) {
		return errors.New("drop table failed")
	}

	return nil
}

func DelRecords(tableName string, condition *dbcore.Condition) error {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when delete from table: ", tableName)
		return errors.New("db client is nil")
	}

	if !client.Delete(tableName, condition) {
		return errors.New("delete from table failed")
	}

	return nil
}

func QueryCount(tableName string, condition *dbcore.Condition) (int, error) {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when QueryCount from table: ", tableName)
		return -1, errors.New("db client is nil")
	}

	count := client.Count(tableName, condition)
	if count < 0 {
		logger.Errorf("db tableOperator is nil when Count from table: ", tableName)
		return -1, errors.New("db tableOperator is nil")
	}

	return count, nil
}

func QueryContentCount(tableName string, condition *dbcore.Condition) (int, error) {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when delete from table: ", tableName)
		return -1, errors.New("db client is nil")
	}

	count := client.ContentCount(tableName, condition)
	if count < 0 {
		logger.Errorf("db tableOperator is nil when Count from table: ", tableName)
		return -1, errors.New("db tableOperator is nil")
	}

	return count, nil
}

func QueryCertainCols(tableName string, cols []string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when query table: ", tableName)
		return nil, errors.New("db client is nil")
	}
	records, err := client.QueryCertainCols(tableName, cols, dbcore.QueryMode.ALL, condition)
	if err != nil {
		return nil, err
	}
	return records, nil
}

func QueryCertainContentInCols(tableName string, cols []string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when query table: ", tableName)
		return nil, errors.New("db client is nil")
	}

	records := client.QueryCertainContentInCols(tableName, cols, dbcore.QueryMode.ALL, condition)
	return records, nil
}

func QueryRecords(tableName string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when query table: ", tableName)
		return nil, errors.New("db client is nil")
	}

	records, err := client.Query(tableName, condition)
	if err != nil {
		return nil, err
	}
	return records, nil
}

func InnerJoinQuery(table1, table2 string, condition *dbcore.Condition)  ([]map[string]interface{}, error) {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when query table1: %s, table2", table1, table2)
		return nil, errors.New("db client is nil")
	}

	records, err := client.InnerJoinQuery(table1, table2, condition)
	if err != nil {
		return nil, err
	}
	return records, nil
}

func InnerJoinCount(table1, table2 string, condition *dbcore.Condition)  (int) {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when query table1: %s, table2", table1, table2)
		return -1
	}

	return client.InnerJoinCount(table1, table2, condition)
}

func UpdateTable(tableName string, cols map[string]interface{}, condition *dbcore.Condition) error {
	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("db client is nil when update table: ", tableName)
		return errors.New("db client is nil")
	}

	if !client.Update(tableName, cols, condition) {
		return errors.New("update table failed")
	}

	return nil
}

//
//func UpdateInstance(tableName string, uuid string, instance interface{}) error {
//	cols, vals, err := separateColsVals(instance)
//	if err != nil {
//		logger.Errorf("UpdateInstance failed, error:", err.Error())
//		return err
//	}
//
//	colsmap := map[string]interface{}{}
//	for i, col := range cols {
//		colsmap[col] = vals[i]
//	}
//
//	cond := dbcore.NewCondition("uuid", dbcore.Operator.EQ, uuid)
//	if err = UpdateTable(tableName, colsmap, cond); err != nil {
//		logger.Errorf("UpdateInstance failed, error:", err.Error())
//		return err
//	}
//	return nil
//}

//func InsertInstances(tableName string, instances ...interface{}) error {
//	var colNames []string
//	colIndex, rows := map[string]int{}, make([]*dbcore.Row, len(instances))
//	for irow, instance := range instances {
//		cols, vals, err := separateColsVals(instance)
//		if err != nil {
//			logger.Errorf("InsertInstances to table", tableName, "failed, error:", err.Error())
//			return err
//		}
//		if irow == 0 {
//			colNames = make([]string, len(cols))
//			copy(colNames, cols)
//			for j, col := range colNames {
//				if !syscache.GetIsDev() {
//					colNames[j] = strconv.Quote(col)
//				}
//				colIndex[col] = j
//			}
//		}
//		rowVals := make([]interface{}, len(colIndex))
//		for ic, col := range cols {
//			rowVals[colIndex[col]] = vals[ic]
//		}
//		rows[irow] = dbcore.NewRow(rowVals...)
//	}
//
//	client := dbutil.GetDbClient()
//	if client == nil {
//		logger.Errorf("InsertInstances db client is nil when insert into table", tableName)
//		return errors.New("db client is nil")
//	}
//	imax := len(rows) / BatchSize
//	success := true
//	for i := 0; i < imax; i++ {
//		if !client.Insert(tableName, colNames, rows[i*BatchSize:(i+1)*BatchSize]...) {
//			success = false
//		}
//	}
//	if !client.Insert(tableName, colNames, rows[imax*BatchSize:]...) {
//		success = false
//	}
//	if !success {
//		return errors.New("insert into table failed")
//	}
//
//	return nil
//}
//
//func FetchInstanceByID(tableName string, uuid string, instance interface{}) error {
//	var colID string
//	switch tableName {
//	case constants.PgTableNameApp:
//		colID = "app_ins_id"
//	case constants.PgTableNameToken:
//		colID = "containeruuid"
//	default:
//		colID = "uuid"
//	}
//
//	condition := dbcore.NewCondition(colID, dbcore.Operator.EQ, uuid)
//	records, err := QueryRecords(tableName, condition)
//	if err != nil {
//		return err
//	}
//	if len(records) != 1 {
//		logger.Errorf("FetchInstanceByID requested instance does not exist, uuid:", uuid, "table:", tableName)
//		return errors.New("Requested instance does not exist")
//	}
//	insType := reflect.TypeOf(instance)
//	if insType.Kind() != reflect.Ptr || insType.Elem().Kind() == reflect.Slice {
//		logger.Errorf("FetchInstanceByID input instance is not pointer of non-slice")
//		return errors.New("Input instance must be pointer of non-slice")
//	}
//
//	instances := []interface{}{instance}
//	if err = deserializeRecords(records, &instances); err != nil {
//		return err
//	}
//
//	return nil
//}

//	func separateColsVals(instance interface{}) ([]string, []interface{}, error) {
//		cols, vals := []string{}, []interface{}{}
//		insType, insVal := reflect.TypeOf(instance), reflect.ValueOf(instance)
//
//		if insType.Kind() == reflect.Ptr {
//			insType = insType.Elem()
//		}
//		if insVal.Kind() == reflect.Ptr {
//			insVal = insVal.Elem()
//		}
//		if insType.Kind() != reflect.Struct {
//			logger.Errorf("InsertInstance type of instance is", insType.Kind(), "not a struct")
//			return cols, vals, errors.New("type of instance is not a struct")
//		}
//
//		extractColsAndVals(insType, insVal, &cols, &vals)
//
//		return cols, vals, nil
//	}
func deserializeRecords(records []map[string]interface{}, instances interface{}) error {
	rawifyJSONBytes(records)
	bs, err := json.Marshal(records)
	if err != nil {
		logger.Errorf("FetchInstance json.Marshal failed, error:", err.Error())
		return err
	}
	if reflect.TypeOf(instances).Kind() != reflect.Ptr {
		logger.Errorf("FetchInstance input instance is not a pointer")
		return errors.New("Input instance must be a pointer")
	}

	err = json.Unmarshal(bs, instances)
	if err != nil {
		logger.Errorf("FetchInstance json.Unmarshal failed, error:", err.Error())
		return err
	}
	return nil
}

func extractColsAndVals(insType reflect.Type, insVal reflect.Value, cols *[]string, vals *[]interface{}) {
	for i := 0; i < insType.NumField(); i++ {
		if !insType.Field(i).Anonymous {
			jTags := strings.Split(insType.Field(i).Tag.Get("json"), ",")
			value := supportifyValue(insVal.Field(i).Interface())
			if util.Contains("omitempty", jTags) && util.IsEmpty(value) {
				continue
			}
			*cols = append(*cols, jTags[0])
			*vals = append(*vals, value)
		} else {
			insValI, insTypeI := reflect.TypeOf(insVal.Field(i).Interface()), reflect.ValueOf(insVal.Field(i).Interface())
			extractColsAndVals(insValI, insTypeI, cols, vals)
		}
	}
}

func supportifyValue(value interface{}) interface{} {
	switch value.(type) {
	case int64, float64, bool, []byte, string, time.Time, nil:
		return value
	default:
		bs, _ := json.Marshal(value)
		return (json.RawMessage)(bs)
	}
}

func rawifyJSONBytes(records []map[string]interface{}) {
	for _, record := range records {
		for key, value := range record {
			switch value.(type) {
			case []byte:
				bs, _ := value.([]byte)
				record[key] = (json.RawMessage)(bs)
			}
		}
	}
}
