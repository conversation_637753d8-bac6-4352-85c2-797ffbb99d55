package storagex

import (
	"cwsm/tools/commontools/db/dbutil"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/devconfig"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/pg/table_operator"
	"encoding/json"
	"errors"
	"reflect"
	"strconv"
	"strings"
	"time"
)

type StoragexIF interface {
	GetUUIDColName(tableName string) string
}

const batchSize = 500

func UpdateInstance(tableName string, uuid string, instance interface{}, si StoragexIF) error {
	colsmap := map[string]interface{}{}
	if reflect.TypeOf(instance).Kind() == reflect.Map {
		maps := instance.(map[string]interface{})
		for key, value := range maps {
			colsmap[key] = supportifyValue(value)
		}
	} else {
		cols, vals, err := separateColsVals(instance)
		if err != nil {
			logger.Errorf("UpdateInstance table:%s uuid:%s failed, error:%s", tableName, uuid, err.Error())
			return err
		}
		for i, col := range cols {
			colsmap[col] = vals[i]
		}
	}

	cond := dbcore.NewCondition(si.GetUUIDColName(tableName), dbcore.Operator.EQ, uuid)
	return table_operator.UpdateTable(tableName, colsmap, cond)
}

func FetchInstances(tableName string, condition *dbcore.Condition, instances interface{}) error {
	records, err := table_operator.QueryRecords(tableName, condition)
	if err != nil {
		logger.Errorf("FetchInstances:QueryRecords tableName:%s error:%s", tableName, err.Error())
		return err
	}

	return transformRecord2Instance(records, instances)
}

func FetchInstanceByID(tableName string, uuid string, instance interface{}, si StoragexIF) error {
	insType := reflect.TypeOf(instance)
	if insType.Kind() != reflect.Ptr || insType.Elem().Kind() == reflect.Slice {
		logger.Errorf("FetchInstanceByID input instance is not pointer of non-slice")
		return errors.New("input instance must be pointer of non-slice")
	}

	con := dbcore.NewCondition(si.GetUUIDColName(tableName), dbcore.Operator.EQ, uuid)
	records, err := table_operator.QueryRecords(tableName, con)
	if err != nil {
		logger.Errorf("FetchInstanceByID query db tableName:%s uuid:%s records error:%s",
			tableName, uuid, err.Error())
		return err
	}
	if len(records) != 1 {
		logger.Errorf("FetchInstanceByID requested instance does not exist, uuid:", uuid, "table:", tableName)
		return errors.New("requested instance does not exist")
	}
	insType, insValue := insType.Elem(), reflect.ValueOf(instance).Elem()
	if err = fillInstanceFields(insType, insValue, records[0]); err != nil {
		return err
	}
	return nil
}

func UpdateInsertInstance(tableName string, uuid string, instance interface{}, si StoragexIF) error {
	condition := dbcore.NewCondition(si.GetUUIDColName(tableName), dbcore.Operator.EQ, uuid)
	result, err := table_operator.QueryRecords(tableName, condition)
	if err != nil {
		logger.Errorf("UpdateInsertInstance QueryRecords failed, error:", err.Error())
		return errors.New("UpdateInsertInstance QueryRecords failed")
	}
	if len(result) > 1 {
		logger.Errorf("UpdateInsertInstance QueryRecords by uuid failed: records are not unique")
		return errors.New("record with requested uuid is not unique")
	} else if len(result) == 0 {
		return InsertInstances(tableName, instance)
	} else if len(result) == 1 {
		return UpdateInstance(tableName, uuid, instance, si)
	}
	return nil
}

func InsertInstances(tableName string, instances ...interface{}) error {
	var colNames []string
	colIndex, rows := map[string]int{}, make([]*dbcore.Row, len(instances))
	for irow, instance := range instances {
		cols, vals, err := separateColsVals(instance)
		if err != nil {
			logger.Errorf("InsertInstances to table", tableName, "failed, error:", err.Error())
			return err
		}
		if irow == 0 {
			colNames = make([]string, len(cols))
			copy(colNames, cols)
			for j, col := range colNames {
				if !devconfig.IsDev {
					colNames[j] = strconv.Quote(col)
				}
				colIndex[col] = j
			}
		}
		rowVals := make([]interface{}, len(colIndex))
		for ic, col := range cols {
			rowVals[colIndex[col]] = vals[ic]
		}
		rows[irow] = dbcore.NewRow(rowVals...)
	}

	client := dbutil.GetDbClient()
	if client == nil {
		logger.Errorf("InsertInstances db client is nil when insert into table", tableName)
		return errors.New("db client is nil")
	}
	imax := len(rows) / batchSize
	success := true
	for i := 0; i < imax; i++ {
		if !client.Insert(tableName, colNames, rows[i*batchSize:(i+1)*batchSize]...) {
			success = false
		}
	}
	if !client.Insert(tableName, colNames, rows[imax*batchSize:]...) {
		success = false
	}
	if !success {
		logger.Errorf("InsertInstances failed")
		return errors.New("insert into table failed")
	}

	return nil
}

func DelInstance(tableName string, columeKey string, columeValue ...string) error {
	condition := dbcore.NewCondition(columeKey, dbcore.Operator.IN, columeValue)
	err := table_operator.DelRecords(tableName, condition)
	if err != nil {
		logger.Errorf("DelInstance:storage.DelRecords tableName:%s, uuid:%s failed, error:%s",
			tableName, columeValue, err.Error())
		return errors.New("DelInstance DelRecords failed")
	}
	return nil
}

func QueryCountInstance(tableName string, condition *dbcore.Condition) int {
	count, err := table_operator.QueryCount(tableName, condition)
	if err != nil {
		logger.Errorf("QueryCountContentInstance:table_operator.QueryContentCount tableName:%s failed, error:%s",
			tableName, err.Error())
		return -1
	}
	return count
}

func QueryContentCountInstance(tableName string, condition *dbcore.Condition) int {
	count, err := table_operator.QueryContentCount(tableName, condition)
	if err != nil {
		logger.Errorf("QueryCountContentInstance:table_operator.QueryContentCount tableName:%s failed, error:%s",
			tableName, err.Error())
		return -1
	}
	return count
}

func QueryCertainContentColsInstances(tableName string, cols []string, condition *dbcore.Condition, instances interface{}) error {
	sType, values := reflect.TypeOf(instances), reflect.ValueOf(instances).Elem()
	if sType.Kind() != reflect.Ptr || sType.Elem().Kind() != reflect.Slice {
		logger.Errorf("FetchInstances input instances is not pointer of slice")
		return errors.New("input instance must be pointer of slice")
	}

	records, err := table_operator.QueryCertainContentInCols(tableName, cols, condition)
	if err != nil {
		logger.Errorf("QueryCertainColsInstance query db tableName:%s cols:%s records error:%s",
			tableName, cols, err.Error())
		return err
	}
	insType, eleType := sType.Elem().Elem(), sType.Elem().Elem()
	if insType.Kind() == reflect.Ptr {
		eleType = insType.Elem()
	}

	error := transformPgData4Instance(records, eleType, insType, values)
	if error != nil {
		logger.Errorf("QueryCertainColsInstance:transformPgData4Instance query db tableName:%s cols:%s records error:%s",
			tableName, cols, error.Error())
		return err
	}

	return nil
}

func InnerJoinQuery(table1, table2 string, condition *dbcore.Condition, instances interface{}) error {
	records, err := table_operator.InnerJoinQuery(table1, table2, condition)
	if err != nil {
		logger.Errorf("InnerJoinQuery:QueryRecords table1: %s, table2: %s, error:%s", table1, table2, err.Error())
		return err
	}

	return transformRecord2Instance(records, instances)
}

func InnerJoinCount(table1, table2 string, condition *dbcore.Condition) int {
	return table_operator.InnerJoinCount(table1, table2, condition)
}

func transformPgData4Instance(records []map[string]interface{}, eleType reflect.Type, insType reflect.Type, values reflect.Value) error {
	for _, record := range records {
		instance := reflect.New(eleType).Interface()
		iType, iValue := reflect.TypeOf(instance).Elem(), reflect.ValueOf(instance).Elem()
		if err := fillInstanceFields(iType, iValue, record); err != nil {
			logger.Errorf("transformPgData4Instance:fillInstanceFields error:", err.Error())
			return err
		}
		vIns := reflect.ValueOf(instance)
		if insType.Kind() != reflect.Ptr {
			vIns = vIns.Elem()
		}
		values.Set(reflect.Append(values, vIns))
	}
	return nil
}

func fillInstanceFields(insType reflect.Type, insValue reflect.Value, record map[string]interface{}) error {
	for i := 0; i < insType.NumField(); i++ {
		if !insType.Field(i).Anonymous {
			pgTag := strings.Split(insType.Field(i).Tag.Get("pgdb"), ",")[0]
			if val, ok := record[pgTag]; ok && !valueIsNull(val) {
				if err := setReflectValue(insValue.Field(i), val); err != nil {
					return err
				}
			}
		} else {
			if err := fillInstanceFields(insType.Field(i).Type, insValue.Field(i), record); err != nil {
				return err
			}
		}
	}
	return nil
}

func valueIsNull(value interface{}) bool {
	if value == nil {
		return true
	}
	valType := reflect.TypeOf(value)
	switch valType.String() {
	case "[]uint8":
		bytes, _ := value.([]uint8)
		if len(bytes) == 4 && string(bytes) == "null" {
			return true
		}
	case "string":
		s, _ := value.(string)
		if s == "null" {
			return true
		}
	}
	return false
}

/* Started by AICoder, pid:0f3d6653654a4416a6129230e1e8ecfd */
func setReflectValue(insFieldVal reflect.Value, data interface{}) error {
	if !insFieldVal.CanSet() {
		logger.Errorf("setReflectValue error: field value %s is not settable", insFieldVal.String())
		return errors.New("non settable value type")
	}

	dataTyp, dataVal := reflect.TypeOf(data), reflect.ValueOf(data)
	for dataVal.Kind() == reflect.Ptr {
		dataTyp, dataVal = dataTyp.Elem(), dataVal.Elem()
	}
	filedTyp := insFieldVal.Type()
	numPtr := 0
	for filedTyp.Kind() == reflect.Ptr {
		numPtr++
		filedTyp = filedTyp.Elem()
	}
	fieldVal := reflect.New(filedTyp)
	err := setValue(fieldVal, dataTyp, dataVal)
	if err != nil {
		return err
	}

	valToBeSet := fieldVal.Elem()
	for i := 0; i < numPtr; i++ {
		tmpVal := reflect.New(valToBeSet.Type())
		tmpVal.Elem().Set(valToBeSet)
		valToBeSet = tmpVal
	}

	insFieldVal.Set(valToBeSet)
	return nil
}

func setValue(fieldVal reflect.Value, dataTyp reflect.Type, dataVal reflect.Value) error {
	switch dataTyp.String() {
	case "[]uint8":
		vi := fieldVal.Interface()
		bs, _ := dataVal.Interface().([]byte)
		if err := json.Unmarshal(bs, vi); err != nil {
			logger.Errorf("fillInstanceFields:setReflectValue json.Unmarshal failed:", err.Error())
			logger.Warnf("error bytes:", bs)
			return err
		}
	case "time.Time":
		t, _ := dataVal.Interface().(time.Time)
		ts := t.Format(time.RFC3339)
		fieldVal.Elem().Set(reflect.ValueOf(ts))
	default:
		if dataTyp != fieldVal.Elem().Type() {
			if dataTyp.ConvertibleTo(fieldVal.Elem().Type()) {
				fieldVal.Elem().Set(dataVal.Convert(fieldVal.Elem().Type()))
			} else {
				logger.Errorf("Cannot convert type", dataTyp, "to type", fieldVal.Elem().Type())
				return errors.New("setReflectValue: Type conversion error")
			}
		} else {
			fieldVal.Elem().Set(dataVal)
		}
	}
	return nil
}

/* Ended by AICoder, pid:0f3d6653654a4416a6129230e1e8ecfd */

func separateColsVals(instance interface{}) ([]string, []interface{}, error) {
	var cols []string
	var vals []interface{}
	insType, insVal := reflect.TypeOf(instance), reflect.ValueOf(instance)

	if insType.Kind() == reflect.Ptr {
		insType = insType.Elem()
		insVal = insVal.Elem()
	}

	if insType.Kind() != reflect.Struct {
		logger.Errorf("type of instance is", insType.Kind(), "not a struct")
		return cols, vals, errors.New("type of instance is not a struct")
	}

	extractColsAndVals(insType, insVal, &cols, &vals)

	return cols, vals, nil
}

func extractColsAndVals(insType reflect.Type, insVal reflect.Value, cols *[]string, vals *[]interface{}) {
	for i := 0; i < insType.NumField(); i++ {
		if !insType.Field(i).Anonymous {
			pgTags := strings.Split(insType.Field(i).Tag.Get("pgdb"), ",")
			value := supportifyValue(insVal.Field(i).Interface())
			if util.Contains("omitempty", pgTags) && util.IsEmpty(value) {
				continue
			}
			*cols = append(*cols, pgTags[0])
			*vals = append(*vals, value)
		} else {
			insTypeI, insValI := reflect.TypeOf(insVal.Field(i).Interface()), reflect.ValueOf(insVal.Field(i).Interface())
			extractColsAndVals(insTypeI, insValI, cols, vals)
		}
	}
}

func supportifyValue(value interface{}) interface{} {
	switch value.(type) {
	case int64, float64, bool, []byte, string, time.Time, nil:
		return value
	default:
		bs, err := json.Marshal(value)
		if err != nil {
			logger.Errorf("supportifyValue: json.Marshal error:", err.Error())
			return []byte{}
		}
		return bs
	}
}

func transformRecord2Instance(records []map[string]interface{}, instances interface{}) error {
	sType, values := reflect.TypeOf(instances), reflect.ValueOf(instances).Elem()
	if sType.Kind() != reflect.Ptr || sType.Elem().Kind() != reflect.Slice {
		logger.Errorf("transformRecord2Instance input instances is not pointer of slice")
		return errors.New("input instance must be pointer of slice")
	}
	insType, eleType := sType.Elem().Elem(), sType.Elem().Elem()
	if insType.Kind() == reflect.Ptr {
		eleType = insType.Elem()
	}

	err := transformPgData4Instance(records, eleType, insType, values)
	if err != nil {
		logger.Errorf("transformRecord2Instance:transformPgData4Instance query db error:%s", err.Error())
		return err
	}
	return nil
}
