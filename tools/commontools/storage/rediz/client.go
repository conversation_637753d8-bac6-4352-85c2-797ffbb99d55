package rediz

import (
	"github.com/go-redis/redis"
	"cwsm/tools/commontools/devconfig"
	"cwsm/tools/commontools/logger"
	marsredis "zte.com.cn/dexcloud/go-mars/database/redis"
)

var client redis.Cmdable

func SetClient(service marsredis.RedisService, clientId string) {
	var err error
	client, err = service.GetClient(clientId)
	if err != nil {
		logger.Errorf("[SetRedisClient] get client error: %v", err)
		return
	}
	_ = Ping()
}

func CleanKeys(keysToBeCleaned ...string) {
	if client == nil {
		logger.Warnf("[CleanKeys] redis client is nil")
		return
	}
	if len(keysToBeCleaned) > 0 {
		_ = Del(keysToBeCleaned...)
	}
}

func UseMemClient() {
	devconfig.IsDev = true
	client = &memClient{}
	client.FlushAll()
}
