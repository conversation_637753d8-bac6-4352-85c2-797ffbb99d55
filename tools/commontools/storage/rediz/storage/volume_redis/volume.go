package volume_redis

import (
	"fmt"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Delete(envId string, volumeId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtVolume, envId), volumeId)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtVolume, envId))
}

func GetAll(envId string) ([]*VolumeRedisDao, error) {
	volumes := []*VolumeRedisDao{}
	key := fmt.Sprintf(storage.RedisKeyFmtVolume, envId)
	if rediz.ExistsAny(key) {
		err := rediz.HGetAllInstances(key, &volumes)
		if err != nil {
			logger.Errorf("volume_redis GetAll error %v", err)
			return nil, err
		}
	}
	return volumes, nil
}

func Set(envId string, volumeId string, dao *VolumeRedisDao) error {
	return rediz.HSet(fmt.Sprintf(storage.RedisKeyFmtVolume, envId), volumeId, dao)
}

func Get(envId, volumeId string) (*VolumeRedisDao, error) {
	vol := &VolumeRedisDao{}
	err := rediz.HGetInstance(fmt.Sprintf(storage.RedisKeyFmtVolume, envId), volumeId, vol)
	return vol, err
}
