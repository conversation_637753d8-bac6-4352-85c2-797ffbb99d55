package volume_redis

type VolumeRedisDao struct {
	Id                  string               `json:"id"`
	Name                string               `json:"name"`
	CloudEnvID          string               `json:"cloudEnvId"`
	Bootable            bool                 `json:"bootable"`
	VolumeImageMetadata *VolumeImageMetadata `json:"volume_image_metadata"`
	GroupId             string               `json:"group_id"`
}

type VolumeImageMetadata struct {
	ImageID string `json:"image_id"`
}
