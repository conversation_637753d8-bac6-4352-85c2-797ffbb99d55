package tenant_redis

import (
	"fmt"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
	"cwsm/tools/commontools/storage/rediz/storage/cloud_env_redis"
)

func Get(envId string, tId string) (*Tenant4Redis, error) {
	tenant := &Tenant4Redis{}
	err := rediz.HGetInstance(fmt.Sprintf(storage.RedisKeyFmtTenant, envId), tId, tenant)
	return tenant, err
}

func GetById(tId string) (*Tenant4Redis, error) {
	envs, err := cloud_env_redis.GetAll()
	if err != nil {
		return nil, err
	}
	for _, env := range envs {
		if tenant, err := Get(env.ID, tId); err == nil {
			return tenant, nil
		}
	}
	return nil, fmt.Errorf("failed to get tenant %s from all envs", tId)
}

func GetAll(envId string) ([]*Tenant4Redis, error) {
	tenants := []*Tenant4Redis{}
	err := rediz.HGetAllInstances(fmt.Sprintf(storage.RedisKeyFmtTenant, envId), &tenants)
	return tenants, err
}

func GetAllKeys(envId string) ([]string, error) {
	return rediz.HGetKeys(fmt.Sprintf(storage.RedisKeyFmtTenant, envId))
}

func Set(envId string, tId string, tenant4Redis *Tenant4Redis) error {
	return rediz.HSet(fmt.Sprintf(storage.RedisKeyFmtTenant, envId), tId, tenant4Redis)
}

func Delete(envId string, tId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtTenant, envId), tId)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtTenant, envId))
}
