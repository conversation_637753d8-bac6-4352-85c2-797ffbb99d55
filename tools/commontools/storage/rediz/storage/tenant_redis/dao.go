package tenant_redis

type Tenant4Redis struct {
	ID          string             `json:"id"`
	Description string             `json:"description"`
	Enabled     bool               `json:"enabled"`
	Name        string             `json:"name"`
	Domain_id   string             `json:"domain_id"`
	EvnId       string             `json:"evnId"`
	EnvName     string             `json:"envName"`
	VdcId       string             `json:"vdcId"`
	VdcName     string             `json:"vdcName"`
	UserRoles   []*UserRoles4Redis `json:"userRoles"`
}

type UserRoles4Redis struct {
	Id       string  `json:"id"`
	Name     string  `json:"name"`
	Roles    []*Role `json:"roles"`
	Password string  `json:"password"`
	Enabled  bool    `json:"enabled"`
}

type Role struct {
	Description string     `json:"description"`
	Id          string     `json:"id"`
	Links       *Link4Role `json:"links"`
	Name        string     `json:"name"`
	Domain_id   string     `json:"domain_id"`
}

type Link4Role struct {
	Self     string `json:"self"`
	Previous string `json:"previous"`
	Next     string `json:"next"`
}
