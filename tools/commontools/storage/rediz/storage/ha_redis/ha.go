package ha_redis

import (
	"fmt"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Delete(envId string, haId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtHa, envId), haId)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtHa, envId))
}

func CountHaNumById(envId string) (int64, error) {
	if len(envId) == 0 {
		return 0, fmt.Errorf("empty env id")
	}

	num, err := rediz.HLen(fmt.Sprintf(RedisKey4Ha, envId))
	if err != nil {
		return 0, fmt.Errorf("get ha num under env %s from redis, failed: %v", envId, err.Error())
	}

	return num, nil
}
