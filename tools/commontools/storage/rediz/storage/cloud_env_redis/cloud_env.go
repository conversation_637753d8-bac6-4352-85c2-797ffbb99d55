package cloud_env_redis

import (
	"fmt"
	"sync"
	"cwsm/tools/commontools/commondao/common_cloud_env"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

var lock sync.Mutex

func ClearOldEnvIds() {
	envIds := rediz.Keys("*_env")
	rediz.CleanKeys(envIds...)
}

func Get(id string) (*CloudEnvRedisDao, error) {
	cloud := &CloudEnvRedisDao{}
	err := rediz.HGetInstance(storage.RedisKeyEnvs, id, cloud)
	return cloud, err
}

func GetEnv4VrmOrPvrm(id string) (*CloudEnvRedisDao, error) {
	cloud4Redis, err := Get(id)
	if err != nil {
		logger.Errorf("GetEnv4VrmOrPvrm Get:%s error:%v", id, err)
		return nil, err
	}
	cloud4Redis.EnvType = common_cloud_env.C2VAndPEnvTypesMap[cloud4Redis.EnvType]
	return cloud4Redis, nil
}

func IsExist(id string) bool {
	return rediz.HExists(storage.RedisKeyEnvs, id)
}

func GetIds() ([]string, error) {
	ids, err := rediz.HKeys(storage.RedisKeyEnvs)
	if err != nil {
		logger.Errorf("cloud_env_redis GetByIds ids:(%s) error:%v", util.ToJSONStr(ids), err)
		return nil, err
	}
	return ids, nil
}

func GetByIds(ids []string) ([]*CloudEnvRedisDao, error) {
	clouds4Redis := []*CloudEnvRedisDao{}
	err := rediz.HMGetInstances(storage.RedisKeyEnvs, &clouds4Redis, ids...)
	if err != nil {
		logger.Errorf("cloud_env_redis GetByIds ids:(%s) error:%v", util.ToJSONStr(ids), err)
		return nil, err
	}
	return clouds4Redis, nil
}

func GetAll() ([]*CloudEnvRedisDao, error) {
	clouds4Redis := []*CloudEnvRedisDao{}
	if rediz.ExistsAny(storage.RedisKeyEnvs) {
		err := rediz.HGetAllInstances(storage.RedisKeyEnvs, &clouds4Redis)
		if err != nil {
			logger.Errorf("cloud_env_redis GetAll error %v", err)
			return nil, err
		}
	}
	return clouds4Redis, nil
}

func Set(id string, dao *CloudEnvRedisDao) error {
	err := rediz.HSet(storage.RedisKeyEnvs, id, dao)
	return err
}

func Delete(id string) bool {
	return rediz.HDel(storage.RedisKeyEnvs, id)
}

func Updatefields(id string, toBeUpdated map[string]interface{}) error {
	lock.Lock()
	defer lock.Unlock()
	cloud := &map[string]interface{}{}
	if err := rediz.HGetInstance(storage.RedisKeyEnvs, id, cloud); err != nil {
		return err
	}
	for field, updateValue := range toBeUpdated {
		if _, ok := (*cloud)[field]; ok {
			(*cloud)[field] = updateValue
		}
	}
	if err := rediz.HSet(storage.RedisKeyEnvs, id, cloud); err != nil {
		return err
	}
	return nil
}

func GetEnvs4Pvrm() ([]*CloudEnvRedisDao, error) {
	clouds4Redis, err := GetAll()
	if err != nil {
		logger.Errorf("GetEnvs4Pvrm GetAll error:%v", err)
		return nil, err
	}
	pvrmClouds := []*CloudEnvRedisDao{}
	for _, cloud4Redis := range clouds4Redis {
		value, ok := common_cloud_env.C2PEnvTypesMap[cloud4Redis.EnvType]
		if ok {
			cloud4Redis.EnvType = value
			pvrmClouds = append(pvrmClouds, cloud4Redis)
		}
	}
	return pvrmClouds, nil
}

func GetEnv4Pvrm(id string) (*CloudEnvRedisDao, error) {
	cloud4Redis, err := Get(id)
	if err != nil {
		logger.Errorf("GetEnv4Pvrm Get:%s error:%v", id, err)
		return nil, err
	}
	cloud4Redis.EnvType = common_cloud_env.C2PEnvTypesMap[cloud4Redis.EnvType]
	return cloud4Redis, nil
}

func GetEnvsByIds4Pvrm(ids []string) ([]*CloudEnvRedisDao, error) {
	clouds4Redis, err := GetByIds(ids)
	if err != nil {
		logger.Errorf("GetEnvsByIds4Pvrm error:%v", err)
		return nil, err
	}
	pvrmClouds := []*CloudEnvRedisDao{}
	for _, cloud4Redis := range clouds4Redis {
		value, ok := common_cloud_env.C2PEnvTypesMap[cloud4Redis.EnvType]
		if ok {
			cloud4Redis.EnvType = value
			pvrmClouds = append(pvrmClouds, cloud4Redis)
		}
	}
	return pvrmClouds, nil
}

func GetEnvFilterName4Pvrm(name string) (*CloudEnvRedisDao, error) {
	clouds4Redis, err := GetEnvs4Pvrm()
	if err != nil {
		logger.Errorf("GetEnvFilterName4Pvrm  error:%v", err)
		return nil, err
	}
	for _, cloud4Redis := range clouds4Redis {
		if cloud4Redis.Name == name {
			return cloud4Redis, nil
		}
	}
	return nil, fmt.Errorf("GetEnvFilterName4Pvrm name:%s failed", name)
}

func GetEnvs4Vrm() ([]*CloudEnvRedisDao, error) {
	clouds4Redis, err := GetAll()
	if err != nil {
		logger.Errorf("GetEnvs4Vrm GetAll error:%v", err)
		return nil, err
	}

	vrmClouds := []*CloudEnvRedisDao{}
	for _, cloud4Redis := range clouds4Redis {
		value, ok := common_cloud_env.C2VEnvTypesMap[cloud4Redis.EnvType]
		if ok {
			cloud4Redis.EnvType = value
			vrmClouds = append(vrmClouds, cloud4Redis)
		}
	}
	return vrmClouds, nil
}

func GetEnvs4Net() ([]*CloudEnvRedisDao, error) {
	clouds4Redis, err := GetAll()
	if err != nil {
		logger.Errorf("GetEnvs4Net GetAll error:%v", err)
		return nil, err
	}

	netClouds := []*CloudEnvRedisDao{}
	for _, cloud4Redis := range clouds4Redis {
		if cloud4Redis.EnvType == common_cloud_env.NETWORKENVTYPE {
			netClouds = append(netClouds, cloud4Redis)
		}
	}
	return netClouds, nil
}

func GetEnvs4Phy() ([]*CloudEnvRedisDao, error) {
	clouds4Redis, err := GetAll()
	if err != nil {
		logger.Errorf("GetEnvs4Phy GetAll error:%v", err)
		return nil, err
	}

	netClouds := []*CloudEnvRedisDao{}
	for _, cloud4Redis := range clouds4Redis {
		if cloud4Redis.EnvType == common_cloud_env.PHYSICALENVTYPE {
			netClouds = append(netClouds, cloud4Redis)
		}
	}
	return netClouds, nil
}

func GetVAndPEnvs() ([]*CloudEnvRedisDao, error) {
	clouds4Redis, err := GetAll()
	if err != nil {
		logger.Errorf("GetVrmPvrmEnvs GetAll error:%v", err)
		return nil, err
	}
	
	envs := []*CloudEnvRedisDao{}
	for _, cloud4Redis := range clouds4Redis {
		value, ok := common_cloud_env.C2VAndPEnvTypesMap[cloud4Redis.EnvType]
		if ok {
			cloud4Redis.EnvType = value
			envs = append(envs, cloud4Redis)
		}
	}
	return envs, nil
}
