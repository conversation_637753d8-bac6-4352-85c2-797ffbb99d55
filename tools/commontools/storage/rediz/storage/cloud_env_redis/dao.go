package cloud_env_redis

import (
	"fmt"
	"net/url"
	"strings"
	"cwsm/tools/commontools/cloudtoken"
	"cwsm/tools/commontools/commondao/common_cloud_env"
	"cwsm/tools/commontools/globalcv"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz/storage/cloud_token"
)

var REDISCols = struct {
	Name           string `json:"name"`
	EnvType        string `json:"env_type"`
	Description    string `json:"description"`
	LinkStatus     string `json:"link_status"`
	Provider       string `json:"provider"`
	RelatedEnvID   string `json:"related_env_id"`
	DcName         string `json:"dc_name"`
	DcID           string `json:"dc_id"`
	Location       string `json:"location"`
	Endpoints      string `json:"endpoints"`
	CreatedAt      string `json:"created_at"`
	UpdatedAt      string `json:"updated_at"`
	ID             string `json:"id"`
	ComputeHa      string `json:"compute_ha"`
	NFVHa          string `json:"nfv_ha"`
	Metadata       string `json:"metadata"`
	APIVersions    string `json:"api_versions"`
	Area           string `json:"area"`
	Sso            string `json:"sso"`
	Labels         string `json:"labels"`
	BridgeInfo     string `json:"bridge_info"`
	PoolPortalURL  string `json:"pool_portal_url"`
	VMBackupConfig string `json:"vm_backup_config"`
}{
	Name:           "name",
	EnvType:        "env_type",
	Description:    "description",
	LinkStatus:     "link_status",
	Provider:       "provider",
	RelatedEnvID:   "related_env_id",
	DcName:         "dc_name",
	DcID:           "dc_id",
	Location:       "location",
	Endpoints:      "endpoints",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	ID:             "id",
	ComputeHa:      "compute_ha",
	NFVHa:          "nfv_ha",
	Metadata:       "metadata",
	APIVersions:    "api_versions",
	Area:           "area",
	Sso:            "sso",
	Labels:         "labels",
	BridgeInfo:     "bridge_info",
	PoolPortalURL:  "pool_portal_url",
	VMBackupConfig: "vm_backup_config",
}

type CloudEnvRedisDao struct {
	Name           string                             `json:"name"`
	EnvType        string                             `json:"env_type"`
	Description    string                             `json:"description"`
	LinkStatus     string                             `json:"link_status"`
	Provider       string                             `json:"provider"`
	RelatedEnvID   string                             `json:"related_env_id"`
	DcName         string                             `json:"dc_name"`
	DcID           string                             `json:"dc_id"`
	Location       string                             `json:"location"`
	Region         string                             `json:"region"`
	Endpoints      []*common_cloud_env.Endpoint4Redis `json:"endpoints"`
	CreateTime     string                             `json:"created_at"`
	UpdateTime     string                             `json:"updated_at"`
	ID             string                             `json:"id"`
	ComputeHa      []*common_cloud_env.ResourcePolicy `json:"compute_ha"`
	NFVHa          []*common_cloud_env.ResourcePolicy `json:"nfv_ha"`
	Metadata       map[string]interface{}             `json:"metadata"`
	APIVersions    map[string]string                  `json:"api_versions"`
	Area           string                             `json:"area"`
	Sso            *common_cloud_env.Sso              `json:"sso"`
	Labels         map[string]interface{}             `json:"labels"`
	BridgeInfo     *common_cloud_env.BridgeInfo       `json:"bridge_info"`
	PoolPortalURL  string                             `json:"pool_portal_url"`
	VMBackupConfig *common_cloud_env.VMBackupConfig   `json:"vm_backup_config"`
	CheckItems     []*common_cloud_env.CheckItem      `json:"checkItems"`
	Nets           []*common_cloud_env.Net            `json:"nets"`
}

func (cr *CloudEnvRedisDao) IsNormal() bool {
	return cr.LinkStatus == globalcv.NORMAL_ENV_STATUS
}

func (cr *CloudEnvRedisDao) FindSpecialUrl(endpoint_name string) (*url.URL, error) {
	for _, e := range cr.Endpoints {
		if e.Name == endpoint_name {
			return url.Parse(e.URL)
		}
	}
	return nil, fmt.Errorf("findSpecialUrl endpoint_name:%s failed", endpoint_name)
}

func (cr *CloudEnvRedisDao) FindSpecialItems(itemName string) *common_cloud_env.CheckItem {
	if cr.CheckItems == nil {
		return nil
	}
	for _, item := range cr.CheckItems {
		if item.Item == itemName {
			return item
		}
	}
	return nil
}

func (cr *CloudEnvRedisDao) FindSpecialEndPointInfo(endpointName string) (cloudtoken.AuthType,
	*common_cloud_env.Endpoint4Redis, error) {
	for _, e := range cr.Endpoints {
		if e.Name == endpointName {
			if strings.EqualFold(cr.EnvType, common_cloud_env.NETWORKENVTYPE) {
				return cloudtoken.AuthTypeNetInsight, e, nil
			}
			if strings.EqualFold(cr.EnvType, common_cloud_env.OPENPALETTEENVTYPE) {
				return cloudtoken.AuthTypeOpenpalette, e, nil
			}
			if strings.EqualFold(e.Version, "v2") {
				return cloudtoken.AuthTypeKeystoneV2, e, nil
			}
			return cloudtoken.AuthTypeKeystoneV3, e, nil
		}
	}
	return "", nil, fmt.Errorf("FindSpecialEndPointTokenType endpoint_name:%s failed", endpointName)
}

func (cr *CloudEnvRedisDao) FindAuthEndPoint() *common_cloud_env.Endpoint4Redis {
	for _, e := range cr.Endpoints {
		if e.Name == common_cloud_env.ENDPOINT_PUBLIC_AUTH {
			return e
		}
	}
	return nil
}

func (cr *CloudEnvRedisDao) FindPrivateAuthEndPoint() *common_cloud_env.Endpoint4Redis {
	for _, e := range cr.Endpoints {
		if e.Name == common_cloud_env.ENDPOINT_PRIVATE_AUTH {
			return e
		}
	}
	return nil
}

func (cr *CloudEnvRedisDao) FindPrivatePortalEndPoint() *common_cloud_env.Endpoint4Redis {
	for _, e := range cr.Endpoints {
		if e.Name == common_cloud_env.ENDPOINT_PRIVATE_PORTAL {
			return e
		}
	}
	return nil
}

func (cr *CloudEnvRedisDao) FindPublicPortalEndPoint() *common_cloud_env.Endpoint4Redis {
	for _, e := range cr.Endpoints {
		if e.Name == common_cloud_env.ENDPOINT_PUBLIC_PORTAL {
			return e
		}
	}
	return nil
}

func (cr *CloudEnvRedisDao) CleanToken() {
	envUrls := []string{}
	ep := cr.FindAuthEndPoint()
	if ep != nil && !util.Contains(ep.URL, envUrls) {
		envUrls = append(envUrls, ep.URL)
	}

	ep = cr.FindPublicPortalEndPoint()
	if ep != nil && !util.Contains(ep.URL, envUrls) {
		envUrls = append(envUrls, ep.URL)
	}

	ep = cr.FindPrivateAuthEndPoint()
	if ep != nil && !util.Contains(ep.URL, envUrls) {
		envUrls = append(envUrls, ep.URL)
	}

	ep = cr.FindPrivatePortalEndPoint()
	if ep != nil && !util.Contains(ep.URL, envUrls) {
		envUrls = append(envUrls, ep.URL)
	}

	logger.Infof("Envredis ClearToken cloud:%s urls:%s", cr.ID, util.ToJSONStr(envUrls))

	keys, err := cloud_token.GetAllKeys()
	if err != nil {
		logger.Errorf("Envredis ClearToken cloud:%s get all keys failed:%v", cr.ID, err)
		return
	}
	for _, key := range keys {
		for _, envUrl := range envUrls {
			if strings.Contains(key, envUrl) {
				logger.Infof("Envredis ClearToken cloud:%s key:%s", cr.ID, key)
				cloud_token.Delete(key)
			}
		}
	}
	return
}
