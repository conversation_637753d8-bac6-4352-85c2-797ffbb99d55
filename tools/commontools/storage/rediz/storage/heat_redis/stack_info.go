package heat_redis

import (
	"fmt"
	"strings"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
)

const (
	RedisKey4StackInfo                     = "%s_stack"
	RedisKey4InProgressOperation           = "IN_PROGRESS"
	RedisKeyExpirationTime4StackInfo_Short = 1
	RedisKeyExpirationTime4StackInfo_Long  = 60
)

func GetStackInfoById(id string) (*StackInfo, error) {
	key := fmt.Sprintf(RedisKey4StackInfo, id)
	logger.Infof("[heat_redis.GetStackInfoById] redis key: %s", key)

	dao := &StackInfo{}
	err := rediz.GetInstance(key, dao)
	if err != nil {
		logger.Errorf("[heat_redis.GetStackInfoById] error: %v", err.Error())
		return nil, err
	}

	return dao, nil
}

func SetStackInfo(id string, stackInfo *StackInfo) error {
	key := fmt.Sprintf(RedisKey4StackInfo, id)

	var err error
	if strings.Contains(stackInfo.StackStatus, RedisKey4InProgressOperation) {
		logger.Info("[heat_redis.SetStackInfo] set with short expiration key for %s", stackInfo.StackName)
		err = rediz.SetWithExpiration(key, stackInfo, RedisKeyExpirationTime4StackInfo_Short)
	} else {
		err = rediz.SetWithExpiration(key, stackInfo, RedisKeyExpirationTime4StackInfo_Long)
	}
	if err != nil {
		logger.Errorf("[heat_redis.SetStackInfo] error: %v", err.Error())
		return err
	}

	return nil
}

func DelStackInfoById(id string) bool {
	return rediz.Del(fmt.Sprintf(RedisKey4StackInfo, id))
}
