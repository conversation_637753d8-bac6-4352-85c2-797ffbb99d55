package heat_redis

type StackInfo struct {
	CreationTime       string           `json:"creation_time"`
	Description        string           `json:"description"`
	DisableRollback    bool             `json:"disable_rollback"`
	ID                 string           `json:"id"`
	Parameters         *StackParameters `json:"parameters"`
	StackName          string           `json:"stack_name"`
	StackOwner         string           `json:"stack_owner"`
	StackStatus        string           `json:"stack_status"`
	StackStatusReason  string           `json:"stack_status_reason"`
	StackUserProjectID string           `json:"stack_user_project_id"`
	UpdatedTime        string           `json:"updated_time"`
}

type StackParameters struct {
	OS__projectID string `json:"OS::project_id"`
	OS__stackID   string `json:"OS::stack_id"`
	OS__stackName string `json:"OS::stack_name"`
}
