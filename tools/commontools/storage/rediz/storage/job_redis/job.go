package job_redis

import (
	"fmt"
	"time"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
)

const (
	RedisKeyFmtJob          = "%s_%s_job"
	RedisKeyFmtJobShort     = "%s_job"
	RedisKeyFmtEmptyJobName = ""
	JobExpiringTime_Hour    = 168
)

func IsAvailable() bool {
	err := rediz.Ping()
	return err == nil
}

// the function below with Time Complexity of O(totalDays * n) = O(n), where n represents the number of job names,
// should be used for single point search with less frequency;
func GetAsyncJobById(id string) (*RunnerAsyncDetail4Redis, error) {
	dao := &RunnerAsyncDetail4Redis{}
	keys := fetchPossibleKeys(RedisKeyFmtEmptyJobName)

	err := fetchAsyncJobInfo(keys, id, dao)
	if err != nil {
		return dao, fmt.Errorf("[GetAsyncJob] get job (id: %s) info failed: %v", id, err.Error())
	}

	return dao, nil
}

func fetchAsyncJobInfo(keys []string, id string, obj *RunnerAsyncDetail4Redis) error {
	if len(keys) == 0 {
		return fmt.Errorf("[fetchAsyncJobInfo] empty key segments")
	}

	var err error
	for _, key := range keys {
		err = rediz.HGetInstance(key, id, obj)
		if len((*obj).Uuid) > 0 {
			return nil
		}
		if err != nil {
			logger.Debugf("[fetchAsyncJobInfo] get job info failed: %v", err.Error())
		}
		if err != nil {
			logger.Debugf("[fetchAsyncJobInfo] get job info failed: %v", err.Error())
		}
	}

	return fmt.Errorf("[fetchAsyncJobInfo] job not found")
}

// this function serves the single point search with both id and job name as input parameters and it is recommended
// since it has lower Time Complexity of O(totalDays) = O(1) than the function above;
func GetAsyncJobByIdAndName(id, name string) (string, *RunnerAsyncDetail4Redis, error) {
	dao := &RunnerAsyncDetail4Redis{}
	keys := fetchPossibleKeys(name)

	var err error
	for _, key := range keys {
		err = rediz.HGetInstance(key, id, dao)
		if len(dao.Uuid) > 0 && err == nil {
			return key, dao, nil
		}
	}

	return "", dao, fmt.Errorf("[GetAsyncJobByIdAndResourceName] the job (id: %s) does not exist", id)
}

// the function below should be used for acquiring async job list called by any other individual micro-service;
func GetAsyncJobListByName(name string) ([]*RunnerAsyncDetail4Redis, error) {
	daos := []*RunnerAsyncDetail4Redis{}
	keys := fetchPossibleKeys(name)

	var err error
	for _, key := range keys {
		dao := []*RunnerAsyncDetail4Redis{}
		err = rediz.HGetAllInstancesIgnoreValueIsEmpty(key, &dao)
		if err != nil {
			logger.Error("[GetAsyncJobListByName] error: %v", err.Error())
		} else {
			daos = append(daos, dao...)
		}
	}

	return daos, err
}

// the function below should only be used for crash recovery;
func GetAllAsyncJobs() ([]*RunnerAsyncDetail4Redis, error) {
	keys := fetchPossibleKeys(RedisKeyFmtEmptyJobName)

	daos := []*RunnerAsyncDetail4Redis{}
	var err error
	for _, key := range keys {
		daoSet := []*RunnerAsyncDetail4Redis{}
		err = rediz.HGetAllInstances(key, &daoSet)
		if err != nil {
			logger.Error("[GetAllAsyncJobs] error: %v", err.Error())
		} else {
			daos = append(daos, daoSet...)
		}
	}

	return daos, err
}

func SetJobInfo(id string, jobContent *RunnerAsyncDetail4Redis) error {
	// Note: once a job is operated, it will be move under the key with the time stamp of current date, which means the job's expiration info will be reset;
	// First of all, we need to determine whether this job has been stored previously under any of old keys;
	// if it does, put it in the key of current date and delete the old one; Otherwise, serialize it directly.
	currYear, currMon, currDate := time.Now().Date()
	oldYear, oldMon, oldDate := jobContent.CreatedTime.Date()
	if currYear != oldYear || currMon != oldMon || currDate != oldDate {
		oldKey, _, err := GetAsyncJobByIdAndName(id, jobContent.Name)
		if err != nil {
			logger.Error("[SetJobInfo] error: %v", err.Error())
		}
		if len(oldKey) > 0 {
			success := rediz.HDel(oldKey, id)
			if !success {
				logger.Info("[SetJobInfo] delete old job info failed: %v", err.Error())
			}
		}
	}

	key := buildRedisKey(currYear, currMon, currDate, jobContent.Name)
	logger.Info("[SetJobInfo] key: %s", key)
	logger.Debugf("[SetJobInfo] key: %s, id: %s jobContent: %s", key, id, util.ToJSONStr(jobContent.Tasks))
	err := rediz.HSet(key, id, jobContent)
	if err != nil {
		return err
	}

	_, err = rediz.SetKeyExpiration(key, JobExpiringTime_Hour*time.Hour)
	if err != nil {
		logger.Error("[SetJobInfo] set expiration for key-%s, failed: %v", key, err.Error())
	}

	return nil
}

func DeleteJobInfo(id string) bool {
	var success bool
	keys := fetchPossibleKeys(RedisKeyFmtEmptyJobName)

	for _, key := range keys {
		success = rediz.HDel(key, id)
		if success {
			return true
		}
	}

	return false
}

func buildRedisKey(yr int, mon time.Month, day int, name string) string {

	timeStamp := fmt.Sprintf("%d_%s_%d", yr, mon, day)

	key := fmt.Sprintf(RedisKeyFmtJob, timeStamp, name)

	return key
}

func fetchPossibleKeys(name string) []string {
	pattern := fmt.Sprintf(RedisKeyFmtJob, "*", name)
	if len(name) == 0 {
		pattern = fmt.Sprintf(RedisKeyFmtJobShort, "*")
	}

	var keys []string
	var err error
	var cursor uint64
	for {
		var keySet []string
		keySet, cursor, err = rediz.Scan(cursor, pattern, 30)
		if err != nil {
			logger.Error("[formulatePossibleKeys] failed: %s", err.Error())
			break
		}

		keys = append(keys, keySet...)

		if cursor == 0 {
			break
		}
	}
	if len(keys) == 0 {
		return []string{}
	}

	logger.Debugf("[formulatePossibleKeys] all possible keys are: %s", util.ToJSONStr(keys))

	return keys
}
