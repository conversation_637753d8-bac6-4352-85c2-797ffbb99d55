package job_redis

import (
	"encoding/json"
	"fmt"
	"time"
)

type RunnerAsyncDetail4Redis struct {
	ResourceName string              `json:"resourceName"`
	Tasks        []*RunnerSyncDetail `json:"tasks"`
	TaskErr      map[string]string   `json:"taskErr"`
	Uuid         string              `json:"uuid"`
	Size         int                 `json:"size"`
	TaskStepSize int                 `json:"taskStepSize"`
	Status       string              `json:"status"`
	Name         string              `json:"name"`
	CreatedTime  time.Time           `json:"createdTime"`
	StartTime    time.Time           `json:"startTime"`
	Progress     int                 `json:"progress"`
	EndTime      time.Time           `json:"endTime"`
}

type RunnerSyncDetail struct {
	Name         string      `json:"name"`
	ResourceName string      `json:"resourceName"`
	StepSize     int         `json:"stepSize"`
	Status       string      `json:"status"`
	Path         []string    `json:"path"`
	StopStep     string      `json:"stopStep"`
	SyncSteps    []*SyncStep `json:"syncSteps"`
	CreateTime   time.Time   `json:"createTime"`
	StartTime    time.Time   `json:"startTime"`
	EndTime      time.Time   `json:"endTime"`
	Progress     int         `json:"progress"`
}

type SyncStep struct {
	Execution  func(args ...interface{}) error `json:"-"`
	Name       string                          `json:"name"`
	CreateTime time.Time                       `json:"createTime"`
	Params     []interface{}                   `json:"params"`
	StartMsg   string                          `json:"-"`
	EndMsg     string                          `json:"-"`
	StartTime  time.Time                       `json:"startTime"`
	EndTime    time.Time                       `json:"endTime"`
	Errs       error                           `json:"-"`
	ErrMsg     string                          `json:"errs"`
	Status     string                          `json:"status"`
}

func (this *RunnerAsyncDetail4Redis) PrintErrors() string {
	x := ""
	for key, value := range this.TaskErr {
		x += fmt.Sprintf("task name:%s, errorMsg:%s \n", key, value)
	}
	return x
}

func (this *RunnerAsyncDetail4Redis) PrintStringErrors() []string {
	x := []string{}
	for key, value := range this.TaskErr {
		x = append(x, fmt.Sprintf("task name:%s, errorMsg:%s \n", key, value))
	}
	return x
}

func (this *RunnerAsyncDetail4Redis) GetStatus() string {
	return this.Status
}

func (this *RunnerAsyncDetail4Redis) PrintDetail() string {
	msg, _ := json.Marshal(this)
	return string(msg)
}

func (this *RunnerAsyncDetail4Redis) GetTasks() []*RunnerSyncDetail {
	return this.Tasks
}
