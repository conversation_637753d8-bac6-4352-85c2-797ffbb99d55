package storage

import (
	"fmt"
)

type RedisCommon struct {
	resourceId string
}

const (
	RedisKeyFmtEnv         = "%s_env"
	RedisKeyEnvs           = "envs"
	RedisKeyVdcs           = "vdcs"
	RedisKeyToken          = "cloudtoken"
	RedisKeyFabricResource = "fabricGroup_hash_key"
	RedisKey4VimHash       = "vimsInfo"
)

const (
	RedisKeyFmtEnvMetadata = "%s_metadata"
)

const (
	RedisKeyFmtAZ                = "%s_az_Last_vrm"
	RedisKeyFmtBareMetalNode     = "%s_baremetalnode_Last_vrm"
	RedisKeyFmtCinder            = "%s_cinder"
	RedisKeyFmtController        = "%s_controller_Last_vrm"
	RedisKeyFmtDevice            = "%s_device_Last_vrm"
	RedisKeyFmtDeviceProfile     = "%s_deviceprofile_Last_vrm"
	RedisKeyFmtFlavor            = "%s_flavor"
	RedisKeyFmtDelFlavor         = "%s_deleted_flavors"
	RedisKeyFmtFreezerBackupData = "%s_freezer_backupdata_Last_vrm"
	RedisKeyFmtFreezerJob        = "%s_freezer_job_Last_vrm"
	RedisKeyFmtHa                = "%s_ha"

	RedisKeyFmtHostBondport   = "%s_host_bondport_Last_vrm"
	RedisKeyFmtHostFileSystem = "%s_host_filesystem_Last_vrm"
	RedisKeyFmtHostPort       = "%s_host_port_Last_vrm"
	RedisKeyFmtHost           = "%s_host"

	RedisKeyFmtNeutronAgent = "%s_neutronagent"
	RedisKeyFmtNova         = "%s_nova"
	RedisKeyFmtPort         = "%s_port_Last_vrm"
	RedisKeyFmtServerGroup  = "%s_servergroup"

	RedisKeyFmtSnapshot       = "%s_snapshot_Last_vrm"
	RedisKeyFmtStorageBackEnd = "%s_storagebackend"

	RedisKeyFmtTenant     = "%s_tenant_Last_vrm"
	RedisKeyFmtUser       = "%s_user"
	RedisKeyFmtVm         = "%s_vm_Last_vrm"
	RedisKeyFmtVolume     = "%s_volume_Last_vrm"
	RedisKeyFmtVolumeType = "%s_volumetype"

	RedisKeyFmtImage = "%s_image_Last_vrm"

	RedisKeyFmtVdc = "%s_VdcTenantEnv"

	RedisKeySecretCacheFmtService = "secret_key_cache_%s"
	RedisKeySecretLatest          = "secret_key_latest"
)

func ResourceKeysUnderEnv(envId string) []string {
	return []string{fmt.Sprintf(RedisKeyFmtAZ, envId),
		fmt.Sprintf(RedisKeyFmtBareMetalNode, envId),
		fmt.Sprintf(RedisKeyFmtCinder, envId),
		fmt.Sprintf(RedisKeyFmtController, envId),
		fmt.Sprintf(RedisKeyFmtDevice, envId),
		fmt.Sprintf(RedisKeyFmtDeviceProfile, envId),
		fmt.Sprintf(RedisKeyFmtFlavor, envId),
		fmt.Sprintf(RedisKeyFmtDelFlavor, envId),
		fmt.Sprintf(RedisKeyFmtFreezerBackupData, envId),
		fmt.Sprintf(RedisKeyFmtFreezerJob, envId),
		fmt.Sprintf(RedisKeyFmtHa, envId),
		fmt.Sprintf(RedisKeyFmtHostBondport, envId),
		fmt.Sprintf(RedisKeyFmtHostFileSystem, envId),
		fmt.Sprintf(RedisKeyFmtHostPort, envId),
		fmt.Sprintf(RedisKeyFmtHost, envId),
		fmt.Sprintf(RedisKeyFmtNeutronAgent, envId),
		fmt.Sprintf(RedisKeyFmtNova, envId),
		fmt.Sprintf(RedisKeyFmtPort, envId),
		fmt.Sprintf(RedisKeyFmtServerGroup, envId),
		fmt.Sprintf(RedisKeyFmtSnapshot, envId),
		fmt.Sprintf(RedisKeyFmtStorageBackEnd, envId),
		fmt.Sprintf(RedisKeyFmtTenant, envId),
		fmt.Sprintf(RedisKeyFmtUser, envId),
		fmt.Sprintf(RedisKeyFmtVm, envId),
		fmt.Sprintf(RedisKeyFmtVolume, envId),
		fmt.Sprintf(RedisKeyFmtVolumeType, envId),
		fmt.Sprintf(RedisKeyFmtImage, envId)}
}
