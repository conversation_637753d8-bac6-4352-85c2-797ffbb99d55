package volumetype_redis

import (
	"fmt"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Delete(envId string, vtId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtVolumeType, envId), vtId)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtVolumeType, envId))
}

func Get(envId string, vtId string) (*VolumeTypeRedisDao, bool) {
	vt := VolumeTypeRedisDao{}
	err := rediz.HGetInstance(fmt.Sprintf(storage.RedisKeyFmtVolumeType, envId), vtId, &vt)
	if err != nil {
		logger.Errorf("get volume_type from redis failed: %s", err.<PERSON>rror())
		return nil, false
	}
	return &vt, true
}

func GetAll(envId string) ([]*VolumeTypeRedisDao, error) {
	types := []*VolumeTypeRedisDao{}
	key := fmt.Sprintf(storage.RedisKeyFmtVolumeType, envId)
	if rediz.ExistsAny(key) {
		err := rediz.HGetAllInstances(key, &types)
		if err != nil {
			logger.Errorf("get all volume_type from redis failed: %v", err)
			return nil, err
		}
	}
	return types, nil
}

func Set(envId string, vtId string, dao *VolumeTypeRedisDao) error {
	return rediz.HSet(fmt.Sprintf(storage.RedisKeyFmtVolumeType, envId), vtId, dao)
}
