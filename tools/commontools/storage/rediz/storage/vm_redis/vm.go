package vm_redis

import (
	"fmt"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Delete(envId string, vmId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtVm, envId), vmId)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtVm, envId))
}

func GetAll(envId string) ([]*VmRedisDao, error) {
	vms := []*VmRedisDao{}
	key := fmt.Sprintf(storage.RedisKeyFmtVm, envId)
	if rediz.ExistsAny(key) {
		err := rediz.HGetAllInstances(key, &vms)
		if err != nil {
			logger.Errorf("vm_redis GetAll error %v", err)
			return nil, err
		}
	}
	return vms, nil
}

func Get(envId, vmId string) (*VmRedisDao, error) {
	vm := &VmRedisDao{}
	err := rediz.HGetInstance(fmt.Sprintf(storage.RedisKeyFmtVm, envId), vmId, vm)
	return vm, err
}
