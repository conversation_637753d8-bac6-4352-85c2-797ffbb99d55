package flavor_redis

import (
	"fmt"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Delete(envId string, flavorId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtFlavor, envId), flavorId)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtFlavor, envId))
}

func DeleteFlavor4Del(envId string, flavorId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtDelFlavor, envId), flavorId)
}

func DeleteAllFlavor4Del(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtDelFlavor, envId))
}

func GetAllFlavorFromRedis(envId string) ([]*RedisFlavor, error) {
	allFlavors := []*RedisFlavor{}
	err := rediz.HGetAllInstances(fmt.Sprintf(storage.RedisKeyFmtFlavor, envId), &allFlavors)
	if err != nil {
		logger.Errorf("[GetAllFlavorFromRedis] get flavors from redis error: %v", err)
		return nil, fmt.Errorf("[GetAllFlavorFromRedis] get flavors from redis error: %v", err)
	}
	return allFlavors, nil
}
