package vdc_tenant_redis

import (
	"fmt"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
	"cwsm/tools/commontools/storage/rediz/storage/vdc_redis"
)

func GetVdcTenantEnvs(vdcId string) ([]*VdcTenant4Redis, error) {
	vdcTenantEnvs := []*VdcTenant4Redis{}
	err := rediz.HGetAllInstances(fmt.Sprintf(storage.RedisKeyFmtVdc, vdcId), &vdcTenantEnvs)
	return vdcTenantEnvs, err
}

func GetVdcTenant(vdcId, tenantId string) (*VdcTenant4Redis, error) {
	vdcTenantEnv := &VdcTenant4Redis{}
	err := rediz.HGetInstance(fmt.Sprintf(storage.RedisKeyFmtVdc, vdcId), tenantId, vdcTenantEnv)
	return vdcTenantEnv, err
}

func Set(vdcId, tenantId string, vdcTenantEnv *VdcTenant4Redis) error {
	return rediz.HSet(fmt.Sprintf(storage.RedisKeyFmtVdc, vdcId), tenantId, vdcTenantEnv)
}

func GetVdcEnv(vdcId, envId string) (*VdcTenant4Redis, error) {
	vdcTenantEnvs, err := GetVdcTenantEnvs(vdcId)
	if err != nil {
		return nil, err
	}
	for _, vdcTenantEnv := range vdcTenantEnvs {
		if vdcTenantEnv.EnvId == envId {
			return vdcTenantEnv, nil
		}
	}
	return nil, fmt.Errorf("can not find env %s under vdc %s", envId, vdcId)
}

func Delete(vdcId, tenantId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtVdc, vdcId), tenantId)
}

func DeleteAll(vdcId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtVdc, vdcId))
}

func GetAllVdcTenantEnvs() ([]*VdcTenant4Redis, error) {
	vdcIds, err := vdc_redis.GetAllVdcIds()
	if err != nil {
		return nil, fmt.Errorf("GetAllVdcTenantEnvs failed: GetAllVdcIds failed %s", err.Error())
	}
	result := []*VdcTenant4Redis{}
	for _, vdcId := range vdcIds {
		vdcTenants, err := GetVdcTenantEnvs(vdcId)
		if err != nil {
			logger.Infof("GetAllVdcTenantEnvs failed: GetVdcTenantEnvs vdcId %s error:%s", vdcId, err.Error())
		} else {
			result = append(result, vdcTenants...)
		}
	}
	return result, nil
}
