package baremetal_node_redis

import (
	"fmt"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Delete(envId string, bmnId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtBareMetalNode, envId), bmnId)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtBareMetalNode, envId))
}

func GetBareMetalInfoById(envId string) ([]*BareMetalNodeInfo, error) {
	if len(envId) == 0 {
		return nil, fmt.Errorf("empty baremetal Id")
	}

	bmnInfo := []*BareMetalNodeInfo{}
	if err := rediz.HGetAllInstances(fmt.Sprintf(storage.RedisKeyFmtBareMetalNode, envId), bmnInfo); err != nil {
		return nil, fmt.Errorf("get baremetal nodes under env %s from redis, failed: %v", envId, err.Error())
	}

	return bmnInfo, nil
}
