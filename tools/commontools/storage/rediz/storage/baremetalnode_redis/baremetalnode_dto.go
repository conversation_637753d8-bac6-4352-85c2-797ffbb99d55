package baremetal_node_redis

type BareMetalNodeInfo struct {
	InstanceID        string                 `json:"instanceId"`
	InstanceName      string                 `json:"name"`
	DcName            string                 `json:"dcName"`
	CloudEnvName      string                 `json:"cloudEnvName"`
	CloudEnvID        string                 `json:"cloudEnvId"`
	VmName            string                 `json:"vmName"`
	PowerState        string                 `json:"powerState"`
	Driver            string                 `json:"driver"`
	Maintenance       bool                   `json:"maintenance"`
	PortNum           int                    `json:"portNum"`
	ProvisionState    string                 `json:"provisionState"`
	ProvisionUpdateAt string                 `json:"provisionUpdateAt"`
	Extra             map[string]interface{} `json:"extra"`
	DriverInfo        *DriverInfo            `json:"driver_info"`
	SerialNo          string                 `json:"serialNo"`
	ResourceClass     string                 `json:"resource_class"`
	Properties        *NodeProperties        `json:"properties"`
	NodeType          string                 `json:"nodeType"`
}

type DriverInfo struct {
	Address string `json:"ipmi_address"`
}

type NodeProperties struct {
	LocalGB  string `json:"local_gb"`
	Cpus     string `json:"cpus"`
	MemoryMB string `json:"memory_mb"`
}
