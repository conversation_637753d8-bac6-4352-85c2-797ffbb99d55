package secret_key_redis

import (
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func SetLatest(service string, skc *SecretKeyDetail) error {
	err := rediz.HSet(storage.RedisKeySecretLatest, service, skc)
	return err
}

func GetLatest(service string) (*SecretKeyDetail, error) {
	skd := &SecretKeyDetail{}
	err := rediz.HGetInstance(storage.RedisKeySecretLatest, service, skd)
	return skd, err
}

func DelLatest(service string) bool {
	return rediz.HDel(storage.RedisKeySecretLatest, service)
}
