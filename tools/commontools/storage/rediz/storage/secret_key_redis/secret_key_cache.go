package secret_key_redis

import (
	"fmt"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func SetCache(service string, skc *SecretKeyDetail) error {
	err := rediz.HSet(fmt.Sprintf(storage.RedisKeySecretCacheFmtService, service), skc.ID, skc)
	return err
}

func GetCache(service, keyID string) (*SecretKeyDetail, error) {
	skd := &SecretKeyDetail{}
	err := rediz.HGetInstance(fmt.Sprintf(storage.RedisKeySecretCacheFmtService, service), keyID, skd)
	return skd, err
}

func GetAllCaches(service string) ([]*SecretKeyDetail, error) {
	skds := []*SecretKeyDetail{}
	err := rediz.HGetAllInstances(fmt.Sprintf(storage.RedisKeySecretCacheFmtService, service), &skds)
	if err != nil {
		logger.Errorf("GrtAllCaches service:%s failed:%v", service, err)
		return nil, err
	}
	return skds, nil
}

func DelCache(service, keyID string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeySecretCacheFmtService, service), keyID)
}

func ClearCache(service string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeySecretCacheFmtService, service))
}
