package leader_selector_redis

import (
	"context"
	"sync"
	"time"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz/storage/lock_redis"
)

var once = &sync.Once{}

type LeaderSelector struct {
	Lock             *lock_redis.RedisLock `json:"Lock"`
	IsLeader         bool                  `json:"IsLeader"`
	beforeLeaderFunc func()
	afterLeaderFunc  func()
}

func NewLeaderSelector(beforeLeaderFunc, afterLeaderFunc func()) *LeaderSelector {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*120)
	lr := lock_redis.NewRedisLock(ctx, cancel, "RedisLock4LeaderSelector")
	return &LeaderSelector{lr, false, beforeLeaderFunc, afterLeaderFunc}
}

func (l *LeaderSelector) Start() *LeaderSelector {
	l.selectLeader4First()
	go l.runLeaderSelector()
	logger.Infof("LeaderSelector start, redisLeaderSelector:%s", util.ToJSONStr(l))
	return l
}

func (l *LeaderSelector) runLeaderSelector() {
	for {
		if ok, err := l.Lock.TryLock(); ok {
			if !l.IsLeader {
				l.IsLeader = true
				l.beforeLeaderFunc()
				logger.Infof("RunLeaderSelector LeaderSelector:%s change to leader", util.ToJSONStr(l))
			}
			logger.Debugf("RunLeaderSelector LeaderSelector:%s success", util.ToJSONStr(l))
		} else if err != nil {
			if l.IsLeader {
				l.IsLeader = false
				l.afterLeaderFunc()
				logger.Infof("RunLeaderSelector LeaderSelector:%s change to no leader", util.ToJSONStr(l))
			}
			logger.Debugf("RunLeaderSelector LeaderSelector:%s failed:%v", util.ToJSONStr(l), err)
		}
		time.Sleep(time.Duration(l.Lock.TimeoutMs*2/3) * time.Millisecond)
	}
}

func (l *LeaderSelector) selectLeader4First() {
	err := l.Lock.Lock()
	defer l.Lock.Unlock()
	if err == nil {
		l.IsLeader = true
		logger.Infof("selectLeader4First LeaderSelector:%s success", util.ToJSONStr(l))
	} else {
		l.IsLeader = false
		logger.Errorf("selectLeader4First LeaderSelector:%s failed:%v", util.ToJSONStr(l), err)
	}
}
