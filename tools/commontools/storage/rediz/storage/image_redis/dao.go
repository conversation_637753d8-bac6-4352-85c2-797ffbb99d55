package image_redis

type Image4Redis struct {
	ID                 string                `json:"id"`
	Name               string                `json:"name"`
	Status             string                `json:"status"`
	DiskFormat         string                `json:"disk_format"`
	ContainerFormat    string                `json:"container_format"`
	VmUsername         string                `json:"vm_username"`
	CreatedAt          string                `json:"created_at"`
	UpdatedAt          string                `json:"updated_at"`
	Visibility         string                `json:"visibility"`
	Self               string                `json:"self"`
	Checksum           string                `json:"checksum"`
	MinDisk            int                   `json:"min_disk"`
	Size               int64                 `json:"size"`
	MinRam             int                   `json:"min_ram"`
	Protect            bool                  `json:"protected"`
	Owner              string                `json:"owner"`
	OsType             string                `json:"os_type"`
	OsVersion          string                `json:"os_version"`
	WindowsAutoLogon   string                `json:"windows_auto_logon"`
	VmwareAdaptertype  string                `json:"vmware_adaptertype"`
	VmwareDisktype     string                `json:"vmware_disktype"`
	HwDiskBus          string                `json:"hw_disk_bus"`
	ServiceMarketUse   string                `json:"service_market_use"`
	ImageType          string                `json:"image_type"`
	AllowEncryption    string                `json:"allow_encryption"`
	Code               string                `json:"code"`
	IronicUse          string                `json:"ironic_use"`
	ProductKey         string                `json:"product_key"`
	Links              []HyperMediaReference `json:"links"`
	VirtualSize        int                   `json:"virtual_size"`
	BlockDeviceMapping string                `json:"block_device_mapping"`
	MissTimes          int                   `json:"missTimes"`
	EncryptTag         string                `json:"encrypt_tag"`
	UsedReference      string                `json:"used_reference"`
	CombinedChunksSize string                `json:"combined_chunks_size"`
	SizeText           string                `json:"size_text"`
	Locations          []ImageLocation       `json:"locations"`
	Description        string                `json:"description"`
	IronicImageType    string                `json:"ironic_image_type"`
	Metadata           map[string]string     `json:"metadata"`
	LastUsedAt         string                `json:"last_used_at,omitempty"`
	CloudEnvId         string                `json:"cloudEnvId"`
	ParentDN           map[string][]string   `json:"parentDn"`
}

type ImageLocation struct {
	Url string `json:"url"`
}

type HyperMediaReference struct {
	Uri string `json:"uri"`
	Rel string `json:"rel"`
}
