package image_redis

import (
	"fmt"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
	"cwsm/tools/commontools/storage/rediz/storage/cloud_env_redis"
	"zte.com.cn/dexcloud/go-mars/logger"
)

func Get(envId string, imageId string) (*Image4Redis, error) {
	image := &Image4Redis{}
	err := rediz.HGetInstance(fmt.Sprintf(storage.RedisKeyFmtImage, envId), imageId, image)
	return image, err
}

func GetAll() ([]*Image4Redis, error) {
	images := []*Image4Redis{}
	envs, err := cloud_env_redis.GetAll()
	if err != nil {
		logger.Errorf("image_redis get all env error %v", err)
		return images, err
	}

	for _, env := range envs {
		curImages := []*Image4Redis{}
		err := rediz.HGetAllInstances(fmt.Sprintf(storage.RedisKeyFmtImage, env.ID), curImages)
		if err != nil {
			logger.Errorf("image_redis ListByEnvId error %v", err)
			return images, err
		}
		images = append(images, curImages...)
	}
	return images, err
}

func Set(envId string, imageId string, image4Redis *Image4Redis) error {
	return rediz.HSet(fmt.Sprintf(storage.RedisKeyFmtImage, envId), imageId, image4Redis)
}

func Delete(envId string, imageId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtImage, envId), imageId)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtImage, envId))
}
