package az_redis

import (
	"fmt"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Delete(envId string, azId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtAZ, envId), azId)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtAZ, envId))
}

func GetAvailabilityZonesById(envId string) ([]*AvailabilityZoneInfo, error) {
	if len(envId) == 0 {
		return nil, fmt.Errorf("empty az id")
	}

	az := []*AvailabilityZoneInfo{}
	if err := rediz.HGetAllInstances(fmt.Sprintf(storage.RedisKeyFmtAZ, envId), az); err != nil {
		return nil, fmt.Errorf("get az list under cloud %s, failed: %v", envId, err.<PERSON><PERSON><PERSON>())
	}

	return az, nil
}
