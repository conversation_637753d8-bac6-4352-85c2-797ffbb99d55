package vdc_redis

import (
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func GetVdcs() ([]*Vdc4Redis, error) {
	vdcs := []*Vdc4Redis{}
	err := rediz.HGetAllInstances(storage.RedisKeyVdcs, &vdcs)
	return vdcs, err
}

func GetVdc(vdcId string) (*Vdc4Redis, error) {
	vdc := &Vdc4Redis{}
	err := rediz.HGetInstance(storage.RedisKeyVdcs, vdcId, vdc)
	return vdc, err
}

func Set(vdcId string, vdc4Redis *Vdc4Redis) error {
	return rediz.HSet(storage.RedisKeyVdcs, vdcId, vdc4Redis)
}

func GetAllVdcIds() ([]string, error) {
	vdcIds, err := rediz.HGetKeys(storage.RedisKeyVdcs)
	return vdcIds, err
}
