package snapshot_redis

type Snapshot4Redis struct {
	Name           string                 `json:"name"`
	Description    string                 `json:"description"`
	CloudEnvID     string                 `json:"cloudEnvId"`
	VolumeID       string                 `json:"volumeId"`
	Size           int                    `json:"size"`
	Status         string                 `json:"status"`
	CreatedAt      string                 `json:"created_at"`
	Metadata       map[string]interface{} `json:"metadata"`
	Progress       string                 `json:"progress"`
	ProjectID      string                 `json:"project_id"`
	UpdatedAt      string                 `json:"updated_at"`
	EnvName        string                 `json:"envName"`
	VdcID          string                 `json:"vdcId"`
	VdcName        string                 `json:"vdcName"`
	TenantName     string                 `json:"tenantName"`
	DcID           string                 `json:"dcId"`
	DcName         string                 `json:"dcName"`
	Grade          string                 `json:"grade"`
	VolumeName     string                 `json:"volumeName"`
	VolumeType     string                 `json:"volumeType"`
	VolumeBootable bool                   `json:"volumeBootable"`
	ID             string                 `json:"id"`
}
