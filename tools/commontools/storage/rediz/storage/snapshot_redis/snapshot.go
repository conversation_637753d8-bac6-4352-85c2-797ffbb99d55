package snapshot_redis

import (
	"fmt"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Delete(envId string, spId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtSnapshot, envId), spId)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtSnapshot, envId))
}

func GetSnapshots(envId string, sIds []string) ([]*Snapshot4Redis, error) {
	snapshots4Redis := []*Snapshot4Redis{}
	err := rediz.HMGetInstances(fmt.Sprintf(storage.RedisKeyFmtSnapshot, envId), &snapshots4Redis, sIds...)
	if err != nil {
		logger.Errorf("snapshot_redis envId:%s getSnapshots ids:(%s) error:%v", envId, util.ToJSONStr(sIds), err)
		return nil, err
	}
	return snapshots4Redis, nil
}
