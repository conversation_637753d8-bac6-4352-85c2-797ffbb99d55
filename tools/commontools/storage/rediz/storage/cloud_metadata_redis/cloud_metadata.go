package nova_redis

import (
	"fmt"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Delete(envId string, key string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtEnvMetadata, envId), key)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtEnvMetadata, envId))
}

func GetHostUsageInfoById(envId string) (*HostUsageInfo, error) {
	if len(envId) == 0 {
		return nil, fmt.Errorf("empty env id")
	}

	hu := &HostUsageInfo{}
	if err := rediz.HGetInstance(fmt.Sprintf(RedisKey4MetaData, envId), fmt.Sprintf(RedisField4HostUsage, envId), hu); err != nil {
		return nil, err
	}

	return hu, nil
}

func GetBackendUsageInfoById(envId string) (*BackendUsageInfo, error) {
	if len(envId) == 0 {
		return nil, fmt.Errorf("empty env id")
	}

	bku := &BackendUsageInfo{}
	if err := rediz.HGetInstance(fmt.Sprintf(RedisKey4MetaData, envId), fmt.Sprintf(RedisField4BackendUsage, envId), bku); err != nil {
		return nil, err
	}

	return bku, nil
}

func GetHostStatisticsInfoById(envId string) (*HostStatisticsInfo, error) {
	if len(envId) == 0 {
		return nil, fmt.Errorf("empty env id")
	}

	hs := &HostStatisticsInfo{}
	if err := rediz.HGetInstance(fmt.Sprintf(RedisKey4MetaData, envId), fmt.Sprintf(RedisField4Statistics, envId), hs); err != nil {
		return nil, err
	}

	return hs, nil
}
