package nova_redis

const (
	RedisKey4MetaData       = "%s_metadata"
	RedisField4HostUsage    = "%s_host_usage"
	RedisField4BackendUsage = "%s_backend_usage"
	RedisField4Statistics   = "%s_host_statistics"
	RedisField4VmStatistics = "%s_vm_statistics"
)

type ResourceSummary struct {
	Used           int     `json:"used"`
	Total          int     `json:"total"`
	RatioTotal     int     `json:"ratio_total"`
	Ratio          float64 `json:"ratio"`
	Reserved       float64 `json:"reserved"`
	TotalProvision float64 `json:"total_provision"`
	TotalUsed      float64 `json:"total_used"`
	Abnormal       int     `json:"abnormal"`
}

type AzHaSummary struct {
	Num int `json:"num"`
}

func (ah *AzHaSummary) Add(ahs *AzHaSummary) {
	if ahs == nil {
		return
	}
	ah.Num += ahs.Num
}

type DiscNetworkSummary struct {
	Total    int `json:"total"`
	Abnormal int `json:"abnormal"`
}

func (ds *DiscNetworkSummary) AddDiscOrNetwork(dn *DiscNetworkSummary) {
	if dn == nil {
		return
	}
	ds.Total += dn.Total
}

type HostSummary struct {
	VmSummary
	Up int `json:"upping"`
}

func (hs *HostSummary) AddHost(host *HostSummary) {
	if host == nil {
		return
	}
	hs.VmSummary.AddVm(&host.VmSummary)
	hs.Up += host.Up
}

type VmSummary struct {
	Total    int `json:"total"`
	Running  int `json:"running"`
	Stopped  int `json:"stopped"`
	Abnormal int `json:"abnormal"`
}

func (vs *VmSummary) CalculateStopped() {
	vs.Stopped = vs.Total - vs.Running
}

func (vs *VmSummary) AddVm(vm *VmSummary) {
	if vm == nil {
		return
	}
	vs.Total += vm.Total
	vs.Running += vm.Running
	vs.Stopped += vm.Stopped
	vs.Abnormal += vm.Abnormal
}

func (rs *ResourceSummary) Add(resource *ResourceSummary) {
	if resource == nil {
		return
	}
	rs.Used += resource.Used
	rs.Total += resource.Total
	rs.RatioTotal += resource.RatioTotal
	rs.Reserved += resource.Reserved
	rs.TotalProvision += resource.TotalProvision
	rs.TotalUsed += resource.TotalUsed
	rs.Abnormal += resource.Abnormal
}

type HostUsageInfo struct {
	CoreUsage *HostUsageSummary `json:"core"`
	MemUsage  *HostUsageSummary `json:"memory"`
	DiscUsage *HostUsageSummary `json:"disk"`
}

type HostUsageSummary struct {
	Occupied       int     `json:"occupied"`
	Total          int     `json:"total"`
	Ratio          float64 `json:"ratio"`
	Provisioned    int     `json:"provisioned"`
	Reserved       float64 `json:"reserved"`
	TotalProvision float64 `json:"total_provision"`
	TotalUsed      float64 `json:"total_used"`
}

func (hu *HostUsageSummary) ToResourceSummary() *ResourceSummary {
	rs := &ResourceSummary{
		Used:           hu.Occupied,
		Total:          hu.Total,
		RatioTotal:     hu.Provisioned,
		Ratio:          hu.Ratio,
		Reserved:       hu.Reserved,
		TotalProvision: hu.TotalProvision,
		TotalUsed:      hu.TotalUsed,
	}
	return rs
}

type HostStatisticsInfo struct {
	Total   int `json:"total"`
	Running int `json:"running"`
	Up      int `json:"upping"`
	Stopped int `json:"stopped"`
}

func (hs *HostStatisticsInfo) ToHostSummary() *HostSummary {
	res := &HostSummary{
		VmSummary: VmSummary{
			Total:   hs.Total,
			Running: hs.Running,
			Stopped: hs.Stopped,
		},
		Up: hs.Up,
	}
	return res
}

type BackendUsageInfo struct {
	ISCSIShareStorage *StorageUsage `json:"iSCSIShareStorage"`
	FCShareStorage    *StorageUsage `json:"FCShareStorage"`
	OtherShareStorage *StorageUsage `json:"otherShareStorage"`
	LocalStorage      *StorageUsage `json:"localStorage"`
	AllStorage        *StorageUsage `json:"allStorage"`
}

type StorageUsage struct {
	Protocol    string `json:"protocol"`
	Total       int    `json:"total"`
	Provisioned int    `json:"provisioned"`
	Used        int    `json:"used"`
	Available   int    `json:"canBeUsed"`
	RatioTotal  int    `json:"ratio_total"`
}

func (su *StorageUsage) ToResourceSummary(envType string) *ResourceSummary {
	var rs *ResourceSummary
	if envType == "openstack" || envType == "iecs" {
		rs = &ResourceSummary{
			Used:           su.Provisioned,
			Total:          su.Total,
			RatioTotal:     su.RatioTotal,
			Reserved:       0,
			TotalProvision: float64(su.RatioTotal),
			TotalUsed:      float64(su.Provisioned),
		}
	} else {
		rs = &ResourceSummary{
			Used:           su.Used,
			Total:          su.Total,
			RatioTotal:     su.RatioTotal,
			TotalProvision: float64(su.RatioTotal),
			TotalUsed:      float64(su.Used),
		}
	}
	return rs
}
