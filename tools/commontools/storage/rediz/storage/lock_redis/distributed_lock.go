package lock_redis

import (
	"context"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"time"
)

const (

	// 先get获取，如果有就刷新ttl，没有再set。 这种是可重入锁，防止在同一线程中多次获取锁而导致死锁发生。
	LockCommand = `if redis.call("GET", KEYS[1]) == ARGV[1] then
			redis.call("SET", KEYS[1], ARGV[1], "PX", ARGV[2])
			return "OK"
		 else
			return redis.call("SET", KEYS[1], ARGV[1], "NX", "PX", ARGV[2])
         end`

	// 删除。必须先匹配id值，防止A超时后，B马上获取到锁，A的解锁把B的锁删了
	DelCommand = `if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
 		 else
			return 0
		 end`

	// 刷新ttl。必须先匹配id值
	UpdatePxCommand = `if redis.call("GET", KEYS[1]) == ARGV[1] then
			redis.call("SET", KEYS[1], ARGV[1], "PX", ARGV[2])
			return "OK"
		 else
	  		return 0
		 end`

	// 获取ttl
	GetPxCommand = `return redis.call("TTL", KEYS[1])`

	letters   = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	randomLen = 10
)

var (
	//redisDb *redis.Client
	// 默认超时时间
	defaultTimeout = 500 * time.Millisecond
	//// 重试间隔
	retryInterval = 1000 * time.Millisecond
	// 上下文取消
	ErrContextCancel = errors.New("context cancel")
)

type RedisLock struct {
	ctx        context.Context
	Cancel4ctx context.CancelFunc `json:"-"`

	TimeoutMs int    `json:"timeoutMs"`
	Key       string `json:"key"`
	Id        string `json:"id"`

	ctx4WatchDog       context.Context
	cancel4WatchDogCtx context.CancelFunc
}

func NewRedisLock(ctx context.Context, cancel4ctx context.CancelFunc, key string) *RedisLock {
	timeout := defaultTimeout
	if deadline, ok := ctx.Deadline(); ok {
		timeout = deadline.Sub(time.Now())
	}

	rl := &RedisLock{
		ctx:        ctx,
		TimeoutMs:  int(timeout.Milliseconds()),
		Key:        key,
		Id:         randomStr(randomLen),
		Cancel4ctx: cancel4ctx,
	}

	return rl
}

func (rl *RedisLock) TryLock() (bool, error) {
	t := strconv.Itoa(rl.TimeoutMs)

	resp, err := rediz.Eval(LockCommand, []string{rl.Key}, rl.Id, t)
	if err != nil {
		errMsg := fmt.Sprintf("TryLock LockCommand redisLock:%s failed, err:%v", util.ToJSONStr(rl), err)
		logger.Errorf(errMsg)
		return false, errors.New(errMsg)
	}
	if resp == nil {
		errMsg := fmt.Sprintf("TryLock LockCommand redisLock:%s failed, resp is nil", util.ToJSONStr(rl))
		logger.Errorf(errMsg)
		return false, errors.New(errMsg)
	}
	reply, ok := resp.(string)
	if ok && reply == "OK" {
		return true, nil
	}
	errMsg := fmt.Sprintf("TryLock LockCommand redisLock:%s failed, resp is not ok:%s",
		util.ToJSONStr(rl), util.ToJSONStr(resp))
	logger.Errorf(errMsg)
	return false, errors.New(errMsg)

}

func (rl *RedisLock) Lock() error {
	start_time := time.Now()
	logger.Infof(fmt.Sprintf("Lock redisLock:%s start:%s", util.ToJSONStr(rl), start_time.String()))
	for {
		select {
		case <-rl.ctx.Done():
			if rl.ctx4WatchDog != nil && rl.cancel4WatchDogCtx != nil {
				rl.cancel4WatchDogCtx()
			}
			logger.Errorf(fmt.Sprintf("Lock redisLock:%s, cost:%d failed, time out", util.ToJSONStr(rl),
				time.Since(start_time).Milliseconds()))
			return ErrContextCancel
		default:
			b, err := rl.TryLock()
			if b {
				logger.Infof(fmt.Sprintf("Lock redisLock:%s, cost:%d success", util.ToJSONStr(rl),
					time.Since(start_time).Milliseconds()))
				if rl.ctx4WatchDog == nil {
					rl.ctx4WatchDog, rl.cancel4WatchDogCtx = context.WithCancel(context.Background())
					go rl.startWatchDog()
				}
				return nil
			}
			logger.Infof(fmt.Sprintf("Lock redisLock:%s, cost:%d failed, err:%v, retry Interval:%s",
				util.ToJSONStr(rl), time.Since(start_time).Milliseconds(), err, retryInterval.String()))
			time.Sleep(retryInterval)
		}
	}
}

func (rl *RedisLock) Unlock() {
	start_time := time.Now()
	logger.Infof(fmt.Sprintf("Unlock redisLock:%s start:%s", util.ToJSONStr(rl), start_time.String()))
	if rl.cancel4WatchDogCtx != nil {
		rl.cancel4WatchDogCtx()
	}
	resp, err := rediz.Eval(DelCommand, []string{rl.Key}, rl.Id)
	if err != nil {
		logger.Errorf("Unlock DelCommand redisLock:%s, cost:%d, failed:%v", util.ToJSONStr(rl),
			time.Since(start_time).Milliseconds(), err)
	} else if resp == nil {
		logger.Errorf("Unlock DelCommand redisLock:%s, cost:%d, failed: resp is nil", util.ToJSONStr(rl),
			time.Since(start_time).Milliseconds())
	} else {
		logger.Infof("Unlock redisLock:%s, cost:%d, success", util.ToJSONStr(rl),
			time.Since(start_time).Milliseconds())
	}
}

func randomStr(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

/* Started by AICoder, pid:7dfe5c73fb8043f3a0386fa1e7e4a9b4 */
func (rl *RedisLock) startWatchDog() {
	period := time.Duration(rl.TimeoutMs/3) * time.Millisecond
	ticker := time.NewTicker(period)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			err := rl.updateRedisLock()
			if err != nil {
				logger.Errorf("watchDog redisLock:%s update failed:%v", util.ToJSONStr(rl), err)
				return
			}
		case <-rl.ctx4WatchDog.Done():
			rl.stopWatchDog()
			return
		}
	}
}

func (rl *RedisLock) updateRedisLock() error {
	resp, err := rediz.Eval(GetPxCommand, []string{rl.Key})
	if err != nil {
		return fmt.Errorf("GetPxCommand failed:%v", err)
	}
	if resp == nil {
		return errors.New("GetPxCommand failed resp is nil")
	}
	reply, ok := resp.(int64)
	if ok && reply == -2 {
		return errors.New("key is not exist")
	} else if ok && reply == -1 {
		return errors.New("ttl is not set")
	}

	resp, err = rediz.Eval(UpdatePxCommand, []string{rl.Key}, rl.Id, strconv.Itoa(rl.TimeoutMs))
	if err != nil {
		return fmt.Errorf("UpdatePxCommand failed:%v", err)
	}
	if resp == nil {
		return errors.New("UpdatePxCommand failed resp is nil")
	}
	reply1, ok1 := resp.(string)
	if ok1 && reply1 == "OK" {
		logger.Infof("watchDog redisLock:%s UpdatePxCommand success, old ttl is:%ds, new ttl is:%ds",
			util.ToJSONStr(rl), reply*1000, rl.TimeoutMs)
	} else {
		return fmt.Errorf("UpdatePxCommand failed: resp is not ok:%s", util.ToJSONStr(resp))
	}
	return nil
}

func (rl *RedisLock) stopWatchDog() {
	rl.ctx4WatchDog = nil
	rl.cancel4WatchDogCtx = nil
	logger.Infof("watchDog redisLock:%s end", util.ToJSONStr(rl))
}

/* Ended by AICoder, pid:7dfe5c73fb8043f3a0386fa1e7e4a9b4 */
