package cloud_token

import (
	"cwsm/tools/commontools/cloudtoken"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Get(key string) (*cloudtoken.Dao, error) {
	dao := &cloudtoken.Dao{}
	err := rediz.HGetInstance(storage.RedisKeyToken, key, dao)
	return dao, err
}

func Set(key string, dao *cloudtoken.Dao) error {
	err := rediz.HSet(storage.RedisKeyToken, key, dao)
	return err
}

func Delete(key string) bool {
	return rediz.HDel(storage.RedisKeyToken, key)
}

func GetAll() ([]*cloudtoken.Dao, error) {
	daos := []*cloudtoken.Dao{}
	err := rediz.HGetAllInstances(storage.RedisKeyToken, &daos)
	return daos, err
}

func GetAllKeys() ([]string, error) {
	return rediz.HKeys(storage.RedisKeyToken)
}
