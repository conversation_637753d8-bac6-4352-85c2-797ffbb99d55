package device_profile_redis

import (
	"fmt"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
	"zte.com.cn/dexcloud/go-mars/logger"
)

func Delete(envId string, deviceProfileId string) bool {
	return rediz.HDel(fmt.Sprintf(storage.RedisKeyFmtDeviceProfile, envId), deviceProfileId)
}

func DeleteAll(envId string) bool {
	return rediz.Del(fmt.Sprintf(storage.RedisKeyFmtDeviceProfile, envId))
}

func GetAllByEnvId(envId string) ([]*DeviceProfileRedisDao, error) {
	deviceProfiles := []*DeviceProfileRedisDao{}
	key := fmt.Sprintf(storage.RedisKeyFmtDeviceProfile, envId)
	if rediz.ExistsAny(key) {
		err := rediz.HGetAllInstances(key, &deviceProfiles)
		if err != nil {
			logger.Errorf("deviceprofile_redis GetAll error %v", err)
			return nil, err
		}
	}
	return deviceProfiles, nil
}
