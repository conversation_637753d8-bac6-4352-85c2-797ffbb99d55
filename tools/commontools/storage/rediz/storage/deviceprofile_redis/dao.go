package device_profile_redis

type DeviceProfileRedisDao struct {
	Name       string              `json:"name"`
	Id         string              `json:"uuid"`
	Groups     []map[string]string `json:"groups"`
	CreatedAt  string              `json:"created_at"`
	UpdatedAt  string              `json:"updated_at"`
	Links      []*Link             `json:"links"`
	CloudEnvId string              `json:"cloudEnvId"`
}

type Link struct {
	Rel  string `json:"rel"`
	Href string `json:"href"`
}
