package fabric_group_redis

type FabricGroup4Redis struct {
	Name        string      `json:"name"`
	Level       string      `json:"level"`
	EnvType     string      `json:"envType"`
	Description string      `json:"description"`
	ID          string      `json:"id"`
	Resources   []Resources `json:"resources"`
}

type Azs struct {
	Name          string   `json:"name"`
	HostAggregate []string `json:"host_aggregate"`
}

type Computes struct {
	Azs []Azs `json:"azs"`
}

type Storages struct {
	VolumeIds []string `json:"volumeIds"`
}

type Networks struct {
	VLans []string `json:"vLans"`
}

type Resources struct {
	EnvID    string   `json:"envId"`
	Computes Computes `json:"computes"`
	Storages Storages `json:"storages"`
	Networks Networks `json:"networks"`
}

type ResourcePolicy struct {
	Name         string   `json:"name"`
	ResourceType []string `json:"resourceType"`
}
