package fabric_group_redis

import (
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Get(id string) (*FabricGroup4Redis, error) {
	fg4Redis := &FabricGroup4Redis{}
	err := rediz.HGetInstance(storage.RedisKeyFabricResource, id, fg4Redis)
	return fg4Redis, err
}

func GetAll() ([]*FabricGroup4Redis, error) {
	fgs4Redis := []*FabricGroup4Redis{}
	err := rediz.HGetAllInstances(storage.RedisKeyFabricResource, &fgs4Redis)
	return fgs4Redis, err
}

func Set(id string, fg *FabricGroup4Redis) error {
	return rediz.HSet(storage.RedisKeyFabricResource, id, fg)
}
