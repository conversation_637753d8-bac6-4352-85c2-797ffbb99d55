package vims_info_redis

type VimsInfo4Redis struct {
	// Example: 6bbcf88e-bf74-4e77-b094-b01580134988
	ID string `json:"id"`
	// Example: 7a5f823e-de38-43be-886f-77b0bd2c5829
	EnvID   string   `json:"envId"`
	Config  *Config  `json:"config"`
	Version *Version `json:"version"`
	// Example: v7.22.30.06
	PaasVersion string `json:"paas_version"`
}
type Config struct {
	// Example: edge cloud
	NorthType   string `json:"northType"`
	Description string `json:"description"`
	// Example: **************
	IP string `json:"ip"`
	// Example: 6bbcf88e-bf74-4e77-b094-b01580134988
	PimID    string `json:"pimId"`
	Location string `json:"location"`
	// Example: ZTE
	Integrator string `json:"integrator"`
	// Example: 6bbcf88e-bf74-4e77-b094-b01580134988
	ID string `json:"id"`
	// Example: default
	Name string `json:"name"`
}

type Version struct {
	// Example: V7.22.30.06
	BigVersionNo string `json:"bigVersionNo"`
	// Example: V7.22.30.06
	VersionNo string `json:"versionNo"`
	// Example: V7.22.30.06
	ShortVersionNo string `json:"shortVersionNo"`
	// Example: Fusionized 3rdparty-IaaS
	TcfScenario string `json:"tcfScenario"`
	// Example: k8s
	ServiceSet string `json:"serviceSet"`
	// Example: TECS CloudFoundation
	ProductName string `json:"productName"`
}
