package vims_info_redis

import (
	"fmt"
	"cwsm/tools/commontools/storage/rediz"
	"cwsm/tools/commontools/storage/rediz/storage"
)

func Get(key string) (*VimsInfo4Redis, error) {
	dao := &VimsInfo4Redis{}
	err := rediz.HGetInstance(storage.RedisKey4VimHash, key, dao)
	return dao, err
}

func GetByEnvId(envId string) (*VimsInfo4Redis, error) {
	vimsInfo, err := GetAll()
	if err != nil {
		return nil, fmt.Errorf("GetByEnvId GetAll failed:%v", err)
	}
	for _, vimInfo := range vimsInfo {
		if vimInfo.EnvID == envId {
			return vimInfo, nil
		}
	}
	return nil, fmt.Errorf("GetByEnvId failed: not found envId:%s", envId)
}

func GetAll() ([]*VimsInfo4Redis, error) {
	daos := []*VimsInfo4Redis{}
	err := rediz.HGetAllInstances(storage.RedisKey4VimHash, &daos)
	return daos, err
}

func Set(id string, vimdInfo *VimsInfo4Redis) error {
	return rediz.HSet(storage.RedisKey4VimHash, id, vimdInfo)
}

func Del(id string) bool {
	return rediz.HDel(storage.RedisKey4VimHash, id)
}
