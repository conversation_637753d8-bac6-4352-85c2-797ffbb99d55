package rediz

import (
	"fmt"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("test for local redis service", func() {

	BeforeEach(func() {
		Prepare4Redis()
	})

	// It("should return keys when conducting pattern search", func() {
	// 	err := HSet("fake_key", "fake_job_id", "fake_data_content")
	// 	Expect(err == nil).To(Equal(true))

	// 	info, _ := HGet("fake_key", "fake_job_id")
	// 	logger.Info("job info: %s", util.ToJSONStr(info))

	// 	var keys []string
	// 	keys = Keys("*_key")
	// 	//if err != nil {
	// 	//	logger.Error("[Scan] error: %v", err.Error())
	// 	//}
	// 	logger.Info("keys: %s", keys)
	// 	//Expect("fake_key").To(Equal(util.ToJSONStr(keys)))
	// })

	It("should Scan", func() {
		for i := 0; i < 100; i++ {
			set := client.Set(fmt.Sprintf("key%d", i), "hello", 0)
			Expect(set.Err()).NotTo(HaveOccurred())
		}

		_, _, err := client.Scan(0, "*", 10).Result()
		Expect(err).NotTo(HaveOccurred())
		//Expect(keys).NotTo(BeEmpty())
		//Expect(cursor).NotTo(BeZero())
	})

})
