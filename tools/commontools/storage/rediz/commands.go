package rediz

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
	"cwsm/tools/commontools/logger"
)

var DefaultTimeOutInSecond = 2 * time.Second

func Ping() error {
	if client == nil {
		logger.Errorf("Ping() error: redis client is nil")
		return fmt.Errorf("redis client is nil")
	}
	res, err := client.Ping().Result()
	if err != nil {
		logger.Errorf("Ping() error: %v", err)
		return err
	}
	logger.Debugf("Ping() response: %s", res)
	return nil
}

func Keys(pattern string) []string {
	res, err := client.Keys(pattern).Result()
	if err != nil {
		logger.Errorf("Keys(%s) error: %v", pattern, err)
		return []string{}
	}
	return res
}

func Scan(cursor uint64, match string, count int64) ([]string, uint64, error) {
	keys := []string{}
	var err error
	keys, cursor, err = client.Scan(cursor, match, count).Result()
	return keys, cursor, err
}

func ExistsAll(keys ...string) bool {
	res, err := client.Exists(keys...).Result()
	if err != nil {
		logger.Errorf("ExistsAll(%s) error: %v", strings.Join(keys, ","), err)
		return false
	}
	return res == int64(len(keys))
}

func ExistsAny(keys ...string) bool {
	res, err := client.Exists(keys...).Result()
	if err != nil {
		logger.Errorf("ExistsAny(%s) error: %v", strings.Join(keys, ","), err)
		return false
	}
	return res >= 1
}

func Del(keys ...string) bool {
	res, err := client.Del(keys...).Result()
	if err != nil {
		logger.Errorf("Del(%s) error: %v", strings.Join(keys, ","), err)
		return false
	}
	logger.Debug("Del key length: %d, successful key length: %d", res, len(keys))
	return res == int64(len(keys))
}

func Get(key string) (string, error) {
	value, err := client.Get(key).Result()
	if err != nil {
		logger.Errorf("Get(%s) error: %v", key, err)
	}
	return value, err
}

func GetInstance(key string, instance interface{}) error {
	bs, err := client.Get(key).Bytes()
	if err != nil {
		logger.Errorf("GetInstance(%s) error: %v", key, err)
		return err
	}
	return json.Unmarshal(bs, instance)
}

func Set(key string, value interface{}) error {
	_, err := client.Set(key, marshalValue(value), -time.Second).Result()
	if err != nil {
		logger.Errorf("Set(%s) error: %v", key, err)
		return err
	}
	return nil
}

func SetWithExpiration(key string, value interface{}, expiration int) error {
	temp := marshalValue(value)
	_, err := client.Set(key, temp, time.Duration(expiration)*time.Minute).Result()
	if err != nil {
		logger.Error("Set(%s) error: %v", key, err)
		return err
	}
	return nil
}

func SetKeyExpiration(key string, span time.Duration) (bool, error) {
	return client.Expire(key, span).Result()
}

func HGet(key, field string) (string, error) {
	res, err := client.HGet(key, field).Result()
	if err != nil {
		logger.Errorf("HGet(key:%s, field:%s) error: %v", key, field, err)
		return "", err
	}
	return res, nil
}

func HGetKeys(key string) ([]string, error) {
	res, err := client.HKeys(key).Result()
	if err != nil {
		logger.Errorf("HGetKeys(key:%s) error: %v", key, err)
		return nil, err
	}
	return res, nil
}

func HGetInstance(key, field string, instance interface{}) error {
	bs, err := client.HGet(key, field).Bytes()
	if err != nil {
		logger.Debugf("HGetInstance(key:%s, field:%s) error: %v", key, field, err)
		return err
	}
	return json.Unmarshal(bs, instance)
}

func HGetAll(key string) (map[string]string, error) {
	res, err := client.HGetAll(key).Result()
	if err != nil {
		logger.Errorf("HGetAll(%s) error: %v", key, err)
		return nil, err
	}
	return res, nil
}

func HGetAllInstancesIgnoreValueIsEmpty(key string, instances interface{}) error {
	bs := []byte("[")
	ms, err := client.HGetAll(key).Result()
	if err != nil {
		logger.Errorf("HGetAllInstancesIgnoreValueIsEmpty(%s) error: %v", key, err)
		return err
	}
	for _, val := range ms {
		if val != "" {
			bs = append(bs, []byte(val+",")...)
		}
	}
	if len(bs) == 1 {
		logger.Warnf("HGetAllInstancesIgnoreValueIsEmpty(%s) empty hash", key)
		return nil
	}
	bs[len(bs)-1] = ']'
	logger.Debugf("HGetAllInstancesIgnoreValueIsEmpty bs (%s)", bs)
	if err = json.Unmarshal(bs, instances); err != nil {
		logger.Errorf("HGetAllInstancesIgnoreValueIsEmpty(%s) unmarshal error: %v", key, err)
		return fmt.Errorf("unmarshal error: %v", err)
	}
	return nil
}

func HGetAllInstances(key string, instances interface{}) error {
	bs := []byte("[")
	ms, err := client.HGetAll(key).Result()
	if err != nil {
		logger.Errorf("HGetAllInstances(%s) error: %v", key, err)
		return err
	}
	for _, val := range ms {
		bs = append(bs, []byte(val+",")...)
	}
	if len(bs) == 1 {
		logger.Warnf("HGetAllInstances(%s) empty hash", key)
		return nil
	}
	bs[len(bs)-1] = ']'
	if err = json.Unmarshal(bs, instances); err != nil {
		logger.Errorf("HGetAllInstances(%s) unmarshal error: %v", key, err)
		return fmt.Errorf("unmarshal error: %v", err)
	}
	return nil
}

func HMGet(key string, fields ...string) ([]interface{}, error) {
	res, err := client.HMGet(key, fields...).Result()
	if err != nil {
		logger.Errorf("HMGet(key:%s, fields:%s) error: %v", key, strings.Join(fields, ","), err)
		return nil, err
	}
	return res, nil
}

func HMGetInstances(key string, instances interface{}, fields ...string) error {
	res, err := client.HMGet(key, fields...).Result()
	if err != nil {
		logger.Errorf("HMGetInstances(key:%s, fields:%s) error: %v", key, strings.Join(fields, ","), err)
		return err
	}
	bs := []byte{'['}
	for _, in := range res {
		if s, ok := in.(string); ok {
			bs = append(bs, []byte(s+",")...)
		}
	}
	if len(bs) == 1 {
		logger.Warnf("HMGetInstances(key:%s, fields:%s) empty result with requested fields", key, strings.Join(fields, ","))
		return nil
	}
	bs[len(bs)-1] = ']'
	if err = json.Unmarshal(bs, instances); err != nil {
		logger.Errorf("HMGetInstances(key:%s, fields:%s) unmarshal error: %v", key, strings.Join(fields, ","), err)
		return fmt.Errorf("unmarshal error: %v", err)
	}
	return nil
}

func HSet(key, field string, value interface{}) error {
	_, err := client.HSet(key, field, marshalValue(value)).Result()
	if err != nil {
		logger.Errorf("HSet(key:%s, field:%s) error: %v", key, field, err)
		return err
	}
	return nil
}

func HMSet(key string, fields map[string]interface{}) error {
	for k, v := range fields {
		fields[k] = marshalValue(v)
	}
	_, err := client.HMSet(key, fields).Result()
	if err != nil {
		logger.Errorf("HMSet(%s) error: %v", err)
		return err
	}
	return nil
}

func HDel(key string, fields ...string) bool {
	res, err := client.HDel(key, fields...).Result()
	if err != nil {
		logger.Errorf("HDel(key:%s, fields:%s) error: %v", key, strings.Join(fields, ","), err)
		return false
	}
	logger.Debug("HDel key length: %d, successful key length: %d", res, len(fields))
	return res == int64(len(fields))
}

func HKeys(key string) ([]string, error) {
	res, err := client.HKeys(key).Result()
	if err != nil {
		logger.Errorf("HKeys(%s) error: %v", key, err)
		return nil, err
	}
	return res, nil
}

func HExists(key string, field string) bool {
	res, err := client.HExists(key, field).Result()
	if err != nil {
		logger.Errorf("HExists(key: %s, filed: %s) error: %v", key, field, err)
		return false
	}
	return res
}

func HLen(key string) (int64, error) {
	res, err := client.HLen(key).Result()
	if err != nil {
		logger.Errorf("HLen(%s) error: %v", key, err)
		return -1, err
	}
	return res, nil
}

func LPush(key string, values ...interface{}) error {
	for i, v := range values {
		values[i] = marshalValue(v)
	}
	res, err := client.LPush(key, values...).Result()
	if err != nil || res != int64(len(values)) {
		logger.Errorf("LPush(%s) requested size: %d, successful size: %d, error: %v", key, len(values), res, err)
		return fmt.Errorf("partial success or exec cmd error: %v", err)
	}
	return nil
}

func RPush(key string, values ...interface{}) error {
	for i, v := range values {
		values[i] = marshalValue(v)
	}
	res, err := client.RPush(key, values...).Result()
	if err != nil || res != int64(len(values)) {
		logger.Errorf("RPush(%s) requested size: %d, successful size: %d, error: %v", key, len(values), res, err)
		return fmt.Errorf("partial success or exec cmd error: %v", err)
	}
	return nil
}

func BLPop(keys ...string) ([]string, error) {
	res, err := client.BLPop(DefaultTimeOutInSecond, keys...).Result()
	if err != nil {
		logger.Errorf("BLPop(%s) error: %v", strings.Join(keys, ","), err)
		return nil, err
	}
	return res, nil
}

func LPop(key string) (string, error) {
	res, err := client.LPop(key).Result()
	if err != nil {
		logger.Errorf("LPop(%s) error: %v", key, err)
		return "", err
	}
	return res, nil
}

func LPopInstance(key string, instance interface{}) error {
	bs, err := client.LPop(key).Bytes()
	if err != nil {
		logger.Errorf("LPopInstance(%s) error: %v", key, err)
		return err
	}
	if err = json.Unmarshal(bs, instance); err != nil {
		logger.Errorf("LPopInstance(%s) unmarshal instance error: %v", key, err)
		return err
	}
	return nil
}

func BRPop(keys ...string) ([]string, error) {
	res, err := client.BRPop(DefaultTimeOutInSecond, keys...).Result()
	if err != nil {
		logger.Errorf("BRPop(%s) error: %v", strings.Join(keys, ","), err)
		return nil, err
	}
	return res, nil
}

func RPop(key string) (string, error) {
	res, err := client.RPop(key).Result()
	if err != nil {
		logger.Errorf("RPop(%s) error: %v", key, err)
		return "", err
	}
	return res, nil
}

func RPopInstance(key string, instance interface{}) error {
	bs, err := client.RPop(key).Bytes()
	if err != nil {
		logger.Errorf("RPopInstance(%s) error: %v", key, err)
		return err
	}
	if err = json.Unmarshal(bs, instance); err != nil {
		logger.Errorf("RPopInstance(%s) unmarshal instance error: %v", key, err)
		return err
	}
	return nil
}

func LLen(key string) (int64, error) {
	res, err := client.LLen(key).Result()
	if err != nil {
		logger.Errorf("LLen(%s) error: %v", key, err)
		return -1, err
	}
	return res, nil
}

func LRange(key string, start, end int64) ([]string, error) {
	res, err := client.LRange(key, start, end).Result()
	if err != nil {
		logger.Errorf("LRange(%s, %d~%d) error: %v", key, start, end, err)
		return nil, err
	}
	return res, nil
}

func LRangeInstances(key string, start, end int64, instances interface{}) error {
	res, err := client.LRange(key, start, end).Result()
	if err != nil {
		logger.Errorf("LRangeInstances(%s, %d~%d) error: %v", key, start, end, err)
		return err
	}
	if err = listOfStringsToInstances(res, instances); err != nil {
		logger.Errorf("LRangeInstances(%s, %d~%d) string to instance error: %v", key, start, end, err)
		return err
	}
	return nil
}

func FlushAll() error {
	if _, err := client.FlushAll().Result(); err != nil {
		logger.Errorf("FlushAll() error: %v", err)
		return err
	}
	return nil
}

func marshalValue(value interface{}) interface{} {
	switch value.(type) {
	case []byte, string, uint8, uint16, uint32, uint64, int8, int16, int32, int64, float32, float64, nil:
		return value
	case bool:
		return fmt.Sprintf("%v", value)
	default:
		bs, err := json.Marshal(value)
		if err != nil {
			logger.Errorf("[marshalValue] json.Marshal error:", err.Error())
			return ""
		}
		return bs
	}
}

func listOfStringsToInstances(strs []string, instances interface{}) error {
	if len(strs) == 0 {
		logger.Warnf("[listOfStringsToInstances] empty list of strings")
		return nil
	}
	bs := []byte{'['}
	for _, s := range strs {
		bs = append(bs, []byte(s+",")...)
	}
	bs[len(bs)-1] = ']'
	if err := json.Unmarshal(bs, instances); err != nil {
		logger.Errorf("[listOfStringsToInstances] unmarshal error: %v", err)
		return fmt.Errorf("unmarshal error: %v", err)
	}
	return nil
}

func Eval(script string, keys []string, args ...string) (interface{}, error) {
	return client.Eval(script, keys, args).Result()
}
