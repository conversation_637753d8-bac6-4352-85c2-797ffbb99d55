package rediz

import (
	"encoding/json"
	"fmt"
	"github.com/go-redis/redis"
	"regexp"
	"strings"
	"sync"
	"time"
)

type memClient struct {
	mu sync.Mutex

	keys   map[string]struct{}
	strs   map[string]string
	hashes map[string]map[string]string
}

func (mcli *memClient) Pipeline() redis.Pipeliner {
	return nil
}

func (mcli *memClient) Pipelined(fn func(redis.Pipeliner) error) ([]redis.Cmder, error) {
	return nil, nil
}

func (mcli *memClient) TxPipelined(fn func(redis.Pipeliner) error) ([]redis.Cmder, error) {
	return nil, nil
}

func (mcli *memClient) TxPipeline() redis.Pipeliner {
	return nil
}

func (mcli *memClient) Command() *redis.CommandsInfoCmd {
	return nil
}

func (mcli *memClient) ClientGetName() *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) Echo(message interface{}) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) Ping() *redis.StatusCmd {
	return redis.NewStatusResult("PONG", nil)
}

func (mcli *memClient) Quit() *redis.StatusCmd {
	return redis.NewStatusResult("OK", nil)
}

func (mcli *memClient) Del(keys ...string) *redis.IntCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()

	count := 0
	for _, k := range keys {
		delete(mcli.keys, k)
		if _, ok := mcli.strs[k]; ok {
			count++
			delete(mcli.strs, k)
		}
		if _, ok := mcli.hashes[k]; ok {
			count++
			delete(mcli.hashes, k)
		}
	}
	return redis.NewIntResult(int64(count), nil)
}

func (mcli *memClient) Unlink(keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) Dump(key string) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) Exists(keys ...string) *redis.IntCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	count := 0
	for _, key := range keys {
		if _, ok := mcli.keys[key]; ok {
			count++
		}
	}
	return redis.NewIntResult(int64(count), nil)
}

func (mcli *memClient) Expire(key string, expiration time.Duration) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) ExpireAt(key string, tm time.Time) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) Keys(pattern string) *redis.StringSliceCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	values := []string{}
	pattern1 := strings.Replace(pattern, "*", ".*", -1)
	for key, _ := range mcli.keys {
		match, _ := regexp.MatchString(pattern1, key)
		if match {
			values = append(values, key)
		}
	}
	return redis.NewStringSliceResult(values, nil)
}

func (mcli *memClient) Migrate(host, port, key string, db int64, timeout time.Duration) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) Move(key string, db int64) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) ObjectRefCount(key string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ObjectEncoding(key string) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) ObjectIdleTime(key string) *redis.DurationCmd {
	return &redis.DurationCmd{}
}

func (mcli *memClient) Persist(key string) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) PExpire(key string, expiration time.Duration) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) PExpireAt(key string, tm time.Time) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) PTTL(key string) *redis.DurationCmd {
	return &redis.DurationCmd{}
}

func (mcli *memClient) RandomKey() *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) Rename(key, newkey string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) RenameNX(key, newkey string) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) Restore(key string, ttl time.Duration, value string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) RestoreReplace(key string, ttl time.Duration, value string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) Sort(key string, sort *redis.Sort) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) SortStore(key, store string, sort *redis.Sort) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) SortInterfaces(key string, sort *redis.Sort) *redis.SliceCmd {
	return &redis.SliceCmd{}
}

func (mcli *memClient) Touch(keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) TTL(key string) *redis.DurationCmd {
	return &redis.DurationCmd{}
}

func (mcli *memClient) Type(key string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) Scan(cursor uint64, match string, count int64) *redis.ScanCmd {
	return &redis.ScanCmd{}
}

func (mcli *memClient) SScan(key string, cursor uint64, match string, count int64) *redis.ScanCmd {
	return &redis.ScanCmd{}
}

func (mcli *memClient) HScan(key string, cursor uint64, match string, count int64) *redis.ScanCmd {
	return &redis.ScanCmd{}
}

func (mcli *memClient) ZScan(key string, cursor uint64, match string, count int64) *redis.ScanCmd {
	return &redis.ScanCmd{}
}

func (mcli *memClient) Append(key, value string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) BitCount(key string, bitCount *redis.BitCount) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) BitOpAnd(destKey string, keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) BitOpOr(destKey string, keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) BitOpXor(destKey string, keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) BitOpNot(destKey string, key string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) BitPos(key string, bit int64, pos ...int64) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) Decr(key string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) DecrBy(key string, decrement int64) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) Get(key string) *redis.StringCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	val, ok := mcli.strs[key]
	if !ok {
		return redis.NewStringResult("", fmt.Errorf("key %s not found", key))
	}
	return redis.NewStringResult(val, nil)
}

func (mcli *memClient) GetBit(key string, offset int64) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) GetRange(key string, start, end int64) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) GetSet(key string, value interface{}) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) Incr(key string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) IncrBy(key string, value int64) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) IncrByFloat(key string, value float64) *redis.FloatCmd {
	return &redis.FloatCmd{}
}

func (mcli *memClient) MGet(keys ...string) *redis.SliceCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	values := []interface{}{}
	for _, k := range keys {
		if val, ok := mcli.strs[k]; ok {
			values = append(values, val)
		}
	}
	return redis.NewSliceResult(values, nil)
}

func (mcli *memClient) MSet(pairs ...interface{}) *redis.StatusCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	return &redis.StatusCmd{}
}

func (mcli *memClient) MSetNX(pairs ...interface{}) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) Set(key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()

	mcli.keys[key] = struct{}{}
	mcli.strs[key] = toString(value)
	return redis.NewStatusResult("OK", nil)
}

func (mcli *memClient) SetBit(key string, offset int64, value int) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) SetNX(key string, value interface{}, expiration time.Duration) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) SetXX(key string, value interface{}, expiration time.Duration) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) SetRange(key string, offset int64, value string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) StrLen(key string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) HDel(key string, fields ...string) *redis.IntCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()

	vals, ok := mcli.hashes[key]
	if !ok {
		return redis.NewIntResult(0, nil)
	}
	count := 0
	for _, f := range fields {
		if _, ok = vals[f]; ok {
			count++
			delete(mcli.hashes[key], f)
		}
	}
	if len(mcli.hashes[key]) == 0 {
		delete(mcli.keys, key)
		delete(mcli.hashes, key)
	}
	return redis.NewIntResult(int64(count), nil)

}

func (mcli *memClient) HExists(key, field string) *redis.BoolCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	vals, ok := mcli.hashes[key]
	if !ok {
		return redis.NewBoolResult(false, nil)
	}
	if _, ok = vals[field]; ok {
		return redis.NewBoolResult(true, nil)
	}
	return redis.NewBoolResult(false, nil)
}

func (mcli *memClient) HGet(key, field string) *redis.StringCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	vals, ok := mcli.hashes[key]
	if !ok {
		return redis.NewStringResult("", fmt.Errorf("key %s not found", key))
	}
	if val, ok := vals[field]; ok {
		return redis.NewStringResult(val, nil)
	}
	return redis.NewStringResult("", fmt.Errorf("field %s not found in hashes with key %s", field, key))
}

func (mcli *memClient) HGetAll(key string) *redis.StringStringMapCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	vals, ok := mcli.hashes[key]
	if !ok {
		return redis.NewStringStringMapResult(nil, fmt.Errorf("key %s not found", key))
	}
	return redis.NewStringStringMapResult(vals, nil)
}

func (mcli *memClient) HIncrBy(key, field string, incr int64) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) HIncrByFloat(key, field string, incr float64) *redis.FloatCmd {
	return &redis.FloatCmd{}
}

func (mcli *memClient) HKeys(key string) *redis.StringSliceCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	vals, ok := mcli.hashes[key]
	if !ok {
		return redis.NewStringSliceResult(nil, fmt.Errorf("key %s not found", key))
	}
	ks := []string{}
	for k := range vals {
		ks = append(ks, k)
	}
	return redis.NewStringSliceResult(ks, nil)
}

func (mcli *memClient) HLen(key string) *redis.IntCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	vals, ok := mcli.hashes[key]
	if !ok {
		return redis.NewIntResult(0, fmt.Errorf("key %s not found", key))
	}
	return redis.NewIntResult(int64(len(vals)), nil)
}

func (mcli *memClient) HMGet(key string, fields ...string) *redis.SliceCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	vals, ok := mcli.hashes[key]
	if !ok {
		return redis.NewSliceResult(nil, fmt.Errorf("key %s not found", key))
	}
	vs := []interface{}{}
	for _, f := range fields {
		if v, ok := vals[f]; ok {
			vs = append(vs, v)
		}
	}
	return redis.NewSliceResult(vs, nil)
}
func (mcli *memClient) HMSet(key string, fields map[string]interface{}) *redis.StatusCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()

	if _, ok := mcli.hashes[key]; !ok {
		mcli.hashes[key] = map[string]string{}
		mcli.keys[key] = struct{}{}
	}
	for f, v := range fields {
		mcli.hashes[key][f] = toString(v)
	}
	return redis.NewStatusResult("OK", nil)
}

func (mcli *memClient) HSet(key, field string, value interface{}) *redis.BoolCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()

	mcli.keys[key] = struct{}{}
	if _, ok := mcli.hashes[key]; !ok {
		mcli.hashes[key] = map[string]string{}
	}
	mcli.hashes[key][field] = toString(value)
	return redis.NewBoolResult(true, nil)
}

func (mcli *memClient) HSetNX(key, field string, value interface{}) *redis.BoolCmd {
	return mcli.HSet(key, field, value)
}
func (mcli *memClient) HVals(key string) *redis.StringSliceCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()
	vals, ok := mcli.hashes[key]
	if !ok {
		return redis.NewStringSliceResult(nil, fmt.Errorf("key %s not found", key))
	}
	vs := []string{}
	for _, v := range vals {
		vs = append(vs, v)
	}
	return redis.NewStringSliceResult(vs, nil)
}

func (mcli *memClient) BLPop(timeout time.Duration, keys ...string) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) BRPop(timeout time.Duration, keys ...string) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) BRPopLPush(source, destination string, timeout time.Duration) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) LIndex(key string, index int64) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) LInsert(key, op string, pivot, value interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) LInsertBefore(key string, pivot, value interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) LInsertAfter(key string, pivot, value interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) LLen(key string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) LPop(key string) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) LPush(key string, values ...interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) LPushX(key string, value interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}
func (mcli *memClient) LRange(key string, start, stop int64) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) LRem(key string, count int64, value interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) LSet(key string, index int64, value interface{}) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) LTrim(key string, start, stop int64) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) RPop(key string) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) RPopLPush(source, destination string) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) RPush(key string, values ...interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) RPushX(key string, value interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}
func (mcli *memClient) SAdd(key string, members ...interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) SCard(key string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) SDiff(keys ...string) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) SDiffStore(destination string, keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) SInter(keys ...string) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) SInterStore(destination string, keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) SIsMember(key string, member interface{}) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) SMembers(key string) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) SMembersMap(key string) *redis.StringStructMapCmd {
	return &redis.StringStructMapCmd{}
}

func (mcli *memClient) SMove(source, destination string, member interface{}) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) SPop(key string) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) SPopN(key string, count int64) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) SRandMember(key string) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) SRandMemberN(key string, count int64) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) SRem(key string, members ...interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) SUnion(keys ...string) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) SUnionStore(destination string, keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) XAdd(a *redis.XAddArgs) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) XDel(stream string, ids ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) XLen(stream string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) XRange(stream, start, stop string) *redis.XMessageSliceCmd {
	return &redis.XMessageSliceCmd{}
}

func (mcli *memClient) XRangeN(stream, start, stop string, count int64) *redis.XMessageSliceCmd {
	return &redis.XMessageSliceCmd{}
}

func (mcli *memClient) XRevRange(stream string, start, stop string) *redis.XMessageSliceCmd {
	return &redis.XMessageSliceCmd{}
}

func (mcli *memClient) XRevRangeN(stream string, start, stop string, count int64) *redis.XMessageSliceCmd {
	return &redis.XMessageSliceCmd{}
}

func (mcli *memClient) XRead(a *redis.XReadArgs) *redis.XStreamSliceCmd {
	return &redis.XStreamSliceCmd{}
}

func (mcli *memClient) XReadStreams(streams ...string) *redis.XStreamSliceCmd {
	return &redis.XStreamSliceCmd{}
}

func (mcli *memClient) XGroupCreate(stream, group, start string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) XGroupCreateMkStream(stream, group, start string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) XGroupSetID(stream, group, start string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) XGroupDestroy(stream, group string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) XGroupDelConsumer(stream, group, consumer string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) XReadGroup(a *redis.XReadGroupArgs) *redis.XStreamSliceCmd {
	return &redis.XStreamSliceCmd{}
}

func (mcli *memClient) XAck(stream, group string, ids ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) XPending(stream, group string) *redis.XPendingCmd {
	return &redis.XPendingCmd{}
}

func (mcli *memClient) XPendingExt(a *redis.XPendingExtArgs) *redis.XPendingExtCmd {
	return &redis.XPendingExtCmd{}
}

func (mcli *memClient) XClaim(a *redis.XClaimArgs) *redis.XMessageSliceCmd {
	return &redis.XMessageSliceCmd{}
}

func (mcli *memClient) XClaimJustID(a *redis.XClaimArgs) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) XTrim(key string, maxLen int64) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) XTrimApprox(key string, maxLen int64) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) BZPopMax(timeout time.Duration, keys ...string) *redis.ZWithKeyCmd {
	return &redis.ZWithKeyCmd{}
}

func (mcli *memClient) BZPopMin(timeout time.Duration, keys ...string) *redis.ZWithKeyCmd {
	return &redis.ZWithKeyCmd{}
}

func (mcli *memClient) ZAdd(key string, members ...redis.Z) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZAddNX(key string, members ...redis.Z) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZAddXX(key string, members ...redis.Z) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZAddCh(key string, members ...redis.Z) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZAddNXCh(key string, members ...redis.Z) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZAddXXCh(key string, members ...redis.Z) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZIncr(key string, member redis.Z) *redis.FloatCmd {
	return &redis.FloatCmd{}
}

func (mcli *memClient) ZIncrNX(key string, member redis.Z) *redis.FloatCmd {
	return &redis.FloatCmd{}
}

func (mcli *memClient) ZIncrXX(key string, member redis.Z) *redis.FloatCmd {
	return &redis.FloatCmd{}
}

func (mcli *memClient) ZCard(key string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZCount(key, min, max string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZLexCount(key, min, max string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZIncrBy(key string, increment float64, member string) *redis.FloatCmd {
	return &redis.FloatCmd{}
}

func (mcli *memClient) ZInterStore(destination string, store redis.ZStore, keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZPopMax(key string, count ...int64) *redis.ZSliceCmd {
	return &redis.ZSliceCmd{}
}

func (mcli *memClient) ZPopMin(key string, count ...int64) *redis.ZSliceCmd {
	return &redis.ZSliceCmd{}
}

func (mcli *memClient) ZRange(key string, start, stop int64) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) ZRangeWithScores(key string, start, stop int64) *redis.ZSliceCmd {
	return &redis.ZSliceCmd{}
}

func (mcli *memClient) ZRangeByScore(key string, opt redis.ZRangeBy) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) ZRangeByLex(key string, opt redis.ZRangeBy) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) ZRangeByScoreWithScores(key string, opt redis.ZRangeBy) *redis.ZSliceCmd {
	return &redis.ZSliceCmd{}
}

func (mcli *memClient) ZRank(key, member string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZRem(key string, members ...interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZRemRangeByRank(key string, start, stop int64) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZRemRangeByScore(key, min, max string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZRemRangeByLex(key, min, max string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZRevRange(key string, start, stop int64) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) ZRevRangeWithScores(key string, start, stop int64) *redis.ZSliceCmd {
	return &redis.ZSliceCmd{}
}

func (mcli *memClient) ZRevRangeByScore(key string, opt redis.ZRangeBy) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) ZRevRangeByLex(key string, opt redis.ZRangeBy) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) ZRevRangeByScoreWithScores(key string, opt redis.ZRangeBy) *redis.ZSliceCmd {
	return &redis.ZSliceCmd{}
}

func (mcli *memClient) ZRevRank(key, member string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ZScore(key, member string) *redis.FloatCmd {
	return &redis.FloatCmd{}
}

func (mcli *memClient) ZUnionStore(dest string, store redis.ZStore, keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) PFAdd(key string, els ...interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) PFCount(keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) PFMerge(dest string, keys ...string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) BgRewriteAOF() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) BgSave() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ClientKill(ipPort string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ClientKillByFilter(keys ...string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ClientList() *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) ClientPause(dur time.Duration) *redis.BoolCmd {
	return &redis.BoolCmd{}
}

func (mcli *memClient) ClientID() *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ConfigGet(parameter string) *redis.SliceCmd {
	return &redis.SliceCmd{}
}

func (mcli *memClient) ConfigResetStat() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ConfigSet(parameter, value string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ConfigRewrite() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) DBSize() *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) FlushAll() *redis.StatusCmd {
	mcli.mu.Lock()
	defer mcli.mu.Unlock()

	mcli.keys = map[string]struct{}{}
	mcli.strs = map[string]string{}
	mcli.hashes = map[string]map[string]string{}
	return redis.NewStatusResult("OK", nil)
}

func (mcli *memClient) FlushAllAsync() *redis.StatusCmd {
	return mcli.FlushAll()
}

func (mcli *memClient) FlushDB() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) FlushDBAsync() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) Info(section ...string) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) LastSave() *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) Save() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) Shutdown() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ShutdownSave() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ShutdownNoSave() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) SlaveOf(host, port string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) Time() *redis.TimeCmd {
	return &redis.TimeCmd{}
}

func (mcli *memClient) Eval(script string, keys []string, args ...interface{}) *redis.Cmd {
	return redis.NewCmdResult("OK", nil)
}

func (mcli *memClient) EvalSha(sha1 string, keys []string, args ...interface{}) *redis.Cmd {
	return &redis.Cmd{}
}

func (mcli *memClient) ScriptExists(hashes ...string) *redis.BoolSliceCmd {
	return &redis.BoolSliceCmd{}
}

func (mcli *memClient) ScriptFlush() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ScriptKill() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ScriptLoad(script string) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) DebugObject(key string) *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) Publish(channel string, message interface{}) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) PubSubChannels(pattern string) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) PubSubNumSub(channels ...string) *redis.StringIntMapCmd {
	return &redis.StringIntMapCmd{}
}

func (mcli *memClient) PubSubNumPat() *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ClusterSlots() *redis.ClusterSlotsCmd {
	return &redis.ClusterSlotsCmd{}
}

func (mcli *memClient) ClusterNodes() *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) ClusterMeet(host, port string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ClusterForget(nodeID string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}
func (mcli *memClient) ClusterReplicate(nodeID string) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ClusterResetSoft() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ClusterResetHard() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ClusterInfo() *redis.StringCmd {
	return &redis.StringCmd{}
}

func (mcli *memClient) ClusterKeySlot(key string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ClusterGetKeysInSlot(slot int, count int) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) ClusterCountFailureReports(nodeID string) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ClusterCountKeysInSlot(slot int) *redis.IntCmd {
	return &redis.IntCmd{}
}

func (mcli *memClient) ClusterDelSlots(slots ...int) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ClusterDelSlotsRange(min, max int) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ClusterSaveConfig() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ClusterSlaves(nodeID string) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) ClusterFailover() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ClusterAddSlots(slots ...int) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ClusterAddSlotsRange(min, max int) *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) GeoAdd(key string, geoLocation ...*redis.GeoLocation) *redis.IntCmd {
	return &redis.IntCmd{}
}
func (mcli *memClient) GeoPos(key string, members ...string) *redis.GeoPosCmd {
	return &redis.GeoPosCmd{}
}

func (mcli *memClient) GeoRadius(key string, longitude, latitude float64, query *redis.GeoRadiusQuery) *redis.GeoLocationCmd {
	return &redis.GeoLocationCmd{}
}

func (mcli *memClient) GeoRadiusRO(key string, longitude, latitude float64, query *redis.GeoRadiusQuery) *redis.GeoLocationCmd {
	return &redis.GeoLocationCmd{}
}

func (mcli *memClient) GeoRadiusByMember(key, member string, query *redis.GeoRadiusQuery) *redis.GeoLocationCmd {
	return &redis.GeoLocationCmd{}
}

func (mcli *memClient) GeoRadiusByMemberRO(key, member string, query *redis.GeoRadiusQuery) *redis.GeoLocationCmd {
	return &redis.GeoLocationCmd{}
}

func (mcli *memClient) GeoDist(key string, member1, member2, unit string) *redis.FloatCmd {
	return &redis.FloatCmd{}
}

func (mcli *memClient) GeoHash(key string, members ...string) *redis.StringSliceCmd {
	return &redis.StringSliceCmd{}
}

func (mcli *memClient) ReadOnly() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) ReadWrite() *redis.StatusCmd {
	return &redis.StatusCmd{}
}

func (mcli *memClient) MemoryUsage(key string, samples ...int) *redis.IntCmd {
	return &redis.IntCmd{}
}

func toString(value interface{}) string {
	switch value.(type) {
	case bool, string, uint8, uint16, uint32, uint64, int8, int16, int32, int64, float32, float64:
		return fmt.Sprintf("%v", value)
	case []byte:
		return string(value.([]byte))
	case nil:
		return "null"
	default:
		bs, err := json.Marshal(value)
		if err != nil {
			return "null"
		}
		return string(bs)
	}
}
