package dc_res

import (
	"errors"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/otcp/resx"
)

func Get(dcId string) (*DataCenterResDao, error) {
	resDao := &DataCenterResDao{}
	err := resx.GetInstance(ResModelNameDC, dcId, resDao)
	if err != nil {
		return nil, errors.New("GetDc from res failed:" + dcId + err.Error())
	}
	return resDao, nil
}

func Gets(condition map[string][]string) ([]*DataCenterResDao, error) {
	resDaos := []*DataCenterResDao{}
	err := resx.GetInstances(ResModelNameDC, condition, &resDaos)
	if err != nil {
		return nil, errors.New("GetDcs from res failed:" + util.ToJSONStr(condition) + err.Error())
	}
	return resDaos, nil
}

func Updates(dcResDtos []*DataCenterResDao) error {
	cloudsResDaos := []*DataCenterResDao{}
	err := resx.UpdateInstances(ResModelNameDC, cloudsResDaos)
	if err != nil {
		return errors.New("Update dcs to res failed:" + util.ToJSONStr(dcResDtos) + err.Error())
	}
	return nil
}

func Update(dcResDto *DataCenterResDao) error {
	_, err := resx.UpdateInstance(ResModelNameDC, dcResDto.ID, dcResDto)
	if err != nil {
		return errors.New("Update dc to res failed:" + util.ToJSONStr(dcResDto) + err.Error())
	}
	return nil
}

func Create(dcResDto *DataCenterResDao) error {
	err := resx.CreateInstance(ResModelNameDC, dcResDto)
	if err != nil {
		return errors.New("Create dc to res failed:" + util.ToJSONStr(dcResDto) + err.Error())
	}
	return nil
}

func Delete(id string) error {
	err := resx.DeleteInstance(ResModelNameDC, id)
	if err != nil {
		return errors.New("Delete dc from res failed:" + id + err.Error())
	}
	return nil
}
