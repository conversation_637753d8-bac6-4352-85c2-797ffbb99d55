package dc_res

import (
	otcpres "cwsm/tools/commontools/otcp/res"
	"cwsm/tools/commontools/storage/res/storage"
)

const (
	ResModelNameDC = "director.dc"
)

type DataCenterResDao struct {
	storage.ResCommon
	Location           string   `json:"location"`
	Region             string   `json:"region"`
	CloudType          string   `json:"cloudType"`
	Areas              string   `json:"areas"`
	OwnerId            string   `json:"ownerId"`
	CreateAt           string   `json:"create_at"`
	UpdateAt           string   `json:"update_at"`
	DirectorDcGroupIds []string `json:"director.dcgroupids"`

	Dn []string `json:"dn,omitempty"`
}

var DcReq = &otcpres.ResModelDto{
	Name:        ResModelNameDC,
	DisplayName: otcpres.I18n{EN_US: "DC", ZH_CN: "数据中心"},
	Fields:      Fields,
	Moc:         Mocs,
}

var Fields = []otcpres.Field{location, region, cloudType, ownerId, create_at, update_at, areas}
var location = otcpres.FieldFactory("location", "位置")
var region = otcpres.FieldFactory("region", "地理区域")
var cloudType = otcpres.FieldFactory("cloudType", "类型")
var ownerId = otcpres.FieldFactory("ownerId", "")
var create_at = otcpres.FieldFactory("create_at", "创建时间")
var update_at = otcpres.FieldFactory("update_at", "更新时间")
var areas = otcpres.FieldFactory("areas", "可用区")

var Mocs = []otcpres.Moc{moc4Dc}
var moc4Dc = otcpres.Moc{
	Name:        ResModelNameDC,
	Description: "",
	DisplayName: otcpres.I18n{EN_US: "DC", ZH_CN: "数据中心"},
	UsedBy:      storage.UsedBy,
	Model:       ResModelNameDC,
}
