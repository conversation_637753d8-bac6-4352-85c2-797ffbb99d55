package domaintype_res

import (
	"errors"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/otcp/resx"
)

func Gets(condition map[string][]string) ([]*DomainType, error) {
	resDaos := []*DomainType{}
	err := resx.GetInstances(ResModelNameDomainType, condition, &resDaos)
	if err != nil {
		return nil, errors.New("GetDomainTypes from res failed:" + util.ToJSONStr(condition) + err.Error())
	}
	return resDaos, nil
}
