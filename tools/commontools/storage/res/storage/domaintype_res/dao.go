package domaintype_res

import (
	"cwsm/tools/commontools/infa/util"
	otcpres "cwsm/tools/commontools/otcp/res"
	"cwsm/tools/commontools/storage/res/storage"
)

var (
	ResModelNameDomainType = "director.domaintype"
)

type DomainType struct {
	storage.ResCommon
}

var DomainTypeReq = &otcpres.ResModelDto{
	Name:        ResModelNameDomainType,
	DisplayName: otcpres.I18n{EN_US: "domainType", ZH_CN: "域类型"},
	Fields:      Fields,
	Moc:         Mocs,
}

var Fields = []otcpres.Field{}

var Mocs = []otcpres.Moc{moc4DomainType}
var moc4DomainType = otcpres.Moc{
	Name:        ResModelNameDomainType,
	Description: "",
	DisplayName: otcpres.I18n{EN_US: "domainType", ZH_CN: "域类型"},
	UsedBy:      storage.UsedBy,
	Model:       ResModelNameDomainType,
}

var DefaultValue4Dc = "DC"
var DomainType4Dc = &storage.ResCommon{ID: util.UUID(), Name: DefaultValue4Dc, DisplayName: DefaultValue4Dc,
	Description: DefaultValue4Dc, Moc: ResModelNameDomainType}
