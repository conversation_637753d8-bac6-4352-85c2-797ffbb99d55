package init_res_model_instance

import (
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/otcp/res"
	"cwsm/tools/commontools/otcp/resx"
	"cwsm/tools/commontools/storage/res/storage/cloud_env_res"
	"cwsm/tools/commontools/storage/res/storage/dc_res"
	"cwsm/tools/commontools/storage/res/storage/dcgroup_res"
	"cwsm/tools/commontools/storage/res/storage/domaintype_res"
	"zte.com.cn/dexcloud/go-mars/logger"
)

func SyncRmModel() {
	var expectModels = []string{domaintype_res.ResModelNameDomainType, dcgroup_res.ResModelNameDcGroup, dc_res.ResModelNameDC}
	for _, model := range expectModels {
		initModel := InitResModelIfNotExist(model)
		logger.Infof("SyncRmModel initModel: %s", initModel)
	}
	var expectDatas = map[string]string{domaintype_res.ResModelNameDomainType: domaintype_res.DefaultValue4Dc,
		dcgroup_res.ResModelNameDcGroup: dcgroup_res.DefaultValue4DcPublic}
	for k, v := range expectDatas {
		initData := InitDataIfNotExist(k, v)
		logger.Infof("SyncRmModel initData: %s", initData)
	}
	res := UpdateRmModel(cloud_env_res.ResModelNameCloud)
	logger.Infof("SyncRmModel UpdateRmModel res: %s", res)
}

func cleanUnUsedRes() {
	unUsedRes := []string{cloud_env_res.ResMoc4ContainerCloud, cloud_env_res.ResModelNamePHCloud}
	for _, modelName := range unUsedRes {
		doCleanRes(modelName)
	}
	doCleanRes(cloud_env_res.ResMoc4ContainerCloud)
}

func doCleanRes(modelName string) {
	oldInstances := []*otcpres.ResInstanceGetRSP{}
	err := resx.GetInstances(modelName, nil, &oldInstances)
	if err != nil {
		logger.Errorf("cleanUnUsedRes: before get %s failed: %s", modelName, err.Error())
	}
	logger.Infof("cleanUnUsedRes before size of %s: %d", modelName, len(oldInstances))
	if len(oldInstances) != 0 {
		oldInstanceIds := []string{}
		for _, oldContainers := range oldInstances {
			oldInstanceIds = append(oldInstanceIds, oldContainers.Id)
		}
		err = resx.DeleteInstances(modelName, oldInstanceIds)
		if err != nil {
			logger.Errorf("cleanUnUsedRes: delete %s %s failed: %s", modelName,
				util.ToJSONStr(oldInstanceIds), err.Error())
			return
		}
	}

	_, err = otcpres.DeleteResModel(modelName)
	if err != nil {
		logger.Errorf("cleanUnUsedRes: DeleteResModel failed: %s", err.Error())
	}
}

func initResModel(modelName string) bool {
	var req *otcpres.ResModelDto
	switch modelName {
	case domaintype_res.ResModelNameDomainType:
		req = domaintype_res.DomainTypeReq
	case dcgroup_res.ResModelNameDcGroup:
		req = dcgroup_res.DcGroupReq
	case dc_res.ResModelNameDC:
		req = dc_res.DcReq
	case cloud_env_res.ResModelNameCloud:
		req = cloud_env_res.CloudReq
	default:
		return false
	}
	_, err := otcpres.CreateResModel(req)
	if err != nil {
		logger.Errorf("initResModel: CreateResModel %s failed: %s", modelName, err.Error())
		return false
	}
	return true
}

func updateModelField(model *otcpres.ResModelDto) bool {
	fields := model.Fields
	model.Fields = []otcpres.Field{}
	for i := range cloud_env_res.Fields {
		exist := false
		for _, f := range fields {
			if f.Name == cloud_env_res.Fields[i].Name {
				exist = true
				if !f.SimpleEqual(&cloud_env_res.Fields[i]) {
					cloud_env_res.Fields[i].Action = "update"
					model.Fields = append(model.Fields, cloud_env_res.Fields[i])
				}
				break
			}
		}
		if !exist {
			cloud_env_res.Fields[i].Action = "create"
			model.Fields = append(model.Fields, cloud_env_res.Fields[i])
		}
	}

	for _, f := range fields {
		if _, ok := cloud_env_res.CurrentFields[f.Name]; !ok {
			f.Action = "delete"
			model.Fields = append(model.Fields, f)
		}
	}

	logger.Infof("updateModelField fields: %s", util.ToJSONStr(model.Fields))
	if len(model.Fields) == 0 {
		return true
	}
	_, err := otcpres.PutResModel(model.Name, map[string]interface{}{"fields": model.Fields})
	if err != nil {
		return false
	}
	return true
}

func updateMoc() bool {
	for _, moc := range cloud_env_res.Mocs {
		rsp, err := otcpres.GetMoc(moc.Name)
		if err != nil || rsp == nil {
			if _, err := otcpres.PostMoc(moc); err != nil {
				return false
			}
			break
		}

		if _, err := otcpres.PutMoc(rsp.Name, map[string]interface{}{"usedBy": cloud_env_res.UsedBy}); err != nil {
			return false
		}
	}
	return true
}

func UpdateRmModel(modelName string) bool {
	cleanUnUsedRes()

	model, err := otcpres.GetResModel(modelName)
	if err != nil || model == nil {
		return initResModel(modelName)
	}

	return updateModelField(model) && updateMoc()
}

func InitResModelIfNotExist(modelName string) bool {
	model, err := otcpres.GetResModel(modelName)
	if err != nil || model == nil {
		return initResModel(modelName)
	}
	return true
}

func InitDataIfNotExist(modelName, defaultValue string) bool {
	var req interface{}
	switch modelName {
	case domaintype_res.ResModelNameDomainType:
		var instances []*interface{}
		req = domaintype_res.DomainType4Dc
		conditon := map[string][]string{"name": {defaultValue}}
		err := resx.GetInstances(modelName, conditon, &instances)
		if err != nil || util.IsEmpty(instances) {
			logger.Infof("InitDataIfNotExist: %s GetInstances failed or empty", modelName)
			logger.Infof("InitDataIfNotExist: %s GetInstances req:%s", modelName, util.ToJSONStr(req))
			err = resx.CreateInstance(modelName, req)
			if err != nil {
				logger.Errorf("InitDataIfNotExist: %s CreateInstance instances failed: %s", modelName, err.Error())
				return false
			}
		}
	case dcgroup_res.ResModelNameDcGroup:
		var instances []*dcgroup_res.DcGroupResDao
		req = dcgroup_res.DcGroup4DcPublic
		err := resx.GetInstances(modelName, nil, &instances)
		if err == nil && !util.IsEmpty(instances) {
			for _, instance := range instances {
				if instance.ID == dcgroup_res.DefaultId || instance.Name == defaultValue {
					return true
				}
			}
		}
		logger.Infof("InitDataIfNotExist: %s GetInstances failed or empty", modelName)
		logger.Infof("InitDataIfNotExist: %s GetInstances req:%s", modelName, util.ToJSONStr(req))
		err = resx.CreateInstance(modelName, req)
		if err != nil {
			logger.Errorf("InitDataIfNotExist: %s CreateInstance instances failed: %s", modelName, err.Error())
			return false
		}
	default:
		return false
	}
	return true
}
