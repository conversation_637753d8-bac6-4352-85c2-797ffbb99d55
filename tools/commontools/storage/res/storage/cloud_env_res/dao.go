package cloud_env_res

import (
	"encoding/json"
	"cwsm/tools/commontools/commondao/common_cloud_env"
	"cwsm/tools/commontools/infa/util"
	otcpres "cwsm/tools/commontools/otcp/res"
	"cwsm/tools/commontools/storage/res/storage"
)

type CloudResDao struct {
	storage.ResCommon
	EnvType         string   `json:"env_type"`
	Provider        string   `json:"provider"`
	Endpoints       string   `json:"endpoints"`
	DcId            string   `json:"dc_id,omitempty"`
	DcIds           []string `json:"director.dcids,omitempty"`
	ConnectionState int      `json:"connectionState,omitempty"`
}

func (resDao *CloudResDao) ToResDto() *CloudResDto {
	endPoints := []*common_cloud_env.Endpoint{}
	_ = json.Unmarshal([]byte(resDao.Endpoints), &endPoints)
	return &CloudResDto{resDao.ResCommon, resDao.EnvType, resDao.Provider,
		endPoints, resDao.DcId, resDao.ConnectionState}
}

type CloudResDto struct {
	storage.ResCommon
	EnvType         string                       `json:"env_type"`
	Provider        string                       `json:"provider"`
	Endpoints       []*common_cloud_env.Endpoint `json:"endpoints"`
	DcId            string                       `json:"dc_id,omitempty"`
	ConnectionState int                          `json:"connectionState,omitempty"`
}

func (resDao *CloudResDto) ToResDao() *CloudResDao {
	return &CloudResDao{resDao.ResCommon, resDao.EnvType, resDao.Provider,
		util.ToJSONStr(resDao.Endpoints), resDao.DcId, []string{resDao.DcId},
		resDao.ConnectionState}
}

const (
	ResModelNameCloud     = "director.cloudenv"
	ResModelNamePHCloud   = "director.physicalenv"
	ResMoc4ContainerCloud = "director.containercloud"
	ResMoc4NetworkCloud   = "director.network"
	ResMoc4PhysicalCloud  = "director.physicalenv"
	UsedBy                = "topochart,topotree,fm,pm,sm,log"
)

var CurrentFields = map[string]string{"id": "", "nbiId": "", "moc": "", "name": "", "displayName": "", "description": "",
	"rm:data_owner": "", "rm:last_modified": "", "rm:pin_yin": "", "rm:hidden_serial": "",
	"provider": "", "connectionState": "", "endpoints": "", "env_type": "", "dc_id": "", "vim_id": ""}

var CloudReq = &otcpres.ResModelDto{
	Name:        ResModelNameCloud,
	DisplayName: otcpres.I18n{EN_US: "cloudEnv", ZH_CN: "云环境"},
	Fields:      Fields,
	Moc:         Mocs,
}

var Fields = []otcpres.Field{provider, connectionState, endpoints, env_type, dc_id, vim_id}
var provider = otcpres.Field{
	Name:        "provider",
	DisplayName: otcpres.I18n{EN_US: "provider", ZH_CN: "供应商"},
	ValueType:   "string",
	Length:      65535,
	Unique:      false,
	Nullable:    true,
	Isstate:     false,
	Enums:       []otcpres.Enum{},
	Moc:         []otcpres.Moc{},
	Action:      "",
}
var connectionState = otcpres.Field{
	Name:        "connectionState",
	DisplayName: otcpres.I18n{EN_US: "connectionState", ZH_CN: "连接状态"},
	ValueType:   "int",
	Length:      8,
	Unique:      false,
	Nullable:    true,
	Isstate:     false,
	Enums:       []otcpres.Enum{},
	Moc:         []otcpres.Moc{},
	Action:      "",
}

var endpoints = otcpres.Field{
	Name:        "endpoints",
	DisplayName: otcpres.I18n{EN_US: "endpoints", ZH_CN: "端点"},
	ValueType:   "string",
	Length:      10000,
	Unique:      false,
	Nullable:    true,
	Isstate:     false,
	Enums:       []otcpres.Enum{},
	Moc:         []otcpres.Moc{},
	Action:      "",
}
var env_type = otcpres.Field{
	Name:        "env_type",
	DisplayName: otcpres.I18n{EN_US: "env_type", ZH_CN: "资源池类型"},
	ValueType:   "string",
	Length:      10000,
	Unique:      false,
	Nullable:    true,
	Isstate:     false,
	Enums:       []otcpres.Enum{},
	Moc:         []otcpres.Moc{},
	Action:      "",
}
var dc_id = otcpres.Field{
	Name:        "dc_id",
	DisplayName: otcpres.I18n{EN_US: "dc_id", ZH_CN: "所属数据中心ID"},
	ValueType:   "string",
	Length:      10000,
	Unique:      false,
	Nullable:    true,
	Isstate:     false,
	Enums:       []otcpres.Enum{},
	Moc:         []otcpres.Moc{},
	Action:      "",
}
var vim_id = otcpres.Field{
	Name:        "vim_id",
	DisplayName: otcpres.I18n{EN_US: "vim_id", ZH_CN: "TECS资源池ID"},
	ValueType:   "string",
	Length:      10000,
	Unique:      false,
	Nullable:    true,
	Isstate:     false,
	Enums:       []otcpres.Enum{},
	Moc:         []otcpres.Moc{},
	Action:      "",
}

var Mocs = []otcpres.Moc{moc4Cloud, moc4CCloud, moc4NCloud, moc4PHCloud}

var moc4Cloud = otcpres.Moc{
	Name:        ResModelNameCloud,
	Description: "",
	DisplayName: otcpres.I18n{EN_US: "cloudEnv", ZH_CN: "云环境"},
	UsedBy:      UsedBy,
	Model:       ResModelNameCloud,
}

var moc4CCloud = otcpres.Moc{
	Name:        ResMoc4ContainerCloud,
	Description: "",
	DisplayName: otcpres.I18n{EN_US: "ContainerCloud", ZH_CN: "容器云"},
	UsedBy:      UsedBy,
	Model:       ResModelNameCloud,
}

var moc4NCloud = otcpres.Moc{
	Name:        ResMoc4NetworkCloud,
	Description: "",
	DisplayName: otcpres.I18n{EN_US: "NetworkCloud", ZH_CN: "网络云"},
	UsedBy:      UsedBy,
	Model:       ResModelNameCloud,
}

var moc4PHCloud = otcpres.Moc{
	Name:        ResMoc4PhysicalCloud,
	Description: "",
	DisplayName: otcpres.I18n{EN_US: "PhysicalEnv", ZH_CN: "物理环境"},
	UsedBy:      UsedBy,
	Model:       ResModelNameCloud,
}
