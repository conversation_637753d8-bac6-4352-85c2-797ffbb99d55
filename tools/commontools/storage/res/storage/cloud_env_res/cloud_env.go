package cloud_env_res

import (
	"errors"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/otcp/resx"
)

func Get(envId string) (*CloudResDto, error) {
	resDao := &CloudResDao{}
	err := resx.GetInstance(ResModelNameCloud, envId, resDao)
	if err != nil {
		return nil, errors.New("GetEnv failed:" + envId + err.Error())
	}
	return resDao.ToResDto(), nil
}

func Gets(condition map[string][]string) ([]*CloudResDto, error) {
	resDaos := []*CloudResDao{}
	err := resx.GetInstances(ResModelNameCloud, condition, &resDaos)
	if err != nil {
		return nil, errors.New("GetEnvs failed:" + util.ToJSONStr(condition) + err.Error())
	}
	resDtos := []*CloudResDto{}
	for _, resDao := range resDaos {
		resDtos = append(resDtos, resDao.ToResDto())
	}
	return resDtos, nil
}

func Updates(cloudsResDto []*CloudResDto) error {
	cloudsResDaos := []*CloudResDao{}
	for _, resDto := range cloudsResDto {
		cloudsResDaos = append(cloudsResDaos, resDto.ToResDao())
	}
	err := resx.UpdateInstances(ResModelNameCloud, cloudsResDaos)
	if err != nil {
		return errors.New("UpdateEnvs failed:" + util.ToJSONStr(cloudsResDto) + err.Error())
	}
	return nil
}

func Update(cloudResDto *CloudResDto) error {
	_, err := resx.UpdateInstance(ResModelNameCloud, cloudResDto.ID, cloudResDto.ToResDao())
	if err != nil {
		return errors.New("UpdateEnv failed:" + util.ToJSONStr(cloudResDto.ToResDao()) + err.Error())
	}
	return nil
}

func Create(cloudResDto *CloudResDto) error {
	err := resx.CreateInstance(ResModelNameCloud, cloudResDto.ToResDao())
	if err != nil {
		return errors.New("CreateEnv failed:" + util.ToJSONStr(cloudResDto.ToResDao()) + err.Error())
	}
	return nil
}

func Delete(id string) error {
	err := resx.DeleteInstance(ResModelNameCloud, id)
	if err != nil {
		return errors.New("DeleteEnv failed:" + id + err.Error())
	}
	return nil
}
