package storage

import (
	"encoding/json"
	"errors"
	"cwsm/tools/commontools/infa/util"
	otcpres "cwsm/tools/commontools/otcp/res"
	"cwsm/tools/commontools/otcp/resx"
	"cwsm/tools/commontools/service/httpservice"
)

const (
	UsedBy = "topochart,topotree,fm,pm,sm,log"
)

var ResModelList = []string{
	"director.snapshot", "director.volume", "director.storagepool", "director.volumetype",
	"director.hostbondport", "director.hostfilesystem", "director.hostport",
	"director.port",
	"director.baremetal", "director.vm",
	"director.host", "director.ha", "director.az",
	"director.tenant",
	"director.device",
	"director.freezerbackupdata",
	"director.freezerjob",
}

var ResModelList4Pvrm = []string{
	"director.containercluster", "director.containercontainer",
	"director.containernamespace", "director.containernode",
	"director.containerpod", "director.containertenant",
}

type Res<PERSON>ommon struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	NBIID       string `json:"nbiId,omitempty"`
	Moc         string `json:"moc"`
	DisplayName string `json:"displayName"`
	Description string `json:"description"`
}

func GetResInstance(modelName string, instanceId string, instance interface{}) error {
	body, _, _, err := httpservice.Get(otcpres.ResPrefix+"/"+modelName+"s/"+instanceId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return errors.New("get res instance failed," + modelName + err.Error())
	}
	return json.Unmarshal(body, instance)

}

func GetResInstances(modelName string, condition map[string][]string, instances interface{}) error {
	condString := ""
	for k, conds := range condition {
		if len(conds) == 0 {
			return nil
		}
		condString += "&" + k + "="
		for _, cond := range conds {
			condString += cond + ","
		}
		condString = condString[0 : len(condString)-1]
	}
	if len(condString) > 0 {
		condString = condString[1:]
	}

	body, _, _, err := httpservice.Get(otcpres.ResPrefix+"/"+modelName+"s/"+condString, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return errors.New("get res instances failed," + modelName + condString + err.Error())
	}

	return json.Unmarshal(body, instances)
}

func UpdateResInstances(modelName string, instances interface{}) ([]byte, error) {
	updateBodys, err := json.Marshal(instances)
	if err != nil {
		return nil, errors.New("UpdateResInstances Marshal failed," + modelName + err.Error())
	}
	body, _, _, err := httpservice.Put(otcpres.ResPrefix+"/"+modelName+"s/batch",
		httpservice.DefaultHeaders(), updateBodys, nil)
	if err != nil {
		return nil, errors.New("update res instances failed," + modelName + err.Error())
	}
	return body, nil
}

func UpdateResInstance(modelName string, instanceId string, instances interface{}) ([]byte, error) {
	updateBody, err := json.Marshal(instances)
	if err != nil {
		return nil, errors.New("UpdateResInstances Marshal failed," + modelName + err.Error())
	}
	body, _, _, err := httpservice.Put(otcpres.ResPrefix+"/"+modelName+"/"+instanceId,
		httpservice.DefaultHeaders(), updateBody, nil)
	if err != nil {
		return nil, errors.New("update res instance failed," + err.Error())
	}
	return body, nil
}

func CreateResInstance(modelName string, instance interface{}) error {
	createBody, err := json.Marshal(instance)
	if err != nil {
		return errors.New("CreateResInstance Marshal failed," + modelName + err.Error())
	}
	_, _, _, err = httpservice.Post(otcpres.ResPrefix+"/"+modelName, httpservice.DefaultHeaders(),
		createBody, nil)
	if err != nil {
		return errors.New("create res instance failed," + err.Error())
	}
	return nil
}

func CreateResInstances(modelName string, bodyBytes []byte) ([]byte, error) {
	body, _, _, err := httpservice.Post(otcpres.ResPrefix+"/"+modelName+"s/batch", httpservice.DefaultHeaders(), bodyBytes, nil)
	if err != nil {
		return nil, errors.New("create res instances failed," + err.Error())
	}
	return body, nil
}

func DeleteResInstance(modelName string, instanceId string) ([]byte, error) {
	body, _, _, err := httpservice.Delete(otcpres.ResPrefix+"/"+modelName+"s/"+instanceId, httpservice.DefaultHeaders(), nil)
	if err != nil {
		return nil, errors.New("delete res instance failed," + err.Error())
	}
	return body, nil
}

func UpdateFields(modelName string, id string, fields interface{}) error {
	_, err := resx.UpdateInstance(modelName, id, fields)
	if err != nil {
		return errors.New("UpdateFields modelName" + modelName + " failed:" + util.ToJSONStr(fields) + err.Error())
	}
	return nil
}
