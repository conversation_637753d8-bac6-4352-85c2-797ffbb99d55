package dcgroup_res

import (
	"time"
	otcpres "cwsm/tools/commontools/otcp/res"
	"cwsm/tools/commontools/storage/res/storage"
)

const (
	ResModelNameDcGroup = "director.dcgroup"
)

type DcGroupResDao struct {
	storage.ResCommon
	CreateAt              string   `json:"create_at"`
	UpdateAt              string   `json:"update_at"`
	DirectorDomainTypeIDs []string `json:"director.domaintypeids,omitempty"`
}

var DcGroupReq = &otcpres.ResModelDto{
	Name:        ResModelNameDcGroup,
	DisplayName: otcpres.I18n{EN_US: "dcGroup", ZH_CN: "域"},
	Fields:      Fields,
	Moc:         Mocs,
}

var Fields = []otcpres.Field{create_at, update_at}
var create_at = otcpres.FieldFactory("create_at", "创建时间")

var update_at = otcpres.FieldFactory("update_at", "更新时间")

var Mocs = []otcpres.Moc{moc4DcGroup}
var moc4DcGroup = otcpres.Moc{
	Name:        ResModelNameDcGroup,
	Description: "",
	DisplayName: otcpres.I18n{EN_US: "dcGroup", ZH_CN: "域"},
	UsedBy:      storage.UsedBy,
	Model:       ResModelNameDcGroup,
}

var DefaultValue4DcPublic = "dcPublic"
var DefaultId = "6c27d9a8-5668-4958-8fb9-f387d1abc152"
var DcGroup4DcPublic = &DcGroupResDao{
	ResCommon: storage.ResCommon{ID: DefaultId,
		Name:        DefaultValue4DcPublic,
		DisplayName: DefaultValue4DcPublic,
		Description: "",
		Moc:         ResModelNameDcGroup},
	CreateAt: getTimeGMT(),
	UpdateAt: getTimeGMT(),
}

func getTimeGMT() string {
	var cstSh, _ = time.LoadLocation("GMT")
	return time.Now().In(cstSh).Format("2006-01-02T15:04:05Z")
}
