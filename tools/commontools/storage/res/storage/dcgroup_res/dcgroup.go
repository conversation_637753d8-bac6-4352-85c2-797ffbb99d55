package dcgroup_res

import (
	"errors"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/otcp/resx"
)

func Get(dgId string) (*DcGroupResDao, error) {
	resDao := &DcGroupResDao{}
	err := resx.GetInstance(ResModelNameDcGroup, dgId, resDao)
	if err != nil {
		return nil, errors.New("GetDcGroup from res failed:" + dgId + err.Error())
	}
	return resDao, nil
}

func Gets(condition map[string][]string) ([]*DcGroupResDao, error) {
	resDaos := []*DcGroupResDao{}
	err := resx.GetInstances(ResModelNameDcGroup, condition, &resDaos)
	if err != nil {
		return nil, errors.New("Get DcGroup from res failed:" + util.ToJSONStr(condition) + err.Error())
	}
	return resDaos, nil
}

func Updates(dgResDtos []*DcGroupResDao) error {
	cloudsResDaos := []*DcGroupResDao{}
	err := resx.UpdateInstances(ResModelNameDcGroup, cloudsResDaos)
	if err != nil {
		return errors.New("Update DcGroups to res failed:" + util.ToJSONStr(dgResDtos) + err.Error())
	}
	return nil
}

func Update(dgResDto *DcGroupResDao) error {
	_, err := resx.UpdateInstance(ResModelNameDcGroup, dgResDto.ID, dgResDto)
	if err != nil {
		return errors.New("Update DcGroup to res failed:" + util.ToJSONStr(dgResDto) + err.Error())
	}
	return nil
}

func Create(dgResDto *DcGroupResDao) error {
	err := resx.CreateInstance(ResModelNameDcGroup, dgResDto)
	if err != nil {
		return errors.New("Create DcGroup to res failed:" + util.ToJSONStr(dgResDto) + err.Error())
	}
	return nil
}

func Delete(id string) error {
	err := resx.DeleteInstance(ResModelNameDcGroup, id)
	if err != nil {
		return errors.New("Delete DcGroup from res failed:" + id + err.Error())
	}
	return nil
}
