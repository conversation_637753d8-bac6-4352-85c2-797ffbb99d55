package cwsmtest

import (
	"bytes"
	"cwsm/infra/constant"
	"cwsm/tools/commontools/restful"
	"errors"

	"net/http"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey"
	beego "github.com/beego/beego/v2/server/web"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func vcjobEvaSubscription(reqBody string) *httptest.ResponseRecorder {
	body := bytes.NewReader([]byte(reqBody))
	request, err := http.NewRequest("POST", constant.CwsmPrefix+"/cluster/cluster1/apts/faultanalacfg", body)
	Expect(err == nil).To(Equal(true))
	request.Header.Add("Content-Type", "application/json")

	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

var _ = Describe("test for activate vcjob", func() {
	var fp ActivateVcjobCfgPatches
	BeforeEach(func() {
		fp.EvaFakePatches()
	})

	Context("activate vcjob", func() {
		It("activate vcjob, Return 200 When activate Succeed", func() {
			response := vcjobEvaSubscription(`{"faultAnalaSwitch": "0"}`)
			Expect(response.Code).To(Equal(http.StatusOK))
		})
	})

	/* Started by AICoder, pid:7004f931d78148c5bb5f7389d7a2a8d3 */
	It("activate vcjob post to wsm fail", func() {
		fp.AddPatches(gomonkey.ApplyFunc(restful.PostMethod, func(url string, reqHeaders map[string]string, body []byte, auth *restful.SSLAuth) ([]byte,
			map[string][]string, int, error) {
			return nil, nil, http.StatusOK, errors.New(`{"err":"activate vcjob info error"}`)
		}))
		response := vcjobEvaSubscription(`{"faultAnalaSwitch": "0"}`)
		Expect(response.Code).To(Equal(http.StatusBadRequest))
		Expect(response.Body.String()).To(MatchJSON(`{"message":"activate vcjob info error"}`))
	})
	/* Ended by AICoder, pid:7004f931d78148c5bb5f7389d7a2a8d3 */

	AfterEach(func() {
		fp.CleanPatches()
	})
})
