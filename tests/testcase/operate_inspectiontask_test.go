package cwsmtest

import (
	"bytes"
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/restful"
	"errors"

	"net/http"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey"
	beego "github.com/beego/beego/v2/server/web"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func inspectiontaskEvaSubscription(reqBody string) *httptest.ResponseRecorder {
	body := bytes.NewReader([]byte(reqBody))
	request, err := http.NewRequest("POST", constant.CwsmPrefix+"/cluster/cluster1/apts/diagtask", body)
	Expect(err == nil).To(Equal(true))
	request.Header.Add("Content-Type", "application/json")

	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

var _ = Describe("test for activate inspectiontask", func() {
	var fp ActivateInspectiontaskPatches
	BeforeEach(func() {
		fp.EvaFakePatches()
	})

	Context("activate inspectiontask", func() {
		It("activate inspectiontask unmarshaling failed", func() {
			response := inspectiontaskEvaSubscription(`{"aaa":111"}`)
			Expect(response.Code).To(Equal(http.StatusBadRequest))
		})

		It("activate inspectiontask, Return 200 When activate Succeed", func() {
			response := inspectiontaskEvaSubscription(`{"inspect_type":"llama70b","mos":["NB4B42LN051","NB4B42LM072"],"diag_level":"quick","inspect_count":1}`)
			Expect(response.Code).To(Equal(http.StatusOK))
		})
	})

	/* Started by AICoder, pid:978fa0bb3bb74cc7afbf0595f0d9a946 */
	It("activate evaluate post to wsm fail", func() {
		fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
			map[string][]string, int, error) {
			return nil, nil, 0, errors.New(`{"err":"activate inspectiontask info error"}`)
		}))
		response := inspectiontaskEvaSubscription(`{"inspect_type":"llama70b","mos":["NB4B42LN051","NB4B42LM072"],"diag_level":"quick","inspect_count":1}`)
		Expect(response.Code).To(Equal(http.StatusBadRequest))
		Expect(response.Body.String()).To(MatchJSON(`{"message":"activate inspectiontask info error"}`))
	})

	Context("activate modelinspection failed because of JSON unmarshal error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				return []byte(`{"invalid_json`), nil, http.StatusOK, nil
			}))
		})

		It("activate modelinspection fails due to JSON unmarshal error", func() {
			response := inspectiontaskEvaSubscription(`{"inspect_type":"llama70b","mos":["NB4B42LN051","NB4B42LM072"],"diag_level":"quick","inspect_count":1}`)
			Expect(response.Code).To(Equal(http.StatusBadRequest))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})
	/* Ended by AICoder, pid:978fa0bb3bb74cc7afbf0595f0d9a946 */

	AfterEach(func() {
		fp.CleanPatches()
	})
})
