package cwsmtest

import (
	"bytes"
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/restful"
	"errors"

	"net/http"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey"
	beego "github.com/beego/beego/v2/server/web"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func modelinspectionEvaSubscription(reqBody string) *httptest.ResponseRecorder {
	body := bytes.NewReader([]byte(reqBody))
	request, err := http.NewRequest("POST", constant.CwsmPrefix+"/cluster/cluster1/apts/modelinspection", body)
	Expect(err == nil).To(Equal(true))
	request.Header.Add("Content-Type", "application/json")

	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

var _ = Describe("test for activate modelinspection", func() {
	var fp ActivateModelinspectionPatches
	BeforeEach(func() {
		fp.EvaFakePatches()
	})

	Context("activate modelinspection", func() {
		It("activate modelinspection unmarshaling failed", func() {
			response := modelinspectionEvaSubscription(`{"aaa":111"}`)
			Expect(response.Code).To(Equal(http.StatusBadRequest))
		})

		It("activate modelinspection, Return 200 When activate Succeed", func() {
			response := modelinspectionEvaSubscription(`{"gpuTotal": 1,"gpuFactory":"nvdia","nodeList":["test1", "test2"],"cpuPerNode":118,"gpuPerNode":8,"memoryPerNode": 1500,"superParam":"{\"env\":[{\"TP_SIZE\":\"1\",\"PP_SIZE\":\"2\",\"MICRO_BATCH_SIZE\":\"4\",\"GLOBAL_BATCH_SIZE\":\"8\",\"NLAYERS\":\"1\",\"STOP_ITER\":\"5000\"}]}","testScene":" cluster"}`)
			Expect(response.Code).To(Equal(http.StatusOK))
		})
	})

	/* Started by AICoder, pid:41605fb56cd1402688b1462a6b69fc6f */
	It("activate modelinspection post to wsm fail", func() {
		fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
			map[string][]string, int, error) {
			return nil, nil, 0, errors.New(`{"err":"activate modelinspection info error"}`)
		}))
		response := modelinspectionEvaSubscription(`{"gpuTotal": 1,"gpuFactory":"nvdia","nodeList":["test1", "test2"],"cpuPerNode":118,"gpuPerNode":8,"memoryPerNode": 1500,"superParam":"{\"env\":[{\"TP_SIZE\":\"1\",\"PP_SIZE\":\"2\",\"MICRO_BATCH_SIZE\":\"4\",\"GLOBAL_BATCH_SIZE\":\"8\",\"NLAYERS\":\"1\",\"STOP_ITER\":\"5000\"}]}","testScene":" cluster"}`)
		Expect(response.Code).To(Equal(http.StatusBadRequest))
		Expect(response.Body.String()).To(MatchJSON(`{"message":"activate modelinspection info error"}`))
	})

	Context("activate modelinspection failed because of JSON unmarshal error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				return []byte(`{"invalid_json`), nil, http.StatusOK, nil
			}))
		})

		It("activate modelinspection fails due to JSON unmarshal error", func() {
			response := modelinspectionEvaSubscription(`{"gpuTotal": 1,"gpuFactory":"nvdia","nodeList":["test1", "test2"],"cpuPerNode":118,"gpuPerNode":8,"memoryPerNode": 1500,"superParam":"{\"env\":[{\"TP_SIZE\":\"1\",\"PP_SIZE\":\"2\",\"MICRO_BATCH_SIZE\":\"4\",\"GLOBAL_BATCH_SIZE\":\"8\",\"NLAYERS\":\"1\",\"STOP_ITER\":\"5000\"}]}","testScene":" cluster"}`)
			Expect(response.Code).To(Equal(http.StatusBadRequest))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})
	/* Ended by AICoder, pid:41605fb56cd1402688b1462a6b69fc6f */

	AfterEach(func() {
		fp.CleanPatches()
	})
})
