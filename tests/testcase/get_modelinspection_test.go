package cwsmtest

import (
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"errors"
	"net/http"
	"reflect"

	"cwsm/tools/commontools/db/dbutil"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/restful"

	"github.com/agiledragon/gomonkey"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("test for get modelinspection", func() {
	var fp GetModelinspectionPatches
	getUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/modelinspection"
	BeforeEach(func() {
		fp.EvaFakePatches()
	})

	Context("GET modelinspection success", func() {
		It("GET modelinspection success", func() {
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})
		AfterEach(func() {
			fp.CleanPatches()
		})
	})

	Context("GET modelinspection failed because of parameter error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(fp.pgclient), "Query", func(_ *dbutil.DBClient, table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
				return []map[string]interface{}{{"cluster": "cluster1"}}, nil
			}))
		})
		It("GET modelinspection failed because url has different cluster id", func() {
			url := constant.CwsmPrefix + "/cluster/cluster1/project/project1/apts/evaluate?evaluateId=testid"
			res := httpTestMethod("GET", url, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"db result error accessing clusterid type"}`))
		})

		It("GET modelinspection failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))

			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		/* Started by AICoder, pid:8cbd70ec52fa49d1a4fca798991c10d5 */
		It("GET modelinspection failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(`{"err":"get modelinspection info error"}`)
			}))

			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get modelinspection info error"}`))
		})

		Context("GET modelinspection failed because of JSON unmarshal error", func() {
			BeforeEach(func() {
				fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
					return []byte(`{"invalid_json`), nil, http.StatusOK, nil
				}))
			})

			It("GET modelinspection fails due to JSON unmarshal error", func() {
				res := httpTestMethod("GET", getUrl, "")
				Expect(res.Code).To(Equal(http.StatusBadRequest))
			})

			AfterEach(func() {
				fp.CleanPatches()
			})
		})
		/* Ended by AICoder, pid:8cbd70ec52fa49d1a4fca798991c10d5 */

		AfterEach(func() {
			fp.CleanPatches()
		})
	})

})
