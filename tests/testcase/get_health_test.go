package cwsmtest

import (
	"cwsm/infra/constant"
	"net/http"
	"reflect"

	"cwsm/tools/commontools/db/dbutil"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/restful"

	"github.com/agiledragon/gomonkey"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("test for get healthcheck", func() {
	var fp GetHealthPatches
	getHealthCheckUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/healthcheck"
	getHealthCheckCfgUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/healthcheckcfg"
	BeforeEach(func() {
		fp.EvaFakePatches()
	})

	Context("GET healthcheck success", func() {
		It("GET healthcheck success", func() {
			res := httpTestMethod("GET", getHealthCheckUrl, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})
		It("GET healthcheckcfg success", func() {
			res := httpTestMethod("GET", getHealthCheckCfgUrl, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})
		AfterEach(func() {
			fp.CleanPatches()
		})
	})

	Context("GET healthcheck failed because of parameter error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(fp.pgclient), "Query", func(_ *dbutil.DBClient, table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
				return []map[string]interface{}{{"cluster": "cluster1"}}, nil
			}))
		})
		It("GET healthcheck failed because url has different cluster id", func() {
			url := constant.CwsmPrefix + "/cluster/cluster1/project/project1/apts/evaluate?evaluateId=testid"
			res := httpTestMethod("GET", url, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET healthcheckcfg failed because url has different cluster id", func() {
			url := constant.CwsmPrefix + "/cluster/cluster1/project/project1/apts/evaluate?evaluateId=testid"
			res := httpTestMethod("GET", url, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET healthcheck failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))

			res := httpTestMethod("GET", getHealthCheckUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET healthcheckcfg failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))

			res := httpTestMethod("GET", getHealthCheckCfgUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})

})
