package cwsmtest

import (
	"bytes"
	"cwsm/infra/authorization"
	"cwsm/infra/wsm"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"reflect"
	"strings"

	"cwsm/tools/commontools/restful"

	"cwsm/tools/commontools/db/dbutil"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/opeanpalette/microservice"

	"github.com/agiledragon/gomonkey"
	beego "github.com/beego/beego/v2/server/web"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type FakePatches struct {
	cachePatches []*gomonkey.Patches
}

func (p *FakePatches) CleanPatches() {
	for _, patch := range p.cachePatches {
		patch.Reset()
	}
	p.cachePatches = []*gomonkey.Patches{}
}

func (p *FakePatches) AddPatches(one *gomonkey.Patches) {
	p.cachePatches = append(p.cachePatches, one)
}

func (p *FakePatches) GetPatches() []*gomonkey.Patches {
	return p.cachePatches
}

func httpTestMethod(method string, url string, reqMsg string) *httptest.ResponseRecorder {
	req := bytes.NewReader([]byte(reqMsg))
	request, _ := http.NewRequest(method, url, req)
	request.Header.Add("Content-Type", "application/json")
	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

func testGetMsbRes() []byte {
	msbRes := microservice.MsbMicroServiceInfoDto{
		ServiceName: "cloudfuze",
		Version:     "",
		URL:         "",
		Protocol:    "",
		NameSpace:   "",
		Path:        "",
		Nodes: []microservice.MsbMicroServiceNodeInfoDto{
			{
				IP:         "http://test_cloudfuze_ip",
				IPV4:       "",
				IPV6:       "",
				Port:       "test_cloudfuze_port",
				NodeID:     "",
				Status:     "",
				Appversion: "",
			},
		},
	}

	data, _ := json.Marshal(msbRes)
	return data
}

func testGetOneMsbRes() []byte {
	msbRes := []map[string]interface{}{
		{
			"aveSample": "4.08",
			"aveTflops": "37.25",
			"maxSample": "4.12",
			"maxTflops": "37.62",
			"mfu":       "0.20",
			"minSample": "2.48",
			"minTflops": "22.61",
			"modelName": "llama7B",
			"taskId":    "task-1709537491885316452",
			"trainTime": 98,
		},
	}

	data, _ := json.Marshal(msbRes)
	return data
}

func testGetCloudEnv() []byte {
	cloudenv := authorization.CloudEnvironment{
		Cloudenv: authorization.Cloudenv{
			Endpoints: []authorization.CloudfuzeEndpoint{
				{
					URL:         "https://cloudfuze:5005",
					Name:        "public-auth",
					Scope:       "",
					Version:     "",
					Keys:        "Y280cmRucWtoWmZU",
					UserName:    "name",
					TenantName:  "",
					KmsPassword: "",
					KmsKeyID:    "",
					SslAuthentication: restful.SSLAuth{
						Method:      "restful.SSLMethodTLS",
						SSLProtocol: "restful.SSLProtocolTLSv12",
						RootCert:    "/usr/share/ssl/director-ca/35c",
						ClientCert:  "/usr/share/ssl/director-south-server/director-server",
						ClientKey:   "/usr/share/ssl/director-south-server/director-server.key",
					},
				},
			},
		},
	}
	data, _ := json.Marshal(cloudenv)
	return data
}

func testGetWsmStatus() []byte {
	wsList := &wsm.WsStatusList{
		Num: 1,
		Workspace: []*wsm.WsStatus{
			{
				Id:     "workspace1",
				Name:   "testWsName",
				Status: "created",
			},
		},
	}
	data, _ := json.Marshal(wsList)
	return data
}

func testGetAdrmData() []byte {
	acceleratorInfo := map[string]interface{}{
		"items": []map[string]interface{}{
			{
				"gpus": []map[string]interface{}{
					{
						"index":    0,
						"name":     "10.160.174.210-birengpu-0000-3a-00.0",
						"status":   "healthy",
						"uuid":     "NB4B45M5066",
						"vendor":   "Biren",
						"vendorId": "1ee0",
					},
					{
						"index":    1,
						"name":     "10.160.174.210-birengpu-0000-3c-00.0",
						"status":   "healthy",
						"uuid":     "NB4B44LJ069",
						"vendor":   "Biren",
						"vendorId": "1ee0",
					},
				},
				"nodeName": "10.160.174.210",
			},
		},
	}
	data, _ := json.Marshal(acceleratorInfo)
	return data
}

func testGetGpuNodeData() []byte {
	acceleratorInfo := map[string]interface{}{
		"nodes": []string{"node1", "node2"},
	}
	data, _ := json.Marshal(acceleratorInfo)
	return data
}

func testQueryReportData() []byte {
	reportInfo := map[string]interface{}{
		"aiResult": []map[string]interface{}{
			{
				"aveSample": "4.08",
				"aveTflops": "37.25",
				"maxSample": "4.12",
				"maxTflops": "37.62",
				"mfu":       "0.20",
				"minSample": "2.48",
				"minTflops": "22.61",
				"modelName": "llama7B",
				"taskId":    "task-1709537491885316452",
				"trainTime": 98,
			},
		},
	}
	data, _ := json.Marshal(reportInfo)
	return data
}

func testActivateData() []byte {
	evaluationActivateInfo := &wsm.EvaluationValue{
		EvaluationInfo: []wsm.EvaluationTaskInfo{
			{
				Id:        "task-eva2",
				Name:      "testEvaName",
				ProjectId: "project1",
				Status:    "Running",
				Err:       "",
				TestTasks: []wsm.EvaluationTask{
					{
						Id:          "task-eva1",
						PlanId:      "evalu-uuid1",
						Name:        "testEvaName",
						Operator:    "admin",
						StartTime:   "2021-03-01T00:00:00Z",
						EndTime:     "2021-04-01T00:00:00Z",
						TestContent: json.RawMessage("{\"extend\":{},\"testCardNum\":1,\"testCase\":{\"creator\":\"admin\",\"id\":\"evalucase-1713514750789138505\",\"modelName\":\"llama\",\"modelPara\":{\"cmd\":[],\"env\":[{\"FFN_HIDDEN_SIZE\":\"5504\",\"GPUS_PER_NODE\":\"2\",\"HIDDEN_SIZE\":\"2048\",\"LR_WARMUP_STEPS\":\"100\",\"MICRO_BATCH_SIZE\":\"1\",\"NUM_HEADS\":\"16\",\"NUM_KV_HEADS\":\"4\",\"NUM_LAYERS\":\"24\",\"PP\":\"2\",\"SEQ_LENGTH\":\"2048\",\"TP\":\"1\",\"TRAIN_STEPS\":\"200\",\"ZERO_STAGE\":\"0\",\"activation_checkpoint\":\"false\",\"bf16\":\"false\",\"gradient_accumulation_steps\":\"1\",\"overlap_comm\":\"false\"}],\"modelType\":\"llama\"},\"name\":\"testmulticard\",\"projectId\":\"4d790f6de12b4013b73672e9da152203\",\"testType\":\"aitest\",\"updateTime\":\"2024/4/19 16:19:10\",\"usedCount\":1},\"testGpus\":[{\"gpu\":[{\"id\":2,\"uuid\":\"GPU-83a19e56-f6f8-ab80-174d-8417fdb10672\"}],\"nodeName\":\"l40s-minion-0-0\",\"resourceList\":[{\"type\":\"nvidia.com/gpu\",\"value\":1}]}],\"testScene\":\"singlecardtest\"}"),
						Gpus: []wsm.Gpu{
							{
								Id:   "GPU-271dc710-550f-df47-e34b-8cb334ae2048",
								Name: "l40s-minion-0-0-nvidiagpu-0000-3d-00.0",
								Type: "GPU",
							},
							{
								Id:   "GPU-83a19e56-f6f8-ab80-174d-8417fdb10672",
								Name: "l40s-minion-0-0-nvidiagpu-0000-bc-00.0",
								Type: "GPU",
							},
						},
						Status:     "Running",
						Count:      "1",
						Result:     "",
						ResultInfo: "resultInfo1",
					},
				},
			},
			{
				Id:        "task-eva1",
				Name:      "testEvaName",
				ProjectId: "project1",
				Status:    "Success",
				Err:       "",
				TestTasks: []wsm.EvaluationTask{
					{
						Id:          "task-eva1",
						PlanId:      "evalu-uuid1",
						Name:        "testEvaName",
						Operator:    "admin",
						StartTime:   "2021-03-01T01:00:00Z",
						EndTime:     "2021-04-01T01:00:00Z",
						TestContent: json.RawMessage("{\"extend\":{},\"testCardNum\":1,\"testCase\":{\"creator\":\"admin\",\"id\":\"evalucase-1713514750789138505\",\"modelName\":\"llama\",\"modelPara\":{\"cmd\":[],\"env\":[{\"FFN_HIDDEN_SIZE\":\"5504\",\"GPUS_PER_NODE\":\"2\",\"HIDDEN_SIZE\":\"2048\",\"LR_WARMUP_STEPS\":\"100\",\"MICRO_BATCH_SIZE\":\"1\",\"NUM_HEADS\":\"16\",\"NUM_KV_HEADS\":\"4\",\"NUM_LAYERS\":\"24\",\"PP\":\"2\",\"SEQ_LENGTH\":\"2048\",\"TP\":\"1\",\"TRAIN_STEPS\":\"200\",\"ZERO_STAGE\":\"0\",\"activation_checkpoint\":\"false\",\"bf16\":\"false\",\"gradient_accumulation_steps\":\"1\",\"overlap_comm\":\"false\"}],\"modelType\":\"llama\"},\"name\":\"testmulticard\",\"projectId\":\"4d790f6de12b4013b73672e9da152203\",\"testType\":\"aitest\",\"updateTime\":\"2024/4/19 16:19:10\",\"usedCount\":1},\"testGpus\":[{\"gpu\":[{\"id\":2,\"uuid\":\"GPU-83a19e56-f6f8-ab80-174d-8417fdb10672\"}],\"nodeName\":\"l40s-minion-0-0\",\"resourceList\":[{\"type\":\"nvidia.com/gpu\",\"value\":1}]}],\"testScene\":\"singlecardtest\"}"),
						Gpus: []wsm.Gpu{
							{
								Id:   "GPU-271dc710-550f-df47-e34b-8cb334ae2049",
								Name: "l40s-minion-0-1-nvidiagpu-0000-3d-00.0",
								Type: "GPU",
							},
							{
								Id:   "GPU-83a19e56-f6f8-ab80-174d-8417fdb10673",
								Name: "l40s-minion-0-1-nvidiagpu-0000-bc-00.0",
								Type: "GPU",
							},
						},
						Status:     "Success",
						Count:      "0",
						Result:     "",
						ResultInfo: "resultInfo1",
					},
				},
			},
		},
	}
	data, _ := json.Marshal(evaluationActivateInfo)
	return data
}

func testStopData() []byte {
	evaluationStopInfo := &wsm.EvaluationValue{
		EvaluationInfo: []wsm.EvaluationTaskInfo{
			{
				Status:  "stop",
				TaskIds: []string{"task-eva1"},
			},
		},
	}
	data, _ := json.Marshal(evaluationStopInfo)
	return data
}

func testPlatPerfanceData() []byte {
	logger.Info("start testPlatPerfanceData")
	platperfanceReportInfo := &wsm.PlatperfanceReport{
		PlatPerformanceReqInfo: []wsm.PlatPerformanceReqInfo{
			{
				Id:        "task-eva1-GPU-319f7338-2b8a-8c3f-9e38-627648a737be",
				TaskId:    "task-eva1",
				PlanId:    "evalu-uuid1",
				ModelName: "nccl",
				TestType:  "aitest",
				GpuAbility: []wsm.GpuAbility{
					{
						GpuUuid:         "GPU-319f7338-2b8a-8c3f-9e38-627648a737be",
						ComputeAbility:  json.RawMessage("{\"aveGpuComprehensiveComputeUtili\":0,\"minGpuComprehensiveComputeUtili\":0}"),
						StorageAbility:  json.RawMessage("{\"storageAbility\":null}"),
						NetAbility:      json.RawMessage("{\"aveGpuReceFromNvSwitchRate\":0,\"aveGpuSendToPCIeRatio\":0}"),
						HealthyAbility:  json.RawMessage("{\"sumGpuAllViolationTime\":0,\"sumGpuNumErrorNvlink\":0}"),
						EnergyAbility:   json.RawMessage("{\"aveGpuCurrentPower\":36.5,\"sumGpuTotalConsumeEnergy\":**********}"),
						ResourceAbility: json.RawMessage("{\"gpuCanUseRatio\":1,\"gpuUsedRatio\":1}"),
						TranAbility:     json.RawMessage("{\"tranAbility\":null}"),
					},
				},
				JobAbility: "{\"computeAbility\":{\"Score\":100,\"aveGpuComputeAbility\":0,\"deflectGpuComputeAbility\":0,\"sqrtGpuComputeAbility\":0},\"memAbility\":{\"Score\":100,\"aveGpuFiDevMemCopyUtil\":0,\"rateGpuFiProfDramActiveMore80\":0},\"tranAbility\":{\"Score\":100},\"netAbility\":{\"Score\":100,\"gpuReceFromNvSwitchRate\":0,\"gpuSendToPCIeRatio\":0},\"healthyAbility\":{\"Score\":100,\"gpuAllViolationTime\":0,\"gpuRatioErrorNvlink\":0},\"energyAbility\":{\"Score\":100,\"gpuNumOfLimitPower\":2,\"gpuTotalConsumeEnergy\":12420462856},\"resourceAbility\":{\"Score\":100,\"gpuCanUseRatio\":1,\"gpuUsedRatio\":1}}",
			},
		},
	}

	data, err := json.Marshal(platperfanceReportInfo)
	if err != nil {
		logger.Errorf("testPlatPerfanceData json.Marshal failed,err:", err)
		return nil
	}
	return data
}

func testInspectiontaskData() []byte {
	inspectiontaskInfo := map[string]interface{}{
		"inspect_type":  "GPU",
		"mos":           []string{"NB4B42LN051"},
		"diag_level":    "quick",
		"inspect_count": 1,
	}
	data, _ := json.Marshal(inspectiontaskInfo)
	return data
}

func testScclinspectionData() []byte {
	scclinspectionInfo := map[string]interface{}{
		"inspectType": "BRCCL",
		"cclType":     "allreduce",
		"command":     "-g 1 -c 0 -b 512 -e 800m -n 100 -t 1 -w 10",
		"nodeList":    []string{"test1", "test2"},
		"gpuPerNode":  8,
		"testScene":   "cluster",
		"outAlgbw":    0,
		"outBusbw":    0,
		"inAlgbw":     0,
		"inBusbw":     0,
	}
	data, _ := json.Marshal(scclinspectionInfo)
	return data
}

func testModelinspectionData() []byte {
	scclinspectionInfo := map[string]interface{}{
		"gpuTotal":      1,
		"gpuFactory":    "nvdia",
		"nodeList":      []string{"test1", "test2"},
		"cpuPerNode":    118,
		"gpuPerNode":    8,
		"memoryPerNode": 1500,
		"superParam":    "{\"env\":[{\"TP_SIZE\":\"1\",\"PP_SIZE\":\"2\",\"MICRO_BATCH_SIZE\":\"4\",\"GLOBAL_BATCH_SIZE\":\"8\",\"NLAYERS\":\"1\",\"STOP_ITER\":\"5000\"}]}",
		"testScene":     " cluster",
	}
	data, _ := json.Marshal(scclinspectionInfo)
	return data
}

func testHealthData() []byte {
	healthInfo := map[string]interface{}{
		"object":     "cluster",
		"objectList": []string{"test1", "test2"},
		"checkSubInfo": map[string]interface{}{
			"gpuConsistencyCheck": "gpuharddropnum",
		},
		"healthCheckCfg": map[string]interface{}{
			"gpuNums":  80,
			"rdmaNums": 80,
		},
	}
	data, _ := json.Marshal(healthInfo)
	return data
}

func testContainersCluster() []byte {
	containersCluster := authorization.ContainerClusters{
		Clusters: []authorization.ContainerCluster{
			{
				Uuid:          "cluster1",
				ContainerUuid: "envid-test",
			},
		},
	}
	data, _ := json.Marshal(containersCluster)
	return data
}

func testContainersClusterFail() []byte {
	containersCluster := authorization.ContainerClusters{
		Clusters: []authorization.ContainerCluster{
			{
				Uuid:          "cluster2",
				ContainerUuid: "envid-test",
			},
		},
	}
	data, _ := json.Marshal(containersCluster)
	return data
}

func testContainersClouds() []byte {
	containersCluster := authorization.ContainerClouds{
		ContainerCloud: []authorization.ContainerCloud{
			{
				Uuid:    "envid-test",
				Uri:     "10.160.174.210",
				EnvType: "tcf-k8s",
				SslAuthentication: authorization.SslAuthentication{
					Method:      string(restful.SSLMethodNoAuth),
					SslProtocol: string(restful.SSLProtocolTLS13),
				},
			},
		},
	}
	data, _ := json.Marshal(containersCluster)
	return data
}

func testContainersCloudsDl() []byte {
	containersCluster := authorization.ContainerClouds{
		ContainerCloud: []authorization.ContainerCloud{
			{
				Uuid:    "envid-test",
				Uri:     "10.160.174.210",
				EnvType: "k8s",
				SslAuthentication: authorization.SslAuthentication{
					Method:      string(restful.SSLMethodNoAuth),
					SslProtocol: string(restful.SSLProtocolTLS13),
				},
			},
		},
	}
	data, _ := json.Marshal(containersCluster)
	return data
}

func testContainersCloudsFail() []byte {
	containersCluster := authorization.ContainerClouds{
		ContainerCloud: []authorization.ContainerCloud{
			{
				Uuid:    "envid-test2",
				Uri:     "10.160.174.210",
				EnvType: "tcf-k8s",
				SslAuthentication: authorization.SslAuthentication{
					Method:      string(restful.SSLMethodNoAuth),
					SslProtocol: string(restful.SSLProtocolTLS13),
				},
			},
		},
	}
	data, _ := json.Marshal(containersCluster)
	return data
}

func testServiceList() []byte {
	containersCluster := v1.ServiceList{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "v1",
			Kind:       "ServiceList",
		},
		Items: []v1.Service{
			{
				TypeMeta: metav1.TypeMeta{
					APIVersion: "v1",
					Kind:       "Service",
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:      "op-aif-wsm",
					Namespace: "aiftest",
				},
				Spec: v1.ServiceSpec{
					Ports: []v1.ServicePort{
						{
							Name:     "op-aif-wsm-port",
							NodePort: 30000,
						},
					},
				},
			},
		},
	}
	data, _ := json.Marshal(containersCluster)
	return data
}

func testRdmaData() []byte {
	rdmaInfo := map[string]interface{}{
		"testScene":          "doubleNode",
		"nodeList":           []string{"test1", "test2"},
		"rdmaPerNode":        8,
		"bandWidthThreshold": 0,
	}

	data, _ := json.Marshal(rdmaInfo)
	return data
}

func testVcjobData() []byte {
	rdmaInfo := map[string]interface{}{
		"faultAnalaSwitch": "0",
	}

	data, _ := json.Marshal(rdmaInfo)
	return data
}

type PGPatches struct {
	FakePatches
	pgQueryWksMock                    []map[string]interface{}
	pgQueryEnvMock                    []map[string]interface{}
	pgQueryComputerMock               []map[string]interface{}
	pgQueryEvaPlanMock                []map[string]interface{}
	pgQueryEvaTaskMock                []map[string]interface{}
	pgQueryEvaPlanCreatedMock         []map[string]interface{}
	pgQueryEvaPlanSuccessMock         []map[string]interface{}
	pgQueryEvaPlanStopMock            []map[string]interface{}
	pgQueryEvaTaskErrMock             []map[string]interface{}
	pgQueryEvaTaskResultMock          []map[string]interface{}
	pgQueryPlatperfanceGpuAbilityMock []map[string]interface{}
	pgQueryPlatperfanceMock           []map[string]interface{}
	pgQueryEvaCaseMock                []map[string]interface{}
	pgQueryDataMock                   []map[string]interface{}
	pgclient                          *dbutil.DBClient
}

func (t *PGPatches) NewEvaluateFakePatches() *FakePatches {
	t.InitEvaluateData()
	fp := &FakePatches{}

	fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(t.pgclient), "Query", func(_ *dbutil.DBClient, table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
		return t.getEvaluatePgMock(table, nil, nil, nil)
	}))
	fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(t.pgclient), "Delete", func(_ *dbutil.DBClient, table string, condition *dbcore.Condition) bool {
		return true
	}))
	fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(t.pgclient), "Insert", func(_ *dbutil.DBClient, table string, colNames []string, rows ...*dbcore.Row) bool {
		return true
	}))
	fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(t.pgclient), "Update", func(_ *dbutil.DBClient, table string, cols map[string]interface{}, condition *dbcore.Condition) bool {
		return true
	}))

	return fp
}

func (t *PGPatches) getEvaluatePgMock(tableName string, evaperr error, evaterr error, evacerr error) ([]map[string]interface{}, error) {
	switch tableName {
	case "EvaluationPlan":
		return t.pgQueryEvaPlanMock, evaperr
	case "pgQueryEvaPlanCreatedMock":
		return t.pgQueryEvaPlanCreatedMock, evaperr
	case "pgQueryEvaTaskResultMock":
		return t.pgQueryEvaTaskResultMock, evaperr
	case "pgQueryEvaPlanSuccessMock":
		return t.pgQueryEvaPlanSuccessMock, evaperr
	case "pgQueryEvaTaskErrMock":
		return t.pgQueryEvaTaskErrMock, evaperr
	case "pgQueryEvaPlanStopMock":
		return t.pgQueryEvaPlanStopMock, evaperr
	case "EvaluationTask":
		return t.pgQueryEvaTaskMock, evaterr
	case "EvaluationCase":
		return t.pgQueryEvaCaseMock, evacerr
	case "EvaluationPlatPerformanceTable":
		return t.pgQueryPlatperfanceMock, evacerr
	case "EvaluationPlatPerformanceGpuAbilityTable":
		return t.pgQueryPlatperfanceGpuAbilityMock, evacerr
	}
	return nil, nil
}

/* Started by AICoder, pid:0dcccba722a446de886d14471dcbeea1 */
func deepCopy(src []map[string]interface{}) []map[string]interface{} {
	dst := make([]map[string]interface{}, len(src))
	for i, m := range src {
		dst[i] = make(map[string]interface{})
		for k, v := range m {
			dst[i][k] = v
		}
	}
	return dst
}

/* Ended by AICoder, pid:0dcccba722a446de886d14471dcbeea1 */

func (t *PGPatches) InitEvaluateData() {
	t.pgclient = &dbutil.DBClient{}

	t.pgQueryEvaPlanMock = []map[string]interface{}{
		{
			"id":            "evalu-uuid1",
			"name":          "testEvaName",
			"projectid":     "project1",
			"projectname":   "admin",
			"creator":       "admin",
			"operator":      "admin",
			"clusterid":     "cluster1",
			"starttime":     "2021-03-01T00:00:00Z",
			"endtime":       "2021-04-01T00:00:00Z",
			"workspaceid":   "workspace1",
			"workspacename": "workspacename1",
			"testtype":      "aitest",
			"testscene":     "[\"singleCardTest\",\"nccl\",\"singleNodeTest\",\"zccl\",\"aiModelTest\",\"sccl\"]",
			"testcontent":   "{\"extend\":\"\",\"testCardNum\":0,\"testCase\":{\"creator\":\"admin\",\"id\":\"evalucase-1709557895588535621\",\"modelName\":\"allreduce\",\"modelPara\":\"{\\\"cmd\\\":[],\\\"env\\\":{\\\"START_MIN_SIZE\\\":\\\"256M\\\", \\\"END_MAX_SIZE\\\":\\\"512M\\\", \\\"MULTIPLY_FACTOR\\\":\\\"1\\\"}}\",\"name\":\"nccl-allreduce\",\"projectId\":\"317c4a116ab74aefa037a42c2da0c442\",\"testType\":\"ccltest\",\"updateTime\":\"2024/3/4 21:11:35\",\"usedCount\":1},\"testGpus\":[{\"gpu\":[{\"id\":1,\"uuid\":\"GPU-0bc9747a-8f57-0bd6-d3dc-380ea258430d\"},{\"id\":2,\"uuid\":\"GPU-9585b577-36fc-d69f-8a5b-1dd74f1ecd1e\"}],\"nodeName\":\"l40s-minion-0-0\",\"resourceList\":[{\"type\":\"nvidia.com/gpu\",\"value\":2}]}],\"testScene\":\"aiclustertest\"}",
			"gpus":          "[{\"uuid\":\"GPU-319f7338-2b8a-8c3f-9e38-627648a737be\",\"gpuModel\":\"NVIDIA L20\"}]",
			"nodes":         "[\"cluster-icf003-minion-0-0\"]",
			"status":        "running",
			"result":        "",
		},
	}

	t.pgQueryEvaPlanCreatedMock = deepCopy(t.pgQueryEvaPlanMock)
	t.pgQueryEvaPlanCreatedMock[0]["status"] = "created"

	t.pgQueryEvaPlanSuccessMock = deepCopy(t.pgQueryEvaPlanMock)
	t.pgQueryEvaPlanSuccessMock[0]["status"] = "Success"

	t.pgQueryEvaPlanStopMock = deepCopy(t.pgQueryEvaPlanMock)
	t.pgQueryEvaPlanStopMock[0]["status"] = "stop"

	t.pgQueryEvaTaskMock = []map[string]interface{}{
		{
			"id":          "task-eva1",
			"planid":      "evalu-uuid1",
			"name":        "testEvaName",
			"projectid":   "project1",
			"projectname": "admin",
			"operator":    "admin",
			"starttime":   "2021-03-01T00:00:00Z",
			"endtime":     "2021-04-01T00:00:00Z",
			"workspaceid": "workspaceid1",
			"testtype":    "aitest",
			"testscene":   "[\"aiclustertest\",\"nccl\",\"singleNodeTest\",\"zccl\",\"aiModelTest\",\"sccl\"]",
			"testcontent": "{\"extend\":\"\",\"testCardNum\":0,\"testCase\":{\"creator\":\"admin\",\"id\":\"evalucase-1709557895588535621\",\"modelName\":\"allreduce\",\"modelPara\":\"{\\\"cmd\\\":[],\\\"env\\\":{\\\"START_MIN_SIZE\\\":\\\"256M\\\", \\\"END_MAX_SIZE\\\":\\\"512M\\\", \\\"MULTIPLY_FACTOR\\\":\\\"1\\\"}}\",\"name\":\"nccl-allreduce\",\"projectId\":\"317c4a116ab74aefa037a42c2da0c442\",\"testType\":\"ccltest\",\"updateTime\":\"2024/3/4 21:11:35\",\"usedCount\":1},\"testGpus\":[{\"gpu\":[{\"id\":1,\"uuid\":\"GPU-0bc9747a-8f57-0bd6-d3dc-380ea258430d\"},{\"id\":2,\"uuid\":\"GPU-9585b577-36fc-d69f-8a5b-1dd74f1ecd1e\"}],\"nodeName\":\"l40s-minion-0-0\",\"resourceList\":[{\"type\":\"nvidia.com/gpu\",\"value\":2}]}],\"testScene\":\"aiclustertest\"}",
			"gpuUuid":     "GPU-83a19e56-f6f8-ab80-174d-8417fdb10672",
			"gpus":        "[{\"id\":\"GPU-271dc710-550f-df47-e34b-8cb334ae2048\",\"name\":\"l40s-minion-0-0-nvidiagpu-0000-3d-00.0\",\"type\":\"GPU\"},{\"id\":\"GPU-83a19e56-f6f8-ab80-174d-8417fdb10672\",\"name\":\"l40s-minion-0-0-nvidiagpu-0000-bc-00.0\",\"type\":\"GPU\"}]",
			"status":      "Running",
			"result":      "",
			"count":       "1",
			"err":         "",
			"resultinfo":  "resultInfo1",
			"performance": "performance1",
		},
	}

	t.pgQueryEvaTaskResultMock = deepCopy(t.pgQueryEvaTaskMock)
	t.pgQueryEvaTaskResultMock[0]["result"] = "error"

	t.pgQueryEvaTaskErrMock = deepCopy(t.pgQueryEvaTaskMock)
	t.pgQueryEvaTaskErrMock[0]["err"] = "error"

	t.pgQueryEvaCaseMock = []map[string]interface{}{
		{
			"id":         "evalucase-uuid",
			"name":       "testEvaCaseName",
			"creator":    "admin",
			"projectid":  "project1",
			"testtype":   "aitest",
			"updatetime": "2021-03-01T00:00:00Z",
			"usedcount":  1,
			"modelname":  "nccl",
			"modelpara":  "{\"env\": {\"key1\": \"value1\"}}",
		},
	}

	t.pgQueryPlatperfanceMock = []map[string]interface{}{
		{
			"id":         "task-eva1-GPU-319f7338-2b8a-8c3f-9e38-627648a737be",
			"taskid":     "task-eva1",
			"planid":     "evalu-uuid1",
			"testtype":   "aitest",
			"modelname":  "nccl",
			"jobability": "{\"computeAbility\":{\"Score\":100,\"aveGpuComputeAbility\":0,\"deflectGpuComputeAbility\":0,\"sqrtGpuComputeAbility\":0},\"memAbility\":{\"Score\":100,\"aveGpuFiDevMemCopyUtil\":0,\"rateGpuFiProfDramActiveMore80\":0},\"tranAbility\":{\"Score\":100},\"netAbility\":{\"Score\":100,\"gpuReceFromNvSwitchRate\":0,\"gpuSendToPCIeRatio\":0},\"healthyAbility\":{\"Score\":100,\"gpuAllViolationTime\":0,\"gpuRatioErrorNvlink\":0},\"energyAbility\":{\"Score\":100,\"gpuNumOfLimitPower\":2,\"gpuTotalConsumeEnergy\":12420462856},\"resourceAbility\":{\"Score\":100,\"gpuCanUseRatio\":1,\"gpuUsedRatio\":1}}",
		},
	}

	t.pgQueryPlatperfanceGpuAbilityMock = []map[string]interface{}{
		{
			"id":         "task-eva1-GPU-319f7338-2b8a-8c3f-9e38-627648a737be",
			"taskid":     "task-eva1",
			"planid":     "evalu-uuid1",
			"testtype":   "aitest",
			"modelname":  "nccl",
			"gpuability": "[{\"gpuUuid\":\"GPU-319f7338-2b8a-8c3f-9e38-627648a737be\",\"computeAbility\":{\"aveGpuComprehensiveComputeUtili\":0,\"minGpuComprehensiveComputeUtili\":0},\"storageAbility\":null,\"netAbility\":{\"aveGpuReceFromNvSwitchRate\":0,\"aveGpuSendToPCIeRatio\":0},\"healthyAbility\":{\"sumGpuAllViolationTime\":0,\"sumGpuNumErrorNvlink\":0},\"energyAbility\":{\"aveGpuCurrentPower\":36.5,\"sumGpuTotalConsumeEnergy\":**********},\"resourceAbility\":{\"gpuCanUseRatio\":1,\"gpuUsedRatio\":1},\"tranAbility\":null}]",
		},
	}

	err := os.Setenv("gateway_host", "inner-router-director")
	if err != nil {
		fmt.Printf("Error setting environment variable: %v\n", err)
		return
	}
}

type OperateEvaluatePatches struct {
	PGPatches
}

func (t *OperateEvaluatePatches) EvaFakePatches() *FakePatches {
	fp := t.PGPatches.NewEvaluateFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
			return testContainersCluster(), make(map[string][]string), http.StatusOK, nil
		}
		if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
			return testContainersClouds(), make(map[string][]string), http.StatusOK, nil
		}
		if strings.Contains(url, "acceleratordevices") {
			return testGetAdrmData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
		}
		if strings.Contains(url, "gpunodes") {
			return testGetGpuNodeData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
		}
		return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
	}))

	fp.AddPatches(gomonkey.ApplyFunc(authorization.GetFromCloudfuzeToGetCloudEnv, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
		return testGetCloudEnv(), make(map[string][]string), http.StatusOK, nil
	}))

	fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		if urlpath == "https://msb:443/opapi/wsm/v1/project1/apts/evaluate/queryreport/evalu-uuid1/action?project_name=admin" {
			return testQueryReportData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
		}
		return nil, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
		return testGetWsmStatus(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	fp.AddPatches(gomonkey.ApplyFunc(restful.StopMethod, func(urlpath string, reqHeaders map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
		return testStopData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type ActivateInspectiontaskPatches struct {
	OperateEvaluatePatches
}

func (t *ActivateInspectiontaskPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		return testInspectiontaskData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type DeleteInspectiontaskPatches struct {
	OperateEvaluatePatches
}

func (t *DeleteInspectiontaskPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	return fp
}

type CreateEvaluatePatches struct {
	OperateEvaluatePatches
}

func (t *CreateEvaluatePatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	return fp
}

func (p *CreateEvaluatePatches) EvaInput() []byte {
	input := map[string]interface{}{
		"id":            "evalu-uuid1",
		"name":          "testEvaName",
		"projectid":     "project1",
		"projectname":   "admin",
		"creator":       "admin",
		"operator":      "admin",
		"clusterid":     "cluster1",
		"starttime":     "2021-03-01T00:00:00Z",
		"endtime":       "2021-04-01T00:00:00Z",
		"workspaceid":   "workspace1",
		"workspacename": "workspacename1",
		"testtype":      "aitest",
		"testscene":     "[\"singleCardTest\",\"nccl\",\"singleNodeTest\",\"zccl\",\"aiModelTest\",\"sccl\"]",
		"testcontent":   "{\"extend\":\"\",\"testCardNum\":0,\"testCase\":{\"creator\":\"admin\",\"id\":\"evalucase-1709557895588535621\",\"modelName\":\"allreduce\",\"modelPara\":\"{\\\"cmd\\\":[],\\\"env\\\":{\\\"START_MIN_SIZE\\\":\\\"256M\\\", \\\"END_MAX_SIZE\\\":\\\"512M\\\", \\\"MULTIPLY_FACTOR\\\":\\\"1\\\"}}\",\"name\":\"nccl-allreduce\",\"projectId\":\"317c4a116ab74aefa037a42c2da0c442\",\"testType\":\"ccltest\",\"updateTime\":\"2024/3/4 21:11:35\",\"usedCount\":1},\"testGpus\":[{\"gpu\":[{\"id\":1,\"uuid\":\"GPU-0bc9747a-8f57-0bd6-d3dc-380ea258430d\"},{\"id\":2,\"uuid\":\"GPU-9585b577-36fc-d69f-8a5b-1dd74f1ecd1e\"}],\"nodeName\":\"l40s-minion-0-0\",\"resourceList\":[{\"type\":\"nvidia.com/gpu\",\"value\":2}]}],\"testScene\":\"aiclustertest\"}",
		"gpus":          "[{\"uuid\":\"GPU-319f7338-2b8a-8c3f-9e38-627648a737be\",\"gpuModel\":\"NVIDIA L20\"}]",
		"nodes":         "[\"cluster-icf003-minion-0-0\"]",
		"status":        "running",
		"result":        "",
	}
	body, _ := json.Marshal(input)
	return body
}

type GetEvaluatePatches struct {
	OperateEvaluatePatches
}

func (t *GetEvaluatePatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		return testPlatPerfanceData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type ActivateEvaluatePatches struct {
	OperateEvaluatePatches
}

func (t *ActivateEvaluatePatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		if urlpath == "https://msb:443/opapi/wsm/v1/project1/apts/evaluate/queryreport/evalu-uuid1/platperfance?project_name=admin" {
			return testPlatPerfanceData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
		}
		return testActivateData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type DeleteEvaluatePatches struct {
	OperateEvaluatePatches
}

func (t *DeleteEvaluatePatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.DeleteToWsm, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string,
		int, error) {
		return nil, make(map[string][]string), http.StatusOK, nil
	}))

	return fp
}

type StopEvaluatePatches struct {
	OperateEvaluatePatches
}

func (t *StopEvaluatePatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()
	return fp
}

type CreateEvaluateCasePatches struct {
	OperateEvaluatePatches
}

func (t *CreateEvaluateCasePatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	return fp
}

func (p *CreateEvaluateCasePatches) EvaInput(modelPara string) []byte {
	input := map[string]interface{}{
		"name":      "testEvaCaseName",
		"creator":   "admin",
		"projectId": "project1",
		"testType":  "ccltest",
		"modelName": "zccl",
		"modelPara": modelPara,
	}
	body, _ := json.Marshal(input)
	return body
}

type DeleteEvaluateCasePatches struct {
	OperateEvaluatePatches
}

func (t *DeleteEvaluateCasePatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	return fp
}

func (p *DeleteEvaluateCasePatches) EvaInput() []byte {
	input := map[string]interface{}{
		"testCaseIds": `["evalucase-uuid1", "evalucase-uuid2"]`,
	}
	body, _ := json.Marshal(input)
	return body
}

type GetScclinspectionPatches struct {
	OperateEvaluatePatches
}

func (t *GetScclinspectionPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	return fp
}

type ActivateScclinspectionPatches struct {
	OperateEvaluatePatches
}

func (t *ActivateScclinspectionPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		return testScclinspectionData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type StopScclinspectionPatches struct {
	OperateEvaluatePatches
}

func (t *StopScclinspectionPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		return nil, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type ActivateRdmaPatches struct {
	OperateEvaluatePatches
}

func (t *ActivateRdmaPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(restful.PostMethod, func(url string, headers map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
		return testRdmaData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type GetRdmaPatches struct {
	OperateEvaluatePatches
}

func (t *GetRdmaPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {

		return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
	}))

	return fp
}

type StopRdmaPatches struct {
	OperateEvaluatePatches
}

func (t *StopRdmaPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		return nil, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type ActivateModelinspectionPatches struct {
	OperateEvaluatePatches
}

func (t *ActivateModelinspectionPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		return testModelinspectionData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type GetModelinspectionPatches struct {
	OperateEvaluatePatches
}

func (t *GetModelinspectionPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	return fp
}

type StopModelinspectionPatches struct {
	OperateEvaluatePatches
}

func (t *StopModelinspectionPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.DeleteToWsm, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string,
		int, error) {
		return nil, make(map[string][]string), http.StatusOK, nil
	}))

	return fp
}

type DeleteModelinspectionPatches struct {
	OperateEvaluatePatches
}

func (t *DeleteModelinspectionPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.DeleteToWsm, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string,
		int, error) {
		return nil, make(map[string][]string), http.StatusOK, nil
	}))

	return fp
}

type ActivateHealthPatches struct {
	OperateEvaluatePatches
}

func (t *ActivateHealthPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(restful.PostMethod, func(url string, headers map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
		return testHealthData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type GetHealthPatches struct {
	OperateEvaluatePatches
}

func (t *GetHealthPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	return fp
}

type GetVcjobEventsPatches struct {
	OperateEvaluatePatches
}

func (t *GetVcjobEventsPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		if url == "https://msb:443/opapi/wsm/v1/apts/nsvcjobevents/projectname1" || url == "https://msb:443/opapi/wsm/v1/apts/vcjobs?projectname=projectname1" {
			return testGetOneMsbRes(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
		}
		return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
	}))

	return fp
}

type ActivateVcjobCfgPatches struct {
	OperateEvaluatePatches
}

func (t *ActivateVcjobCfgPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(restful.PostMethod, func(url string, headers map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
		return testVcjobData(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type ExportReportPatches struct {
	OperateEvaluatePatches
}

func (t *ExportReportPatches) EvaFakePatches() *FakePatches {
	fp := t.OperateEvaluatePatches.EvaFakePatches()

	return fp
}

func (t *PGPatches) NewFakePatches() *FakePatches {
	t.InitData()
	fp := &FakePatches{}

	fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(t.pgclient), "Query", func(_ *dbutil.DBClient, table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
		return t.getPgMock(table, nil, nil, nil, nil)
	}))
	fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(t.pgclient), "Delete", func(_ *dbutil.DBClient, table string, condition *dbcore.Condition) bool {
		return true
	}))
	fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(t.pgclient), "Insert", func(_ *dbutil.DBClient, table string, colNames []string, rows ...*dbcore.Row) bool {
		return true
	}))
	fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(t.pgclient), "Update", func(_ *dbutil.DBClient, table string, cols map[string]interface{}, condition *dbcore.Condition) bool {
		return true
	}))

	return fp
}

func (t *PGPatches) getPgMock(tableName string, wkserr error, enverr error, computererr error, dataerr error) ([]map[string]interface{}, error) {
	switch tableName {
	case "WorkSpace":
		return t.pgQueryWksMock, wkserr
	case "Environment":
		return t.pgQueryEnvMock, enverr
	case "ComputeRsc":
		return t.pgQueryComputerMock, computererr
	case "DataRsc":
		return t.pgQueryDataMock, dataerr
	}
	return nil, nil
}

func (t *PGPatches) InitData() {
	t.pgclient = &dbutil.DBClient{}

	t.pgQueryWksMock = []map[string]interface{}{
		{
			"name":              "testWsName",
			"description":       "This is workspace 1",
			"clusterid":         "cluster1",
			"id":                "workspace1",
			"projectid":         "project1",
			"projectname":       "admin",
			"status":            "stopped",
			"environmentid":     "env_uuid",
			"computeresourceid": "computeResource_uuid",
			"dataresourceid":    "dataResource_uuid",
			"createtime":        "2021-01-01T00:00:00Z",
			"activetime":        "2021-02-01T00:00:00Z",
			"updatetime":        "2021-03-01T00:00:00Z",
			"stoptime":          "2021-04-01T00:00:00Z",
			"creator":           "admin",
			"activateuser":      "admin",
			"metadata":          `{"key1": "value1", "key2": "value3"}`,
		},
	}

	t.pgQueryEnvMock = []map[string]interface{}{
		{
			"id":         "env_uuid",
			"name":       "defaultEnvironmentName",
			"projectid":  "defaultProjectId",
			"isshared":   true,
			"tools":      "[{\"toolName\":\"op-ai-colossalai\",\"type\":\"AIE\",\"command\":\"testCommand\",\"arguments\":[\"arg1\",\"arg2\"],\"packageName\":\"op-ai-colossalai-7.23.30-06-15922854\",\"url\":\"http://1.2.3.4:8080\"}]",
			"creator":    "admin",
			"updatetime": "2023-01-01 01:01:01",
			"metadata":   `{"key1": "value1","key2": "value2"}`,
		},
	}

	t.pgQueryComputerMock = []map[string]interface{}{
		{
			"id":         "computeResource_uuid",
			"name":       "defaultComputeResourceName",
			"projectId":  "defaultProjectId",
			"isShared":   true,
			"cpuCores":   10.0,
			"cpuMemory":  50000000000,
			"gpu":        "2",
			"creator":    "admin",
			"updateTime": "2023-01-01 01:01:01",
			"metadata":   `{"key1": "value1","key2": "value2"}`,
		},
	}

	t.pgQueryDataMock = []map[string]interface{}{
		{
			"id":         "dataResource_uuid",
			"name":       "defaultDataResourceName",
			"projectId":  "defaultProjectId",
			"isShared":   true,
			"context":    "{\"dataResourceTools\":[{\"dataResourceType\":\"NFS\",\"dataResourceContext\":\"nfsName/***********:/home/<USER>/;/uuuu/dddd/;/configs\"},{\"dataResourceType\":\"PVC\",\"dataResourceContext\":\"pvcName/claimName/storageClassName/accessModes/storage/volumeMode\"}]}",
			"creator":    "admin",
			"updateTime": "2023-01-01 01:01:01",
			"metadata":   `{"key1": "value1","key2": "value2"}`,
		},
	}

	err := os.Setenv("gateway_host", "inner-router-director")
	if err != nil {
		fmt.Printf("Error setting environment variable: %v\n", err)
		return
	}
}

type OperateWorkSpacePatches struct {
	PGPatches
}

func (t *OperateWorkSpacePatches) NewFakePatches() *FakePatches {
	fp := t.PGPatches.NewFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
			return testContainersCluster(), make(map[string][]string), http.StatusOK, nil
		}
		if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
			return testContainersClouds(), make(map[string][]string), http.StatusOK, nil
		}
		return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
	}))

	fp.AddPatches(gomonkey.ApplyFunc(authorization.GetFromCloudfuzeToGetCloudEnv, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
		return testGetCloudEnv(), make(map[string][]string), http.StatusOK, nil
	}))

	fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		return nil, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
		return testGetWsmStatus(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

type CreateWorkSpacePatches struct {
	OperateWorkSpacePatches
}

func (t *CreateWorkSpacePatches) NewFakePatches() *FakePatches {
	fp := t.OperateWorkSpacePatches.NewFakePatches()

	return fp
}

func (p *CreateWorkSpacePatches) Input() []byte {
	input := map[string]interface{}{
		"name":              "workspacename1",
		"projectName":       "project1",
		"description":       "This is workspace 1",
		"environmentId":     "environment1",
		"computeResourceId": "computeResource1",
		"dataResourceId":    "dataResource1",
		"updateTime":        "2023-01-01 01:01:01",
		"creator":           "admin",
		"metadata":          "aa",
	}
	body, _ := json.Marshal(input)
	return body
}

type DeleteWorkSpacePatches struct {
	OperateWorkSpacePatches
}

func (t *DeleteWorkSpacePatches) NewFakePatches() *FakePatches {
	fp := t.OperateWorkSpacePatches.NewFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.DeleteToWsm, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string,
		int, error) {
		return nil, make(map[string][]string), http.StatusOK, nil
	}))

	return fp
}

type KeepaliveWorkSpacePatches struct {
	keepaliveRsp wsm.WsmKeepaliveRsp
	OperateWorkSpacePatches
}

func (t *KeepaliveWorkSpacePatches) NewFakePatches() *FakePatches {
	fp := t.OperateWorkSpacePatches.NewFakePatches()

	fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
		map[string][]string, int, error) {
		return t.KeepaliveRsp(), map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))

	return fp
}

func (t *KeepaliveWorkSpacePatches) SetWorkSpaces(worksapcecs []map[string]interface{}) {
	var result []map[string]interface{}
	for _, w := range worksapcecs {
		workspace := make(map[string]interface{})
		wksMock := reflect.ValueOf(t.pgQueryWksMock[0])
		for _, key := range wksMock.MapKeys() {
			workspace[key.String()] = wksMock.MapIndex(key).Interface()
		}
		workspace["clusterid"] = w["clusterid"]
		workspace["id"] = w["id"]
		workspace["projectid"] = w["projectid"]
		result = append(result, workspace)
	}
	t.pgQueryWksMock = result
}

func (t *KeepaliveWorkSpacePatches) SetKeepaliveRspUuid(uuids []string) {
	t.keepaliveRsp.Num = len(uuids)
	t.keepaliveRsp.Ids = uuids
}

func (t *KeepaliveWorkSpacePatches) KeepaliveRsp() []byte {
	data, _ := json.Marshal(t.keepaliveRsp)
	return data
}

type ModifyWorkSpacePatches struct {
	OperateWorkSpacePatches
}

func (t *ModifyWorkSpacePatches) NewFakePatches() *FakePatches {
	fp := t.OperateWorkSpacePatches.NewFakePatches()
	t.AddThreeElements()
	fp.AddPatches(gomonkey.ApplyFunc(restful.PutMethod, func(urlpath string, reqHeaders map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
		return nil, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
	}))
	return fp
}

func (p *ModifyWorkSpacePatches) Input(wsName string) []byte {
	input := map[string]interface{}{
		"name":              wsName,
		"projectName":       "project1",
		"description":       "This is workspace 1 modify",
		"environmentId":     "env_uuid_2",
		"computeResourceId": "computeResource_uuid_2",
		"dataResourceId":    "dataResource_uuid_2",
		"updateTime":        "2023-01-01 01:01:01",
		"creator":           "admin",
		"metadata":          "aa",
	}
	body, _ := json.Marshal(input)
	return body
}

func (t *ModifyWorkSpacePatches) AddThreeElements() {
	newEnv := make(map[string]interface{})
	for k, v := range t.pgQueryEnvMock[0] {
		newEnv[k] = v
	}
	newEnv["id"] = "env_uuid_2"
	t.pgQueryEnvMock = append(t.pgQueryEnvMock, newEnv)

	newComputer := make(map[string]interface{})
	for k, v := range t.pgQueryEnvMock[0] {
		newComputer[k] = v
	}
	newComputer["id"] = "computeResource_uuid_2"
	t.pgQueryComputerMock = append(t.pgQueryComputerMock, newComputer)

	newData := make(map[string]interface{})
	for k, v := range t.pgQueryDataMock[0] {
		newData[k] = v
	}
	newData["id"] = "dataResource_uuid_2"
	t.pgQueryDataMock = append(t.pgQueryDataMock, newData)
}

func (t *ModifyWorkSpacePatches) AddWorkspace(wsName string) {
	newWS := make(map[string]interface{})
	for k, v := range t.pgQueryEnvMock[0] {
		newWS[k] = v
	}
	newWS["name"] = wsName
	newWS["id"] = "workspace2"
	t.pgQueryWksMock = append(t.pgQueryWksMock, newWS)
}
