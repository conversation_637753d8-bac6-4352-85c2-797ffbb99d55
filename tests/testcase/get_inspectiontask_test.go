package cwsmtest

import (
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"errors"
	"net/http"
	"reflect"

	"cwsm/tools/commontools/db/dbutil"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/restful"

	"github.com/agiledragon/gomonkey"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("test for get inspectiontask", func() {
	var fp OperateEvaluatePatches
	getUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/inspectiontask"
	BeforeEach(func() {
		fp.NewEvaluateFakePatches()
	})

	Context("GET inspectiontask success", func() {
		It("GET inspectiontask success", func() {
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})
		AfterEach(func() {
			fp.CleanPatches()
		})
	})

	Context("GET inspectiontask failed because of parameter error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(fp.pgclient), "Query", func(_ *dbutil.DBClient, table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
				return []map[string]interface{}{{"cluster": "cluster1"}}, nil
			}))
		})
		It("GET inspectiontask success", func() {
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})
		It("GET inspectiontask failed because url has different cluster id", func() {
			url := constant.CwsmPrefix + "/cluster/cluster1/project/project1/apts/evaluate?evaluateId=testid"
			res := httpTestMethod("GET", url, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"db result error accessing clusterid type"}`))
		})

		It("GET inspectiontask failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))

			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})

	/* Started by AICoder, pid:c2b9b6c21962470face2bf4ab6e54e8c */
	It("GET inspectiontask returns error from WSM", func() {
		fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
			return nil, nil, 0, errors.New(`{"err":"get inspectiontask info error"}`)
		}))

		res := httpTestMethod("GET", getUrl, "")
		Expect(res.Code).To(Equal(http.StatusBadRequest))
		Expect(res.Body.String()).To(MatchJSON(`{"message":"get inspectiontask info error"}`))
	})

	AfterEach(func() {
		fp.CleanPatches()
	})

	Context("GET inspectiontask failed because of JSON unmarshal error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{"invalid_json`), nil, http.StatusOK, nil
			}))
		})

		It("GET inspectiontask fails due to JSON unmarshal error", func() {
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})
	/* Ended by AICoder, pid:c2b9b6c21962470face2bf4ab6e54e8c */

})
