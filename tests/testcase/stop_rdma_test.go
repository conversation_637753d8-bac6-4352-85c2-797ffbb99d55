package cwsmtest

import (
	"bytes"
	"cwsm/infra/constant"
	"cwsm/tools/commontools/restful"
	"errors"
	"net/http"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey"
	beego "github.com/beego/beego/v2/server/web"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func stopRdmaEvaSubscription(reqBody string) *httptest.ResponseRecorder {
	body := bytes.NewReader([]byte(reqBody))
	request, err := http.NewRequest("POST", constant.CwsmPrefix+"/cluster/cluster1/apts/rdma/stop", body)
	Expect(err == nil).To(Equal(true))
	request.Header.Add("Content-Type", "application/json")

	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

var _ = Describe("test for stopping by rdma", func() {
	var fp StopRdmaPatches
	BeforeEach(func() {
		fp.NewEvaluateFakePatches()
	})

	It("stop rdma, Return 200 When stop rdma Succeed", func() {
		response := stopRdmaEvaSubscription(`{}`)
		Expect(response.Code).To(Equal(http.StatusOK))
	})

	/* Started by AICoder, pid:afa7194e432144968fcf6ada9e869269 */
	It("stop rdma, Return 400 When Wsm stop rdma Build Restful Req Failed", func() {
		fp.AddPatches(gomonkey.ApplyFunc(restful.PostMethod, func(urlpath string, reqHeaders map[string]string, body []byte, auth *restful.SSLAuth) ([]byte,
			map[string][]string, int, error) {
			return nil, nil, 0, errors.New(`{"err":"stop rdma info error"}`)
		}))

		response := stopRdmaEvaSubscription("")
		Expect(response.Code).To(Equal(http.StatusBadRequest))
		Expect(response.Body.String()).To(MatchJSON(`{"message":"stop rdma info error"}`))
	})

	It("stop rdma, Return 400 When Wsm stop rdma Build Restful Req Failed", func() {
		fp.AddPatches(gomonkey.ApplyFunc(restful.PostMethod, func(urlpath string, reqHeaders map[string]string, body []byte, auth *restful.SSLAuth) ([]byte,
			map[string][]string, int, error) {
			return nil, make(map[string][]string), http.StatusUnauthorized, nil
		}))

		response := stopRdmaEvaSubscription("")
		Expect(response.Code).To(Equal(http.StatusBadRequest))
	})

	Context("stop rdma failed because of JSON unmarshal error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.PostMethod, func(url string, headers map[string]string, body []byte, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				return []byte(`{"invalid_json`), nil, http.StatusUnauthorized, nil
			}))
		})

		It("stop rdma fails due to JSON unmarshal error", func() {
			response := stopRdmaEvaSubscription("")
			Expect(response.Code).To(Equal(http.StatusBadRequest))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})
	/* Ended by AICoder, pid:afa7194e432144968fcf6ada9e869269 */

	AfterEach(func() {
		fp.CleanPatches()
	})
})
