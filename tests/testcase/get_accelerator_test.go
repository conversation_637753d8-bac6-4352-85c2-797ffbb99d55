package cwsmtest

import (
	"cwsm/infra/authorization"
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/restful"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/agiledragon/gomonkey"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("test for get accelerator devices", func() {
	var fp OperateEvaluatePatches
	getUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/acceleratordevices"
	getUrlNodes := constant.CwsmPrefix + "/cluster/cluster1/apts/gpunodes"
	BeforeEach(func() {
		fp.EvaFakePatches()
	})

	Context("GET accelerator devices success", func() {
		It("GET accelerator devices info success", func() {
			res := httpTestMethod("GET", getUrl, "")
			var result map[string]interface{}
			json.Unmarshal(res.Body.Bytes(), &result)
			Expect(res.Code).To(Equal(http.StatusOK))
			Expect(len(result["items"].([]interface{}))).To(Equal(1))
		})
		It("GET gpu nodes info success", func() {
			res := httpTestMethod("GET", getUrlNodes, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})

	Context("GET accelerator devices failed because of adrm reason", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyFunc(authorization.GetFromCloudfuzeToGetCloudEnv, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return testGetCloudEnv(), make(map[string][]string), http.StatusOK, nil
			}))
		})

		It("GET accelerator devices failed because get cloudfuze port failed", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
					return testContainersCluster(), make(map[string][]string), http.StatusOK, nil
				}
				if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
					return testContainersClouds(), make(map[string][]string), http.StatusOK, nil
				}
				return []byte{}, map[string][]string{}, http.StatusBadRequest, errors.New("get cloudfuze port error")
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})
		It("GET accelerator devices failed because unmarshal cloudfuze port failed ", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
					return testContainersCluster(), make(map[string][]string), http.StatusOK, nil
				}
				if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
					return testContainersClouds(), make(map[string][]string), http.StatusOK, nil
				}
				return []byte{}, map[string][]string{}, http.StatusOK, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})
		It("GET accelerator devices failed because get keystone params form cloudfuze failed ", func() {
			fp.AddPatches(gomonkey.ApplyFunc(authorization.GetFromCloudfuzeToGetCloudEnv, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{}, http.StatusBadRequest, errors.New("get keystone params form cloudfuze error")
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})
		It("GET accelerator devices failed because unmarshal keystone params failed ", func() {
			fp.AddPatches(gomonkey.ApplyFunc(authorization.GetFromCloudfuzeToGetCloudEnv, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{}, http.StatusBadRequest, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})
		It("GET accelerator devices failed because get openpaletteauth from keystone failed ", func() {
			fp.AddPatches(gomonkey.ApplyFunc(authorization.PostToGetToken, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{}, http.StatusBadRequest, errors.New("get openpaletteauth from keystone error")
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})
		It("GET accelerator devices failed because get openpaletteauth from keystone response not statusCreated", func() {
			fp.AddPatches(gomonkey.ApplyFunc(authorization.PostToGetToken, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{}, http.StatusBadRequest, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})
		It("GET accelerator devices failed because keystone header error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(authorization.PostToGetToken, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{}, http.StatusCreated, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})
		It("GET accelerator devices failed because Unmarshal openpaletteauth from keystone failed", func() {
			fp.AddPatches(gomonkey.ApplyFunc(authorization.PostToGetToken, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{}, http.StatusCreated, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})
		It("GET accelerator devices failed because get data from adrm has error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetData, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusCreated, errors.New("get data form adrm error")
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})
		It("GET accelerator devices failed because get data from adrm response code not ok", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetData, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusBadRequest, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})
		It("GET accelerator device failed because get data from adrm unmarshal body error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetData, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})
		/* Started by AICoder, pid:8fa26162c7764097add362353046b180 */
		It("GET accelerator device failed env not found", func() {
			err := os.Unsetenv("gateway_host")
			if err != nil {
				fmt.Printf("Error unsetting environment variable: %v\n", err)
				return
			}
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})

		It("GET accelerator device failed envid not found", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
					return testContainersCluster(), make(map[string][]string), http.StatusBadRequest, fmt.Errorf("request failed")
				}
				if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
					return testContainersClouds(), make(map[string][]string), http.StatusOK, nil
				}
				return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})

		It("GET accelerator device failed for dl scene", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
					return testContainersCluster(), make(map[string][]string), http.StatusBadRequest, fmt.Errorf("request failed")
				}
				if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
					return testContainersCloudsDl(), make(map[string][]string), http.StatusOK, nil
				}
				return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
			}))
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetData, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})

		It("GET accelerator device failed for dl scene env not found", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
					return testContainersCluster(), make(map[string][]string), http.StatusBadRequest, fmt.Errorf("request failed")
				}
				if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
					return testContainersCloudsDl(), make(map[string][]string), http.StatusOK, nil
				}
				return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
			}))
			err := os.Unsetenv("gateway_host")
			if err != nil {
				fmt.Printf("Error unsetting environment variable: %v\n", err)
				return
			}
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})

		It("GET accelerator device failed clouds unmarshal failed", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
					return testContainersCluster(), make(map[string][]string), http.StatusOK, nil
				}
				if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
					return []byte("invalid json"), make(map[string][]string), http.StatusOK, nil
				}
				return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})

		It("GET accelerator device failed clouds not found envid", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
					return testContainersCluster(), make(map[string][]string), http.StatusOK, nil
				}
				if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
					return testContainersCloudsFail(), make(map[string][]string), http.StatusOK, nil
				}
				return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})

		It("GET accelerator device failed for dl scene getmethod for clusters failed", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
					return testContainersCluster(), make(map[string][]string), http.StatusBadRequest, fmt.Errorf("request failed")
				}
				if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
					return testContainersClouds(), make(map[string][]string), http.StatusOK, nil
				}
				return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})

		It("GET accelerator device failed clusters not found clusterid", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
					return testContainersClusterFail(), make(map[string][]string), http.StatusOK, nil
				}
				if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
					return testContainersClouds(), make(map[string][]string), http.StatusOK, nil
				}
				return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get gpu info failed"}`))
		})

		It("GET accelerator device success for dl scene", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				if strings.Contains(url, "/api/v1.0/pvrm/containerclusters") {
					return testContainersCluster(), make(map[string][]string), http.StatusOK, nil
				}
				if strings.Contains(url, "/api/v1.0/pvrm/containerclouds") {
					return testContainersCloudsDl(), make(map[string][]string), http.StatusOK, nil
				}
				if strings.Contains(url, "/api/v1/services") {
					return testServiceList(), make(map[string][]string), http.StatusOK, nil
				}
				return testGetMsbRes(), make(map[string][]string), http.StatusOK, nil
			}))
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetData, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		/* Ended by AICoder, pid:8fa26162c7764097add362353046b180 */

		/* Started by AICoder, pid:0b254682a8874035a70719a8989217ef */
		It("GET gpu nodes failed because get data from wsm has error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetData, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusCreated, errors.New("get data form adrm error")
			}))
			res := httpTestMethod("GET", getUrlNodes, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get node info failed"}`))
		})
		It("GET gpu nodes failed because get data from wsm response code not ok", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetData, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusBadRequest, nil
			}))
			res := httpTestMethod("GET", getUrlNodes, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get node info failed"}`))
		})
		It("GET gpu nodes failed because get data from wsm unmarshal body error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetData, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))
			res := httpTestMethod("GET", getUrlNodes, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get node info failed"}`))
		})
		/* Ended by AICoder, pid:0b254682a8874035a70719a8989217ef */

		AfterEach(func() {
			fp.CleanPatches()
		})
	})

	AfterEach(func() {
		fp.CleanPatches()
	})
})
