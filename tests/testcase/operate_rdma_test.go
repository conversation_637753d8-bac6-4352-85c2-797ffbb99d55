package cwsmtest

import (
	"bytes"
	"cwsm/infra/constant"
	"cwsm/tools/commontools/restful"
	"errors"

	"net/http"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey"
	beego "github.com/beego/beego/v2/server/web"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func rdmaEvaSubscription(reqBody string) *httptest.ResponseRecorder {
	body := bytes.NewReader([]byte(reqBody))
	request, err := http.NewRequest("POST", constant.CwsmPrefix+"/cluster/cluster1/apts/rdma", body)
	Expect(err == nil).To(Equal(true))
	request.Header.Add("Content-Type", "application/json")

	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

var _ = Describe("test for activate rdma", func() {
	var fp ActivateRdmaPatches
	BeforeEach(func() {
		fp.EvaFakePatches()
	})

	Context("activate rdma", func() {

		It("activate rdma, Return 200 When activate Succeed", func() {
			response := rdmaEvaSubscription(`{"testScene": "doubleNode","nodeList":["test1", "test2"],"rdmaPerNode":8,"bandWidthThreshold":0}`)
			Expect(response.Code).To(Equal(http.StatusOK))
		})
	})
	AfterEach(func() {
		fp.CleanPatches()
	})

	/* Started by AICoder, pid:617a866860734c9386bbc2c67c5f6e3b */
	It("activate rdma post to wsm fail", func() {
		fp.AddPatches(gomonkey.ApplyFunc(restful.PostMethod, func(url string, headers map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
			return nil, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, errors.New(`{"err":"activate rdma info error"}`)
		}))

		response := rdmaEvaSubscription(`{"testScene": "doubleNode","nodeList":["test1", "test2"],"rdmaPerNode":8,"bandWidthThreshold":0}`)
		Expect(response.Code).To(Equal(http.StatusBadRequest))
		Expect(response.Body.String()).To(MatchJSON(`{"message":"activate rdma info error"}`))
	})

	AfterEach(func() {
		fp.CleanPatches()
	})

	Context("activate rdma failed because of JSON unmarshal error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.PostMethod, func(url string, headers map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{"invalid_json`), nil, http.StatusOK, nil
			}))
		})

		It("activate rdma fails due to JSON unmarshal error", func() {
			response := rdmaEvaSubscription(`{"testScene": "doubleNode","nodeList":["test1", "test2"],"rdmaPerNode":8,"bandWidthThreshold":0}`)
			Expect(response.Code).To(Equal(http.StatusBadRequest))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})
	/* Ended by AICoder, pid:617a866860734c9386bbc2c67c5f6e3b */

})
