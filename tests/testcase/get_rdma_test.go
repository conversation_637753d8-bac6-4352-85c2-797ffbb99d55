package cwsmtest

import (
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"errors"
	"net/http"
	"reflect"

	"cwsm/tools/commontools/db/dbutil"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/restful"

	"github.com/agiledragon/gomonkey"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("test for get healthcheck", func() {
	var fp GetRdmaPatches
	getRdmaCheckUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/rdmabandwidth"
	getRdmaCheckCfgUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/queryrdmatopo"
	BeforeEach(func() {
		fp.NewEvaluateFakePatches()
	})

	Context("GET rdmacheck success", func() {
		It("GET rdmacheck success", func() {
			res := httpTestMethod("GET", getRdmaCheckUrl, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})
		It("GET rdmacheckcfg success", func() {
			res := httpTestMethod("GET", getRdmaCheckCfgUrl, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})
		AfterEach(func() {
			fp.CleanPatches()
		})
	})

	Context("GET rdmacheck failed because of parameter error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(fp.pgclient), "Query", func(_ *dbutil.DBClient, table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
				return []map[string]interface{}{{"cluster": "cluster1"}}, nil
			}))
		})
		It("GET rdmacheck failed because url has different cluster id", func() {
			url := constant.CwsmPrefix + "/cluster/cluster1/project/project1/apts/evaluate?evaluateId=testid"
			res := httpTestMethod("GET", url, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET rdmacheckcfg failed because url has different cluster id", func() {
			url := constant.CwsmPrefix + "/cluster/cluster1/project/project1/apts/evaluate?evaluateId=testid"
			res := httpTestMethod("GET", url, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET rdmacheck failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))

			res := httpTestMethod("GET", getRdmaCheckUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET rdmacheckcfg failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))

			res := httpTestMethod("GET", getRdmaCheckCfgUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		/* Started by AICoder, pid:04c53a5d221f457bb185bccc3700233d */
		It("GET rdmacheck returns internal error from WSM", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(`{"err":"get rdmacheck info error"}`)
			}))

			res := httpTestMethod("GET", getRdmaCheckUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET rdmacheckcfg returns internal error from WSM", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(`{"err":"get rdmacheckcfg info error"}`)
			}))

			res := httpTestMethod("GET", getRdmaCheckCfgUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET rdmacheck returns error from WSM", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(`{"err":"get rdmacheck info error"}`)
			}))

			res := httpTestMethod("GET", getRdmaCheckUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get rdmacheck info error"}`))
		})

		It("GET rdmacheckcfg returns error from WSM", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(`{"err":"get rdmacheckcfg info error"}`)
			}))

			res := httpTestMethod("GET", getRdmaCheckCfgUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get rdmacheckcfg info error"}`))
		})
		/* Ended by AICoder, pid:04c53a5d221f457bb185bccc3700233d */

		AfterEach(func() {
			fp.CleanPatches()
		})
	})

})
