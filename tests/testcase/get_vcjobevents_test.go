package cwsmtest

import (
	"cwsm/infra/constant"
	"net/http"
	"reflect"

	"cwsm/tools/commontools/db/dbutil"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/restful"

	"github.com/agiledragon/gomonkey"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("test for get vcjobevent", func() {
	var fp GetVcjobEventsPatches
	getVcjobEventsCheckUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/vcjobevents/projectName1/name1"
	getVcjobNsEventsCheckUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/nsvcjobevents/projectName1"
	getVcjobsCheckCfgUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/vcjobs/projectname1"
	getVcjobsCfgUrl := constant.CwsmPrefix + "/cluster/cluster1/apts//faultanalacfg"
	BeforeEach(func() {
		fp.NewEvaluateFakePatches()
	})

	Context("GET vcjobevent success", func() {
		It("GET vcjobevents success", func() {
			res := httpTestMethod("GET", getVcjobEventsCheckUrl, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})
		It("GET vcjobnsevent success", func() {
			res := httpTestMethod("GET", getVcjobNsEventsCheckUrl, "")
			Expect(res.Body.String()).To(MatchJSON(`{"message":"an internal error occurred in the code"}`))
		})
		It("GET vcjob success", func() {
			res := httpTestMethod("GET", getVcjobsCheckCfgUrl, "")
			Expect(res.Body.String()).To(MatchJSON(`{"message":"an internal error occurred in the code"}`))
		})
		It("GET vcjob cfg success", func() {
			res := httpTestMethod("GET", getVcjobsCfgUrl, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})
		AfterEach(func() {
			fp.CleanPatches()
		})
	})

	Context("GET vcjobevent failed because of parameter error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(fp.pgclient), "Query", func(_ *dbutil.DBClient, table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
				return []map[string]interface{}{{"cluster": "cluster1"}}, nil
			}))
		})
		It("GET vcjobevents failed because url has different cluster id", func() {
			url := constant.CwsmPrefix + "/cluster/cluster1/project/project1/apts/evaluate?evaluateId=testid"
			res := httpTestMethod("GET", url, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET vcjobnsevent failed because url has different cluster id", func() {
			url := constant.CwsmPrefix + "/cluster/cluster1/project/project1/apts/evaluate?evaluateId=testid"
			res := httpTestMethod("GET", url, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET vcjob failed because url has different cluster id", func() {
			url := constant.CwsmPrefix + "/cluster/cluster1/project/project1/apts/evaluate?evaluateId=testid"
			res := httpTestMethod("GET", url, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET vcjob cfg failed because url has different cluster id", func() {
			url := constant.CwsmPrefix + "/cluster/cluster1/project/project1/apts/evaluate?evaluateId=testid"
			res := httpTestMethod("GET", url, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET vcjobevents failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))

			res := httpTestMethod("GET", getVcjobEventsCheckUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET vcjobnsevent failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))

			res := httpTestMethod("GET", getVcjobNsEventsCheckUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET vcjob failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))

			res := httpTestMethod("GET", getVcjobsCheckCfgUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET vcjob cfg failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(url string, headers map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))

			res := httpTestMethod("GET", getVcjobsCfgUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})

})
