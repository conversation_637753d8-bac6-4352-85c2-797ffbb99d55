package main

import (
	"context"
	"cwsm/controllers"
	"cwsm/infra/configcenter"
	"cwsm/kafkaservice"
	"cwsm/menu"
	"cwsm/models"
	"cwsm/models/vcjob_handler"
	_ "cwsm/routers"
	"cwsm/tools/commontools/kafka"
	"cwsm/tools/commontools/logger"
	"os"

	db_init "cwsm/tools/commontools/db/dbutil/db-init"

	zteconfigure "zte.com.cn/cms/crmX/commontools/configure"

	ztemsbservice "zte.com.cn/cms/crmX/commontools/service/msbservice"

	zteinitcheck "zte.com.cn/cms/crmX/commontools/infa/initcheck"

	beego "github.com/beego/beego/v2/server/web"
)

func main() {
	//logger
	logger.Init()

	if beego.BConfig.RunMode == "dev" {
		beego.BConfig.WebConfig.DirectoryIndex = true
		beego.BConfig.WebConfig.StaticDir["/swagger"] = "swagger"
	}
	settingInfo, err := zteconfigure.NewConfigure("conf/setting.json")
	if err != nil {
		logger.Errorf("NewConfigure setting failed: %v", err.Error())
		return
	} else {
		logger.Info("NewConfigure setting success")
	}

	//msb配置
	msbConf := settingInfo.GetMSBConfig()
	ztemsbservice.InitMSB(*msbConf)
	logger.Info("msbConf:%v", msbConf)

	err = zteinitcheck.BeforeCheck4Otcp()
	if err != nil {
		logger.Errorf("BeforeCheck4Otcp failed: %v", err.Error())
		return
	}

	// 开发模式的配置
	devConf := settingInfo.GetDevConfig()
	// 数据库的配置信息
	db_init.InitDB(*devConf)
	models.CreateDbTable()

	configcenter.RegisterVcjobRetentionConfigToConfigCenter()
	configcenter.GetSwitchAndDayAndNumberConfigCenterValue()
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	go controllers.GetVcjobSwitchSynchronization(ctx)

	kafka.InitKafka(&kafkaservice.ConsumerCWSM{})

	logger.Info("cwsm begin reg menu")
	menu.RegisterIUIMenu()

	logger.Info("Init history vcjob handler")
	go vcjob_handler.InitHistoryVcjobHandler(ctx)

	go controllers.StartUpdateRunningPlan(ctx)

	/* Started by AICoder, pid:w3c94l9c44145e4145d20a78a02b2b05759969ef */
	// 从环境变量中获取 enable_ssl 的值，并判断是否为 "true"
	enableSSL := os.Getenv("enable_ssl") == "true"

	// 根据 enableSSL 的值设置 Beego 的 HTTP 和 HTTPS 配置
	beego.BConfig.Listen.EnableHTTP = !enableSSL
	beego.BConfig.Listen.EnableHTTPS = enableSSL

	// 启动 Beego 应用
	beego.Run()
	/* Ended by AICoder, pid:w3c94l9c44145e4145d20a78a02b2b05759969ef */
}
