package menu

import (
	"cwsm/infra/constant"
	"cwsm/tools/commontools/logger"
	"os"
	"time"

	"cwsm/tools/commontools/kafka"

	"github.com/beego/beego/v2/core/logs"
	"zte.com.cn/cms/crmX/commontools/infa/util"
)

func RegisterIUIMenu() {
	fileContent, err := util.ReadFile(constant.MemuFilePath)
	if err != nil {
		logger.Error("init menu register error:", err.Error())
		return
	}

	register := func() {
		for {
			if success := doRegisterToIUI(fileContent); !success {
				logs.Error("register to ui failed")
				logs.Error("sleep 5 second and try again")
				time.Sleep(time.Second * 5)
				continue
			}
			break
		}
	}
	go register()
}

func doRegisterToIUI(valueByte []byte) bool {
	getKafkaTopic := func() string {
		tenant := os.Getenv("OPENPALETTE_NAMESPACE")
		if tenant == "" {
			return "zenap-uiframe-menu-change"
		}
		return tenant + "-zenap-uiframe-menu-change"
	}
	return kafka.GetKafkaProducer().SendKafkaMsgWithKey(getKafkaTopic(), valueByte, "apts-cwsm")
}
