package menu

import (
	"fmt"
	"strings"
)

type IuiMenu struct {
	MenusConf []Menus `json:"menusConf"`
	MenuType  string  `json:"type"`
}

type Menus struct {
	ID              string `json:"id"`
	EnName          string `json:"en_name"`
	ZhN<PERSON>          string `json:"zh_name"`
	IconClass       string `json:"iconClass"`
	Href            string `json:"href"`
	ParentID        string `json:"parentId"`
	Display         string `json:"display"`
	Order           string `json:"order"`
	CacheName       string `json:"cacheName"`
	ShiftJs         string `json:"shiftJs"`
	HidePageSidebar string `json:"hidePageSidebar"`
	Operation       string `json:"operation"`
}

func (i *IuiMenu) GetViewPageURL(productID string) string {
	return i.getUrlOfSampleID("console-view", productID)
}

func (i *IuiMenu) GetBuyPageURL(productID string) string {
	return i.getUrlOfSampleID("console-buy", productID)
}

func (i *IuiMenu) getUrlOfSampleID(id, productID string) string {
	for _, menu := range i.MenusConf {
		if strings.EqualFold(fmt.Sprintf("%s-%s", productID, id), menu.ID) {
			return menu.Href
		}
	}
	return ""
}
