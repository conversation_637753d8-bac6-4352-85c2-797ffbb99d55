package menu

import (
	"cwsm/tools/commontools/kafka"
	"testing"

	"zte.com.cn/cms/crmX/commontools/infa/util"

	. "github.com/smartystreets/goconvey/convey"

	"github.com/agiledragon/gomonkey"
)

func TestRegister1(t *testing.T) {
	Convey("Test get tools name and version by package Name success", t, func() {
		kafka.InitKafkaProducerClient()
		fileContent := []byte("test menu content")
		// 打桩 util.ReadFile 函数
		patches := gomonkey.ApplyFunc(util.ReadFile, func(_ string) ([]byte, error) {
			return fileContent, nil
		})
		patch1 := gomonkey.ApplyFuncSeq(doRegisterToIUI, []gomonkey.OutputCell{
			{Values: gomonkey.Params{false}},
			{Values: gomonkey.Params{true}},
		})
		defer patches.Reset()
		defer patch1.Reset()
		RegisterIUIMenu()
	})

}
