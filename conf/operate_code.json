{"service_type": "cwsm", "service_version": "v6.18.10.35", "override": false, "operation_list": [{"nodeid": "operation.maintenance", "allowtenant": false, "nodename": ["Operation Maintenance", "运维"], "leaf": false, "level": 4, "description": ["Operation Maintenance", "运维"]}, {"nodeid": "operation.maintenance.enhancement", "allowtenant": false, "nodename": ["Intelligent Computing Performance Evaluation", "智算性能评测"], "leaf": false, "level": 3, "description": ["Intelligent Computing Performance Evaluation", "智算性能评测"], "parent_id": "operation.maintenance", "menuID": "director-operation-maintenance-enhancement"}, {"nodeid": "operation.maintenance.enhancement.health", "allowtenant": false, "nodename": ["Health Check", "健康检查"], "leaf": false, "level": 3, "description": ["Health Check", "健康检查"], "parent_id": "operation.maintenance.enhancement", "menuID": "director-operation-maintenance-enhancement-health"}, {"urlMap": ["/cluster/[\\w-_]+healthcheckcfg_GET", "/cluster/[\\w-_]+healthcheck_GET"], "nodeid": "operation.maintenance.enhancement.health.view", "allowtenant": false, "nodename": ["View Health Check Inspect", "查看健康检查"], "leaf": true, "level": 3, "description": ["View Health Check Inspect", "查看健康检查"], "parent_id": "operation.maintenance.enhancement.health", "pre_operation": ""}, {"nodeid": "operation.maintenance.inspection", "allowtenant": false, "nodename": ["Performance Inspection", "性能巡检"], "leaf": false, "level": 3, "description": ["Performance Inspection", "性能巡检"], "parent_id": "operation.maintenance.enhancement"}, {"nodeid": "operation.maintenance.inspection.ips", "allowtenant": false, "nodename": ["Inspection Parameter Settings", "巡检参数设置"], "leaf": true, "level": 3, "description": ["Inspection Parameter Settings", "巡检参数设置"], "parent_id": "operation.maintenance.inspection", "menuID": "director-operation-maintenance-inspection-ips"}, {"urlMap": ["/cluster/[\\w-_]+/inspectiontask_POST"], "nodeid": "operation.maintenance.inspection.ips.apply", "allowtenant": false, "nodename": ["Apply Inspection Parameter Settings", "执行巡检参数设置"], "leaf": true, "level": 3, "description": ["Apply Inspection Parameter Settings", "执行巡检参数设置"], "parent_id": "operation.maintenance.inspection.ips", "pre_operation": "operation.maintenance.inspection.ips.view"}, {"urlMap": ["/cluster/[\\w-_]+/inspectiontask_GET"], "nodeid": "operation.maintenance.inspection.ips.view", "allowtenant": false, "nodename": ["View Inspection Parameter Settings", "查看巡检参数设置"], "leaf": true, "level": 3, "description": ["View Inspection Parameter Settings", "查看巡检参数设置"], "parent_id": "operation.maintenance.inspection.ips", "pre_operation": ""}, {"nodeid": "operation.maintenance.inspection.gpu", "allowtenant": false, "nodename": ["GPU Diagnostic", "GPU诊断"], "leaf": true, "level": 3, "description": ["GPU Diagnostic", "GPU诊断"], "parent_id": "operation.maintenance.inspection", "menuID": "director-operation-maintenance-inspection-gpu"}, {"urlMap": ["/cluster/[\\w-_]+/inspectiontask_POST"], "nodeid": "operation.maintenance.inspection.gpu.apply", "allowtenant": false, "nodename": ["Apply GPU Diagnostic Inspection", "执行GPU诊断"], "leaf": true, "level": 3, "description": ["Apply GPU Diagnostic Inspection", "执行GPU诊断"], "parent_id": "operation.maintenance.inspection.gpu", "pre_operation": "operation.maintenance.inspection.gpu.view"}, {"urlMap": ["/cluster/[\\w-_]+/inspectiontask_GET"], "nodeid": "operation.maintenance.inspection.gpu.view", "allowtenant": false, "nodename": ["View GPU Diagnostic Inspection", "查看GPU诊断"], "leaf": true, "level": 3, "description": ["View GPU Diagnostic Inspection", "查看GPU诊断"], "parent_id": "operation.maintenance.inspection.gpu", "pre_operation": ""}, {"nodeid": "operation.maintenance.inspection.rdma", "allowtenant": false, "nodename": ["RDMA Performance Test", "RDMA性能检测"], "leaf": true, "level": 3, "description": ["RDMA Performance Test", "RDMA性能检测"], "parent_id": "operation.maintenance.inspection", "menuID": "director-operation-maintenance-inspection-rdma"}, {"urlMap": ["/cluster/[\\w-_]+/rdma_POST"], "nodeid": "operation.maintenance.inspection.rdma.apply", "allowtenant": false, "nodename": ["Apply RDMA Performance Test Inspection", "执行RDMA性能检测"], "leaf": true, "level": 3, "description": ["Apply RDMA Performance Test Inspection", "执行RDMA性能检测"], "parent_id": "operation.maintenance.inspection.rdma", "pre_operation": "operation.maintenance.inspection.rdma.view"}, {"urlMap": ["/cluster/[\\w-_]+/rdma_GET"], "nodeid": "operation.maintenance.inspection.rdma.view", "allowtenant": false, "nodename": ["View RDMA Performance Test Inspection", "查看RDMA性能检测"], "leaf": true, "level": 3, "description": ["View RDMA Performance Test Inspection", "查看RDMA性能检测"], "parent_id": "operation.maintenance.inspection.rdma", "pre_operation": ""}, {"nodeid": "operation.maintenance.inspection.clt", "allowtenant": false, "nodename": ["Collective Communication Performance Test", "集合通讯性能检测"], "leaf": true, "level": 3, "description": ["Collective Communication Performance Test", "集合通讯性能检测"], "parent_id": "operation.maintenance.inspection", "menuID": "director-operation-maintenance-inspection-clt"}, {"urlMap": ["/cluster/[\\w-_]+/scclinspection_POST"], "nodeid": "operation.maintenance.inspection.clt.apply", "allowtenant": false, "nodename": ["Apply Collective Communication Performance Test Inspection", "执行集合通讯性能检测"], "leaf": true, "level": 3, "description": ["Apply Collective Communication Performance Test Inspection", "执行集合通讯性能检测"], "parent_id": "operation.maintenance.inspection.clt", "pre_operation": "operation.maintenance.inspection.clt.view"}, {"urlMap": ["/cluster/[\\w-_]+/scclinspection_GET"], "nodeid": "operation.maintenance.inspection.clt.view", "allowtenant": false, "nodename": ["View Collective Communication Performance Test Inspection", "查看集合通讯性能检测"], "leaf": true, "level": 3, "description": ["View Collective Communication Performance Test Inspection", "查看集合通讯性能检测"], "parent_id": "operation.maintenance.inspection.clt", "pre_operation": ""}, {"nodeid": "operation.maintenance.inspection.mdt", "allowtenant": false, "nodename": ["Model Performance Test", "模型性能检测"], "leaf": true, "level": 3, "description": ["Model Performance Test", "模型性能检测"], "parent_id": "operation.maintenance.inspection", "menuID": "director-operation-maintenance-inspection-mdt"}, {"urlMap": ["/cluster/[\\w-_]+/scclinspection_POST"], "nodeid": "operation.maintenance.inspection.mdt.apply", "allowtenant": false, "nodename": ["Apply Model Performance Test Inspection", "执行模型性能检测"], "leaf": true, "level": 3, "description": ["Apply Model Performance Test Inspection", "执行模型性能检测"], "parent_id": "operation.maintenance.inspection.mdt", "pre_operation": "operation.maintenance.inspection.mdt.view"}, {"urlMap": ["/cluster/[\\w-_]+/scclinspection_GET"], "nodeid": "operation.maintenance.inspection.mdt.view", "allowtenant": false, "nodename": ["View Model Performance Test Inspection", "查看模型性能检测"], "leaf": true, "level": 3, "description": ["View Model Performance Test Inspection", "查看模型性能检测"], "parent_id": "operation.maintenance.inspection.mdt", "pre_operation": "operation.maintenance.inspection.mdt.view"}, {"nodeid": "operation.infrastructure.container.vcjob.faultllocate", "nodename": ["Fault Demarcation", "故障定界"], "leaf": true, "level": 4, "description": ["Fault Demarcation", "故障定界"], "parent_id": "operation.infrastructure.container.vcjob", "pre_operation": "", "doubleAuth": false, "allowtenant": false}, {"nodeid": "operation.maintenance.plan", "allowtenant": false, "nodename": ["Inspection Plan", "巡检计划"], "leaf": false, "level": 3, "description": ["Inspection Plan", "巡检计划"], "parent_id": "operation.maintenance.enhancement", "menuID": "director-operation-maintenance-plan"}, {"urlMap": ["/cluster/[\\w-_]+inspectionplan_GET"], "nodeid": "operation.maintenance.plan.view", "allowtenant": false, "nodename": ["Inspection Plan List", "查看巡检计划列表"], "leaf": true, "level": 3, "description": ["Inspection Plan List", "查看巡检计划列表"], "parent_id": "operation.maintenance.plan", "pre_operation": ""}, {"nodeid": "operation.infrastructure.container.vcjob.modelinspection", "nodename": ["modelinspection", "模型性能检测"], "leaf": true, "level": 4, "description": ["perform model performance evaluation on historical vcjob", "对历史作业进行模型性能检测"], "parent_id": "operation.infrastructure.container.vcjob", "pre_operation": "", "doubleAuth": false, "allowtenant": false}]}