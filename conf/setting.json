{"devconf": {"isdev": false, "msbip": "*************", "msbinnerport": "10080", "header": {"csrftoken": "107a0efc0f745eecf6144761943b16c2", "Cookie": "csrftoken-10080=107a0efc0f745eecf6144761943b16c2; Z-AUTH-CODE-10080=1003010635_2a05ed0fa3ba3b921c33ffbb389d4284", "AuthCode": "1045931579_bbb82bce3468da3556e8c958860678a6", "UserAgent": "Director-cwsm"}, "database": {"dbdriver": "postgres", "dbtype": "memory", "dbhost": "*************", "dbport": "4555", "dbuser": "postgres", "dbpassword": "", "dbname": "cwsm"}}, "msb": {"msbip": "inner-router-director", "msbinnerport": "8241"}, "other": {"language": "zh"}}