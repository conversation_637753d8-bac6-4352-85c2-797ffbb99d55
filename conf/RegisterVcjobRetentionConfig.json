{"service": {"name": "cwsm", "displayNameI18n": {"zh-CN": "cwsm", "en-US": "cwsm"}}, "microservice": {"name": "cwsm-cwsm", "displayNameI18n": {"zh-CN": "cwsm 服务", "en-US": "cwsm Service"}}, "config": {"groups": [{"groupId": "root-vcjob-config", "sorter": 10, "displayNameI18n": {"zh-CN": "智算任务配置", "en-US": "Intelligent Calculation Tasks config"}, "visible": true}, {"groupId": "vcjob-faultanalaswitch-config", "parentGroupId": "root-vcjob-config", "sorter": 30, "displayNameI18n": {"zh-CN": "故障定界", "en-US": "Fault Demarcation"}, "visible": true}], "items": [{"key": "resource-faultanala-switch", "groupId": "vcjob-faultanalaswitch-config", "category": "basic", "sorter": 10, "attribute": "dynamic", "displayNameI18n": {"zh-CN": "作业调度故障定界开关", "en-US": "<PERSON>uling <PERSON>ault Demarcation Switch"}, "descriptionI18n": {"zh-CN": "用于支持故障定界，默认数值为关，不支持故障定界", "en-US": "Used to support fault demarcation. The default value is off"}, "visible": true, "required": false, "maskType": "plaintext", "currentValue": "false", "value": {"type": "boolean", "boolDisplayStyle": "switch"}}]}}