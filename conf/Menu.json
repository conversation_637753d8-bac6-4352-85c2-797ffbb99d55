{"menusConf": [{"id": "director-operation-maintenance-enhancement", "en_name": "Intelligent Computing Performance Evaluation", "zh_name": "智算性能评测", "order": "3000", "iconClass": "", "parentId": "director-operation-maintenance", "operation": "", "href": "", "rootUrl": "", "cacheName": "", "shiftJs": "", "licenseId": ""}, {"id": "director-operation-maintenance-enhancement-health", "en_name": "Health Check", "zh_name": "健康检查", "order": "3010", "iconClass": "plx-ico-health-check-16", "parentId": "director-operation-maintenance-enhancement", "operation": "operation.maintenance.enhancement.health", "href": "/cwsmportal/#/main/inspection/health", "rootUrl": "", "cacheName": "", "shiftJs": "", "licenseId": ""}, {"id": "director-operation-maintenance-inspection", "en_name": "Performance Inspection", "zh_name": "性能巡检", "order": "3020", "iconClass": "plx-ico-performance-16", "parentId": "director-operation-maintenance-enhancement", "operation": "", "href": "", "rootUrl": "", "cacheName": "", "shiftJs": "", "licenseId": ""}, {"id": "director-operation-maintenance-inspection-ips", "en_name": "Inspection Parameter Settings", "zh_name": "巡检参数设置", "order": "3030", "iconClass": "", "parentId": "director-operation-maintenance-inspection", "operation": "operation.maintenance.inspection.ips.view", "href": "/cwsmportal/#/main/inspection/ips", "rootUrl": "", "cacheName": "", "shiftJs": "", "licenseId": ""}, {"id": "director-operation-maintenance-inspection-gpu", "en_name": "GPU Diagnostic", "zh_name": "GPU诊断", "order": "3040", "iconClass": "", "parentId": "director-operation-maintenance-inspection", "operation": "operation.maintenance.inspection.gpu.view", "href": "/cwsmportal/#/main/inspection/gpu", "rootUrl": "", "cacheName": "", "shiftJs": "", "licenseId": ""}, {"id": "director-operation-maintenance-inspection-rdma", "en_name": "RDMA Performance Test", "zh_name": "RDMA性能检测", "order": "3050", "iconClass": "", "parentId": "director-operation-maintenance-inspection", "operation": "operation.maintenance.inspection.rdma.view", "href": "/cwsmportal/#/main/inspection/rdma", "rootUrl": "", "cacheName": "", "shiftJs": "", "licenseId": ""}, {"id": "director-operation-maintenance-inspection-clt", "en_name": "Collective Communication Performance Test", "zh_name": "集合通讯性能检测", "order": "3060", "iconClass": "", "parentId": "director-operation-maintenance-inspection", "operation": "operation.maintenance.inspection.clt.view", "href": "/cwsmportal/#/main/inspection/clt", "rootUrl": "", "cacheName": "", "shiftJs": "", "licenseId": ""}, {"id": "director-operation-maintenance-inspection-mdt", "en_name": "Model Performance Test", "zh_name": "模型性能检测", "order": "3070", "iconClass": "", "parentId": "director-operation-maintenance-inspection", "operation": "operation.maintenance.inspection.mdt.view", "href": "/cwsmportal/#/main/inspection/mdt", "rootUrl": "", "cacheName": "", "shiftJs": "", "licenseId": ""}, {"id": "director-operation-maintenance-plan", "en_name": "Inspection Plan", "zh_name": "巡检计划", "order": "3080", "iconClass": "plx-ico-inspection-plan-16", "parentId": "director-operation-maintenance-enhancement", "operation": "operation.maintenance.plan.view", "href": "/cwsmportal/#/main/plan", "rootUrl": "", "cacheName": "", "shiftJs": "", "licenseId": ""}], "type": "register"}