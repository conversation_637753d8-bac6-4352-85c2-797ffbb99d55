
# 任务
作为一名资深的go语言编程师，使用代码实现深拷贝[]map[string]interface{}

------------------------------

# 任务
作为一名资深的go语言编程师，请实现一个函数func updateEvaluationResultFromWsm(evaluateId string, reqParam EvaluationRequestParam, task map[string]interface{}) (string, error)。调用getEvaluationstatusFromWsm和updateTaskResult函数。如果错误返回return "", err。taskResult不需要定义一个为""的初始值
功能为输出string类型的变量taskResult

type EvaluationRequestParam struct {
	ClusterId  string
	ProjectId  string
	EvaluateId string
}

func getEvaluationstatusFromWsm(evaluateId string, reqParam EvaluationRequestParam, task map[string]interface{}) (*wsm.TaskStatusList, error) {
	wsmKeywords := wsm.EvaluationWsmKeywords{
		ClusterId:   reqParam.ClusterId,
		ProjectId:   reqParam.ProjectId,
		ProjectName: task["projectName"].(string),
		Name:        strings.ToLower(task["name"].(string)),
		Id:          reqParam.EvaluateId,
	}
	evaluationValue, suc := wsm.AptsHandler(wsmKeywords, nil, "get")
	if !suc {
		return nil, errors.New("AptsHandler failed")
	}
	updatePlanDbStatus(evaluateId, evaluationValue.Status)
	taskStatusList := &wsm.TaskStatusList{}
	for _, evalTask := range evaluationValue.TestTasks {
		if evalTask.Status == "" {
			evalTask.Status = "created"
		}
		taskStatus := &wsm.TaskStatus{
			Id:     evalTask.Id,
			Name:   evalTask.Name,
			Status: evalTask.Status,
			Result: evalTask.Result,
		}
		updateTaskDbStatus(evalTask.Id, evalTask.Status)
		taskStatusList.Task = append(taskStatusList.Task, taskStatus)
	}
	taskStatusList.Num = len(taskStatusList.Task)
	return taskStatusList, nil
}

func updateTaskResult(task map[string]interface{}, statusList *wsm.TaskStatusList) (string, error) {
	taskId, ok := task["id"].(string)
	if !ok {
		return "", errors.New("taskId not found")
	}
	if taskId == "" {
		return "", errors.New("task id is none")
	} else {
		for _, eva := range statusList.Task {
			if task["result"] != eva.Result {
				if updateTaskDbResult(eva.Id, eva.Result) {
					task["result"] = eva.Result
				}
			}
			return task["result"].(string), nil
		}
	}
	return task["result"].(string), nil
}

func updateTaskDbResult(taskName string, result string) bool {
	if suc := models.UpdateResourceByName(constant.TABLE_NAME_EvaluationTask, taskName, map[string]interface{}{"result": result}); suc {
		logger.Debug("update evaluate %s status to %s", taskName, result)
		return true
	}
	logger.Errorf("update db evaluate %s status %s failed", taskName, result)
	return false
}

## 代码输出要求
- 生成的代码中不得出现中文，打印也不要出现中文。

------------------------------

# 任务
作为一名资深的go语言编程师，请实现一个函数func func updateEvaluationEndTimeFromWsm(evaluateId string, reqParam EvaluationRequestParam, task map[string]interface{}) error。调用getEvaluationEndTimeFromWsm和updateTaskEndtime函数。如果错误返回return err, err。evaEndTime不需要定义一个为""的初始值，调用updateTaskEndtime函数的返回值定义为errtaskEndTime

type EvaluationRequestParam struct {
	ClusterId  string
	ProjectId  string
	EvaluateId string
}

func getEvaluationEndTimeFromWsm(evaluateId string, reqParam EvaluationRequestParam, task map[string]interface{}) (*wsm.TaskEndTime, error) {
	wsmKeywords := wsm.EvaluationWsmKeywords{
		ClusterId:   reqParam.ClusterId,
		ProjectId:   reqParam.ProjectId,
		ProjectName: task["projectName"].(string),
		Name:        strings.ToLower(task["name"].(string)),
		Id:          reqParam.EvaluateId,
	}
	evaluationValue, suc := wsm.AptsHandler(wsmKeywords, nil, "get")
	if !suc {
		return nil, errors.New("AptsHandler failed")
	}
	taskEndTime := &wsm.TaskEndTime{}
	for _, evalTask := range evaluationValue.TestTasks {
		taskEndTime = &wsm.TaskEndTime{
			Id:      evalTask.Id,
			Name:    evalTask.Name,
			EndTime: evalTask.EndTime,
		}
		updateTaskDbEndtime(evaluateId, evalTask.EndTime)
	}
	return taskEndTime, nil
}

func updateTaskEndtime(task map[string]interface{}, endTime *wsm.TaskEndTime) error {
	taskId, ok := task["id"].(string)
	if !ok {
		return errors.New("taskId not found")
	}
	if taskId == "" {
		return errors.New("task id is none")
	} else {
		if task["endTime"] != endTime.EndTime {
			if updateTaskDbEndtime(endTime.Id, endTime.EndTime) {
				task["endTime"] = endTime.EndTime
			}
		}
		return nil
	}
}

func updateTaskDbEndtime(taskId string, endTime string) bool {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_EvaluationTask, taskId, map[string]interface{}{"endTime": endTime}); suc {
		logger.Debug("update evaluate %s status to %s", taskId, endTime)
		return true
	}
	logger.Errorf("update db evaluate %s status %s failed", taskId, endTime)
	return false
}

## 代码输出要求
- 生成的代码中不得出现中文，打印也不要出现中文。

------------------------------
# 任务
作为一名资深的go语言编程师，请实现一个函数func MapSlicetoMap(in []map[string]interface{}) (map[string]interface{}, error)
功能为使[]map[string]interface{}类型转换成map[string]interface{}类型。
## 代码输出要求
- 生成的代码中不得出现中文，打印也不要出现中文。
