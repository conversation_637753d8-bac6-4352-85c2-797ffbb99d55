# 任务
作为一个资深的go语言程序员，你需要编写一个函数func StringToMap(s string) map[string]interface{}，用于将string类型转换成map[string]interface{}，函数不需要返回error，json.Unmarshal需要err := json.Unmarshal([]byte(s), &m)调用方法，error输出为logger.Errorf("string to map failed %s", s)
## 代码输出要求
- 生成的代码中不得出现中文，打印也不要出现中文。

------------------------------

# 任务
作为一个资深的go语言程序员，你需要编写一个函数func planReqBodyToJson(data PlatPerfance) (map[string]interface{}, error) 。其中data PlatPerfance为输入输出为(map[string]interface{}, error) 类型，不需要模拟数据。
type PlatPerfance struct {
	PlatPerformanceReqInfo PlatPerformanceReqInfo `json:"platPerformanceReqInfo"`
}

type EvaluationPerformanceRequestParam struct {
	ClusterId        string `json:"clusterId"`
	ProjectId        string `json:"projectId"`
	EvaluationTaskId string `json:"evaluationTaskId"`
}

type GpuData struct {
	Name     string `json:"name"`
	Status   string `json:"status"`
	Vendor   string `json:"vendor"`
	VendorId string `json:"vendorId"`
	Index    int    `json:"index"`
	UUID     string `json:"uuid"`
	GpuModel string `json:"gpuModel"`
	Node     string `json:"node"`
}
## 代码输出要求
- 生成的代码中不得出现中文，打印也不要出现中文。

------------------------------

# 任务
作为一个资深的go语言程序员，你需要编写一个函数func Struct2Map(in interface{}) (map[string]interface{}, error)，用于将 interface{}类型转换成map[string]interface{}，需要使用到j, err := json.Marshal(in)和err = json.Unmarshal(j, &m)和for i := 0; i < t.NumField(); i++
## 代码输出要求
- 生成的代码中不得出现中文，打印也不要出现中文。

------------------------------

# 任务
作为一个资深的go语言程序员，你需要编写一个函数func PlatPerformanceReqData(data *wsm.PlatPerformanceReqInfo) error，使用platperfanceReqCols := models.PlatPerformanceReqInfoCols方法将data里面的数据存到结构体 models.PlatPerformanceReqInfoCols中，并且使用platPerformanceValue, err := cwsmutils.Struct2Map(platperfanceReqCols)将struct转化成map，最后通过if !models.InsertNewResource(constant.TABLE_NAME_EvaluationPlatPerformanceTable, platPerformanceValue) 插入数据库

type PlatPerformanceReqInfoCols struct {
	Id         string `json:"id"`
	TaskId     string `json:"taskId"`
	PlanId     string `json:"planId"`
	ModelName  string `json:"modelName"`
	TestType   string `json:"testType"`
	JobAbility string `json:"jobAbility"`
}
## 代码输出要求
- 生成的代码中不得出现中文，打印也不要出现中文。

------------------------------

# 任务
作为一个资深的go语言程序员，你需要编写一个函数func StringToMapSlice(str string) []map[string]interface{}，用于将string类型转换成[]map[string]interface{}，函数不需要返回error，json.Unmarshal需要err := json.Unmarshal([]byte(str), &m)调用方法
## 代码输出要求
- 生成的代码中不得出现中文，打印也不要出现中文。

------------------------------