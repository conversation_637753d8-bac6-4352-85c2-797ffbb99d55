作为一个资深的go语言专家，你需要将sortingEquals函数分解成5个函数，要求每个函数的行数不能超过20行，函数圈复杂度不超过6
func sortingEquals(x, y reflect.Value) bool {
	if !x.Is<PERSON>alid() || !y.<PERSON>() {
		return false
	}
	switch x.Type().Kind() {
	case reflect.Bool, reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr,
		reflect.Float32, reflect.Float64, reflect.Complex64, reflect.Complex128, reflect.String:
		if !equals(x, y) {
			return false
		}
	case reflect.Array, reflect.Slice:
		if x.Len() != y.Len() {
			return false
		}
		sort.RecursiveSort(x)
		sort.RecursiveSort(y)
		if !equals(x, y) {
			return false
		}
	case reflect.Map:
		if x.Len() != y.Len() {
			return false
		}
		for _, key := range x.MapKeys() {
			if !sortingEquals(x.MapIndex(key), y.MapIndex(key)) {
				return false
			}
		}
	case reflect.Struct:
		for i := 0; i < x.NumField(); i++ {
			if !sortingEquals(x.Field(i), y.Field(i)) {
				return false
			}
		}
	case reflect.Ptr:
		if !x.IsNil() && !y.IsNil() {
			if !sortingEquals(x.Elem(), y.Elem()) {
				return false
			}
		} else if (x.IsNil() && !y.IsNil()) || (!x.IsNil() && y.IsNil()) {
			return false
		}
	default:
		sx, sy := fmt.Sprintf("%+v", x.Interface()), fmt.Sprintf("%+v", y.Interface())
		if sx != sy {
			return false
		}
	}
	return true
}

------------------------------

作为一个资深的go语言专家，你需要将RecordOperLog函数分解成3个函数，要求每个函数的行数不能超过20行，函数圈复杂度不超过6，最后在使用RecordOperLog名称的函数将分解的函数汇总
func RecordOperLog(request *http.Request, optObj *OptObjContent, oper, decription, sourceType, logSource, operErr string, rank int, operTime time.Time) error {
	//必填
	if request.Header.Get(globalcv.HeaderAccessToken) == "" &&
		request.Header.Get(globalcv.ProviderHeaderAccessToken) == "" {
		logger.Infof("no need to RecordOperLog, token is null")
		return nil
	}
	userName := request.Header.Get(globalcv.HeaderOperateuser)
	if userName == "" {
		userName = "unknown"
	}
	//客户端IP
	var clientIp string
	xForwardedFor := request.Header.Get(globalcv.HeaderXForwardedFor)
	xForwards := strings.Split(xForwardedFor, ",")
	if len(xForwards) > 0 {
		tempIP := xForwards[0]
		if strings.HasPrefix(tempIP, "::ffff:") {
			tempIP = strings.TrimLeft(tempIP, "::ffff:")
		}
		clientIp = tempIP
	} else {
		clientIp = request.RemoteAddr
	}
	if clientIp == "" {
		clientIp = "127.0.0.1"
	}

	recordTime := time.Now()
	operJson := GetOperJson(oper, sourceType) //按照规范要求自行构建操作信息
	if operJson == "" {
		operJson = "unknown"
	}
	decriptionInfoJson := GetOperDescriptionInfo(decription) //按照规范要求自行构建descriptionInfo信息
	if decriptionInfoJson == "" {
		decriptionInfoJson = "unknown"
	}
	detailJson := GetOperDetail(optObj) //按照规范要求自行构建detail信息
	if detailJson == "" {
		detailJson = "unknown"
	}
	operRes := fmt.Sprintf("%s(%s)", optObj.ObjName, optObj.ObjID)
	optObjType := GetOptObjType(sourceType)
	logMsg, ok := logagent.NewOperLogMessage(userName, operJson, decriptionInfoJson, clientIp, detailJson, rank, operTime, recordTime) //logagent提供的构建操作日志的函数, NewSecLogMessage安全日志,NewSysLogMessageϵ系统日志
	if !ok {
		return fmt.Errorf("Fail to create NewOperLogMessage, id: %d", logMsg.Id)
	}

	if logSource == "OpenPalette" {
		logMsg.SetAppModule("{\"en_US\":\"OpenPalette\", \"zh_CN\": \"容器云平台\"}")
	} else {
		logMsg.SetAppModule("{\"en_US\":\"Resource management\", \"zh_CN\": \"资源管理\"}")
	}
	logMsg.SetConnectMode("WEB")
	logMsg.SetOperateType(oper)
	logMsg.SetOperateResult(logagent.OPERLOG_SUSSESS)
	logMsg.SetOperateResource([]string{operRes})

	//logMsg.ExtFieldsV1 = GetExtensionMsg(sourceType, logSource, optObj)
	//logMsg.ExtFieldsV1 = make(map[string]interface{}, 0)
	logMsg.SetX_KI18N(OPTOBJTYPE, optObjType)     //扩展字段
	logMsg.SetX_K(PROJECTNAME, optObj.TenantName) //扩展字段
	logMsg.SetX_K(PROJECTID, optObj.TenantID)
	logMsg.SetX_K(ENVNAME, optObj.EnvName)
	logMsg.SetX_K(ENVID, optObj.EnvID)
	if optObj.EnvID != "" {
		if vim, err := vims_info_redis.GetByEnvId(optObj.EnvID); err == nil {
			logMsg.SetX_K(VIMID, vim.ID)
		}
	}
	if operErr != "" {
		logMsg.SetOperateResult(logagent.OPERLOG_ERROR)
		failReason := GetFailReason(operErr)
		logMsg.SetFailReason(failReason)
	}
	_ = logagent.RecordLog(logMsg) //logagent封装上报操作日志的接口
	return nil
}

------------------------------

作为一个资深的go语言专家，你需要将handleTaskStatus函数分解成几个函数，要求每个函数的行数不能超过30行，函数圈复杂度不超过6.
其中
taskSuccessInstance := wsm.TaskStatus{
				Id:         evaluationTask.Id,
				PlanId:     evaluationTask.PlanId,
				Name:       evaluationTask.Name,
				Status:     evaluationTask.Status,
				Err:        reqInfo.Err,
				PlanStatus: reqInfo.Status,
				Result:     evaluationTask.Result,
				EndTime:    evaluationTask.EndTime,
			}
			for _, gpu := range evaluationTask.Gpus {
				taskSuccessInstance.Gpus = gpu.Id + "," + gpu.Name + ";" + gpu.Type + ";"
				taskSuccessInstance.GpuUuid = gpu.Id
				errTaskGpus := updateTaskGpus(task, &taskSuccessInstance)
				if errTaskGpus != nil {
					return errTaskGpus
				}
			}
			successInstance, err := cwsmutils.Struct2Map(taskSuccessInstance)
			if err != nil {
				return err
			}
			evaluateSuccessId, ok := successInstance["id"].(string)
			if !ok {
				return errors.New("evaluate success id not found in evaluateTask")
			}
			taskStatusFromPg, err := getTaskStatusFromPg(evaluateSuccessId)
			if err != nil {
				logger.Errorf("get task status from Pg failed:%v", err)
				return err
			}
			if evaluationTask.Status == "Success" && taskStatusFromPg != "Success" {
				errplanStatus := updateStatusResultAndEndTimeFromWsm(evaluateId, successInstance, &taskSuccessInstance)
				if errplanStatus != nil {
					logger.Errorf("update status result and endtime from wsm failed:%v", errplanStatus)
					return err
				}
				errPlatPerformanceToWsm := PlatPerformanceToWsm(reqParam, successInstance)
				if errPlatPerformanceToWsm != nil {
					logger.Errorf("platperformance to wsm failed:%v", errPlatPerformanceToWsm)
					return err
				}
			}
代码需要单独提出，组成一个函数

func handleTaskStatus(evaluateId string, reqInfo EvaluationInfo, evaluationTask TestTask, tasks []map[string]interface{}, newInstance wsm.TaskStatus) error {
	for _, task := range tasks {
		taskPgStatus, ok := task["status"].(string)
		if !ok {
			return errors.New("task success id not found in evaluateTask")
		}
		if evaluationTask.Status == "Success" && taskPgStatus != "Success" {
			taskSuccessInstance := wsm.TaskStatus{
				Id:         evaluationTask.Id,
				PlanId:     evaluationTask.PlanId,
				Name:       evaluationTask.Name,
				Status:     evaluationTask.Status,
				Err:        reqInfo.Err,
				PlanStatus: reqInfo.Status,
				Result:     evaluationTask.Result,
				EndTime:    evaluationTask.EndTime,
			}
			for _, gpu := range evaluationTask.Gpus {
				taskSuccessInstance.Gpus = gpu.Id + "," + gpu.Name + ";" + gpu.Type + ";"
				taskSuccessInstance.GpuUuid = gpu.Id
				errTaskGpus := updateTaskGpus(task, &taskSuccessInstance)
				if errTaskGpus != nil {
					return errTaskGpus
				}
			}
			successInstance, err := cwsmutils.Struct2Map(taskSuccessInstance)
			if err != nil {
				return err
			}
			evaluateSuccessId, ok := successInstance["id"].(string)
			if !ok {
				return errors.New("evaluate success id not found in evaluateTask")
			}
			taskStatusFromPg, err := getTaskStatusFromPg(evaluateSuccessId)
			if err != nil {
				logger.Errorf("get task status from Pg failed:%v", err)
				return err
			}
			if evaluationTask.Status == "Success" && taskStatusFromPg != "Success" {
				errplanStatus := updateStatusResultAndEndTimeFromWsm(evaluateId, successInstance, &taskSuccessInstance)
				if errplanStatus != nil {
					logger.Errorf("update status result and endtime from wsm failed:%v", errplanStatus)
					return err
				}
				errPlatPerformanceToWsm := PlatPerformanceToWsm(reqParam, successInstance)
				if errPlatPerformanceToWsm != nil {
					logger.Errorf("platperformance to wsm failed:%v", errPlatPerformanceToWsm)
					return err
				}
			}
		} else {
			errplanStatus := updateStatusResultAndEndTimeFromWsm(evaluateId, task, &newInstance)
			if errplanStatus != nil {
				logger.Errorf("update status result and endtime from wsm failed:%v", errplanStatus)
				return err
			}
		}
	}
	return nil
}

------------------------------

作为一个资深的go语言专家，你需要将IsGetEvaluationFromWsm函数分解成几个函数，要求每个函数的行数不能超过30行，函数圈复杂度不超过6.
其中
 for _, evaluationTask := range reqInfo.TestTasks {
            newInstance := wsm.TaskStatus{
                Id:         evaluationTask.Id,
                PlanId:     evaluationTask.PlanId,
                Name:       evaluationTask.Name,
                Status:     evaluationTask.Status,
                Err:        reqInfo.Err,
                PlanStatus: reqInfo.Status,
                Result:     evaluationTask.Result,
                EndTime:    evaluationTask.EndTime,
            }
            for _, gpu := range evaluationTask.Gpus {
                newInstance.Gpus = gpu.Id + "," + gpu.Name + ";" + gpu.Type + ";"
                newInstance.GpuUuid = gpu.Id
            }
            if infoFromErr != "" {
                task, err := cwsmutils.MapSlicetoMap(tasks)
                if err != nil {
                    return false, err
                }
                errInfo := updateTaskErr(task, &newInstance)
                if errInfo != nil {
                    return false, errInfo
                }
                logger.Errorf("get evaluation value from wsm failed:%s", infoFromErr)
                return true, nil
            }
            err := handleTaskStatus(evaluateId, reqInfo, evaluationTask, tasks, newInstance)
            if err != nil {
                return false, err
            }
        }
代码需要单独提出，组成一个函数

func IsGetEvaluationFromWsm(evaluateId string, reqParam EvaluationRequestParam, tasks []map[string]interface{}) (bool, error) {
    evaluationValue, infoFromErr, err := getEvaluationValueFromWsm(evaluateId, reqParam, tasks)
    if err != nil {
        logger.Errorf("get evaluation value from wsm failed:%v", err)
        return false, err
    }
    if evaluationValue == nil {
        return true, nil
    }
    for _, reqInfo := range evaluationValue.EvaluationInfo {
        for _, evaluationTask := range reqInfo.TestTasks {
            newInstance := wsm.TaskStatus{
                Id:         evaluationTask.Id,
                PlanId:     evaluationTask.PlanId,
                Name:       evaluationTask.Name,
                Status:     evaluationTask.Status,
                Err:        reqInfo.Err,
                PlanStatus: reqInfo.Status,
                Result:     evaluationTask.Result,
                EndTime:    evaluationTask.EndTime,
            }
            for _, gpu := range evaluationTask.Gpus {
                newInstance.Gpus = gpu.Id + "," + gpu.Name + ";" + gpu.Type + ";"
                newInstance.GpuUuid = gpu.Id
            }
            if infoFromErr != "" {
                task, err := cwsmutils.MapSlicetoMap(tasks)
                if err != nil {
                    return false, err
                }
                errInfo := updateTaskErr(task, &newInstance)
                if errInfo != nil {
                    return false, errInfo
                }
                logger.Errorf("get evaluation value from wsm failed:%s", infoFromErr)
                return true, nil
            }
            err := handleTaskStatus(evaluateId, reqInfo, evaluationTask, tasks, newInstance)
            if err != nil {
                return false, err
            }
        }
    }
    return false, nil
}

------------------------------

作为一个资深的go语言专家，你需要将handleStruct函数分解成4个函数，要求每个函数的行数不能超过20行，函数圈复杂度不超过6
func handleStruct(x, y reflect.Value, cmpDepth int, currBranchFlagEqual, globalFlagEqual bool, disparities []string, fieldName string, stackX, stackY *my_stack.MyStack, ignoredMap map[string]string, cellX, cellY CompCell, needDetails bool) (bool, bool, []string) {
	if cellX.Value.NumField() != cellY.Value.NumField() {
		currBranchFlagEqual = false
		globalFlagEqual = false
		stackX.Pop()
		stackY.Pop()
		if cmpDepth <= 1 {
			disparities = append(disparities, cellX.FieldName)
			currBranchFlagEqual = true
		}
		return globalFlagEqual, currBranchFlagEqual, disparities
	}
	if cellX.Visited {
		stackX.Pop()
		stackY.Pop()
		if (cmpDepth == 0 && len(disparities) > 0) || (!currBranchFlagEqual && cmpDepth <= 1) {
			disparities = append(disparities, cellX.FieldName)
			currBranchFlagEqual = true
		}
		return globalFlagEqual, currBranchFlagEqual, disparities
	}
	cellX.Visited = true
	stackX.Pop()
	stackX.Push(cellX)
	if cmpDepth <= 1 {
		currBranchFlagEqual = true
	}
	xLen, yLen := x.NumField(), y.NumField()
	index := 0
	for index < xLen && index < yLen {
		cmpTags := strings.Split(x.Type().Field(index).Tag.Get("cmp"), ",")
		if ignoredField, ok := ignoredMap[reflect.TypeOf(x.Interface()).Field(index).Name]; ok && ignoredField == cellX.FieldName {
			index++
			continue
		}
		for _, tag := range cmpTags {
			if tag != "ignore" {
				stackX.Push(CompCell{Value: x.Field(index), FieldName: reflect.TypeOf(x.Interface()).Field(index).Name, CompLevel: cmpDepth + 1, Visited: false})
				stackY.Push(CompCell{Value: y.Field(index), FieldName: reflect.TypeOf(y.Interface()).Field(index).Name, CompLevel: cmpDepth + 1, Visited: false})
			}
		}
		index++
	}

	if index < xLen || index < yLen {
		globalFlagEqual = false
		if needDetails && cmpDepth == 0 {
			for index < xLen {
				disparities = append(disparities, reflect.TypeOf(x.Interface()).Field(index).Name)
				index++
			}
			for index < yLen {
				disparities = append(disparities, reflect.TypeOf(y.Interface()).Field(index).Name)
				index++
			}
		}
	}
	return globalFlagEqual, currBranchFlagEqual, disparities
}

------------------------------

作为一个资深的go语言专家，你需要将setReflectValue函数分解成多个函数，要求每个函数的行数不能超过20行，函数圈复杂度不超过6
func setReflectValue(insFieldVal reflect.Value, data interface{}) error {
	if !insFieldVal.CanSet() {
		logger.Errorf("setReflectValue error: field value %s is not settable", insFieldVal.String())
		return errors.New("non settable value type")
	}

	dataTyp, dataVal := reflect.TypeOf(data), reflect.ValueOf(data)
	for dataVal.Kind() == reflect.Ptr {
		dataTyp, dataVal = dataTyp.Elem(), dataVal.Elem()
	}
	filedTyp := insFieldVal.Type()
	numPtr := 0
	for filedTyp.Kind() == reflect.Ptr {
		numPtr++
		filedTyp = filedTyp.Elem()
	}
	fieldVal := reflect.New(filedTyp)
	switch dataTyp.String() {
	case "[]uint8":
		vi := fieldVal.Interface()
		bs, _ := dataVal.Interface().([]byte)
		if err := json.Unmarshal(bs, vi); err != nil {
			logger.Errorf("fillInstanceFields:setReflectValue json.Unmarshal failed:", err.Error())
			logger.Warnf("error bytes:", bs)
			return err
		}
	case "time.Time":
		t, _ := dataVal.Interface().(time.Time)
		ts := t.Format(time.RFC3339)
		fieldVal.Elem().Set(reflect.ValueOf(ts))
	default:
		if dataTyp != filedTyp {
			if dataTyp.ConvertibleTo(filedTyp) {
				fieldVal.Elem().Set(dataVal.Convert(filedTyp))
			} else {
				logger.Errorf("Cannot convert type", dataTyp, "to type", filedTyp)
				return errors.New("setReflectValue: Type conversion error")
			}
		} else {
			fieldVal.Elem().Set(dataVal)
		}
	}

	valToBeSet := fieldVal.Elem()
	for i := 0; i < numPtr; i++ {
		tmpVal := reflect.New(valToBeSet.Type())
		tmpVal.Elem().Set(valToBeSet)
		valToBeSet = tmpVal
	}

	insFieldVal.Set(valToBeSet)
	return nil
}

------------------------------

作为一个资深的go语言专家，你需要将getUniversalRSPFromDaoWithFilter函数的圈复杂度降到不超过6
func getUniversalRSPFromDaoWithFilter(dao *Dao, key string, catalogs, interfaces []string) (*UniversalRSP, error) {
	rsp := &UniversalRSP{Key: key, Token: dao.Token, Catalog: []*Catalog{}, IssuedAt: dao.IssuedAt,
		ExpiresAt: dao.ExpiresAt, SSLAuth: dao.Request.SSLAuth, Request: dao.Request, TenantId: dao.TenantId,
		UserId: dao.UserId}
	intfs := map[string]struct{}{}
	for _, f := range interfaces {
		intfs[strings.ToLower(f)] = struct{}{}
	}
	switch len(catalogs) {
	case 0:
	case 1:
		catName := catalogs[0]
		if strings.EqualFold(catName, "all") {
			for _, cat := range dao.Catalog {
				rsp.Catalog = append(rsp.Catalog, filterEndpoints(cat, intfs))
			}
		} else if strings.EqualFold(catName, common_cloud_env.CATALOG_OPENPALETTE) {
			if cat, ok := dao.Catalog[common_cloud_env.NO_AUTH_CATALOG]; ok {
				rsp.Catalog = append(rsp.Catalog, filterEndpoints(cat, intfs))
			}
			if cat, ok := dao.Catalog[common_cloud_env.NEED_AUTH_CATALOG]; ok {
				rsp.Catalog = append(rsp.Catalog, filterEndpoints(cat, intfs))
			}
		} else if cat, ok := extractCatalog(dao.Catalog, catName); ok {
			rsp.Catalog = []*Catalog{filterEndpoints(cat, intfs)}
		}
	default:
		for _, c := range catalogs {
			if strings.EqualFold(c, common_cloud_env.CATALOG_OPENPALETTE) {
				if cat, ok := dao.Catalog[common_cloud_env.NO_AUTH_CATALOG]; ok {
					rsp.Catalog = append(rsp.Catalog, filterEndpoints(cat, intfs))
				}
				if cat, ok := dao.Catalog[common_cloud_env.NEED_AUTH_CATALOG]; ok {
					rsp.Catalog = append(rsp.Catalog, filterEndpoints(cat, intfs))
				}
			} else if cat, ok := extractCatalog(dao.Catalog, c); ok {
				rsp.Catalog = append(rsp.Catalog, filterEndpoints(cat, intfs))
			}
		}
	}
	return rsp, nil
}