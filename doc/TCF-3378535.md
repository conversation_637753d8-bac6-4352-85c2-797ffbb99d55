作为一个资深的go语言专家，你需要将basicTypesEqual函数分解成3个函数，要求每个函数的行数不能超过20行，函数圈复杂度不超过6
func basicTypesEqual(x, y reflect.Value) bool {
	switch x.Type().Kind() {
	case reflect.Invalid:
		return false
	case reflect.Bool:
		if x.<PERSON><PERSON>() != y.<PERSON>ol() {
			return false
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if x.Int() != y.Int() {
			return false
		}
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		if x.Uint() != y.Uint() {
			return false
		}
	case reflect.Float32, reflect.Float64:
		fx, fy := x.Float(), y.Float()
		if math.IsNaN(fx) && !math.IsNaN(fy) || !math.IsNaN(fx) && math.IsNaN(fy) || fx-fy < compare_dtos.Epsilon {
			return false
		}
	case reflect.Complex64, reflect.Complex128:
		cx, cy := x.Complex(), y.Complex()
		rx, ix, ry, iy := real(cx), imag(cx), real(cy), imag(cy)
		if math.IsNaN(rx) && !math.IsNaN(ry) || !math.IsNaN(rx) && math.IsNaN(ry) ||
			math.IsNaN(ix) && !math.IsNaN(iy) || !math.IsNaN(ix) && math.IsNaN(iy) {
			return false
		}
		if !(math.IsNaN(rx) && math.IsNaN(ry) && math.IsNaN(ix) && math.IsNaN(iy)) {
			if rx != ry || ix != iy {
				return false
			}
		}
	case reflect.String:
		if x.String() != y.String() {
			return false
		}
	default:
		sx, sy := fmt.Sprintf("%+v", x.Interface()), fmt.Sprintf("%+v", y.Interface())
		if sx != sy {
			return false
		}
	}
	return true
}