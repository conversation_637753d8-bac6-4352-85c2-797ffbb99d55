## 已有代码
func queryOneTypeEvaluationCaseFromDb(reqParam EvaluationCaseRequestParam) ([]map[string]interface{}, error) {
	queryCondition := map[string]interface{}{"projectId": reqParam.ProjectId, "testType": reqParam.TestType}
	oneTypeEvaluationCase, err := models.QueryResourceByCondition(constant.TABLE_NAME_EvaluationCase, queryCondition)
	if err != nil {
		logger.Error("get one type evalutation case from db failed : %s", err.Error())
		return nil, err
	}
	return oneTypeEvaluationCase, nil
}

# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明

------------------------------

## 已有代码
func getOneTypeEvaluationCase(reqParam EvaluationCaseRequestParam) ([]map[string]interface{}, error) {
	oneTypeEvaluationCase, err := queryOneTypeEvaluationCaseFromDb(reqParam)
	if err != nil {
		return nil, err
	}
	return oneTypeEvaluationCase, nil
}

# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明

------------------------------

## 已有代码
func GetEvaluationCase(reqParam EvaluationCaseRequestParam) (interface{}, error) {
	if reqParam.TestType != "" {
		return getOneTypeEvaluationCase(reqParam)
	} else if reqParam.EvaluationCaseId == "" {
		return getAllEvaluationCase(reqParam)
	} else {
		return getOneEvaluationCase(reqParam)
	}
}

# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明