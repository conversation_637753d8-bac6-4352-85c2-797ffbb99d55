# 任务
你作为一名资深的go语言编程师，请编写一个函数func extractAcceleratorInfo(data *wsm.AcceleratorRes) AcceleratorInfo 从data中提取所需的数据，组成新的数据，返回

wsm.AcceleratorRes格式为
type AcceleratorRes struct {
	Items []GpuRes `json:"items"`
}

type GpuRes struct {
	MetaData MetaData `json:"metadata"`
	Spec     Spec     `json:"spec"`
	Status   Status   `json:"status"`
}

type MetaData struct {
	Name string `json:"name"`
}

type Spec struct {
	NodeName     string       `json:"nodename"`
	Descriptions Descriptions `json:"descriptions"`
}

type Descriptions struct {
	Vendor   string `json:"vendor"`
	VendorId string `json:"vendorid"`
	Index    int    `json:"index"`
	Uuid     string `json:"uuid"`
}

type Status struct {
	Status string `json:"status"`
}


AcceleratorInfo格式为
type AcceleratorInfo struct {
	Items []Item `json:"items"`
}

type Item struct {
	NodeName string    `json:"nodeName"`
	GPUs     []GpuInfo `json:"gpus"`
}

type GpuInfo struct {
	Name     string `json:"name"`
	Status   string `json:"status"`
	Vendor   string `json:"vendor"`
	VendorId string `json:"vendorId"`
	Index    string `json:"index"`
	Uuid     string `json:"uuid"`
}

## 代码输出要求
 - 生成代码，以/****** ===Started by AICoder>>> ******/ 开始，以/****** <<<Ended by AICoder=== ******/结束
 - 代码中不要增加其他的注释
 - 一个Item中的NodeName为item[“spec”]中的”nodename”，GPUs为属于该NodeName的所有gpu的信息