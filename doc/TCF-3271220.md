# 任务描述
作为资深的go语言专家，你需要写一个函数func GetScclinspection(reqParam ScclinspectionRequestParam) (interface{}, error)
# 功能描述
首先你需要定义个变量调用 wsm.ScclinspectionWsmKeywords结构体，并给ScclinspectionWsmKeywords结构体中的ClusterId从reqParam赋值，然后再调用ScclinspectionHandler函数，将输出返回，注：需要考虑调用ScclinspectionHandler函数!suc的情况
# 输出要求
return的结果是调用ScclinspectionHandler函数的内容
# 接口举例
type ScclinspectionRequestParam struct {
	ClusterId string
}

func ScclinspectionHandler(keyswords ScclinspectionWsmKeywords, reqBody interface{}, method string) (map[string]interface{}, bool) {
	restfulParams, suc := buildScclinspectionRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s scclinspection build restful req failed", method)
		return nil, false
	}

	switch method {

	case "get":
		logger.Info("get from apts")
		return GetScclinspection(restfulParams)

	case "post":
		logger.Info("post from apts")
		return PostScclinspection(restfulParams, reqBody.(interface{}))

	case "stop":
		logger.Info("stop from apts")
		return nil, StopScclinspection(restfulParams)

	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, false
	}
}

------------------------------

# 任务描述
作为资深的go语言专家，你需要写一个函数func func ActivateScclinspection(reqParam ScclinspectionRequestParam, reqBody interface{}) (interface{}, error)
# 功能描述
首先你需要定义个变量调用 wsm.ScclinspectionWsmKeywords结构体，并给ScclinspectionWsmKeywords结构体中的ClusterId从reqParam赋值，然后再调用ScclinspectionHandler函数，将输出返回，注：需要考虑调用ScclinspectionHandler函数!suc的情况
# 输出要求
return的结果是调用ScclinspectionHandler函数的内容
# 接口举例
type ScclinspectionRequestParam struct {
	ClusterId string
}

func ScclinspectionHandler(keyswords ScclinspectionWsmKeywords, reqBody interface{}, method string) (map[string]interface{}, bool) {
	restfulParams, suc := buildScclinspectionRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s scclinspection build restful req failed", method)
		return nil, false
	}

	switch method {

	case "get":
		logger.Info("get from apts")
		return GetScclinspection(restfulParams)

	case "post":
		logger.Info("post from apts")
		return PostScclinspection(restfulParams, reqBody.(interface{}))

	case "stop":
		logger.Info("stop from apts")
		return nil, StopScclinspection(restfulParams)

	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, false
	}
}

------------------------------

# 任务描述
作为资深的go语言专家，你需要写一个函数func func func StopScclinspection(reqParam ScclinspectionRequestParam) error
# 功能描述
首先你需要定义个变量调用 wsm.ScclinspectionWsmKeywords结构体，并给ScclinspectionWsmKeywords结构体中的ClusterId从reqParam赋值，然后再调用ScclinspectionHandler函数，将输出返回，注：需要考虑调用ScclinspectionHandler函数!suc的情况
# 输出要求
return的结果为nil
# 接口举例
type ScclinspectionRequestParam struct {
	ClusterId string
}

func ScclinspectionHandler(keyswords ScclinspectionWsmKeywords, reqBody interface{}, method string) (map[string]interface{}, bool) {
	restfulParams, suc := buildScclinspectionRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s scclinspection build restful req failed", method)
		return nil, false
	}

	switch method {

	case "get":
		logger.Info("get from apts")
		return GetScclinspection(restfulParams)

	case "post":
		logger.Info("post from apts")
		return PostScclinspection(restfulParams, reqBody.(interface{}))

	case "stop":
		logger.Info("stop from apts")
		return nil, StopScclinspection(restfulParams)

	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, false
	}
}