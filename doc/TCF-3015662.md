# 任务
作为一名资深的go语言编程师，请实现一个函数func StringToSlice(s string) ([]string, error)
功能为使string类型的值转换成[]string类型的值。其中s为入参。string到[]string的转换，需要使用json.Unmarshal。
## 代码输出要求
- 生成的代码中不得出现中文，打印也不要出现中文。

------------------------------

# 任务
作为一个资深的go语言工程师，已知你可以通过tasks, _ := models.QueryResourceByCondition(constant.TABLE_NAME_EvaluationTask, condtion)代码获得[]map[string]interface{}类型的tasks。然后从tasks中找出最大的number为maxNumber，其中maxNumber和number类型均为string
## 代码输出要求
- 生成的代码中不得出现中文，打印也不要出现中文。