# TDD实践
# 任务描述 
作为一个高级Go程序员，需要完成下列功能的测试用例。
使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
# 功能描述
给定一个map[string]interface{}，转换成map[string]string。
# 接口举例
调用ConvertToStringMap(src map[string]interface{})map[string]string

# 输出要求
- 仅生成测试用例
- interface{}的类型考虑整型， 浮点数，字符串，布尔型转成相应的字符串，如果是切片等，转成“--”
- 测试用例中增加浮点型的用例
# 输出示例
```
func TestConvertToStringMap(t *testing.T) {
    var _=Describe("ConvertToStringMap",func(){
        ...
    } )
    RegisterFailHandler(Fail)
    RunSpecs(t, "ConvertToStringMap Suite")
}
```

------------------------------

# 任务说明，你是一名资深的Go程序员，请根据下面的测试用例生成功能代码

# 测试用例
```go
func TestConvertToStringMap(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "ConvertToStringMap Suite")
}

var _ = Describe("ConvertToStringMap", func() {
	var (
		src map[string]interface{}
	)

	BeforeEach(func() {
		src = make(map[string]interface{})
	})

	AfterEach(func() {
		src = nil
	})

	Context("When the source map is empty", func() {
		It("should return an empty map", func() {
			result := ConvertToStringMap(src)
			Expect(result).To(BeEmpty())
		})
	})

	Context("When the source map has string values", func() {
		BeforeEach(func() {
			src["key1"] = "value1"
			src["key2"] = "value2"
		})

		It("should return a map with the same keys and values", func() {
			result := ConvertToStringMap(src)
			Expect(result).To(HaveLen(2))
			Expect(result["key1"]).To(Equal("value1"))
			Expect(result["key2"]).To(Equal("value2"))
		})
	})

	Context("When the source map has non-string values", func() {
		BeforeEach(func() {
			src["key1"] = 123
			src["key2"] = true
			src["key3"] = 1.23
		})

		It("should return a map with string values", func() {
			result := ConvertToStringMap(src)
			Expect(result).To(HaveLen(3))
			Expect(result["key1"]).To(Equal("123"))
			Expect(result["key2"]).To(Equal("true"))
			Expect(result["key3"]).To(Equal("1.23"))
		})
	})

	Context("When the source map has unsupported types", func() {
		BeforeEach(func() {
			src["key1"] = []int{1, 2, 3}
			src["key2"] = make(chan int)
		})

		It("should return a map with '--' for unsupported types", func() {
			result := ConvertToStringMap(src)
			Expect(result).To(HaveLen(2))
			Expect(result["key1"]).To(Equal("--"))
			Expect(result["key2"]).To(Equal("--"))
		})
	})
})

```

# 输出要求
    - 使用strconv来转换，如果是不支持的类型需要打印出相应的类型


备记：不加输出要求时，大模型会针对浮点通过fmt.Sprintf("%f", v.(float64))转换，导致1.23转化成1.230000

------------------------------
# 已有代码
const (
	JobAbility      = "jobAbility"
	ComputeAbility  = "computeAbility"
	MemAbility      = "memAbility"
	TranAbility     = "tranAbility"
	NetAbility      = "netAbility"
	HealthyAbility  = "healthyAbility"
	EnergyAbility   = "energyAbility"
	ResourceAbility = "resourceAbility"
)

var JobAbilityCol = []string{ComputeAbility, MemAbility, NetAbility, HealthyAbility, EnergyAbility, ResourceAbility, JobAbility}

var ComputeAbilityCol = []string{"Score", "aveGpuComputeAbility", "sqrtGpuComputeAbility", "modeGpuComputeAbility", "minGpuComputeAbility",
	"maxGpuComputeAbility", "onequarterGpuComputeAbility", "middleGpuComputeAbility", "threequarterGpuComputeAbility", "rateLowGpuComputeAbility",
	"rateOnequarterGpuComputeAbility", "rateTwoquarterGpuComputeAbility", "rateThreequarterGpuComputeAbility", "rateFourquarterGpuComputeAbility", "rateDgcmSmActiveLowHalf"}

var MemAbilityCol = []string{"Score", "aveGpuFiDevMemCopyUtil", "rateGpuFiDevMemCopyUtilMore80", "aveGpuFiProfDramActive", "rateGpuFiProfDramActiveMore80",
	"gpuFiDevFbUsedRate", "rateGpuFbUsedRateMore80"}

var TranAbilityCol = []string{"Score"}

var NetAbilityCol = []string{"Score", "gpuReceFromPCIeRate", "gpuSendToPCIeRate", "gpuReceFromNvlinkRate", "gpuSendToNvlinkRate",
	"gpuReceFromNvSwitchRate", "gpuSendToNvSwitchRate", "gpuReceFromPCIeRatio", "gpuSendToPCIeRatio", "gpuReceFromNvlinkRatio",
	"gpuSendToNvlinkRatio", "gpuReceFromNvSwitchRatio", "gpuSendToNvSwitchRatio", "byterateInNetwork", "byterateOutNetwork",
	"errorrateInNetwork", "lostrateInNetwork"}

var HealthyAbilityCol = []string{"Score", "gpuErrorEvent", "gpuAllViolationTime", "gpuRatioAllViolation", "gpuRatioErrorNvlink",
	"gpuRatioErrorEvent"}

var EnergyAbilityCol = []string{"Score", "gpuTotalConsumeEnergy", "gpuRatioOfLimitPower"}

var ResourceAbilityCol = []string{"Score", "gpuCanUseRatio", "gpuUsedRatio"}

var JobAbilityInnerCol = []string{"Score", "gpuRatioRunSuccesse", "jobRunTime"}


func JobAbilityToSlice(data map[string]interface{}) []string {
	
	row := []string{}
	for _, value := range JobAbilityCol {
		column := getMetricCol(value)

		rowValue := make([]string, len(column))
		rowData, ok := data[value].(map[string]interface{})

		if ok {
			rowValue = AssembleRow(rowData, column)
		} else {
			logger.Errorf("cann't find %s in jobability", value)
			rowData = make(map[string]interface{})
			rowValue = AssembleRow(rowData, column)
		}

		row = append(row, rowValue...)
	}
	return row
}

func AssembleRow(data map[string]interface{}, column []string) []string {
	metrics := ConvertToStringMap(data)
	row := make([]string, 0)
	for _, value := range column {
		row = append(row, metrics[value])
	}
	return row
}

func getMetricCol(metricType string) []string {
	switch metricType {
	case ComputeAbility:
		return ComputeAbilityCol
	case MemAbility:
		return MemAbilityCol
	case TranAbility:
		return TranAbilityCol
	case NetAbility:
		return NetAbilityCol
	case HealthyAbility:
		return HealthyAbilityCol
	case EnergyAbility:
		return EnergyAbilityCol
	case ResourceAbility:
		return ResourceAbilityCol
	case JobAbility:
		return JobAbilityInnerCol
	case EvaluationTaskInfo:
		return EvaluationTaskInfoCol
	default:
		return []string{}
	}
}

# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成JobAbilityToSlice的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、请生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明
5、使用fmt.Errorf代替Errors.New
6、ut需要包含每一个分支和每一种错误


------------------------------
# 已有代码
func MergeInfoAndMatrix(info [][]string, metrics [][]string) [][]string {
	matrix := [][]string{}
	if len(info) < 2 || len(metrics) < 2 {
		logger.Errorf("info len or metrics len is invalid")
		return matrix
	}
	infoCol := info[0]
	infoValue := info[1]

	infoCol = append(infoCol, metrics[0]...)
	matrix = append(matrix, infoCol)
	for index := 1; index < len(metrics); index++ {
		tmp := []string{}
		tmp = append(tmp, infoValue...)
		tmp = append(tmp, metrics[index]...)
		matrix = append(matrix, tmp)
	}
	return matrix
}
# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、请生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明
5、使用fmt.Errorf代替Errors.New
6、ut需要包含每一个分支和每一种错误