# 任务描述
作为资深的go语言专家，你需要写一个函数func GetInspectiontask(reqParam InspectiontaskRequestParam) (interface{}, error)
# 功能描述
首先你需要定义个变量调用 wsm.InspectiontaskWsmKeywords结构体，并给InspectiontaskWsmKeywords结构体中的ClusterId从reqParam赋值，然后再调用InspectiontaskHandler函数，将输出返回，注：需要考虑调用InspectiontaskHandler函数!suc的情况
# 输出要求
return的结果是调用InspectiontaskHandler函数的内容
# 接口举例
type InspectiontaskWsmKeywords struct {
	ClusterId string `json:"clusterId"`
}

func InspectiontaskHandler(keyswords InspectiontaskWsmKeywords, reqBody interface{}, method string) (map[string]interface{}, bool) {
	restfulParams, suc := buildInspectiontaskRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s Inspectiontask build restful req failed", method)
		return nil, false
	}

	switch method {

	case "get":
		return GetInspectiontask(restfulParams)

	case "post":
		return PostInspectiontask(restfulParams, reqBody.(interface{}))

	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, false
	}
}

------------------------------
# 任务描述
作为资深的go语言专家，你需要写一个函数func ActivateInspectiontask(reqParam InspectiontaskRequestParam, reqBody interface{}) (interface{}, error)
# 功能描述
首先你需要定义个变量调用 wsm.InspectiontaskWsmKeywords结构体，并给InspectiontaskWsmKeywords结构体中的ClusterId从reqParam赋值，然后再调用InspectiontaskHandler函数，将输出返回，注：需要考虑调用InspectiontaskHandler函数!suc的情况
# 输出要求
return的结果是调用InspectiontaskHandler函数的内容
# 接口举例
type InspectiontaskWsmKeywords struct {
	ClusterId string `json:"clusterId"`
}

func InspectiontaskHandler(keyswords InspectiontaskWsmKeywords, reqBody interface{}, method string) (map[string]interface{}, bool) {
	restfulParams, suc := buildInspectiontaskRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s Inspectiontask build restful req failed", method)
		return nil, false
	}

	switch method {

	case "get":
		return GetInspectiontask(restfulParams)

	case "post":
		return PostInspectiontask(restfulParams, reqBody.(interface{}))

	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, false
	}
}

------------------------------

# 任务描述
作为资深的go语言专家，你需要写一个函数func DeleteInspectiontask(reqParam InspectiontaskRequestParam) error
# 功能描述
首先你需要定义个变量调用 wsm.InspectiontaskWsmKeywords结构体，并给InspectiontaskWsmKeywords结构体中的ClusterId从reqParam赋值，然后再调用InspectiontaskHandler函数，将输出返回，注：需要考虑调用InspectiontaskHandler函数!suc的情况
# 输出要求
结果无需return出调用InspectiontaskHandler函数的内容
# 接口举例
type InspectiontaskWsmKeywords struct {
	ClusterId string `json:"clusterId"`
}

func InspectiontaskHandler(keyswords InspectiontaskWsmKeywords, reqBody interface{}, method string) (map[string]interface{}, bool) {
	restfulParams, suc := buildInspectiontaskRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s Inspectiontask build restful req failed", method)
		return nil, false
	}

	switch method {

	case "get":
		return GetInspectiontask(restfulParams)

	case "post":
		return PostInspectiontask(restfulParams, reqBody.(interface{}))

    case "delete":
		return nil, DeleteInspectiontask(restfulParams)

	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, false
	}
}
