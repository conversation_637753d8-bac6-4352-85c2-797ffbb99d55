作为资深的go语言专家，你需要对compareRecursively函数拆分为一些独立的小函数，要求重构后的函数圈复杂度不超过6，每个函数的代码行数不超过30。

func compareRecursively(x, y reflect.Value, needDetails bool, ignoredMap map[string]string, disparities *[]string, recursionDepth int, fieldName string) bool {
	if !x.Is<PERSON>alid() && !y.Is<PERSON>alid() {
		return true
	}
	if !x.IsValid() || !y.Is<PERSON>alid() {
		return false
	}

	switch x.Type().Kind() {
	case reflect.Invalid:
		logger.Infof("****** invalid field: %s", fieldName)
		return false
	case reflect.Bool, reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr,
		reflect.Float32, reflect.Float64, reflect.Complex64, reflect.Complex128, reflect.String:
		isEqual := basicTypesEqual(x, y)
		if !isEqual {
			if needDetails && recursionDepth <= 1 {
				*disparities = append(*disparities, fieldName)
			}
			return false
		}
	case reflect.Array, reflect.Slice:
		sort.RecursiveSort(x)
		sort.RecursiveSort(y)

		xLen, yLen := x.Len(), y.Len()
		res := true
		index := 0

		for index < xLen && index < yLen {
			isEqual := compareRecursively(x.Index(index), y.Index(index), needDetails, ignoredMap, disparities, recursionDepth+1, "")
			if !isEqual {
				if !needDetails {
					return false
				}
				res = false
			}
			index++
		}

		if index < xLen || index < yLen {
			res = false
		}

		if !res && recursionDepth <= 1 {
			*disparities = append(*disparities, fieldName)
		}

		return res
	case reflect.Struct:
		xLen, yLen := x.NumField(), y.NumField()
		index := 0
		res := true
		for index < xLen && index < yLen {
			if ignoredField, ok := ignoredMap[reflect.TypeOf(x.Interface()).Field(index).Name]; ok && ignoredField == fieldName {
				index++
				continue
			}
			cmpTags := strings.Split(x.Type().Field(index).Tag.Get("cmp"), ",")
			for _, tag := range cmpTags {
				if tag != "ignore" {
					isEquals := compareRecursively(x.Field(index), y.Field(index), needDetails, ignoredMap, disparities, recursionDepth+1, reflect.TypeOf(x.Interface()).Field(index).Name)
					if !isEquals {
						if !needDetails {
							return false
						}
						res = false
					}
				}
			}
			index++
		}

		if index < xLen || index < yLen {
			if needDetails && recursionDepth == 0 {
				for index < xLen {
					*disparities = append(*disparities, reflect.TypeOf(x.Interface()).Field(index).Name)
					index++
				}
				for index < yLen {
					*disparities = append(*disparities, reflect.TypeOf(y.Interface()).Field(index).Name)
					index++
				}
			}
			res = false
		}

		if !res && recursionDepth <= 1 {
			*disparities = append(*disparities, fieldName)
		}
		return res
	case reflect.Map:
		usedKeys := make(map[string]bool)
		res := true
		index := 0

		keysX := x.MapKeys()
		for _, key := range keysX {
			isEqual := compareRecursively(x.MapIndex(key), y.MapIndex(key), needDetails, ignoredMap, disparities, recursionDepth+1, key.String())
			if !isEqual {
				if !needDetails {
					return false
				}
				res = false
			}
			usedKeys[key.String()] = true
			index++
		}

		keysY := y.MapKeys()
		for _, key := range keysY {
			if used, ok := usedKeys[key.String()]; !ok && !used {
				res = false
				if needDetails && recursionDepth == 0 {
					*disparities = append(*disparities, key.String())
				}
			}
		}
		return res
	case reflect.Ptr:
		return compareRecursively(x.Elem(), y.Elem(), needDetails, ignoredMap, disparities, recursionDepth, fieldName)
	default:
		sx, sy := fmt.Sprintf("%+v", x.Interface()), fmt.Sprintf("%+v", y.Interface())
		if sx != sy {
			return false
		}
	}
	return true
}

------------------------------

作为资深的go语言专家，你需要对equals函数拆分为一些独立的小函数，要求重构后的函数圈复杂度不超过6，每个函数的代码行数不超过30。

func equals(x, y reflect.Value) bool {
	if !x.IsValid() || !y.IsValid() {
		logger.Warnf("x: %v is not equal to y: %v", x, y)
		return false
	}
	switch x.Type().Kind() {
	case reflect.Bool:
		if x.Bool() != y.Bool() {
			logger.Warnf("x: %v is not equal to y: %v", x, y)
			return false
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if x.Int() != y.Int() {
			logger.Warnf("x: %v is not equal to y: %v", x, y)
			return false
		}
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		if x.Uint() != y.Uint() {
			logger.Warnf("x: %v is not equal to y: %v", x, y)
			return false
		}
	case reflect.Float32, reflect.Float64:
		fx, fy := x.Float(), y.Float()
		if math.IsNaN(fx) && !math.IsNaN(fy) || !math.IsNaN(fx) && math.IsNaN(fy) || fx-fy < epsilon {
			logger.Warnf("x: %v is not equal to y: %v", x, y)
			return false
		}
	case reflect.Complex64, reflect.Complex128:
		cx, cy := x.Complex(), y.Complex()
		rx, ix, ry, iy := real(cx), imag(cx), real(cy), imag(cy)
		if math.IsNaN(rx) && !math.IsNaN(ry) || !math.IsNaN(rx) && math.IsNaN(ry) ||
			math.IsNaN(ix) && !math.IsNaN(iy) || !math.IsNaN(ix) && math.IsNaN(iy) {
			logger.Warnf("x: %v is not equal to y: %v", x, y)
			return false
		}
		if !(math.IsNaN(rx) && math.IsNaN(ry) && math.IsNaN(ix) && math.IsNaN(iy)) {
			if rx != ry || ix != iy {
				logger.Warnf("x: %v is not equal to y: %v", x, y)
				return false
			}
		}
	case reflect.String:
		if x.String() != y.String() {
			logger.Warnf("x: %v is not equal to y: %v", x, y)
			return false
		}
	case reflect.Struct:
		for i := 0; i < x.NumField(); i++ {
			if !equals(x.Field(i), y.Field(i)) {
				logger.Warnf("x: %v is not equal to y: %v", x.Field(i), y.Field(i))
				return false
			}
		}
	case reflect.Array, reflect.Slice:
		if x.Len() != y.Len() {
			return false
		}
		for i := 0; i < x.Len(); i++ {
			if !equals(x.Index(i), y.Index(i)) {
				logger.Warnf("x: %v is not equal to y: %v", x.Index(i), y.Index(i))
				return false
			}
		}
	case reflect.Map:
		if x.Len() != y.Len() {
			logger.Warnf("x: %v is not equal to y: %v", x, y)
			return false
		}
		keys := x.MapKeys()
		for _, key := range keys {
			if !equals(x.MapIndex(key), y.MapIndex(key)) {
				logger.Warnf("x: %v is not equal to y: %v", x.MapIndex(key), y.MapIndex(key))
				return false
			}
		}
	case reflect.Ptr:
		if !x.IsNil() && !y.IsNil() {
			if !equals(x.Elem(), y.Elem()) {
				logger.Warnf("x: %v is not equal to y: %v", x.Elem(), y.Elem())
				return false
			}
		} else if (x.IsNil() && !y.IsNil()) || (!x.IsNil() && y.IsNil()) {
			logger.Warnf("x: %v is not equal to y: %v", x, y)
			return false
		}
	default:
		sx, sy := fmt.Sprintf("%+v", x.Interface()), fmt.Sprintf("%+v", y.Interface())
		if sx != sy {
			logger.Warnf("x: %v is not equal to y: %v", x, y)
			return false
		}
	}
	return true
}

------------------------------

作为资深的go语言专家，你需要对CompareOldNew函数拆分为一些独立的小函数，要求重构后的函数圈复杂度不超过6，每个函数的代码行数不超过30。

func CompareOldNew(oldObj, newObj interface{}, compConfig *compare_dtos.CompareConfig) (bool, *compare_dtos.CompareDetail, error) {
	logger.Infof("Enter the major API portal...")

	result := &compare_dtos.CompareDetail{
		AddedObjPKeys:   []string{},
		AddedObjects:    []interface{}{},
		DeletedObjPKeys: []string{},
		DeletedObjects:  []interface{}{},
		UpdatedObjPKeys: []string{},
		UpdatedObjects:  []*compare_dtos.UpdateDetail{},
	}
	isEqual := true
	var disparities []string
	determineCompareActor(compConfig.CompareApproach)

	switch reflect.TypeOf(oldObj).Kind() {
	case reflect.Slice, reflect.Array:
		oldObjValue := reflect.ValueOf(oldObj)
		newObjValue := reflect.ValueOf(newObj)

		oldObjs := make([]interface{}, 0)
		newObjs := make([]interface{}, 0)

		oldLen := oldObjValue.Len()
		newLen := newObjValue.Len()

		for i := 0; i < oldLen; i++ {
			value := oldObjValue.Index(i).Interface()
			oldVal := reflect.ValueOf(value)
			if oldVal.Kind() == reflect.Ptr {
				oldObjs = append(oldObjs, oldVal.Elem().Interface())
			} else {
				oldObjs = append(oldObjs, value)
			}
		}
		for i := 0; i < newLen; i++ {
			value := newObjValue.Index(i).Interface()
			newVal := reflect.ValueOf(value)
			if newVal.Kind() == reflect.Ptr {
				newObjs = append(newObjs, newVal.Elem().Interface())
			} else {
				newObjs = append(newObjs, value)
			}
		}

		var kind reflect.Kind
		if oldLen > 0 {
			kind = reflect.ValueOf(oldObjs[0]).Kind()
		} else if newLen > 0 {
			kind = reflect.ValueOf(newObjs[0]).Kind()
		} else {
			return false, result, fmt.Errorf("empty object list for both old and new input parameters")
		}

		switch kind {
		case reflect.Struct:
			err := disposeStructList(oldObjs, newObjs, result, compConfig)
			if err != nil {
				logger.Errorf("****** [CompareOldNew] dispose struct list, error: %v", err.Error())
				return false, result, err
			}

		default:
			isEqual, disparities = compareActor.Equals(oldObjs, newObjs, compConfig.NeedDetails, compConfig.IgnoredFields)
			if !isEqual {
				result.UpdatedObjPKeys = append(result.UpdatedObjPKeys, disparities...)
			}
		}

	case reflect.Struct:
		err := disposeStructList([]interface{}{oldObj}, []interface{}{newObj}, result, compConfig)
		if err != nil {
			logger.Errorf("****** [CompareOldNew] dispose single struct obj, error: %v", err.Error())
			return false, result, err
		}

	default:
		isEqual, disparities = compareActor.Equals(oldObj, newObj, compConfig.NeedDetails, compConfig.IgnoredFields)
		if !isEqual {
			result.UpdatedObjPKeys = append(result.UpdatedObjPKeys, disparities...)
		}
	}

	return isEqual && len(result.AddedObjPKeys) == 0 && len(result.DeletedObjPKeys) == 0 && len(result.UpdatedObjPKeys) == 0 && len(result.UpdatedObjects) == 0, result, nil
}

------------------------------

作为资深的go语言专家，你需要对toRunnerAsyncDetail4Redis函数拆分为一些独立的小函数，要求重构后的函数圈复杂度不超过6，每个函数的代码行数不超过30。

func (runnerAsync *RunnerAsync) toRunnerAsyncDetail4Redis() *job_redis.RunnerAsyncDetail4Redis {
	rad := &job_redis.RunnerAsyncDetail4Redis{
		ResourceName: runnerAsync.ResourceName,
		TaskErr:      runnerAsync.TaskErr,
		Uuid:         runnerAsync.Uuid,
		Size:         runnerAsync.Size,
		TaskStepSize: runnerAsync.TaskStepSize,
		Status:       runnerAsync.Status,
		Name:         runnerAsync.Name,
		CreatedTime:  runnerAsync.CreatedTime,
		StartTime:    runnerAsync.StartTime,
		Progress:     runnerAsync.Progress,
		EndTime:      runnerAsync.EndTime,
	}

	var tmpTasks []*job_redis.RunnerSyncDetail
	for _, rs := range runnerAsync.Tasks {
		tmpTask := &job_redis.RunnerSyncDetail{
			Name:         rs.Name,
			ResourceName: rs.ResourceName,
			StepSize:     rs.StepSize,
			Status:       rs.Status,
			Path:         rs.Path,
			StopStep:     rs.StopStep,
			CreateTime:   rs.CreateTime,
			StartTime:    rs.StartTime,
			EndTime:      rs.EndTime,
			Progress:     rs.Progress,
		}
		var tmpSss []*job_redis.SyncStep
		for _, sstep := range rs.SyncSteps {
			tmpSs := &job_redis.SyncStep{
				Execution:  sstep.Execution,
				Name:       sstep.Name,
				CreateTime: sstep.CreateTime,
				StartMsg:   sstep.StartMsg,
				EndMsg:     sstep.EndMsg,
				StartTime:  sstep.StartTime,
				EndTime:    sstep.EndTime,
				Errs:       sstep.Errs,
				Status:     sstep.Status,
			}
			if sstep.Errs != nil {
				tmpSs.ErrMsg = sstep.Errs.Error()
			}

			var tmpParams []interface{}
			for _, param := range sstep.Params {
				var tmpParam interface{}
				switch reflect.ValueOf(param).Kind() {
				case reflect.Chan, reflect.Func, reflect.Invalid:
					tmpParam = "place_holder_for_channel"
				default:
					tmpParam = param
				}
				tmpParams = append(tmpParams, tmpParam)
			}
			tmpSs.Params = tmpParams
			tmpSss = append(tmpSss, tmpSs)
		}
		tmpTask.SyncSteps = tmpSss
		tmpTasks = append(tmpTasks, tmpTask)
	}

	rad.Tasks = tmpTasks

	return rad
}

------------------------------

作为资深的go语言专家，你需要对ComboQueryInstances函数拆分为一些独立的小函数，要求重构后的函数圈复杂度不超过6，每个函数的代码行数不超过30。

func ComboQueryInstances(modelName string, andFilter, orFilter map[string][]string, urlPara map[string]string,
	returnFields []string, neednot4Add bool, neednot4Or bool, instances interface{}) error {
	andCondition := Condition{}
	orCondition := Condition{}
	contions := []Condition{}
	url := ResPrefix + "/conbinationQuery/" + modelName + "s"
	var condString = ""
	for k, v := range urlPara {
		condString += "&" + k + "=" + v
	}
	var includeAttrString = ""
	for _, field := range returnFields {
		includeAttrString += field + ","
	}
	if len(includeAttrString) > 0 {
		condString += "&includeAttr=" + includeAttrString
	}
	if len(condString) > 0 {
		url += "?" + condString[1:]
	}

	andAttrs := []Attribute{}
	for key, con := range andFilter {
		if len(con) == 0 {
			continue
		}
		af := make(map[string][]string)
		af[key] = con
		andAttr := Attribute{af}
		andAttrs = append(andAttrs, andAttr)
	}

	andCondition = Condition{"and", andAttrs, neednot4Add}
	contions = append(contions, andCondition)

	orAttrs := []Attribute{}
	for key, con := range orFilter {
		if len(con) == 0 {
			continue
		}
		of := make(map[string][]string)
		of[key] = con
		orAttr := Attribute{of}
		orAttrs = append(orAttrs, orAttr)
	}
	orCondition = Condition{"or", orAttrs, neednot4Or}
	contions = append(contions, orCondition)

	comboQuery := ComboQueryREQ{"and", contions}

	reqBody, err := json.Marshal(comboQuery)
	if err != nil {
		logger.Errorf("ComboQueryInstances Marshal conditions error: %v", err)
		return err
	}
	logger.Info("ComboQueryInstances model:%s, url:%s, reqBody:%s", modelName, url, string(reqBody))

	resBody, _, _, err := httpservice.Post(url, httpservice.DefaultHeaders(), reqBody, nil)

	if err != nil {
		return fmt.Errorf("ComboQueryInstances model:%s from res failed:%v", modelName, err.Error())
	}

	logger.Debug("ComboQueryInstances Response body:%s", string(resBody))

	err = json.Unmarshal(resBody, instances)
	if err != nil {
		logger.Errorf("ComboQueryInstances model %s json unmarshal failed: %v", modelName, err)
		return fmt.Errorf("ComboQueryInstances model %s json unmarshal error: %v", modelName, err)
	}
	return nil
}

------------------------------

作为资深的go语言专家，你需要对PostOperateCodeToSM函数拆分为一些独立的小函数，要求重构后的函数圈复杂度不超过6，每个函数的代码行数不超过30。

func PostOperateCodeToSM() error {
	var absFilePath string
	var fileContent []byte
	var err error
	var reg_file_path string
	scene := os.Getenv("scene")
	switch scene {
	case common_scen.CCM_SCENE, common_scen.MCM_SCENE:
		reg_file_path = "conf/operate_code_ccm.json"
	case common_scen.CIIA_SCENE:
		reg_file_path = "conf/operate_code_ciia.json"
	case common_scen.TOB_SCENE:
		reg_file_path = "conf/operate_code_toB.json"
	default:
		reg_file_path = "conf/operate_code.json"
	}
	absFilePath = util.GetFilePath(reg_file_path)
	fileContent, err = util.ReadFile(absFilePath)
	if err != nil {
		logger.Errorf("PostOperateCodeToSM Open %s failed:%v", reg_file_path, err)
		switch scene {
		case common_scen.TOB_SCENE:
			reg_file_path = "conf/operate_code.json"
			absFilePath = util.GetFilePath(reg_file_path)
			fileContent, err = util.ReadFile(absFilePath)
			if err != nil {
				logger.Errorf("PostOperateCodeToSM Open %s failed:%v", reg_file_path, err)
				return err
			}
		default:
			return err
		}
	}
	err = SmRegOperateCode(fileContent)
	if err != nil {
		logger.Errorf("PostOperateCodeToSM SmRegOperateCode failed:", err.Error())
		return err
	}
	logger.Infof("PostOperateCodeToSM Init %s To SM successfully", reg_file_path)

	err = json.Unmarshal(fileContent, &AuthCheckModel)
	if err != nil {
		logger.Errorf("PostOperateCodeToSM json.Unmarshal AuthCheckModel to authcheck failed:", err.Error())
		return err
	}

	absFilePath = util.GetFilePath("conf/tenant_url_operatecode.json")
	fileContent, err = util.ReadFile(absFilePath)
	if err != nil {
		logger.Errorf("PostOperateCodeToSM Open tanent_url_operatecode.json failed:", err)
		return err
	}
	var tenant_url_codes []*AuthINFOpModelDto
	err = json.Unmarshal(fileContent, &tenant_url_codes)
	if err != nil {
		logger.Errorf("PostOperateCodeToSM json.Unmarshal tenant_url_code to authcheck failed:", err.Error())
		return err
	}
	for _, tenant_url_code := range tenant_url_codes {
		AuthCheckModel.OperationList = append(AuthCheckModel.OperationList, tenant_url_code)
	}

	logger.Infof("PostOperateCodeToSM FileContent to authcheck:", util.ToJSONStr(AuthCheckModel))
	return nil
}

------------------------------

作为资深的go语言专家，你需要对disposeStructList函数拆分为一些独立的小函数，要求重构后的函数圈复杂度不超过6，每个函数的代码行数不超过30。

func disposeStructList(oldObjs, newObjs []interface{}, result *compare_dtos.CompareDetail, compConfig *compare_dtos.CompareConfig) error {
	if len(oldObjs) == 0 && len(newObjs) == 0 {
		return fmt.Errorf("empty object list for both old and new input parameters")
	}

	// Determine the primary key for the comparison;
	primaryKey := compConfig.PrimaryKey
	if len(primaryKey) == 0 {
		primaryKey = findPossiblePrimaryKey(oldObjs[0])
	}
	logger.Infof("****** [CompareOldNew] Primary Key: %s", primaryKey)

	// Build the two value list. If the element type of the original list is pointer, extract the substantial value pointed and form the value lists;
	if len(oldObjs) == 0 {
		for _, obj := range newObjs {
			value := reflect.ValueOf(obj)
			if len(primaryKey) > 0 {
				result.AddedObjPKeys = append(result.AddedObjPKeys, value.FieldByName(primaryKey).String())
			}
			result.AddedObjects = append(result.AddedObjects, obj)
		}

		return nil
	}

	if len(newObjs) == 0 {
		for _, obj := range oldObjs {
			value := reflect.ValueOf(obj)
			if len(primaryKey) > 0 {
				result.DeletedObjPKeys = append(result.DeletedObjPKeys, value.FieldByName(primaryKey).String())
			}

			result.DeletedObjects = append(result.DeletedObjects, obj)
		}

		return nil
	}

	// For multiple elements comparison, if there is no possible primary key and no designated p-key, throw error;
	if len(primaryKey) == 0 && (len(oldObjs) > 1 || len(newObjs) > 1) {
		return fmt.Errorf("primary key not found for batch struct comparison")
	}

	// Dispose comparison of two lists containing single object;
	if len(primaryKey) == 0 && len(oldObjs) == 1 && len(newObjs) == 1 {
		identical, disparities := compareActor.Equals(oldObjs[0], newObjs[0], compConfig.NeedDetails, compConfig.IgnoredFields)
		if !identical {
			result.UpdatedObjects = append(result.UpdatedObjects, &compare_dtos.UpdateDetail{Content: newObjs[0], ChangedFields: disparities})
		}

		return nil
	}

	// When reaching here, it means that both of the two object lists are comprised of multiple elements;
	// Pre-process for the list containing multiple new objects;
	newObjMap := make(map[string]interface{})
	for i := range newObjs {
		newVal := reflect.ValueOf(newObjs[i])
		if newVal.Kind() != reflect.Struct {
			return fmt.Errorf("all elements in the new object list must be homogeneous")
		}
		newObjMap[newVal.FieldByName(primaryKey).String()] = newObjs[i]
	}

	// Enter essential multiple objects comparing process;
	for i := range oldObjs {
		oldVal := reflect.ValueOf(oldObjs[i])
		if oldVal.Kind() != reflect.Struct {
			return fmt.Errorf("all elements in the old object list must be homogeneous")
		}
		pKeyValue := oldVal.FieldByName(primaryKey).String()
		logger.Infof("****** [CompareOldNew] Primary Key Value: %s", pKeyValue)

		newObj, ok := newObjMap[pKeyValue]
		if ok {
			identical, disparities := compareActor.Equals(oldObjs[i], newObj, compConfig.NeedDetails, compConfig.IgnoredFields)
			if !identical {
				result.UpdatedObjPKeys = append(result.UpdatedObjPKeys, pKeyValue)
				result.UpdatedObjects = append(result.UpdatedObjects, &compare_dtos.UpdateDetail{Content: newObj, ChangedFields: disparities})
			}
			delete(newObjMap, pKeyValue)
		} else {
			result.DeletedObjPKeys = append(result.DeletedObjPKeys, pKeyValue)
			result.DeletedObjects = append(result.DeletedObjects, oldObjs[i])
		}
	}

	// Post-process for the left new objects;
	for pKey, obj := range newObjMap {
		result.AddedObjPKeys = append(result.AddedObjPKeys, pKey)
		result.AddedObjects = append(result.AddedObjects, obj)
	}

	return nil
}

------------------------------

作为资深的go语言专家，你需要对Create函数拆分为一些独立的小函数，要求重构后的函数圈复杂度不超过6，每个函数的代码行数不超过30。

func Create(unir *UniversalREQ, force bool) error {
	var lastedVisitedAt string
	dao := &Dao{}
	if err := rediz.HGetInstance(storage.RedisKeyToken, unir.Key(), dao); err == nil {
		if !dao.Hibernated() && !force {
			return nil
		}
		lastedVisitedAt = dao.LastVisitedAt
	} else {
		logger.Debugf("[Create] get token %s from redis error: %v", unir.Key(), err)
	}

	if (unir.UserID == "" && unir.User == "") || (unir.CipherCode == "" && unir.Password == "") {
		logger.Errorf("[Create] url: %s ToCloudTokenREQ user %s or pwd is empty.", unir.URL, unir.User)
		return fmt.Errorf("user or pwd is empty")
	}

	request, err := unir.ToCloudTokenREQ()
	if err != nil {
		logger.Errorf("[Create] ToCloudTokenREQ error: %v", err)
		return err
	}
	url := fmt.Sprintf("%s%s", unir.URL, request.TokenApi())
	bs, headers, err := PostTokenProvider(url, unir.Type, request, unir.SSLAuth)
	if err != nil {
		logger.Debugf("[Create] http PostToken error: %v", err)
		return err
	}

	var response CloudTokenRSP
	var token string
	switch unir.Type {
	case AuthTypeNetInsight:
		response = &NetInsightRSP{}
	case AuthTypeOpenpalette:
		response = &OpenPaletteRSP{}
	case AuthTypeKeystoneV2:
		response = &KeystoneV2RSP{}
	case AuthTypeKeystoneV3:
		response = &KeystoneV3RSP{}
		for k, vs := range headers {
			if strings.EqualFold(k, "x-subject-token") {
				for _, v := range vs {
					token = v
					break
				}
			}
		}
	default:
		return fmt.Errorf("unsupported cloud type %s", unir.Type)
	}
	if err = json.Unmarshal(bs, response); err != nil {
		logger.Errorf("[Create] json.Unmarshal error: %v", err)
		return fmt.Errorf("failed to deserilize response: %v", err)
	}
	dao = response.ToDao(unir, token, lastedVisitedAt)
	if err = rediz.HSet(storage.RedisKeyToken, unir.Key(), dao); err != nil {
		logger.Errorf("[Create] rediz.HSet error: %v", err)
		return fmt.Errorf("store token error: %v", err)
	}
	return nil
}

------------------------------

作为资深的go语言专家，你需要对Equals函数拆进行复杂度降低的操作，要求重构后的函数圈复杂度不超过6。

func (c *CompareRecursively) Equals(x, y interface{}, needDetails bool, ignoredFields []*compare_dtos.IgnoredField) (bool, []string) {
	logger.Infof("Enter Equals - Recursive...")
	vx, vy := reflect.ValueOf(x), reflect.ValueOf(y)
	tx, ty := reflect.TypeOf(x), reflect.TypeOf(y)
	if tx.String() != ty.String() {
		return false, []string{}
	}

	ignoreMap := make(map[string]string)
	for _, pair := range ignoredFields {
		ignoreMap[pair.FieldName] = pair.StructName
	}

	if tx.String() != ty.String() {
		logger.Infof("*********tx.String() - %s != ty.String() - %s**********", tx.String(), ty.String())
		return false, []string{}
	}

	var disparities []string
	result := compareRecursively(vx, vy, needDetails, ignoreMap, &disparities, 0, tx.Name())

	return result, disparities
}

------------------------------

作为一个资深的go语言专家，你需要将compareIteratively函数分解成多个函数，要求重构后的函数代码行数不超过30，且圈复杂度不超过6。
func compareIteratively(valX, valY reflect.Value, needDetails bool, ignoredMap map[string]string) (bool, []string) {
	logger.Infof("Enter compareByIterative...")
	stackX := my_stack.NewStack()
	stackX.Push(CompCell{Value: valX, FieldName: reflect.TypeOf(valX.Interface()).Name(), CompLevel: 0, Visited: false})

	stackY := my_stack.NewStack()
	stackY.Push(CompCell{Value: valY, FieldName: reflect.TypeOf(valY.Interface()).Name(), CompLevel: 0, Visited: false})

	var disparities []string
	var currBranchFlagEqual = true
	var globalFlagEqual = true

	// Traverse the data struct tree through DFS manner;
	for !stackX.IsEmpty() && !stackY.IsEmpty() {
		if !needDetails && !globalFlagEqual {
			return false, []string{}
		}

		cellX := stackX.Peek().(CompCell)
		x := cellX.Value

		cellY := stackY.Peek().(CompCell)
		y := cellY.Value

		cmpDepth := cellX.CompLevel

		if !x.IsValid() && !y.IsValid() {
			stackX.Pop()
			stackY.Pop()
			continue
		}

		if !x.IsValid() || !y.IsValid() || x.Type().Kind() == reflect.Invalid || y.Type().Kind() == reflect.Invalid || (x.Type().Kind() != y.Type().Kind()) {
			currBranchFlagEqual = false
			globalFlagEqual = false
			stackX.Pop()
			stackY.Pop()
			continue
		}

		switch x.Type().Kind() {
		case reflect.Invalid:
			logger.Errorf("****** invalid field: %s", cellX.FieldName)
			currBranchFlagEqual = false
			globalFlagEqual = false
			stackX.Pop()
			stackY.Pop()
		case reflect.Bool:
			if x.Bool() != y.Bool() {
				currBranchFlagEqual = false
				globalFlagEqual = false
				if cmpDepth <= 1 {
					disparities = append(disparities, cellX.FieldName)
					currBranchFlagEqual = true
				}
			}
			stackX.Pop()
			stackY.Pop()
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			if x.Int() != y.Int() {
				currBranchFlagEqual = false
				globalFlagEqual = false
				if cmpDepth <= 1 {
					disparities = append(disparities, cellX.FieldName)
					currBranchFlagEqual = true
				}
			}
			stackX.Pop()
			stackY.Pop()
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
			if x.Uint() != y.Uint() {
				currBranchFlagEqual = false
				globalFlagEqual = false
				if cmpDepth <= 1 {
					disparities = append(disparities, cellX.FieldName)
					currBranchFlagEqual = true
				}
			}
			stackX.Pop()
			stackY.Pop()
		case reflect.Float32, reflect.Float64:
			fx, fy := x.Float(), y.Float()
			if math.IsNaN(fx) && !math.IsNaN(fy) || !math.IsNaN(fx) && math.IsNaN(fy) || fx-fy < compare_dtos.Epsilon {
				currBranchFlagEqual = false
				globalFlagEqual = false
				if cmpDepth <= 1 {
					disparities = append(disparities, cellX.FieldName)
					currBranchFlagEqual = true
				}
			}
			stackX.Pop()
			stackY.Pop()
		case reflect.Complex64, reflect.Complex128:
			cx, cy := x.Complex(), y.Complex()
			rx, ix, ry, iy := real(cx), imag(cx), real(cy), imag(cy)
			if math.IsNaN(rx) && !math.IsNaN(ry) || !math.IsNaN(rx) && math.IsNaN(ry) ||
				math.IsNaN(ix) && !math.IsNaN(iy) || !math.IsNaN(ix) && math.IsNaN(iy) {
				currBranchFlagEqual = false
				globalFlagEqual = false
				if cmpDepth <= 1 {
					disparities = append(disparities, cellX.FieldName)
					currBranchFlagEqual = true
				}
			}
			if !(math.IsNaN(rx) && math.IsNaN(ry) && math.IsNaN(ix) && math.IsNaN(iy)) {
				if rx != ry || ix != iy {
					currBranchFlagEqual = false
					globalFlagEqual = false
				}
			}
			stackX.Pop()
			stackY.Pop()
		case reflect.String:
			if x.String() != y.String() {
				currBranchFlagEqual = false
				globalFlagEqual = false
				if cmpDepth <= 1 {
					disparities = append(disparities, cellX.FieldName)
					currBranchFlagEqual = true
				}
			}
			stackX.Pop()
			stackY.Pop()
		case reflect.Struct:
			if cellX.Value.NumField() != cellY.Value.NumField() {
				currBranchFlagEqual = false
				globalFlagEqual = false
				stackX.Pop()
				stackY.Pop()
				if cmpDepth <= 1 {
					disparities = append(disparities, cellX.FieldName)
					currBranchFlagEqual = true
				}
				continue
			}
			if cellX.Visited {
				stackX.Pop()
				stackY.Pop()
				if (cmpDepth == 0 && len(disparities) > 0) || (!currBranchFlagEqual && cmpDepth <= 1) {
					disparities = append(disparities, cellX.FieldName)
					currBranchFlagEqual = true
				}
				continue
			}
			cellX.Visited = true
			stackX.Pop()
			stackX.Push(cellX)
			if cmpDepth <= 1 {
				currBranchFlagEqual = true
			}
			xLen, yLen := x.NumField(), y.NumField()
			index := 0
			for index < xLen && index < yLen {
				cmpTags := strings.Split(x.Type().Field(index).Tag.Get("cmp"), ",")
				if ignoredField, ok := ignoredMap[reflect.TypeOf(x.Interface()).Field(index).Name]; ok && ignoredField == cellX.FieldName {
					index++
					continue
				}
				for _, tag := range cmpTags {
					if tag != "ignore" {
						stackX.Push(CompCell{Value: x.Field(index), FieldName: reflect.TypeOf(x.Interface()).Field(index).Name, CompLevel: cmpDepth + 1, Visited: false})
						stackY.Push(CompCell{Value: y.Field(index), FieldName: reflect.TypeOf(y.Interface()).Field(index).Name, CompLevel: cmpDepth + 1, Visited: false})
					}
				}
				index++
			}

			if index < xLen || index < yLen {
				globalFlagEqual = false
				if needDetails && cmpDepth == 0 {
					for index < xLen {
						disparities = append(disparities, reflect.TypeOf(x.Interface()).Field(index).Name)
						index++
					}
					for index < yLen {
						disparities = append(disparities, reflect.TypeOf(y.Interface()).Field(index).Name)
						index++
					}
				}
			}

		case reflect.Array, reflect.Slice:
			if x.Len() != y.Len() {
				currBranchFlagEqual = false
				globalFlagEqual = false
				stackX.Pop()
				stackY.Pop()
				if cmpDepth <= 1 {
					disparities = append(disparities, cellX.FieldName)
					currBranchFlagEqual = true
				}
				continue
			}
			if cellX.Visited {
				stackX.Pop()
				stackY.Pop()
				if !currBranchFlagEqual && cmpDepth <= 1 {
					disparities = append(disparities, cellX.FieldName)
					currBranchFlagEqual = true
				}
				continue
			}

			sort.IterativeSort(cellX.Value)
			sort.IterativeSort(cellY.Value)

			cellX.Visited = true
			stackX.Pop()
			stackX.Push(cellX)
			stackY.Pop()
			stackY.Push(cellY)
			if cmpDepth <= 1 {
				currBranchFlagEqual = true
			}

			xLen, yLen := x.Len(), y.Len()
			index := 0
			for index < xLen && index < yLen {
				stackX.Push(CompCell{Value: x.Index(index), FieldName: "", CompLevel: cmpDepth + 1, Visited: false})
				stackY.Push(CompCell{Value: y.Index(index), FieldName: "", CompLevel: cmpDepth + 1, Visited: false})
				index++
			}

			if index < xLen || index < yLen {
				globalFlagEqual = false
			}
		case reflect.Map:
			if cellX.Visited {
				stackX.Pop()
				stackY.Pop()
				if !currBranchFlagEqual && cmpDepth <= 1 {
					disparities = append(disparities, cellX.FieldName)
				}
				continue
			}
			cellX.Visited = true
			stackX.Pop()
			stackX.Push(cellX)
			if cmpDepth <= 1 {
				currBranchFlagEqual = true
			}
			keysX := x.MapKeys()
			usedKeys := make(map[string]bool)
			for _, key := range keysX {
				if _, ok := ignoredMap[key.String()]; !ok {
					stackX.Push(CompCell{Value: x.MapIndex(key), FieldName: key.String(), CompLevel: cmpDepth + 1, Visited: false})
					stackY.Push(CompCell{Value: y.MapIndex(key), FieldName: key.String(), CompLevel: cmpDepth + 1, Visited: false})
				}
				usedKeys[key.String()] = true
			}

			keysY := y.MapKeys()
			for _, key := range keysY {
				if used, ok := usedKeys[key.String()]; !ok && !used {
					globalFlagEqual = false
					if needDetails && cmpDepth == 0 {
						disparities = append(disparities, key.String())
					}
				}
			}

		case reflect.Ptr:
			stackX.Pop()
			stackX.Push(CompCell{Value: x.Elem(), FieldName: cellX.FieldName, CompLevel: cmpDepth, Visited: false})
			stackY.Pop()
			stackY.Push(CompCell{Value: y.Elem(), FieldName: cellY.FieldName, CompLevel: cmpDepth, Visited: false})

		default:
			sx, sy := fmt.Sprintf("%+v", x.Interface()), fmt.Sprintf("%+v", y.Interface())
			if sx != sy {
				currBranchFlagEqual = false
				globalFlagEqual = false
				if cmpDepth <= 1 {
					disparities = append(disparities, cellX.FieldName)
					currBranchFlagEqual = true
				}
			}
			stackX.Pop()
			stackY.Pop()
		}
	}

	// post-process;
	if !stackX.IsEmpty() {
		cellX := stackX.Pop().(CompCell)
		if cellX.CompLevel <= 1 {
			disparities = append(disparities, cellX.FieldName)
		}
	}

	if !stackY.IsEmpty() {
		cellY := stackY.Pop().(CompCell)
		if cellY.CompLevel <= 1 {
			disparities = append(disparities, cellY.FieldName)
		}
	}

	return globalFlagEqual, disparities
}

------------------------------

作为一个资深的go语言专家，你需要将startWatchDog函数的圈复杂度降到不超过6
func (rl *RedisLock) startWatchDog() {
	period := time.Duration(rl.TimeoutMs/3) * time.Millisecond
	ticker := time.NewTicker(period)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			resp, err := rediz.Eval(GetPxCommand, []string{rl.Key})
			if err != nil {
				logger.Errorf("watchDog redisLock:%s GetPxCommand failed:%v", util.ToJSONStr(rl), err)
				return
			}
			if resp == nil {
				logger.Errorf("watchDog redisLock:%s GetPxCommand failed resp is nil", util.ToJSONStr(rl))
				return
			}
			reply, ok := resp.(int64)
			if ok && reply == -2 {
				logger.Infof("watchDog redisLock:%s GetPxCommand failed, key is not exist", util.ToJSONStr(rl))
				return
			} else if ok && reply == -1 {
				logger.Infof("watchDog redisLock:%s GetPxCommand failed, ttl is not set", util.ToJSONStr(rl))
			}

			resp, err = rediz.Eval(UpdatePxCommand, []string{rl.Key}, rl.Id, strconv.Itoa(rl.TimeoutMs))
			if err != nil {
				logger.Errorf("watchDog redisLock:%s UpdatePxCommand failed:%v", util.ToJSONStr(rl), err)
				return
			}
			if resp == nil {
				logger.Errorf("watchDog redisLock:%s UpdatePxCommand failed resp is nil", util.ToJSONStr(rl))
				return
			}
			reply1, ok1 := resp.(string)
			if ok1 && reply1 == "OK" {
				logger.Infof("watchDog redisLock:%s UpdatePxCommand success, old ttl is:%ds, new ttl is:%ds",
					util.ToJSONStr(rl), reply*1000, rl.TimeoutMs)
			} else {
				logger.Errorf("watchDog redisLock:%s UpdatePxCommand failed: resp is not ok:%s",
					util.ToJSONStr(rl), util.ToJSONStr(resp))
				return
			}
		case <-rl.ctx4WatchDog.Done():
			rl.ctx4WatchDog = nil
			rl.cancel4WatchDogCtx = nil
			logger.Infof("watchDog redisLock:%s end", util.ToJSONStr(rl))
			return
		}
	}
}