# 任务描述
作为资深的go语言专家，你需要写一个函数func ActivateModelinspection(reqParam ModelinspectionRequestParam, reqBody interface{}) (interface{}, error)
# 功能描述
首先你需要定义个变量调用 wsm.ModelinspectionWsmKeywords结构体，并给ModelinspectionWsmKeywords结构体中的ClusterId从reqParam赋值，然后再调用wsm.ModelinspectionHandler函数，将输出返回，注：需要考虑调用ModelinspectionHandler函数!suc的情况,并返回fmt.Errorf(err)
# 输出要求
return的结果是调用ModelinspectionHandler函数的内容
# 接口举例
type ModelinspectionRequestParam struct {
	ClusterId string
	TaskId    string
}

func ModelinspectionHandler(keyswords ModelinspectionWsmKeywords, reqBody interface{}, method string) (map[string]interface{}, string, bool) {
	restfulParams, suc := buildModelinspectionRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s modelinspection build restful req failed", method)
		return nil, "", false
	}

	logger.Info("keyswords.TaskId:%v", keyswords.TaskId)

	switch method {

	case "get":
		logger.Info("get from apts")
		return GetModelinspection(restfulParams)

	case "post":
		logger.Info("post from apts")
		return PostModelinspection(restfulParams, reqBody.(interface{}))

	case "stop":
		logger.Info("stop from apts")
		return DeleteModelinspection(restfulParams)

	case "delete":
		logger.Info("delete from apts")
		return DeleteModelinspection(restfulParams)

	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, "", false
	}
}

------------------------------

# 任务描述
作为资深的go语言专家，你需要写一个函数func func GetModelinspection(reqParam ModelinspectionRequestParam) (interface{}, error)
# 功能描述
首先你需要定义个变量调用 wsm.ModelinspectionWsmKeywords结构体，并给ModelinspectionWsmKeywords结构体中的ClusterId从reqParam赋值，然后再调用wsm.ModelinspectionHandler函数，将输出返回，注：需要考虑调用ModelinspectionHandler函数!suc的情况,并返回fmt.Errorf(err)
# 输出要求
return的结果是调用ModelinspectionHandler函数的内容
# 接口举例
type ModelinspectionRequestParam struct {
	ClusterId string
	TaskId    string
}

func ModelinspectionHandler(keyswords ModelinspectionWsmKeywords, reqBody interface{}, method string) (map[string]interface{}, string, bool) {
	restfulParams, suc := buildModelinspectionRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s modelinspection build restful req failed", method)
		return nil, "", false
	}

	logger.Info("keyswords.TaskId:%v", keyswords.TaskId)

	switch method {

	case "get":
		logger.Info("get from apts")
		return GetModelinspection(restfulParams)

	case "post":
		logger.Info("post from apts")
		return PostModelinspection(restfulParams, reqBody.(interface{}))

	case "stop":
		logger.Info("stop from apts")
		return DeleteModelinspection(restfulParams)

	case "delete":
		logger.Info("delete from apts")
		return DeleteModelinspection(restfulParams)

	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, "", false
	}
}

------------------------------

# 任务描述
作为资深的go语言专家，你需要写一个函数func DeleteModelinspection(reqParam ModelinspectionRequestParam) error
# 功能描述
首先你需要定义个变量调用 wsm.ModelinspectionWsmKeywords结构体，并给ModelinspectionWsmKeywords结构体中的ClusterId和TaskId从reqParam赋值，然后再调用wsm.ModelinspectionHandler函数，将输出返回，注：需要考虑调用ModelinspectionHandler函数!suc的情况,并返回fmt.Errorf(err)
# 输出要求
return的结果是调用ModelinspectionHandler函数的内容
# 接口举例
type ModelinspectionRequestParam struct {
	ClusterId string
	TaskId    string
}

func ModelinspectionHandler(keyswords ModelinspectionWsmKeywords, reqBody interface{}, method string) (map[string]interface{}, string, bool) {
	restfulParams, suc := buildModelinspectionRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s modelinspection build restful req failed", method)
		return nil, "", false
	}

	logger.Info("keyswords.TaskId:%v", keyswords.TaskId)

	switch method {

	case "get":
		logger.Info("get from apts")
		return GetModelinspection(restfulParams)

	case "post":
		logger.Info("post from apts")
		return PostModelinspection(restfulParams, reqBody.(interface{}))

	case "stop":
		logger.Info("stop from apts")
		return DeleteModelinspection(restfulParams)

	case "delete":
		logger.Info("delete from apts")
		return DeleteModelinspection(restfulParams)

	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, "", false
	}
}

------------------------------

# 任务描述
// 作为资深的go语言专家，你需要写一个函数func DeleteModelinspection(reqParam ModelinspectionRequestParam) error
// # 功能描述
// 首先你需要定义个变量调用 wsm.ModelinspectionWsmKeywords结构体，并给ModelinspectionWsmKeywords结构体中的ClusterId和TaskId从reqParam赋值，然后再调用wsm.ModelinspectionHandler函数，将输出返回，注：需要考虑调用ModelinspectionHandler函数!suc的情况,并返回fmt.Errorf(err)
// # 输出要求
// return的结果是调用ModelinspectionHandler函数的内容
// # 接口举例
// type ModelinspectionRequestParam struct {
// 	ClusterId string
// 	TaskId    string
// }

// func ModelinspectionHandler(keyswords ModelinspectionWsmKeywords, reqBody interface{}, method string) (map[string]interface{}, string, bool) {
// 	restfulParams, suc := buildModelinspectionRestfulReq(keyswords, method)
// 	if !suc {
// 		logger.Error("wsm %s modelinspection build restful req failed", method)
// 		return nil, "", false
// 	}

// 	logger.Info("keyswords.TaskId:%v", keyswords.TaskId)

// 	switch method {

// 	case "get":
// 		logger.Info("get from apts")
// 		return GetModelinspection(restfulParams)

// 	case "post":
// 		logger.Info("post from apts")
// 		return PostModelinspection(restfulParams, reqBody.(interface{}))

// 	case "stop":
// 		logger.Info("stop from apts")
// 		return DeleteModelinspection(restfulParams)

// 	case "delete":
// 		logger.Info("delete from apts")
// 		return DeleteModelinspection(restfulParams)

// 	default:
// 		logger.Errorf("wsm unsupport method:%s", method)
// 		return nil, "", false
// 	}
// }

------------------------------

作为一个资深的go语言专家，你需要将GetLuckyUser函数分解成多个函数，要求每个函数的行数不能超过20行，函数圈复杂度不超过6
func GetLuckyUser(envId, tenantId, endpointname string) (string, string, string, cloudtoken.AuthType,
	*common_cloud_env.Endpoint4Redis, error) {
	env4Redis, err := cloud_env_redis.Get(envId)
	if err != nil {
		return "", "", "", "", nil, fmt.Errorf("fetchLuckerUser cloud_env_redis Get "+
			"failed: envId %s is not exist: %v", envId, err)
	}

	authType, endPoint, err := env4Redis.FindSpecialEndPointInfo(endpointname)
	if err != nil {
		return "", "", "", "", nil, fmt.Errorf("fetchLuckerUser FindSpecialEndPointInfo failed:"+
			" envId %s endpoint %s is not exist: %v", envId, endpointname, err)
	}

	if common_cloud_env.CheckIsOpenstackCloud(env4Redis.EnvType) {
		tenant4Redis, err := tenant_redis.Get(envId, tenantId)
		if err != nil {
			return "", "", "", "", nil, fmt.Errorf("fetchLuckerUser tenant_redis Get failed:"+
				" tenant %s not under envId %s: %v", tenantId, envId, err)
		}

		if authType == cloudtoken.AuthTypeKeystoneV3 {
			validUsers := []*tenant_redis.UserRoles4Redis{}
			for _, userRole := range tenant4Redis.UserRoles {
				if userRole.Name != "" && userRole.Password != "" {
					validUsers = append(validUsers, userRole)
				}
			}
			leftUsers := []*tenant_redis.UserRoles4Redis{}
			for _, userRole := range validUsers {
				for _, role := range userRole.Roles {
					if role.Name == "prj_manager" {
						leftUsers = append(leftUsers, userRole)
					}
				}
			}
			if len(leftUsers) == 0 {
				for _, userRole := range validUsers {
					for _, role := range userRole.Roles {
						if role.Name == "admin" {
							leftUsers = append(leftUsers, userRole)
						}
					}
				}
			}
			if len(leftUsers) != 0 {
				return leftUsers[0].Name, leftUsers[0].Password, tenant4Redis.Name, authType, endPoint, nil
			} else if len(validUsers) != 0 {
				return validUsers[0].Name, leftUsers[0].Password, tenant4Redis.Name, authType, endPoint, nil
			} else {
				return "", "", "", "", nil, fmt.Errorf("fetchLuckerUser failed: can not find valid user "+
					"under tenant:%s env:%s ", tenant4Redis.ID, env4Redis.ID)
			}
		} else {
			return endPoint.UserName, endPoint.Base64OriginalPwd, tenant4Redis.Name, authType, endPoint, nil
		}
	} else {
		return endPoint.UserName, endPoint.Base64OriginalPwd, endPoint.TenantName, authType, endPoint, nil
	}
}

------------------------------

作为一个资深的go语言专家，你需要将getOptDetail函数分解成多个函数，要求每个函数的行数不能超过20行，函数圈复杂度不超过6
func (log *OptLogInfo) getOptDetail() string {
	detail := InternationalField{}
	var detailEn, detailZh []string

	for _, envLog := range log.EnvInfo {
		if envLog.Id != "" && envLog.Name == "" {
			env, err := cloud_env_redis.Get(envLog.Id)
			if err != nil {
				logger.Errorf("[SetOptDetail] get env: %s from redis failed: %v", envLog.Id, err)
				continue
			}
			envLog.Name = env.Name
		}
		detailEn = append(detailEn, "envName: "+envLog.Name+", envId: "+envLog.Id)
		detailZh = append(detailZh, "云环境名："+envLog.Name+"，云环境ID："+envLog.Id)
	}

	for _, tenantLog := range log.TenantInfo {
		if tenantLog.Id != "" && tenantLog.Name == "" {
			tenant, err := tenant_redis.GetById(tenantLog.Id)
			if err != nil {
				logger.Errorf("[SetOptDetail] get tenant: %s from redis failed: %v", tenantLog.Id, err)
				continue
			}
			tenantLog.Name = tenant.Name
		}
		detailEn = append(detailEn, "tenantName: "+tenantLog.Name+", tenantId: "+tenantLog.Id)
		detailZh = append(detailZh, "租户名："+tenantLog.Name+"，租户ID："+tenantLog.Id)
	}

	for _, vdcLog := range log.VdcInfo {
		if vdcLog.Id != "" && vdcLog.Name == "" {
			vdc, err := vdc_redis.GetVdc(vdcLog.Id)
			if err != nil {
				logger.Errorf("[SetOptDetail] get vdc: %s from redis failed: %v", vdcLog.Id, err)
				continue
			}
			vdcLog.Name = vdc.Name
		}
		detailEn = append(detailEn, "vdcName: "+vdcLog.Name+", vdcId: "+vdcLog.Id)
		detailZh = append(detailZh, "VDC名："+vdcLog.Name+"，VDC ID："+vdcLog.Id)
	}

	if log.ObjNum != 0 {
		detailEn = append(detailEn, "objNum: "+strconv.Itoa(log.ObjNum))
		detailZh = append(detailZh, "操作对象数："+strconv.Itoa(log.ObjNum))
	}

	for _, obj := range log.ObjInfo {
		log.OptResource = append(log.OptResource, fmt.Sprintf("%s(%s)", obj.Name, obj.Id))
		detailEn = append(detailEn, "objName: "+obj.Name+", objId: "+obj.Id)
		detailZh = append(detailZh, "操作对象名："+obj.Name+"，操作对象ID："+obj.Id)
	}

	detail.En_US = strings.Join(detailEn, ", ")
	detail.Zh_CN = strings.Join(detailZh, "，")

	return util.ToJSONStr(detail)
}

------------------------------

作为一个资深的go语言专家，你需要将setReflectValue函数分解成多个函数，要求每个函数的行数不能超过20行，函数圈复杂度不超过6
func setReflectValue(insFieldVal reflect.Value, data interface{}) error {
	if !insFieldVal.CanSet() {
		logger.Errorf("setReflectValue error: field value %s is not settable", insFieldVal.String())
		return errors.New("non settable value type")
	}

	dataTyp, dataVal := reflect.TypeOf(data), reflect.ValueOf(data)
	for dataVal.Kind() == reflect.Ptr {
		dataTyp, dataVal = dataTyp.Elem(), dataVal.Elem()
	}
	filedTyp := insFieldVal.Type()
	numPtr := 0
	for filedTyp.Kind() == reflect.Ptr {
		numPtr++
		filedTyp = filedTyp.Elem()
	}
	fieldVal := reflect.New(filedTyp)
	switch dataTyp.String() {
	case "[]uint8":
		vi := fieldVal.Interface()
		bs, _ := dataVal.Interface().([]byte)
		if err := json.Unmarshal(bs, vi); err != nil {
			logger.Errorf("fillInstanceFields:setReflectValue json.Unmarshal failed:", err.Error())
			logger.Warnf("error bytes:", bs)
			return err
		}
	case "time.Time":
		t, _ := dataVal.Interface().(time.Time)
		ts := t.Format(time.RFC3339)
		fieldVal.Elem().Set(reflect.ValueOf(ts))
	default:
		if dataTyp != filedTyp {
			if dataTyp.ConvertibleTo(filedTyp) {
				fieldVal.Elem().Set(dataVal.Convert(filedTyp))
			} else {
				logger.Errorf("Cannot convert type", dataTyp, "to type", filedTyp)
				return errors.New("setReflectValue: Type conversion error")
			}
		} else {
			fieldVal.Elem().Set(dataVal)
		}
	}

	valToBeSet := fieldVal.Elem()
	for i := 0; i < numPtr; i++ {
		tmpVal := reflect.New(valToBeSet.Type())
		tmpVal.Elem().Set(valToBeSet)
		valToBeSet = tmpVal
	}

	insFieldVal.Set(valToBeSet)
	return nil
}