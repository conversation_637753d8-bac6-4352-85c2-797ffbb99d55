# 任务描述
作为一个go语言专家，你需要仿照AptsHandler函数，写一个func CaptsHandler(keyswords EvaluationWsmKeywords, reqBody interface{}, method string) (*EvaluationValue, bool) 函数
# 功能描述
需要调用buildEvaluationRestfulReq函数，并且只使用case "activate":，
# 输出要求
return的结果是PostEvaluateTask函数的内容
# 接口举例
func AptsHandler(keyswords EvaluationWsmKeywords, reqBody interface{}, method string) (*EvaluationInfo, bool) {
	restfulParams, suc := buildEvaluationRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s evaluation build restful req failed", method)
		return nil, false
	}
	switch method {
	case "get":
		return GetEvaluateTask(restfulParams)
	case "delete":
		return nil, DeleteEvaluation(restfulParams)
	case "stop":
		return StopEvaluateTask(restfulParams, reqBody.(map[string]interface{}))
	case "activate":
		return PostEvaluateTask(restfulParams, reqBody.(map[string]interface{}))
	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, false
	}
}

------------------------------

# 任务描述
作为一个go语言专家，你需要写一个函数func ActivateEvaluation(reqParam EvaluationRequestParam, reqBody ActiveEvaluationReq) error，
# 功能描述
首先调用generatePlan函数，然后所获得的值，通过cwsmutils.MapSlicetoMap函数转换成map[string]interface{}类型的值，作为调用evaluationValueFromWsm函数的入参

# 接口举例
func generatePlan(evaluateId string, reqBody ActiveEvaluationReq) (planTables []map[string]interface{}, err error)

func MapSlicetoMap(in []map[string]interface{}) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	for _, m := range in {
		for key, value := range m {
			result[key] = value
		}
	}
	return result, nil
}

func evaluationValueFromWsm(evaluateId string, reqParam EvaluationRequestParam, task map[string]interface{}, action string) error

------------------------------

# 任务描述
作为一个go语言专家，你需要写一个函数func (e *EvaluationController) Stop(),
# 功能描述
为此你需要仿照函数Delete()来写，其中你需要调用StopEvaluation函数
# 接口举例
func (e *EvaluationController) Delete() {
	reqParam := e.getRequestParam("delete")
	err := DeleteEvaluation(reqParam)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

func StopEvaluation(reqParam EvaluationRequestParam) error

------------------------------

# 任务描述
作为一个go语言专家，你需要写一个函数func (e *EvaluationController) Delete(),
# 功能描述
为此你需要仿照函数Stop()来写，其中你需要调用DeleteEvaluation函数
# 接口举例
func (e *EvaluationController) Stop() {
	reqParam := e.getRequestParam("stop")
	err := StopEvaluation(reqParam)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

func DeleteEvaluation(reqParam EvaluationRequestParam) error

------------------------------

# 任务描述
作为一个资深的go语言专家，写一个函数func getReqBodyToWsm() (map[string]interface{}, error),
# 功能描述
首先将Action: "stop",赋值到StopToWsm结构体内，再调用cwsmutils.Struct2Map函数，输出变量为res的map[string]interface{}

# 接口举例
type StopToWsm struct {
	Action string `json:"action"`
}

func Struct2Map(in interface{}) (map[string]interface{}, error) {
	m := make(map[string]interface{})

	j, err := json.Marshal(in)
	if err != nil {
		return m, err
	}

	err = json.Unmarshal(j, &m)
	if err != nil {
		return m, err
	}

	var data = make(map[string]interface{})
	t := reflect.TypeOf(in)
	v := reflect.ValueOf(in)

	for i := 0; i < t.NumField(); i++ {
		for key := range m {
			if strings.EqualFold(key, t.Field(i).Name) {
				data[key] = v.Field(i).Interface()
			}
		}
	}

	return data, nil
}

------------------------------

# 任务描述
作为一个资深的go语言专家，仿照getReqBodyToWsm函数的格式，写一个函数func func genReqBodyToWsm(keywords wsm.EvaluationWsmKeywords, task map[string]interface{}, action string) (map[string]interface{}, error) ,

# 功能描述
首先将Action:       action,
		EvaluatePlan: task["evaluatePlan"].(models.EvaluationPlanCols),,赋值到EvaluationToWsm结构体内，再调用cwsmutils.Struct2Map函数，输出变量为res的map[string]interface{}
# 接口举例
func getReqBodyToWsm() (map[string]interface{}, error) {
	reqbody := models.StopToWsm{
		Action: "stop",
	}
	res, err := cwsmutils.Struct2Map(reqbody)
	if err != nil {
		return nil, err
	}
	return res, nil
}

type EvaluationToWsm struct {
	Action       string             `json:"action"`
	EvaluatePlan EvaluationPlanCols `json:"evaluatePlan"`
}

func Struct2Map(in interface{}) (map[string]interface{}, error) {
	m := make(map[string]interface{})

	j, err := json.Marshal(in)
	if err != nil {
		return m, err
	}

	err = json.Unmarshal(j, &m)
	if err != nil {
		return m, err
	}

	var data = make(map[string]interface{})
	t := reflect.TypeOf(in)
	v := reflect.ValueOf(in)

	for i := 0; i < t.NumField(); i++ {
		for key := range m {
			if strings.EqualFold(key, t.Field(i).Name) {
				data[key] = v.Field(i).Interface()
			}
		}
	}

	return data, nil
}

------------------------------

# 任务描述
作为一个资深的go语言专家，你需要写一个函数func NewTimeUuid(prefix string) string，
# 功能描述
它用于生成一个基于时间戳的唯一ID。这个函数接收一个字符串参数prefix，然后获取当前的时间戳（精确到毫秒），将其转换为字符串形式，然后将这个字符串和输入的prefix拼接起来，形成一个新的唯一ID。
# 输出要求
你需要直接return prefix + string(strTimestamp)

------------------------------

# 任务描述
作为一个资深的go语言专家，你需要写一个函数func isPostRestfuleMethodSuc(err error, method string, body interface{}, restfulParams WsmRestfulParams) bool,
# 功能描述
你可以仿照isRestfuleMethodSuc函数，只需要将body map[string]interface{}改成body interface{}，cwsmutils.PrintJsonData(body)地方调用cwsmutils.PostPrintJsonData(body)即可

# 接口举例
func isRestfuleMethodSuc(err error, method string, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
	if err == nil {
		return true
	}

	logger.Errorf("restful method:%s, err: %v, restful params: %v", method, err.Error(), restfulParams)
	if body != nil {
		cwsmutils.PrintJsonData(body)
	}
	return false
}

------------------------------

# 任务描述
作为一个资深的go语言专家，你需要写一个函数func PostPrintJsonData(input interface{}),
# 功能描述
你可以仿照PrintJsonData函数，只需要将input map[string]interface{}改成input interface{}，即可
# 接口举例
func PrintJsonData(input map[string]interface{}) {
	output, _ := json.MarshalIndent(input, "", "  ")
	logger.Errorf(string(output))
}

------------------------------

# 任务描述
作为一个资深的go语言专家，你需要写一个函数func isPostResponseSuc(rspCode int, body interface{}, restfulParams WsmRestfulParams) bool,
# 功能描述
你可以仿照isResponseSuc函数，只需要将body map[string]interface{}改成body interface{}，cwsmutils.PrintJsonData(body)地方调用cwsmutils.PostPrintJsonData(body)即可
# 接口举例
func isResponseSuc(rspCode int, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
	if rspCode == http.StatusOK {
		return true
	}

	logger.Errorf("response code: %v, restful params: %v", rspCode, restfulParams)
	if body != nil {
		cwsmutils.PrintJsonData(body)
	}
	return false
}

------------------------------

# 任务描述
作为一个资深的go语言专家，你需要写一个函数func isRestfuleMethodSuc(err error, method string, body map[string]interface{}, restfulParams WsmRestfulParams) bool,
# 功能描述
你可以仿照isPostRestfuleMethodSuc函数，只需要将body interface{}改成body map[string]interface{}，cwsmutils.PostPrintJsonData(body)地方调用cwsmutils.PrintJsonData(body)即可
# 接口举例
func isPostRestfuleMethodSuc(err error, method string, body interface{}, restfulParams WsmRestfulParams) bool {
	if err == nil {
		return true
	}

	logger.Errorf("restful method:%s, err: %v, restful params: %v", method, err.Error(), restfulParams)
	if body != nil {
		cwsmutils.PostPrintJsonData(body)
	}
	return false
}

------------------------------

# 任务描述
作为一个资深的go语言专家，你需要写一个函数func isResponseSuc(rspCode int, body map[string]interface{}, restfulParams WsmRestfulParams) bool,
# 功能描述
你可以仿照isPostRestfuleMethodSuc函数，只需要将body interface{}改成body map[string]interface{}，cwsmutils.PostPrintJsonData(body)地方调用cwsmutils.PrintJsonData(body)即可
# 接口举例
func isPostResponseSuc(rspCode int, body interface{}, restfulParams WsmRestfulParams) bool {
	if rspCode == http.StatusOK {
		return true
	}

	logger.Errorf("response code: %v, restful params: %v", rspCode, restfulParams)
	if body != nil {
		cwsmutils.PostPrintJsonData(body)
	}
	return false
}

------------------------------

# 任务描述
作为一个资深的go语言专家，你需要写一个函数func PrintJsonData(input map[string]interface{}),
# 功能描述
你可以仿照PostPrintJsonData函数，只需要将input interface{}改成input map[string]interface{}，即可
# 接口举例
func PostPrintJsonData(input interface{}) {
	output, _ := json.MarshalIndent(input, "", "  ")
	logger.Errorf(string(output))
}