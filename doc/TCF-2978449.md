## 已有代码
func checkModelPara(modelParaInput string) error {
	var modelPara map[string]interface{}
	if err := json.Unmarshal([]byte(modelParaInput), &modelPara); err != nil {
		return err
	}
	for key := range modelPara {
		if key != "env" && key != "cmd" {
			return fmt.Errorf("key cannot be a value other than env or cmd")
		} else if key == "env" {
			if _, ok := modelPara["env"].(map[string]interface{}); !ok {
				return fmt.Errorf("the value of env not json")
			}
		} else if key == "cmd" {
			if _, ok := modelPara["cmd"].([]interface{}); !ok {
				return fmt.Errorf("the value of cmd not list")
			}
		}
	}
	return nil
}

# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、请生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明
5、使用fmt.Errorf代替Errors.New
6、ut需要包含每一个分支

---------------------------

## 已有代码
func genReporterReqBodyToWsm(reqParam EvaluationTaskRequestParam, evaluTask map[string]interface{}) (TrainReportReq, error) {
	var data map[string]interface{}
	err := json.Unmarshal([]byte(evaluTask["testContent"].(string)), &data)
	if err != nil {
		logger.Error("get testcontent from evaluationtask info failed")
		return TrainReportReq{}, err
	}
	modelName, ok := data["testCase"].(map[string]interface{})["modelName"].(string)
	if !ok {
		logger.Error("get modelName from testContent info failed")
		return TrainReportReq{}, fmt.Errorf("get modelName from testContent info failed")
	}
	reporterReqBody := TrainReportReq{
		Action: "queryreport",
		TrainReportReqInfo: []EvaluTaskInfo{
			{
				TestType:      evaluTask["testType"].(string),
				ModelName:     modelName,
				TasKId:        evaluTask["id"].(string),
				TaskBeginTime: evaluTask["startTime"].(string),
			},
		},
	}
	return reporterReqBody, nil
}

# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、请生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明
5、使用fmt.Errorf代替Errors.New
6、ut需要包含每一个分支和每一种错误

---------------

## 已有代码

func GetEvaluationTaskReporter(reqParam EvaluationTaskRequestParam) (interface{}, error) {
	evaluationTask, err := models.QueryResourceById(constant.TABLE_NAME_EvaluationTask, reqParam.EvaluationTaskId)
	if err != nil {
		logger.Error("get evaluation task %s failed", err.Error())
		return nil, err
	}
	reqBody, err := genReporterReqBodyToWsm(reqParam, evaluationTask)
	if err != nil {
		return nil, err
	}
	wsmKeywords := wsm.EvaluationWsmKeywords{
		ClusterId:   reqParam.ClusterId,
		ProjectId:   reqParam.ProjectId,
		ProjectName: evaluationTask["projectName"].(string),
		Name:        strings.ToLower(evaluationTask["name"].(string)),
		Id:          evaluationTask["planId"].(string),
	}

	taskReport, suc := wsm.ReportHandler(wsmKeywords, reqBody, "queryreport")
	if !suc {
		return nil, fmt.Errorf("get evalu task reporter failed")
	}
	return taskReport, nil
}

# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、请生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明
5、使用fmt.Errorf代替Errors.New
6、ut需要包含每一个分支和每一种错误
7、wsm.ReportHandler为
func ReportHandler(keyswords EvaluationWsmKeywords, reqBody interface{}, method string) (map[string]interface{}, bool)，需要通过Patcher.ApplyFunc进行打桩