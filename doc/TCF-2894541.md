## 已有代码
func extractEvaluationTaskInfo(data wsm.EvaluationInfo) (models.EvaluationTaskCols, error) {
	var taskCols models.EvaluationTaskCols
	if len(data.TestTasks) > 0 {
		taskCols = models.EvaluationTaskCols{
			Id:          data.TestTasks[0].Id,
			PlanId:      data.TestTasks[0].PlanId,
			Name:        data.TestTasks[0].Name,
			Operator:    data.TestTasks[0].Operator,
			StartTime:   data.TestTasks[0].StartTime,
			EndTime:     data.TestTasks[0].EndTime,
			TestContent: string(data.TestTasks[0].TestContent),
			Status:      data.TestTasks[0].Status,
			Result:      data.TestTasks[0].Result,
			ResultInfo:  data.TestTasks[0].ResultInfo,
		}
	} else {
		return taskCols, errors.New("no test tasks found")
	}
	return taskCols, nil
}
# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明

------------------------------

## 已有代码
func insertEvaluationTask(data *wsm.EvaluationInfo, evaluateId string) error {
	task, err := extractEvaluationTaskInfo(evaluateId, *data)
	if err != nil {
		return err
	}
	newTaskMap, err := cwsmutils.Struct2Map(*task)
	if err != nil {
		return err
	}
	if !models.InsertNewResource(constant.TABLE_NAME_EvaluationTask, newTaskMap) {
		return fmt.Errorf("insert new resource failed")
	}
	return nil
}
# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明

------------------------------

## 已有代码
func generatePlan(evaluateId string, reqBody ActiveEvaluationReq) (planTables []map[string]interface{}, err error) {
	evaluatePlan, err := models.QueryResourceById(constant.TABLE_NAME_EvaluationPlan, evaluateId)
	if err != nil {
		logger.Errorf("QueryResourceById failed: %v", err)
		return nil, err
	}
	newplan := models.EvaluationPlanCols{
		Id:            evaluateId,
		Name:          evaluatePlan["name"].(string),
		ProjectId:     evaluatePlan["projectId"].(string),
		ProjectName:   evaluatePlan["projectName"].(string),
		Creator:       evaluatePlan["creator"].(string),
		Operator:      reqBody.Operator,
		ClusterId:     evaluatePlan["clusterId"].(string),
		StartTime:     "",
		EndTime:       "",
		WorkspaceId:   evaluatePlan["workspaceId"].(string),
		WorkspaceName: evaluatePlan["workspaceName"].(string),
		TestType:      evaluatePlan["testType"].(string),
		TestScene:     evaluatePlan["testScene"].(string),
		TestContent:   evaluatePlan["testContent"].(string),
		Result:        "",
		Status:        "created",
	}
	plan := models.EvaluationPlan{
		Action:       "activate",
		Name:         evaluatePlan["name"].(string),
		ProjectId:    evaluatePlan["projectId"].(string),
		EvaluatePlan: newplan,
	}
	planMap, err := cwsmutils.Struct2Map(plan)
	if err != nil {
		return nil, err
	}
	newplanMap, err := cwsmutils.Struct2Map(newplan)
	if err != nil {
		return nil, err
	}
	if !models.UpdateResourceById(constant.TABLE_NAME_EvaluationPlan, evaluateId, newplanMap) {
		logger.Errorf("Insert plan resource failed: %v", err)
		return nil, err
	}
	planTables = append(planTables, planMap)
	return planTables, nil
}
# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明