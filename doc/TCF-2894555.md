## 已有代码
func extractEvaluationTaskInfo(evaluateId string, data wsm.EvaluationInfo) (*models.EvaluationTaskCols, error) {
	var taskCols models.EvaluationTaskCols
	evaluatePlan, err := models.QueryResourceById(constant.TABLE_NAME_EvaluationPlan, evaluateId)
	if err != nil {
		return nil, err
	}
	if len(data.TestTasks) > 0 {
		taskCols = models.EvaluationTaskCols{
			Id:          data.TestTasks[0].Id,
			PlanId:      data.TestTasks[0].PlanId,
			Name:        data.TestTasks[0].Name,
			ProjectId:   evaluatePlan["projectId"].(string),
			ProjectName: evaluatePlan["projectName"].(string),
			Operator:    data.TestTasks[0].Operator,
			StartTime:   data.TestTasks[0].StartTime,
			EndTime:     data.TestTasks[0].EndTime,
			WorkspaceId: evaluatePlan["workspaceId"].(string),
			TestType:    evaluatePlan["testType"].(string),
			TestScene:   evaluatePlan["testScene"].(string),
			TestContent: string(data.TestTasks[0].TestContent),
			Status:      data.TestTasks[0].Status,
			Result:      data.TestTasks[0].Result,
			ResultInfo:  data.TestTasks[0].ResultInfo,
			Performance: "",
		}
	} else {
		return &taskCols, errors.New("no test tasks found")
	}
	return &taskCols, nil
}
# 任务
我是一名go开发人员，现在有一段代码，代码内容如上所示，现在请你帮我生成单元测试代码，要求如下：
1、使用gotests框架生成一份完整的单元测试，import ("github.com/agiledragon/gomonkey" . "github.com/onsi/ginkgo" . "github.com/onsi/gomega")
2、考虑正常、异常、边界等场景
3、生成没有编译错误的go代码
4、请以字符串形式直接输出单元测试代码，不需要添加任何描述说明