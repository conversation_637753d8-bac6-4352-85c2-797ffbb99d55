package constant

const HTTPPrefix = "http://"
const HTTPSPrefix = "https://"
const CwsmPrefix = "/api/v1.0/cwsm"
const WsmName = "wsm"

const MicroServiceName = "cwsm"
const MicroServiceNameCwsm = "cwsm-cwsm"
const CludFuzeName = "cloudfuze"
const PvrmName = "pvrm"
const FaultManagementName = "fault-management"
const ConfigcenterName = "configcenter-configserver"
const DirectorInnerIp = "gateway_host"
const DirectorInnerPort = "8241"
const DirectorCa = "/usr/share/ssl/director-ca/"
const DirectorCrt = "/usr/share/ssl/director-south-server/"
const TimeFormat = "2006/1/2 15:04:05"

const CloudfuzeInnerCloudenvs = "/api/v1.0/cloudfuze/inner/cloudenvs/"
const CloudfuzeCloudenvs = "/api/v1.0/cloudfuze/cloudenvs/"

const PvrmContainerClusters = "/api/v1.0/pvrm/containerclusters"
const PvrmContainerClouds = "/api/v1.0/pvrm/containerclouds"
const PvrmVcjobs = "/api/v1.0/pvrm/vcjobs"

const FaultManagementFault = "/api/v1.0/fault-management/fault"

const ConfigcenterConfigserver = "/api/configcenter-configserver/v1/getProperty"

const DirectorMsbIp = "OPENPALETTE_MSB_IP"
const DirectorMsbPort = "10081"

const (
	KafkaTopicDcChange = "DataCenterChange"
	KafkaLocalHostIP   = "127.0.0.1"
	KafkaLocalHostPort = "9092"

	KafkaClientID              = "cwsm"
	ConfigCenterRefresh        = "configCenterRefresh"
	ConfigCenterResourceChange = "resource.change"
)

const (
	VcjobFaultanalaSwitchConfig = "vcjob-faultanalaswitch-config"
	VcjobRetentionConfig        = "vcjob-retention-config"

	ResourceFaultanalaSwitch = "resource-faultanala-switch"
	ResourceRetentionSwitch  = "resource-retention-switch"
	ResourceRetentionDay     = "resource-retention-day"
	ResourceRetentionNumber  = "resource-retention-number"
)

const (
	MemuFilePath = "conf/Menu.json"
)

const (
	TABLE_NAME_VCJOBFAULTHISTORY                        = "VcjobFaultHistory"
	TABLE_NAME_FAULTINFO                                = "FaultInfo"
	TABLE_NAME_InspectionPlanTable                      = "InspectionPlanTable"
	TABLE_NAME_InspectionResultTable                    = "InspectionResultTable"
)

const (
	KeepAliveInterval = 30
)
