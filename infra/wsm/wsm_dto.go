package wsm

import (
	"encoding/json"

	"zte.com.cn/cms/crmX/commontools-base/restful"
)

type WsStatus struct {
	Id     string `json:"id"`
	Name   string `json:"name"`
	Status string `json:"Status"`
}

type WsStatusList struct {
	Num       int         `json:"num,omitempty"`
	Workspace []*WsStatus `json:"workspace,omitempty"`
}

type TaskStatus struct {
	Id         string `json:"id"`
	PlanId     string `json:"planId"`
	Name       string `json:"name"`
	Status     string `json:"status"`
	Gpus       string `json:"gpus"`
	GpuUuid    string `json:"gpuUuid"`
	Err        string `json:"err"`
	PlanStatus string `json:"planStatus"`
	Result     string `json:"result"`
	EndTime    string `json:"endTime"`
}

type WsmKeywords struct {
	ClusterId   string `json:"clusterId"`
	ProjectId   string `json:"projectId"`
	ProjectName string `json:"projectName"`
	Id          string `json:"id"`
}

type WsmCreateWorkspaceReq struct {
	ID              string                 `json:"id"`
	Name            string                 `json:"name"`
	ProjectId       string                 `json:"projectId"`
	ProjectName     string                 `json:"projectName"`
	Description     string                 `json:"description"`
	Environment     map[string]interface{} `json:"environment"`
	ComputeResource map[string]interface{} `json:"computeResource"`
	DataResource    map[string]interface{} `json:"dataResource"`
	Metadata        string                 `json:"metadata,omitempty"`
	Creator         string                 `json:"creator"`
	CreateTime      string                 `json:"createTime"`
	Status          string                 `json:"status"`
}

type WsmRestfulParams struct {
	Header map[string]string `json:"header"`
	URL    string            `json:"url"`
	SSL    *restful.SSLAuth  `json:"ssl"`
}

type WsmKeepaliveReq struct {
	ProjectName string `json:"projectName"`
	WsmKeepaliveRsp
}

type WsmKeepaliveRsp struct {
	Num int      `json:"num"`
	Ids []string `json:"ids"`
}

type EvaluationWsmKeywords struct {
	ClusterId   string `json:"clusterId"`
	ProjectId   string `json:"projectId"`
	ProjectName string `json:"projectName"`
	Name        string `json:"name"`
	Id          string `json:"id"`
}

type InspectiontaskWsmKeywords struct {
	ClusterId string `json:"clusterId"`
}

type ScclinspectionWsmKeywords struct {
	ClusterId string `json:"clusterId"`
}

type ModelinspectionWsmKeywords struct {
	ClusterId string `json:"clusterId"`
	TaskId    string `json:"taskId"`
}
type RdmaWsmKeywords struct {
	ClusterId string `json:"clusterId"`
}

type VcjobEventsWsmKeywords struct {
	ClusterId   string `json:"clusterId"`
	ProjectName string `json:"projectname"`
	Name        string `json:"name"`
}

type EvaluationValueErr struct {
	Err string `json:"err"`
}

type EvaluationValue struct {
	EvaluationInfo []EvaluationTaskInfo `json:"evaluationInfo"`
}

type HealthWsmKeywords struct {
	ClusterId string `json:"clusterId"`
}

type EvaluationTaskInfo struct {
	Id        string           `json:"id"`
	Name      string           `json:"name"`
	ProjectId string           `json:"projectId"`
	Status    string           `json:"status"`
	TaskIds   []string         `json:"taskIds"`
	Err       string           `json:"err"`
	TestTasks []EvaluationTask `json:"testTasks"`
}

type EvaluationTask struct {
	Id          string          `json:"id"`
	PlanId      string          `json:"planId"`
	Name        string          `json:"name"`
	Operator    string          `json:"operator"`
	StartTime   string          `json:"startTime"`
	EndTime     string          `json:"endTime"`
	TestContent json.RawMessage `json:"testContent"`
	Gpus        []Gpu           `json:"gpus"`
	Status      string          `json:"status"`
	Count       string          `json:"count"`
	Result      string          `json:"result"`
	ResultInfo  string          `json:"resultInfo"`
}

type Gpu struct {
	Id   string `json:"id"`
	Name string `json:"name"`
	Type string `json:"type"`
}

type EvaluationAllInfo struct {
	Id          string                   `json:"id"`
	Name        string                   `json:"name"`
	ProjectId   string                   `json:"projectId"`
	ProjectName string                   `json:"projectName"`
	Creator     string                   `json:"creator"`
	Operator    string                   `json:"operator"`
	StartTime   string                   `json:"startTime"`
	EndTime     string                   `json:"endTime"`
	WorkspaceId string                   `json:"workspaceId"`
	TestType    string                   `json:"testType"`
	TestScene   string                   `json:"testScene"`
	TestContent string                   `json:"testContent"`
	Nodes       string                   `json:"nodes"`
	Gpus        string                   `json:"gpus"`
	Status      string                   `json:"status"`
	Result      string                   `json:"result"`
	TestTasks   []map[string]interface{} `json:"testTasks"`
}

type PlatperfanceReport struct {
	PlatPerformanceReqInfo []PlatPerformanceReqInfo `json:"platPerformanceReqInfo"`
}

type PlatPerformanceReqInfo struct {
	Id         string       `json:"id"`
	TaskId     string       `json:"taskId"`
	PlanId     string       `json:"planId"`
	ModelName  string       `json:"modelName"`
	TestType   string       `json:"testType"`
	GpuAbility []GpuAbility `json:"gpuAbility"`
	JobAbility string       `json:"jobAbility"`
}

type GpuAbility struct {
	GpuUuid         string          `json:"gpuUuid"`
	ComputeAbility  json.RawMessage `json:"computeAbility"`
	StorageAbility  json.RawMessage `json:"storageAbility"`
	NetAbility      json.RawMessage `json:"netAbility"`
	HealthyAbility  json.RawMessage `json:"healthyAbility"`
	EnergyAbility   json.RawMessage `json:"energyAbility"`
	ResourceAbility json.RawMessage `json:"resourceAbility"`
	TranAbility     json.RawMessage `json:"tranAbility"`
}
