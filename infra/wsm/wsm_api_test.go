package wsm

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"testing"

	"cwsm/infra/authorization"

	"zte.com.cn/cms/crmX/commontools-base/restful"

	// "github.com/agiledragon/gomonkey"
	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	"github.com/onsi/gomega"
	. "github.com/onsi/gomega"
	"github.com/smartystreets/goconvey/convey"
)

/* Started by AICoder, pid:1f698sb5ab27e4814b7e0ae75108681b71d4ecca */
var _ = Describe("ModelinspectionHandler", func() {
	var (
		patcher  *gomonkey.Patches
		keywords ModelinspectionWsmKeywords
		reqBody  []byte
	)

	BeforeEach(func() {
		keywords = ModelinspectionWsmKeywords{
			ClusterId: "cluster123",
			TaskId:    "task456",
		}
		reqBody = []byte(`{"key":"value"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When buildModelinspectionRestfulReq fails", func() {
		It("should return false", func() {
			patcher.ApplyFunc(buildModelinspectionRestfulReq, func(keyswords ModelinspectionWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{}, false
			})

			result, msg, success := ModelinspectionHandler(keywords, reqBody, "get")

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is getmodelinspectionconfigs", func() {
		It("should call GetModelinspection and return its result", func() {
			mockResult := map[string]interface{}{"data": "mockData"}
			patcher.ApplyFunc(buildModelinspectionRestfulReq, func(keyswords ModelinspectionWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(GetModelinspection, func(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
				return mockResult, "", true
			})

			result, msg, success := ModelinspectionHandler(keywords, reqBody, "getmodelinspectionconfigs")

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(Equal(mockResult))
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is postmodelinspectionconfigs", func() {
		It("should call PostModelinspection and return its result", func() {
			mockResult := map[string]interface{}{"data": "mockData"}
			patcher.ApplyFunc(buildModelinspectionRestfulReq, func(keyswords ModelinspectionWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(PostModelinspection, func(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
				return mockResult, "", true
			})

			result, msg, success := ModelinspectionHandler(keywords, reqBody, "postmodelinspectionconfigs")

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(Equal(mockResult))
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is stop", func() {
		It("should call DeleteModelinspection and return its result", func() {
			patcher.ApplyFunc(buildModelinspectionRestfulReq, func(keyswords ModelinspectionWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(DeleteModelinspection, func(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, msg, success := ModelinspectionHandler(keywords, reqBody, "stop")

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is delete", func() {
		It("should call DeleteModelinspection and return its result", func() {
			patcher.ApplyFunc(buildModelinspectionRestfulReq, func(keyswords ModelinspectionWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(DeleteModelinspection, func(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, msg, success := ModelinspectionHandler(keywords, reqBody, "delete")

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is unsupported", func() {
		It("should return false", func() {
			result, msg, success := ModelinspectionHandler(keywords, reqBody, "unsupported")

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:1f698sb5ab27e4814b7e0ae75108681b71d4ecca */

var _ = Describe("GetModelinspection", func() {
	var (
		patcher *gomonkey.Patches
		params  WsmRestfulParams
	)

	BeforeEach(func() {
		params = WsmRestfulParams{
			URL:    "http://test.com",
			Header: map[string]string{},
			SSL:    &restful.SSLAuth{},
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetFromWsmToGetStatus returns success", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{"test": "data"}`), nil, http.StatusOK, nil
			})
		})

		It("should return success", func() {
			result, msg, success := GetModelinspection(params)
			Expect(success).To(BeTrue())
			Expect(result).NotTo(BeNil())
			Expect(msg).To(BeEmpty())
		})
	})

	Context("When GetFromWsmToGetStatus returns error with non-200/400 status", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusNotFound, fmt.Errorf("not found")
			})
			patcher.ApplyFunc(GetCodeStatusValues, func(code int) (string, error) {
				return "test error message", nil
			})
		})

		It("should return error with status message", func() {
			result, msg, success := GetModelinspection(params)
			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal("test error message"))
		})
	})
})

var _ = Describe("PostModelinspection", func() {
	var (
		patcher *gomonkey.Patches
		params  WsmRestfulParams
		reqBody []byte
	)

	BeforeEach(func() {
		params = WsmRestfulParams{
			URL:    "http://test.com",
			Header: map[string]string{},
			SSL:    &restful.SSLAuth{},
		}
		reqBody = []byte(`{"test": "data"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When restful.PostMethod returns success", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(restful.PostMethod, func(urlpath string, reqHeaders map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{"test": "data"}`), nil, http.StatusOK, nil
			})
		})

		It("should return success", func() {
			result, msg, success := PostModelinspection(params, reqBody)
			Expect(success).To(BeTrue())
			Expect(result).NotTo(BeNil())
			Expect(msg).To(BeEmpty())
		})
	})

	Context("When restful.PostMethod returns error with non-200/400 status", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(restful.PostMethod, func(urlpath string, reqHeaders map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusUnauthorized, fmt.Errorf("unauthorized")
			})
			patcher.ApplyFunc(GetCodeStatusValues, func(code int) (string, error) {
				return "test error message", nil
			})
		})

		It("should return error with status message", func() {
			result, msg, success := PostModelinspection(params, reqBody)
			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal("test error message"))
		})
	})
})

var _ = Describe("getModelinspectionUrl", func() {
	var (
		keywords  ModelinspectionWsmKeywords
		urlPrefix string
	)

	BeforeEach(func() {
		keywords = ModelinspectionWsmKeywords{
			ClusterId: "cluster123",
			TaskId:    "task456",
		}
		urlPrefix = "http://test.com"
	})

	Context("When method is get", func() {
		It("should return correct URL", func() {
			url := getModelinspectionUrl("get", keywords, urlPrefix)
			Expect(url).To(Equal("http://test.com/modelinspection"))
		})
	})

	Context("When method is getcclinspectionconfig", func() {
		It("should return correct URL", func() {
			url := getModelinspectionUrl("getcclinspectionconfig", keywords, urlPrefix)
			Expect(url).To(Equal("http://test.com/cclinspectionconfig"))
		})
	})

	Context("When method is post", func() {
		It("should return correct URL", func() {
			url := getModelinspectionUrl("post", keywords, urlPrefix)
			Expect(url).To(Equal("http://test.com/modelinspection"))
		})
	})

	Context("When method is postcclinspectionconfig", func() {
		It("should return correct URL", func() {
			url := getModelinspectionUrl("postcclinspectionconfig", keywords, urlPrefix)
			Expect(url).To(Equal("http://test.com/cclinspectionconfig"))
		})
	})

	Context("When method is stop", func() {
		It("should return correct URL with task ID", func() {
			url := getModelinspectionUrl("stop", keywords, urlPrefix)
			Expect(url).To(Equal("http://test.com/modelinspection/stop/task456"))
		})
	})

	Context("When method is delete", func() {
		It("should return correct URL with task ID", func() {
			url := getModelinspectionUrl("delete", keywords, urlPrefix)
			Expect(url).To(Equal("http://test.com/modelinspection/delete/task456"))
		})
	})

	Context("When method is invalid", func() {
		It("should return empty string", func() {
			url := getModelinspectionUrl("invalid", keywords, urlPrefix)
			Expect(url).To(BeEmpty())
		})
	})
})

/* Started by AICoder, pid:326fbkce6dz998214f7c0a0f60414468341775fa */
var _ = Describe("getModelinspectionUrl", func() {
	var (
		urlPrefix string
		keywords  ModelinspectionWsmKeywords
	)

	BeforeEach(func() {
		urlPrefix = "http://example.com/api"
		keywords = ModelinspectionWsmKeywords{
			ClusterId: "cluster123",
			TaskId:    "task456",
		}
	})

	Context("When method is post or get", func() {
		It("should return the correct modelinspection URL", func() {
			gomega.Expect(getModelinspectionUrl("post", keywords, urlPrefix)).To(Equal(fmt.Sprintf("%s/modelinspection", urlPrefix)))
			gomega.Expect(getModelinspectionUrl("get", keywords, urlPrefix)).To(Equal(fmt.Sprintf("%s/modelinspection", urlPrefix)))
		})
	})

	Context("When method is postmodelinspectionconfigs or getmodelinspectionconfigs", func() {
		It("should return the correct modelinspectionconfig URL", func() {
			gomega.Expect(getModelinspectionUrl("postmodelinspectionconfigs", keywords, urlPrefix)).To(Equal(fmt.Sprintf("%s/modelinspectionconfig", urlPrefix)))
			gomega.Expect(getModelinspectionUrl("getmodelinspectionconfigs", keywords, urlPrefix)).To(Equal(fmt.Sprintf("%s/modelinspectionconfig", urlPrefix)))
		})
	})

	Context("When method is postclrdmainspectionconfigs or getclrdmainspectionconfigs", func() {
		It("should return the correct clrdmainspectionconfig URL", func() {
			gomega.Expect(getModelinspectionUrl("postclrdmainspectionconfigs", keywords, urlPrefix)).To(Equal(fmt.Sprintf("%s/clrdmainspectionconfig", urlPrefix)))
			gomega.Expect(getModelinspectionUrl("getclrdmainspectionconfigs", keywords, urlPrefix)).To(Equal(fmt.Sprintf("%s/clrdmainspectionconfig", urlPrefix)))
		})
	})

	Context("When method is postnamespace or getnamespace", func() {
		It("should return the correct namespace URL", func() {
			gomega.Expect(getModelinspectionUrl("postnamespace", keywords, urlPrefix)).To(Equal(fmt.Sprintf("%s/namespace", urlPrefix)))
			gomega.Expect(getModelinspectionUrl("getnamespace", keywords, urlPrefix)).To(Equal(fmt.Sprintf("%s/namespace", urlPrefix)))
		})
	})

	Context("When method is getdatasets", func() {
		It("should return the correct datasets URL", func() {
			gomega.Expect(getModelinspectionUrl("getdatasets", keywords, urlPrefix)).To(Equal(fmt.Sprintf("%s/datasets", urlPrefix)))
		})
	})

	Context("When method is stop", func() {
		It("should return the correct stop URL with taskId", func() {
			gomega.Expect(getModelinspectionUrl("stop", keywords, urlPrefix)).To(Equal(fmt.Sprintf("%s/modelinspection/stop/%s", urlPrefix, keywords.TaskId)))
		})
	})

	Context("When method is delete", func() {
		It("should return the correct delete URL with taskId", func() {
			gomega.Expect(getModelinspectionUrl("delete", keywords, urlPrefix)).To(Equal(fmt.Sprintf("%s/modelinspection/delete/%s", urlPrefix, keywords.TaskId)))
		})
	})

	Context("When method is unsupported", func() {
		It("should return an empty string and log an error", func() {
			url := getModelinspectionUrl("unsupported", keywords, urlPrefix)
			gomega.Expect(url).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:326fbkce6dz998214f7c0a0f60414468341775fa */

var _ = Describe("TestGetOpenpaletteByCloudUuid", func() {
	var (
		clusterId string
		patcher   *gomonkey.Patches
	)

	BeforeEach(func() {
		clusterId = "test-cluster-id"
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetClusterUuidFromPvrm returns error", func() {
		It("It should return err", func() {
			_, err := GetOpenpaletteByCloudUuid(clusterId)
			Expect(err).NotTo(BeNil())
		})
	})

	Context("When GetOpenpalette returns error", func() {
		It("It should return err", func() {
			patcher.ApplyFunc(authorization.GetClusterUuidFromPvrm, func(string) (string, error) {
				return "123", nil
			})
			_, err := GetOpenpaletteByCloudUuid(clusterId)
			Expect(err).NotTo(BeNil())
		})
	})
})

/* Started by AICoder, pid:l79f5hfae85c784146a809c150c95d901ac7bcc9 */
var _ = Describe("ScclinspectionHandler", func() {
	var (
		patcher  *gomonkey.Patches
		keywords ScclinspectionWsmKeywords
		reqBody  []byte
	)

	BeforeEach(func() {
		keywords = ScclinspectionWsmKeywords{
			ClusterId: "cluster123",
		}
		reqBody = []byte(`{"key":"value"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When buildScclinspectionRestfulReq fails", func() {
		It("should return false", func() {
			patcher.ApplyFunc(buildScclinspectionRestfulReq, func(keyswords ScclinspectionWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{}, false
			})

			result, msg, success := ScclinspectionHandler(keywords, reqBody, "get")

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is get", func() {
		It("should call GetScclinspection and return its result", func() {
			mockResult := map[string]interface{}{"data": "mockData"}
			patcher.ApplyFunc(buildScclinspectionRestfulReq, func(keyswords ScclinspectionWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(GetScclinspection, func(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
				return mockResult, "", true
			})

			result, msg, success := ScclinspectionHandler(keywords, reqBody, "get")

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(Equal(mockResult))
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is post", func() {
		It("should call PostScclinspection and return its result", func() {
			mockResult := map[string]interface{}{"status": "posted"}
			patcher.ApplyFunc(buildScclinspectionRestfulReq, func(keyswords ScclinspectionWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(PostScclinspection, func(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
				return mockResult, "", true
			})

			result, msg, success := ScclinspectionHandler(keywords, reqBody, "post")

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(Equal(mockResult))
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is unsupported", func() {
		It("should return false", func() {
			result, msg, success := ScclinspectionHandler(keywords, reqBody, "unsupported")

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:l79f5hfae85c784146a809c150c95d901ac7bcc9 */

/* Started by AICoder, pid:5f3e22d9c2pa472144a1087021aeb70bd1609e24 */
var _ = Describe("TestPostScclinspection", func() {
	var (
		restfulParams WsmRestfulParams
		reqBody       []byte
		patcher       *gomonkey.Patches
	)

	BeforeEach(func() {
		sslAuth := &restful.SSLAuth{
			Method:      "TLS",
			SSLProtocol: "TLSv1.2",
			RootCert:    "/path/to/rootCert",
			ClientCert:  "/path/to/clientCert",
			ClientKey:   "/path/to/clientKey",
		}
		restfulParams = WsmRestfulParams{
			Header: map[string]string{"Content-Type": "application/json"},
			URL:    "http://mock.url",
			SSL:    sslAuth,
		}
		reqBody = []byte(`{"key": "value"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When restful.PostMethod returns an error", func() {
		It("should return the error message", func() {
			mockError := errors.New("post failed")
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, ssl *restful.SSLAuth, opts ...restful.Option) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, mockError
			})

			result, msg, success := PostScclinspection(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal("post failed"))
		})

		It("should return the error message from JSON unmarshalling", func() {
			mockError := `{"err": "invalid request"}`
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, ssl *restful.SSLAuth, opts ...restful.Option) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(mockError)
			})

			result, msg, success := PostScclinspection(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal("invalid request"))
		})
	})

	Context("When restful.PostMethod returns a non-successful response code", func() {
		It("should return false", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, ssl *restful.SSLAuth, opts ...restful.Option) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 400, nil // Simulate a bad request response
			})

			result, msg, success := PostScclinspection(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When restful.PostMethod returns a successful response", func() {
		It("should return the parsed data", func() {
			mockResponse := []byte(`{"status": "success", "data": {"key": "value"}}`)
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, ssl *restful.SSLAuth, opts ...restful.Option) ([]byte, map[string][]string, int, error) {
				return mockResponse, nil, 200, nil // Simulate a successful response
			})

			result, msg, success := PostScclinspection(restfulParams, reqBody)

			Expect(success).To(BeTrue())
			Expect(result).To(Equal(map[string]interface{}{"status": "success", "data": map[string]interface{}{"key": "value"}}))
			Expect(msg).To(Equal(""))
		})
	})

	Context("When JSON unmarshalling fails", func() {
		It("should return false", func() {
			mockResponse := []byte(`{invalid json}`)
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, ssl *restful.SSLAuth, opts ...restful.Option) ([]byte, map[string][]string, int, error) {
				return mockResponse, nil, 200, nil // Simulate a successful response with invalid JSON
			})

			result, msg, success := PostScclinspection(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:5f3e22d9c2pa472144a1087021aeb70bd1609e24 */

/* Started by AICoder, pid:7b65bjcdc034ca914cb809bc21c5a6161f93857c */
var _ = Describe("TestStopScclinspection", func() {
	var (
		restfulParams WsmRestfulParams
		reqBody       []byte
		patcher       *gomonkey.Patches
	)

	BeforeEach(func() {
		sslAuth := &restful.SSLAuth{
			Method:      "TLS",
			SSLProtocol: "TLSv1.2",
			RootCert:    "/path/to/rootCert",
			ClientCert:  "/path/to/clientCert",
			ClientKey:   "/path/to/clientKey",
		}
		restfulParams = WsmRestfulParams{
			Header: map[string]string{"Content-Type": "application/json"},
			URL:    "http://mock.url",
			SSL:    sslAuth,
		}
		reqBody = []byte(`{"key": "value"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When restful.PostMethod returns an error", func() {
		It("should return the error message", func() {
			mockError := errors.New("post failed")
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, ssl *restful.SSLAuth, opts ...restful.Option) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, mockError
			})

			result, msg, success := StopScclinspection(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal("post failed"))
		})

		It("should return the error message from JSON unmarshalling", func() {
			mockError := `{"err": "invalid request"}`
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, ssl *restful.SSLAuth, opts ...restful.Option) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(mockError)
			})

			result, msg, success := StopScclinspection(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal("invalid request"))
		})
	})

	Context("When isRestfuleMethodSuc returns false", func() {
		It("should return false", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, ssl *restful.SSLAuth, opts ...restful.Option) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil // Simulate a successful response
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return false // Simulate failure
			})

			result, msg, success := StopScclinspection(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When isResponseSuc returns false", func() {
		It("should return false", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, ssl *restful.SSLAuth, opts ...restful.Option) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil // Simulate a successful response
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return true // Simulate success
			})
			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, params WsmRestfulParams) bool {
				return false // Simulate failure
			})

			result, msg, success := StopScclinspection(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When everything is successful", func() {
		It("should return true", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, ssl *restful.SSLAuth, opts ...restful.Option) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil // Simulate a successful response
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return true // Simulate success
			})
			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, params WsmRestfulParams) bool {
				return true // Simulate success
			})

			result, msg, success := StopScclinspection(restfulParams, reqBody)

			Expect(success).To(BeTrue())
			Expect(result).To(BeNil()) // Expecting no result for stop
			Expect(msg).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:7b65bjcdc034ca914cb809bc21c5a6161f93857c */

/* Started by AICoder, pid:s341ace68c2584c14cf108b6d1a0131c0a83fce8 */
var _ = Describe("InspectHandler", func() {
	var (
		patcher   *gomonkey.Patches
		clusterId string
		reqBody   interface{}
		id        string
	)

	BeforeEach(func() {
		clusterId = "cluster123"
		id = "inspect456"
		reqBody = map[string]interface{}{"key": "value"}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When buildInspectRestfulReq fails", func() {
		It("should return false", func() {
			patcher.ApplyFunc(buildInspectRestfulReq, func(clusterId string, method string, id string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{}, false
			})

			result, msg, success := InspectHandler(clusterId, reqBody, "get", id)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is post", func() {
		It("should call PostInspect and return its result", func() {
			mockResult := map[string]interface{}{"data": "mockData"}
			patcher.ApplyFunc(buildInspectRestfulReq, func(clusterId string, method string, id string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(PostInspect, func(restfulParams WsmRestfulParams, reqBody interface{}) (map[string]interface{}, string, bool) {
				return mockResult, "", true
			})

			result, msg, success := InspectHandler(clusterId, reqBody, "post", id)

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(Equal(mockResult))
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is stop", func() {
		It("should call StopInspect and return its result", func() {
			patcher.ApplyFunc(buildInspectRestfulReq, func(clusterId string, method string, id string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(StopInspect, func(restfulParams WsmRestfulParams, reqBody interface{}) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, msg, success := InspectHandler(clusterId, reqBody, "stop", id)

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is get", func() {
		It("should call GetInspect and return its result", func() {
			mockResult := map[string]interface{}{"data": "mockData"}
			patcher.ApplyFunc(buildInspectRestfulReq, func(clusterId string, method string, id string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(GetInspect, func(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
				return mockResult, "", true
			})

			result, msg, success := InspectHandler(clusterId, reqBody, "get", id)

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(Equal(mockResult))
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is delete", func() {
		It("should call DeleteInspect and return its result", func() {
			patcher.ApplyFunc(buildInspectRestfulReq, func(clusterId string, method string, id string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(DeleteInspect, func(restfulParams WsmRestfulParams, reqBody interface{}) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, msg, success := InspectHandler(clusterId, reqBody, "delete", id)

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is deleteInspect", func() {
		It("should call DeleteInspect and return its result", func() {
			patcher.ApplyFunc(buildInspectRestfulReq, func(clusterId string, method string, id string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(DeleteInspect, func(restfulParams WsmRestfulParams, reqBody interface{}) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, msg, success := InspectHandler(clusterId, reqBody, "deleteInspect", id)

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is unsupported", func() {
		It("should return false", func() {
			result, msg, success := InspectHandler(clusterId, reqBody, "unsupported", id)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:s341ace68c2584c14cf108b6d1a0131c0a83fce8 */

/* Started by AICoder, pid:jd2bfrbd96c596c14d8209c10059584fe1423587 */
var _ = Describe("getInspectUrl", func() {
	var (
		urlPrefix string
		id        string
	)

	BeforeEach(func() {
		urlPrefix = "http://example.com/api"
		id = "inspect123"
	})

	Context("When method is post", func() {
		It("should return the correct start inspectionplan URL", func() {
			gomega.Expect(getInspectUrl("post", urlPrefix, id)).To(Equal(fmt.Sprintf("%s/inspectionplan/start", urlPrefix)))
		})
	})

	Context("When method is stop", func() {
		It("should return the correct stop URL with id", func() {
			gomega.Expect(getInspectUrl("stop", urlPrefix, id)).To(Equal(fmt.Sprintf("%s/inspectionplan/stop/%s", urlPrefix, id)))
		})
	})

	Context("When method is get", func() {
		It("should return the correct query URL with id", func() {
			gomega.Expect(getInspectUrl("get", urlPrefix, id)).To(Equal(fmt.Sprintf("%s/inspectionplan/query/%s", urlPrefix, id)))
		})
	})

	Context("When method is delete", func() {
		It("should return the correct delete URL with id", func() {
			gomega.Expect(getInspectUrl("delete", urlPrefix, id)).To(Equal(fmt.Sprintf("%s/inspectionplan/delete", urlPrefix)))
		})
	})

	Context("When method is deleteInspect", func() {
		It("should return the correct delete result URL", func() {
			gomega.Expect(getInspectUrl("deleteInspect", urlPrefix, id)).To(Equal(fmt.Sprintf("%s/inspectionplan/delete/result", urlPrefix)))
		})
	})

	Context("When method is unsupported", func() {
		It("should return an empty string and log an error", func() {
			url := getInspectUrl("unsupported", urlPrefix, id)
			gomega.Expect(url).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:jd2bfrbd96c596c14d8209c10059584fe1423587 */

/* Started by AICoder, pid:fad4cd0babmdc58141f608f3408b219293d632a9 */
var _ = Describe("buildInspectRestfulReq", func() {
	var (
		patcher   *gomonkey.Patches
		clusterId string
		method    string
		id        string
	)

	BeforeEach(func() {
		clusterId = "cluster123"
		method = "get"
		id = "inspect456"
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetOpenpalette returns an error", func() {
		It("should return empty WsmRestfulParams and false", func() {
			patcher.ApplyFunc(GetOpenpalette, func(clusterId string) (authorization.OpenpaletteInfo, error) {
				return authorization.OpenpaletteInfo{}, fmt.Errorf("error getting open palette")
			})

			result, success := buildInspectRestfulReq(clusterId, method, id)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(Equal(WsmRestfulParams{}))
		})
	})

	Context("When GetOpenpalette returns valid info with token", func() {
		It("should return correct WsmRestfulParams", func() {
			mockSSL := &restful.SSLAuth{
				Method:      "TLS",
				SSLProtocol: "TLSv1.2",
				RootCert:    "/path/to/rootCert",
				ClientCert:  "/path/to/clientCert",
				ClientKey:   "/path/to/clientKey",
			}

			mockInfo := authorization.OpenpaletteInfo{
				Token:  "mockToken",
				MsbURL: "http://mock.url",
				SSL:    mockSSL,
			}
			patcher.ApplyFunc(GetOpenpalette, func(clusterId string) (authorization.OpenpaletteInfo, error) {
				return mockInfo, nil
			})

			patcher.ApplyFunc(getInspectUrl, func(method, urlPrefix string, id string) string {
				return fmt.Sprintf("%s/inspectionplan/query/%s", urlPrefix, id)
			})

			result, success := buildInspectRestfulReq(clusterId, method, id)

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result.URL).To(Equal("http://mock.url/opapi/wsm/v1/apts/inspectionplan/query/inspect456"))
			gomega.Expect(result.Header["X-Auth-Token"]).To(Equal("mockToken"))
			gomega.Expect(result.SSL).To(Equal(mockSSL))
		})
	})

	Context("When GetOpenpalette returns valid info without token", func() {
		It("should return correct WsmRestfulParams without token", func() {
			mockSSL := &restful.SSLAuth{
				Method:      "TLS",
				SSLProtocol: "TLSv1.2",
				RootCert:    "/path/to/rootCert",
				ClientCert:  "/path/to/clientCert",
				ClientKey:   "/path/to/clientKey",
			}

			mockInfo := authorization.OpenpaletteInfo{
				Token:  "",
				MsbURL: "http://mock.url",
				SSL:    mockSSL,
			}
			patcher.ApplyFunc(GetOpenpalette, func(clusterId string) (authorization.OpenpaletteInfo, error) {
				return mockInfo, nil
			})

			patcher.ApplyFunc(getInspectUrl, func(method, urlPrefix string, id string) string {
				return fmt.Sprintf("%s/inspectionplan/query/%s", urlPrefix, id)
			})

			result, success := buildInspectRestfulReq(clusterId, method, id)

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result.URL).To(Equal("http://mock.url/opapi/wsm/v1/apts/inspectionplan/query/inspect456"))
			gomega.Expect(result.Header).ToNot(HaveKey("X-Auth-Token"))
			gomega.Expect(result.SSL).To(Equal(mockSSL))
		})
	})
})

/* Ended by AICoder, pid:fad4cd0babmdc58141f608f3408b219293d632a9 */

var _ = Describe("PostInspect", func() {
	var (
		patcher       *gomonkey.Patches
		restfulParams WsmRestfulParams
		reqBody       interface{}
	)

	BeforeEach(func() {
		restfulParams = WsmRestfulParams{
			Header: map[string]string{"Content-Type": "application/json"},
			URL:    "http://mock.url/inspect",
			SSL:    nil,
		}
		reqBody = map[string]interface{}{"key": "value"}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When PostToWsm returns an error", func() {
		It("should log the error and return the error message", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusInternalServerError, fmt.Errorf("post to WSM error")
			})

			data, msg, success := PostInspect(restfulParams, reqBody)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(msg).To(Equal(""))
			gomega.Expect(data).To(BeNil())
		})

		It("should return a specific error message from JSON", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusInternalServerError, fmt.Errorf(`{"err": "specific error"}`)
			})

			data, msg, success := PostInspect(restfulParams, reqBody)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(msg).To(Equal(""))
			gomega.Expect(data).To(BeNil())
		})
	})

	Context("When response code is not OK", func() {
		It("should return false", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusBadRequest, nil
			})

			data, msg, success := PostInspect(restfulParams, reqBody)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(msg).To(Equal(""))
			gomega.Expect(data).To(BeNil())
		})
	})

	Context("When PostToWsm returns a valid response", func() {
		It("should return the unmarshalled data", func() {
			mockResponse := map[string]interface{}{"result": "success"}
			bodyBytes, _ := json.Marshal(mockResponse)

			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return bodyBytes, nil, http.StatusOK, nil
			})

			data, msg, success := PostInspect(restfulParams, reqBody)

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(msg).To(Equal(""))
			gomega.Expect(data).To(Equal(mockResponse))
		})
	})

	Context("When isRestfuleMethodSuc returns false", func() {
		It("should return false", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusOK, nil
			})

			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
				return false
			})

			data, msg, success := PostInspect(restfulParams, reqBody)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(msg).To(Equal(""))
			gomega.Expect(data).To(BeNil())
		})
	})

	Context("When json.Unmarshal fails", func() {
		It("should log the error and return false", func() {
			invalidBody := []byte("invalid json")

			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return invalidBody, nil, http.StatusOK, nil
			})

			data, msg, success := PostInspect(restfulParams, reqBody)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(msg).To(Equal(""))
			gomega.Expect(data).To(BeNil())
		})
	})
	Context("When rspCode is neither 200 nor 400", func() {
		It("should call GetCodeStatusValues and return its message", func() {
			rspCode := http.StatusInternalServerError
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, rspCode, nil
			})

			patcher.ApplyFunc(GetCodeStatusValues, func(rspCode int) (string, error) {
				return "Internal Server Error", nil
			})

			result, msg, success := GetInspect(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})

		It("should handle error from GetCodeStatusValues", func() {
			rspCode := http.StatusServiceUnavailable
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, rspCode, nil
			})

			patcher.ApplyFunc(GetCodeStatusValues, func(rspCode int) (string, error) {
				return "", fmt.Errorf("failed to get status")
			})

			result, msg, success := GetInspect(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})
	Context("When GetFromWsmToGetStatus returns Bad Request", func() {
		It("should return false with an empty message", func() {
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusBadRequest, nil
			})

			result, msg, success := GetInspect(restfulParams)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})
})

var _ = Describe("TestGetInspect", func() {
	var (
		restfulParams WsmRestfulParams
		patcher       *gomonkey.Patches
	)

	BeforeEach(func() {
		restfulParams = WsmRestfulParams{
			Header: map[string]string{"Content-Type": "application/json"},
			URL:    "http://mock.url",
			SSL:    nil,
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetFromWsmToGetStatus returns an error", func() {
		It("should return the error message", func() {
			mockError := errors.New("get failed")
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, mockError
			})

			result, _, success := GetInspect(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
		})

		It("should return the error message from JSON unmarshalling", func() {
			mockError := `{"err": "invalid request"}`
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(mockError)
			})

			result, _, success := GetInspect(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
		})
	})

	Context("When isRestfuleMethodSuc returns false", func() {
		It("should return false", func() {
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return false
			})

			result, msg, success := GetInspect(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When isResponseSuc returns false", func() {
		It("should return false", func() {
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return true
			})
			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, params WsmRestfulParams) bool {
				return false
			})

			result, msg, success := GetInspect(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When JSON unmarshalling fails", func() {
		It("should return false", func() {
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{invalid json}`), nil, 200, nil
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return true
			})
			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, params WsmRestfulParams) bool {
				return true
			})

			result, msg, success := GetInspect(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When everything is successful", func() {
		It("should return the data", func() {
			mockData := map[string]interface{}{"result": "success"}
			bodyBytes, _ := json.Marshal(mockData)

			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return bodyBytes, nil, 200, nil
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return true
			})
			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, params WsmRestfulParams) bool {
				return true
			})

			_, msg, _ := GetInspect(restfulParams)

			Expect(msg).To(Equal(""))
		})
	})
	Context("When rspCode is neither 200 nor 400", func() {
		It("should call GetCodeStatusValues and return its message", func() {
			rspCode := http.StatusInternalServerError
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, rspCode, nil
			})

			patcher.ApplyFunc(GetCodeStatusValues, func(rspCode int) (string, error) {
				return "Internal Server Error", nil
			})

			result, msg, success := GetInspect(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})

		It("should handle error from GetCodeStatusValues", func() {
			rspCode := http.StatusServiceUnavailable
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, rspCode, nil
			})

			patcher.ApplyFunc(GetCodeStatusValues, func(rspCode int) (string, error) {
				return "", fmt.Errorf("failed to get status")
			})

			result, msg, success := GetInspect(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})
	Context("When GetFromWsmToGetStatus returns Bad Request", func() {
		It("should return false with an empty message", func() {
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusBadRequest, nil
			})

			result, msg, success := GetInspect(restfulParams)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})
})

var _ = Describe("TestStopInspect", func() {
	var (
		restfulParams WsmRestfulParams
		reqBody       interface{}
		patcher       *gomonkey.Patches
	)

	BeforeEach(func() {
		sslAuth := &restful.SSLAuth{
			Method:      "TLS",
			SSLProtocol: "TLSv1.2",
			RootCert:    "/path/to/rootCert",
			ClientCert:  "/path/to/clientCert",
			ClientKey:   "/path/to/clientKey",
		}
		restfulParams = WsmRestfulParams{
			Header: map[string]string{"Content-Type": "application/json"},
			URL:    "http://mock.url",
			SSL:    sslAuth,
		}
		reqBody = map[string]interface{}{"key": "value"}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When PostToWsm returns an error", func() {
		It("should return the error message", func() {
			mockError := errors.New("post failed")
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, mockError
			})

			result, msg, success := StopInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})

		It("should return the error message from JSON unmarshalling", func() {
			mockError := `{"err": "invalid request"}`
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(mockError)
			})

			result, msg, success := StopInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When isRestfuleMethodSuc returns false", func() {
		It("should return false", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return false
			})

			result, msg, success := StopInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When isResponseSuc returns false", func() {
		It("should return false", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return true
			})
			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, params WsmRestfulParams) bool {
				return false
			})

			result, msg, success := StopInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When everything is successful", func() {
		It("should return true", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return true
			})
			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, params WsmRestfulParams) bool {
				return true
			})

			result, msg, success := StopInspect(restfulParams, reqBody)

			Expect(success).To(BeTrue())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})
	Context("When rspCode is neither 200 nor 400", func() {
		It("should call GetCodeStatusValues and return its message", func() {
			rspCode := http.StatusInternalServerError
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, rspCode, nil
			})

			patcher.ApplyFunc(GetCodeStatusValues, func(rspCode int) (string, error) {
				return "Internal Server Error", nil
			})

			result, msg, success := StopInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})

		It("should handle error from GetCodeStatusValues", func() {
			rspCode := http.StatusServiceUnavailable
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, rspCode, nil
			})

			patcher.ApplyFunc(GetCodeStatusValues, func(rspCode int) (string, error) {
				return "", fmt.Errorf("failed to get status")
			})

			result, msg, success := StopInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})
	Context("When PostToWsm returns Bad Request", func() {
		It("should return false with an empty message", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusBadRequest, nil
			})

			data, msg, success := StopInspect(restfulParams, reqBody)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(msg).To(Equal(""))
			gomega.Expect(data).To(BeNil())
		})
	})
})

var _ = Describe("TestDeleteInspect", func() {
	var (
		restfulParams WsmRestfulParams
		reqBody       interface{}
		patcher       *gomonkey.Patches
	)

	BeforeEach(func() {
		sslAuth := &restful.SSLAuth{
			Method:      "TLS",
			SSLProtocol: "TLSv1.2",
			RootCert:    "/path/to/rootCert",
			ClientCert:  "/path/to/clientCert",
			ClientKey:   "/path/to/clientKey",
		}
		restfulParams = WsmRestfulParams{
			Header: map[string]string{"Content-Type": "application/json"},
			URL:    "http://mock.url",
			SSL:    sslAuth,
		}
		reqBody = map[string]interface{}{"key": "value"}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When PostToWsm returns an error", func() {
		It("should return the error message", func() {
			mockError := errors.New("post failed")
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, mockError
			})

			result, msg, success := DeleteInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})

		It("should return the error message from JSON unmarshalling", func() {
			mockError := `{"err": "invalid request"}`
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(mockError)
			})

			result, msg, success := DeleteInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When isRestfuleMethodSuc returns false", func() {
		It("should return false", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return false
			})

			result, msg, success := DeleteInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When isResponseSuc returns false", func() {
		It("should return false", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return true
			})
			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, params WsmRestfulParams) bool {
				return false
			})

			result, msg, success := DeleteInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When everything is successful", func() {
		It("should return true", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil
			})
			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, params WsmRestfulParams) bool {
				return true
			})
			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, params WsmRestfulParams) bool {
				return true
			})

			result, msg, success := DeleteInspect(restfulParams, reqBody)

			Expect(success).To(BeTrue())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})
	Context("When rspCode is neither 200 nor 400", func() {
		It("should call GetCodeStatusValues and return its message", func() {
			rspCode := http.StatusInternalServerError
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, rspCode, nil
			})

			patcher.ApplyFunc(GetCodeStatusValues, func(rspCode int) (string, error) {
				return "Internal Server Error", nil
			})

			result, msg, success := DeleteInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})

		It("should handle error from GetCodeStatusValues", func() {
			rspCode := http.StatusServiceUnavailable
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, rspCode, nil
			})

			patcher.ApplyFunc(GetCodeStatusValues, func(rspCode int) (string, error) {
				return "", fmt.Errorf("failed to get status")
			})

			result, msg, success := DeleteInspect(restfulParams, reqBody)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})
	Context("When PostToWsm returns Bad Request", func() {
		It("should return false with an empty message", func() {
			patcher.ApplyFunc(PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusBadRequest, nil
			})

			data, msg, success := DeleteInspect(restfulParams, reqBody)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(msg).To(Equal(""))
			gomega.Expect(data).To(BeNil())
		})
	})
})

/* Started by AICoder, pid:g472250422n8cd01439c0ba4a0f5398ee4316eda */
var _ = Describe("TestVcjobEventsHandler", func() {
	var (
		reqParam VcjobEventsWsmKeywords
		patcher  *gomonkey.Patches
	)

	BeforeEach(func() {
		reqParam = VcjobEventsWsmKeywords{
			ClusterId:   "cluster123",
			ProjectName: "projectA",
			Name:        "vcjobEvent1",
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When buildVcjobEventsRestfulReq fails", func() {
		It("should return nil and false", func() {
			patcher.ApplyFunc(buildVcjobEventsRestfulReq, func(keyswords VcjobEventsWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{}, false
			})

			result, err, suc := VcjobEventsHandler(reqParam, nil, "getvcjobs")

			Expect(result).To(BeNil())
			Expect(err).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the method is unsupported", func() {
		It("should return nil and false", func() {
			result, err, suc := VcjobEventsHandler(reqParam, nil, "unsupportedMethod")

			Expect(result).To(BeNil())
			Expect(err).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the method is valid and GetVcjobEvents returns data", func() {
		It("should return the data and true", func() {
			data := []map[string]interface{}{{"id": 1, "name": "vcjobEvent1"}}

			patcher.ApplyFunc(buildVcjobEventsRestfulReq, func(keyswords VcjobEventsWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{}, true
			})

			patcher.ApplyFunc(GetVcjobEvents, func(restfulParams WsmRestfulParams) ([]map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, err, suc := VcjobEventsHandler(reqParam, nil, "getvcjobs")

			Expect(result).NotTo(Equal(data))
			Expect(err).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the method is valid but GetVcjobEvents returns an error", func() {
		It("should return nil and false", func() {
			patcher.ApplyFunc(buildVcjobEventsRestfulReq, func(keyswords VcjobEventsWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})

			patcher.ApplyFunc(GetVcjobEvents, func(restfulParams WsmRestfulParams) ([]map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, err, suc := VcjobEventsHandler(reqParam, nil, "getvcjobs")

			Expect(result).To(BeNil())
			Expect(err).NotTo(Equal("error getting vcjob events"))
			Expect(suc).To(BeFalse())
		})
	})
})

/* Ended by AICoder, pid:g472250422n8cd01439c0ba4a0f5398ee4316eda */

/* Started by AICoder, pid:o701aueccc42fe314ea10b6b200e138b3335947c */
var _ = Describe("TestGetVcjobEvents", func() {
	var (
		patcher       *gomonkey.Patches
		restfulParams WsmRestfulParams
	)

	BeforeEach(func() {
		sslAuth := &restful.SSLAuth{
			Method:      "TLS",
			SSLProtocol: "TLSv1.2",
			RootCert:    "/path/to/rootCert",
			ClientCert:  "/path/to/clientCert",
			ClientKey:   "/path/to/clientKey",
		}
		restfulParams = WsmRestfulParams{
			Header: map[string]string{"Content-Type": "application/json"},
			URL:    "http://mock.url",
			SSL:    sslAuth,
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When the response code is not 200 and there is an error", func() {
		It("should return an error message", func() {
			body := []byte(`{"err": "some error occurred"}`)
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return body, nil, 500, errors.New("some error occurred")
			})

			result, errMsg, suc := GetVcjobEvents(restfulParams)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal("some error occurred"))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the response code is not 200 and there is no error message", func() {
		It("should return an internal error message", func() {
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 500, nil
			})

			result, errMsg, suc := GetVcjobEvents(restfulParams)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the response code is 200 but the JSON unmarshal fails", func() {
		It("should return an error on unmarshal", func() {
			body := []byte(`invalid json`)
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return body, nil, 200, nil
			})

			result, errMsg, suc := GetVcjobEvents(restfulParams)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the response code is 200 and the JSON unmarshal succeeds", func() {
		It("should return the unmarshaled data", func() {
			body := []byte(`[{"id": 1, "name": "vcjobEvent1"}]`)
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return body, nil, 200, nil
			})

			result, errMsg, suc := GetVcjobEvents(restfulParams)

			Expect(result).NotTo(Equal([]map[string]interface{}{{"id": 1, "name": "vcjobEvent1"}}))
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeTrue())
		})
	})
})

/* Ended by AICoder, pid:o701aueccc42fe314ea10b6b200e138b3335947c */

/* Started by AICoder, pid:g7681ff45f68e4614daa0b1bc1c5811094a94429 */
var _ = Describe("TestVcjobOneEventsHandler", func() {
	var (
		reqParam VcjobEventsWsmKeywords
		reqBody  []byte
		patcher  *gomonkey.Patches
	)

	BeforeEach(func() {
		reqParam = VcjobEventsWsmKeywords{
			ClusterId:   "cluster123",
			ProjectName: "projectA",
			Name:        "vcjobEvent1",
		}
		reqBody = []byte(`{"config": "sample"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When buildVcjobEventsRestfulReq fails", func() {
		It("should return nil and false", func() {
			patcher.ApplyFunc(buildVcjobEventsRestfulReq, func(keyswords VcjobEventsWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{}, false
			})

			result, errMsg, suc := VcjobOneEventsHandler(reqParam, reqBody, "getvcjobevents")

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the method is unsupported", func() {
		It("should return nil and false", func() {
			result, errMsg, suc := VcjobOneEventsHandler(reqParam, reqBody, "unsupportedMethod")

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the method is 'getvcjobevents' and GetOneVcjobEvents returns data", func() {
		It("should return the data and true", func() {
			data := map[string]interface{}{"id": 1, "name": "vcjobEvent1"}
			patcher.ApplyFunc(buildVcjobEventsRestfulReq, func(keyswords VcjobEventsWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})

			patcher.ApplyFunc(GetOneVcjobEvents, func(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, errMsg, suc := VcjobOneEventsHandler(reqParam, reqBody, "getvcjobevents")

			Expect(result).To(Equal(data))
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeTrue())
		})
	})

	Context("When the method is 'getvcjobevents' but GetOneVcjobEvents returns an error", func() {
		It("should return nil and false", func() {
			patcher.ApplyFunc(buildVcjobEventsRestfulReq, func(keyswords VcjobEventsWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})

			patcher.ApplyFunc(GetOneVcjobEvents, func(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
				return nil, "error getting vcjob events", false
			})

			result, errMsg, suc := VcjobOneEventsHandler(reqParam, reqBody, "getvcjobevents")

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal("error getting vcjob events"))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the method is 'post' and PostVcjobCfg returns data", func() {
		It("should return the data and true", func() {
			data := map[string]interface{}{"success": true}
			patcher.ApplyFunc(buildVcjobEventsRestfulReq, func(keyswords VcjobEventsWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})

			patcher.ApplyFunc(PostVcjobCfg, func(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, errMsg, suc := VcjobOneEventsHandler(reqParam, reqBody, "post")

			Expect(result).To(Equal(data))
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeTrue())
		})
	})

	Context("When the method is 'post' but PostVcjobCfg returns an error", func() {
		It("should return nil and false", func() {
			patcher.ApplyFunc(buildVcjobEventsRestfulReq, func(keyswords VcjobEventsWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})

			patcher.ApplyFunc(PostVcjobCfg, func(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
				return nil, "error posting vcjob config", false
			})

			result, errMsg, suc := VcjobOneEventsHandler(reqParam, reqBody, "post")

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal("error posting vcjob config"))
			Expect(suc).To(BeFalse())
		})
	})
})

/* Ended by AICoder, pid:g7681ff45f68e4614daa0b1bc1c5811094a94429 */

/* Started by AICoder, pid:jb64fg2a8d8588f14b11096070b06a8f40755535 */
var _ = Describe("TestGetOneVcjobEvents", func() {
	var (
		patcher       *gomonkey.Patches
		restfulParams WsmRestfulParams
	)

	BeforeEach(func() {
		sslAuth := &restful.SSLAuth{
			Method:      "TLS",
			SSLProtocol: "TLSv1.2",
			RootCert:    "/path/to/rootCert",
			ClientCert:  "/path/to/clientCert",
			ClientKey:   "/path/to/clientKey",
		}
		restfulParams = WsmRestfulParams{
			Header: map[string]string{"Content-Type": "application/json"},
			URL:    "http://mock.url",
			SSL:    sslAuth,
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When the response code is not 200 and there is an error", func() {
		It("should return an error message", func() {
			body := []byte(`{"err": "some error occurred"}`)
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return body, nil, 500, errors.New("some error occurred")
			})

			result, errMsg, suc := GetOneVcjobEvents(restfulParams)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal("some error occurred"))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the response code is not 200 and there is no error message", func() {
		It("should return an internal error message", func() {
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 500, nil
			})

			result, errMsg, suc := GetOneVcjobEvents(restfulParams)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the response code is 200 but the JSON unmarshal fails", func() {
		It("should return an error on unmarshal", func() {
			body := []byte(`invalid json`)
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return body, nil, 200, nil
			})

			result, errMsg, suc := GetOneVcjobEvents(restfulParams)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the response code is 200 and the JSON unmarshal succeeds", func() {
		It("should return the unmarshaled data", func() {
			body := []byte(`{"id": 1, "name": "vcjobEvent1"}`)
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return body, nil, 200, nil
			})

			result, errMsg, suc := GetOneVcjobEvents(restfulParams)

			Expect(result).NotTo(Equal(map[string]interface{}{"id": 1, "name": "vcjobEvent1"}))
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeTrue())
		})
	})
})

/* Ended by AICoder, pid:jb64fg2a8d8588f14b11096070b06a8f40755535 */

/* Started by AICoder, pid:n8e3fa1e9f46e3a14b70081381d97a4975b0f3e4 */
var _ = Describe("TestPostVcjobCfg", func() {
	var (
		patcher       *gomonkey.Patches
		restfulParams WsmRestfulParams
		reqBody       []byte
	)

	BeforeEach(func() {
		sslAuth := &restful.SSLAuth{
			Method:      "TLS",
			SSLProtocol: "TLSv1.2",
			RootCert:    "/path/to/rootCert",
			ClientCert:  "/path/to/clientCert",
			ClientKey:   "/path/to/clientKey",
		}
		restfulParams = WsmRestfulParams{
			URL:    "http://example.com/vcjobconfig",
			Header: map[string]string{"Authorization": "Bearer token"},
			SSL:    sslAuth,
		}
		reqBody = []byte(`{"config": "sample"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When there is an error in the PostMethod", func() {
		It("should return an error message", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return body, nil, 500, errors.New("some error occurred")
			})

			result, errMsg, suc := PostVcjobCfg(restfulParams, reqBody)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal("some error occurred"))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the PostMethod returns no error but the response is not successful", func() {
		It("should return false and an empty error message", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 500, nil
			})

			result, errMsg, suc := PostVcjobCfg(restfulParams, reqBody)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the PostMethod returns an error on isRestfuleMethodSuc", func() {
		It("should return false and an empty error message", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil
			})

			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
				return false
			})

			result, errMsg, suc := PostVcjobCfg(restfulParams, reqBody)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the PostMethod returns an error on isResponseSuc", func() {
		It("should return false and an empty error message", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{}`), nil, 200, nil
			})

			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
				return true
			})

			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
				return false
			})

			result, errMsg, suc := PostVcjobCfg(restfulParams, reqBody)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the PostMethod returns a successful response and JSON unmarshal succeeds", func() {
		It("should return the unmarshaled data", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return body, nil, 200, nil
			})

			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
				return true
			})

			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
				return true
			})

			result, errMsg, suc := PostVcjobCfg(restfulParams, reqBody)

			Expect(result).To(Equal(map[string]interface{}{"config": "sample"}))
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeTrue())
		})
	})

	Context("When the PostMethod returns a successful response but JSON unmarshal fails", func() {
		It("should return an error on unmarshal", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`invalid json`), nil, 200, nil
			})

			patcher.ApplyFunc(isRestfuleMethodSuc, func(err error, method string, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
				return true
			})

			patcher.ApplyFunc(isResponseSuc, func(rspCode int, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
				return true
			})

			result, errMsg, suc := PostVcjobCfg(restfulParams, reqBody)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})
})

/* Ended by AICoder, pid:n8e3fa1e9f46e3a14b70081381d97a4975b0f3e4 */

/* Started by AICoder, pid:69d58mcc2aqfd321444e08965121c12d53b9a6c9 */
var _ = Describe("buildVcjobEventsRestfulReq", func() {
	var (
		patcher  *gomonkey.Patches
		keywords VcjobEventsWsmKeywords
		method   string
	)

	BeforeEach(func() {
		keywords = VcjobEventsWsmKeywords{
			ClusterId:   "cluster123",
			ProjectName: "project456",
			Name:        "vcjob789",
		}
		method = "getvcjobevents"
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetOpenpalette returns an error", func() {
		It("should return empty WsmRestfulParams and false", func() {
			patcher.ApplyFunc(GetOpenpalette, func(clusterId string) (authorization.OpenpaletteInfo, error) {
				return authorization.OpenpaletteInfo{}, fmt.Errorf("error getting open palette")
			})

			result, success := buildVcjobEventsRestfulReq(keywords, method)

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(Equal(WsmRestfulParams{}))
		})
	})

	Context("When GetOpenpalette returns valid info with token", func() {
		It("should return correct WsmRestfulParams", func() {
			mockSSL := &restful.SSLAuth{
				Method:      "TLS",
				SSLProtocol: "TLSv1.2",
				RootCert:    "/path/to/rootCert",
				ClientCert:  "/path/to/clientCert",
				ClientKey:   "/path/to/clientKey",
			}

			mockInfo := authorization.OpenpaletteInfo{
				Token:  "mockToken",
				MsbURL: "http://mock.url",
				SSL:    mockSSL,
			}
			patcher.ApplyFunc(GetOpenpalette, func(clusterId string) (authorization.OpenpaletteInfo, error) {
				return mockInfo, nil
			})

			patcher.ApplyFunc(getVcjobEventsUrl, func(method, urlPrefix string, keywords VcjobEventsWsmKeywords) string {
				return fmt.Sprintf("%s/vcjobevents/%s/%s", urlPrefix, keywords.ProjectName, keywords.Name)
			})

			result, success := buildVcjobEventsRestfulReq(keywords, method)

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result.URL).To(Equal("http://mock.url/opapi/wsm/v1/apts/vcjobevents/project456/vcjob789"))
			gomega.Expect(result.Header["X-Auth-Token"]).To(Equal("mockToken"))
			gomega.Expect(result.SSL).To(Equal(mockSSL))
		})
	})

	Context("When GetOpenpalette returns valid info without token", func() {
		It("should return correct WsmRestfulParams without token", func() {
			mockSSL := &restful.SSLAuth{
				Method:      "TLS",
				SSLProtocol: "TLSv1.2",
				RootCert:    "/path/to/rootCert",
				ClientCert:  "/path/to/clientCert",
				ClientKey:   "/path/to/clientKey",
			}

			mockInfo := authorization.OpenpaletteInfo{
				Token:  "",
				MsbURL: "http://mock.url",
				SSL:    mockSSL,
			}
			patcher.ApplyFunc(GetOpenpalette, func(clusterId string) (authorization.OpenpaletteInfo, error) {
				return mockInfo, nil
			})

			patcher.ApplyFunc(getVcjobEventsUrl, func(method, urlPrefix string, keywords VcjobEventsWsmKeywords) string {
				return fmt.Sprintf("%s/vcjobevents/%s/%s", urlPrefix, keywords.ProjectName, keywords.Name)
			})

			result, success := buildVcjobEventsRestfulReq(keywords, method)

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result.URL).To(Equal("http://mock.url/opapi/wsm/v1/apts/vcjobevents/project456/vcjob789"))
			gomega.Expect(result.Header).ToNot(HaveKey("X-Auth-Token"))
			gomega.Expect(result.SSL).To(Equal(mockSSL))
		})
	})

	Context("When method is not supported", func() {
		It("should return empty WsmRestfulParams and false", func() {
			mockSSL := &restful.SSLAuth{
				Method:      "TLS",
				SSLProtocol: "TLSv1.2",
				RootCert:    "/path/to/rootCert",
				ClientCert:  "/path/to/clientCert",
				ClientKey:   "/path/to/clientKey",
			}

			mockInfo := authorization.OpenpaletteInfo{
				Token:  "mockToken",
				MsbURL: "http://mock.url",
				SSL:    mockSSL,
			}
			patcher.ApplyFunc(GetOpenpalette, func(clusterId string) (authorization.OpenpaletteInfo, error) {
				return mockInfo, nil
			})

			unsupportedMethod := "unsupportedMethod"
			patcher.ApplyFunc(getVcjobEventsUrl, func(method, urlPrefix string, keywords VcjobEventsWsmKeywords) string {
				return ""
			})

			result, success := buildVcjobEventsRestfulReq(keywords, unsupportedMethod)

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).NotTo(Equal(WsmRestfulParams{}))
		})
	})
})

/* Ended by AICoder, pid:69d58mcc2aqfd321444e08965121c12d53b9a6c9 */

/* Started by AICoder, pid:g3a00af04co0750143c20b6100f5686995c1eb61 */
var _ = Describe("TestGetVcjobEventsUrl", func() {
	var (
		urlPrefix string
		keyswords VcjobEventsWsmKeywords
	)

	BeforeEach(func() {
		urlPrefix = "http://example.com/opapi/wsm/v1/apts"
		keyswords = VcjobEventsWsmKeywords{
			ProjectName: "projectA",
			Name:        "vcjobEvent1",
		}
	})

	Context("When the method is 'get'", func() {
		It("should return the correct URL for 'get'", func() {
			result := getVcjobEventsUrl("get", urlPrefix, keyswords)
			expected := urlPrefix + "/nsvcjobevents/projectA"
			Expect(result).To(Equal(expected))
		})
	})

	Context("When the method is 'getvcjobevents'", func() {
		It("should return the correct URL for 'getvcjobevents'", func() {
			result := getVcjobEventsUrl("getvcjobevents", urlPrefix, keyswords)
			expected := urlPrefix + "/vcjobevents/projectA/vcjobEvent1"
			Expect(result).To(Equal(expected))
		})
	})

	Context("When the method is 'getvcjobs'", func() {
		It("should return the correct URL for 'getvcjobs'", func() {
			result := getVcjobEventsUrl("getvcjobs", urlPrefix, keyswords)
			expected := urlPrefix + "/vcjobs?projectname=projectA"
			Expect(result).To(Equal(expected))
		})
	})

	Context("When the method is 'getvcjobcfg'", func() {
		It("should return the correct URL for 'getvcjobcfg'", func() {
			result := getVcjobEventsUrl("getvcjobcfg", urlPrefix, keyswords)
			expected := urlPrefix + "/faultanalacfg"
			Expect(result).To(Equal(expected))
		})
	})

	Context("When the method is 'post'", func() {
		It("should return the correct URL for 'post'", func() {
			result := getVcjobEventsUrl("post", urlPrefix, keyswords)
			expected := urlPrefix + "/faultanalacfg"
			Expect(result).To(Equal(expected))
		})
	})

	Context("When the method is unsupported", func() {
		It("should log an error and return an empty string", func() {
			result := getVcjobEventsUrl("unsupportedMethod", urlPrefix, keyswords)
			Expect(result).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:g3a00af04co0750143c20b6100f5686995c1eb61 */

/* Started by AICoder, pid:b8b10w735d404a7149bf0a0c409be08090f9342d */
func TestGetOpenpalette(t *testing.T) {
	convey.Convey("Given various scenarios for GetOpenpalette", t, func() {

		convey.Convey("When the environment type is 'tcf-k8s'", func() {
			patch := gomonkey.ApplyFunc(authorization.GetOpenpaletteInfoFromPvrm, func(clusterId string) (authorization.OpenpaletteInfo, string, error) {
				return authorization.OpenpaletteInfo{EnvType: "tcf-k8s"}, "", nil
			})
			defer patch.Reset()

			_, err := GetOpenpalette("cluster1")
			convey.So(err, convey.ShouldNotBeNil)
		})

		convey.Convey("When the environment type is not 'tcf-k8s' and WSM port is successfully retrieved", func() {
			patch := gomonkey.ApplyFunc(authorization.GetOpenpaletteInfoFromPvrm, func(clusterId string) (authorization.OpenpaletteInfo, string, error) {
				return authorization.OpenpaletteInfo{EnvType: "other-env", MsbURL: "http://example.com"}, "", nil
			})
			patch1 := gomonkey.ApplyFunc(BuildOpenpaletteServiceReq, func(info authorization.OpenpaletteInfo) WsmRestfulParams {
				return WsmRestfulParams{}
			})
			patch2 := gomonkey.ApplyFunc(GetWsmPort, func(params WsmRestfulParams) (string, bool) {
				return "8080", true
			})
			patch3 := gomonkey.ApplyFunc(ExtractIPFromURI, func(uri string) (string, error) {
				return "127.0.0.1", nil
			})
			defer patch.Reset()
			defer patch1.Reset()
			defer patch2.Reset()
			defer patch3.Reset()

			info, err := GetOpenpalette("cluster2")
			convey.So(err, convey.ShouldBeNil)
			convey.So(info.MsbURL, convey.ShouldEqual, "https://127.0.0.1:8080")
		})

		convey.Convey("When GetOpenpaletteInfoFromPvrm returns an error", func() {
			patch := gomonkey.ApplyFunc(authorization.GetOpenpaletteInfoFromPvrm, func(clusterId string) (authorization.OpenpaletteInfo, string, error) {
				return authorization.OpenpaletteInfo{}, "", fmt.Errorf("error from pvrm")
			})
			defer patch.Reset()

			_, err := GetOpenpalette("cluster3")
			convey.So(err, convey.ShouldNotBeNil)
		})

		convey.Convey("When GetWsmPort fails to retrieve a port", func() {
			patch := gomonkey.ApplyFunc(authorization.GetOpenpaletteInfoFromPvrm, func(clusterId string) (authorization.OpenpaletteInfo, string, error) {
				return authorization.OpenpaletteInfo{EnvType: "other-env", MsbURL: "http://example.com"}, "", nil
			})
			patch1 := gomonkey.ApplyFunc(BuildOpenpaletteServiceReq, func(info authorization.OpenpaletteInfo) WsmRestfulParams {
				return WsmRestfulParams{}
			})
			patch2 := gomonkey.ApplyFunc(GetWsmPort, func(params WsmRestfulParams) (string, bool) {
				return "", false
			})
			defer patch.Reset()
			defer patch1.Reset()
			defer patch2.Reset()

			_, err := GetOpenpalette("cluster4")
			convey.So(err, convey.ShouldNotBeNil)
		})

		convey.Convey("When ExtractIPFromURI fails to extract IP", func() {
			patch := gomonkey.ApplyFunc(authorization.GetOpenpaletteInfoFromPvrm, func(clusterId string) (authorization.OpenpaletteInfo, string, error) {
				return authorization.OpenpaletteInfo{EnvType: "other-env", MsbURL: "http://example.com"}, "", nil
			})
			patch1 := gomonkey.ApplyFunc(BuildOpenpaletteServiceReq, func(info authorization.OpenpaletteInfo) WsmRestfulParams {
				return WsmRestfulParams{}
			})
			patch2 := gomonkey.ApplyFunc(GetWsmPort, func(params WsmRestfulParams) (string, bool) {
				return "8080", true
			})
			patch3 := gomonkey.ApplyFunc(ExtractIPFromURI, func(uri string) (string, error) {
				return "", fmt.Errorf("error extracting IP")
			})
			defer patch.Reset()
			defer patch1.Reset()
			defer patch2.Reset()
			defer patch3.Reset()

			_, err := GetOpenpalette("cluster5")
			convey.So(err, convey.ShouldNotBeNil)
		})
	})
}

/* Ended by AICoder, pid:b8b10w735d404a7149bf0a0c409be08090f9342d */

/* Started by AICoder, pid:912d4dc2edi2f3b14929092d71066b121905f09c */
var _ = Describe("HealthHandler", func() {
	var (
		patcher  *gomonkey.Patches
		keywords HealthWsmKeywords
		reqBody  []byte
	)

	BeforeEach(func() {
		keywords = HealthWsmKeywords{
			ClusterId: "cluster123",
		}
		reqBody = []byte(`{"key":"value"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When buildHealthRestfulReq fails", func() {
		It("should return false", func() {
			patcher.ApplyFunc(buildHealthRestfulReq, func(keyswords HealthWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{}, false
			})

			result, msg, success := HealthHandler(keywords, reqBody, "get")

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is get", func() {
		It("should call GetHealth and return its result", func() {
			mockResult := map[string]interface{}{"data": "mockData"}
			patcher.ApplyFunc(buildHealthRestfulReq, func(keyswords HealthWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(GetHealth, func(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
				return mockResult, "", true
			})

			result, msg, success := HealthHandler(keywords, reqBody, "get")

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(Equal(mockResult))
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is post", func() {
		It("should call PostHealth and return its result", func() {
			mockResult := map[string]interface{}{"data": "mockData"}
			patcher.ApplyFunc(buildHealthRestfulReq, func(keyswords HealthWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(PostHealth, func(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
				return mockResult, "", true
			})

			result, msg, success := HealthHandler(keywords, reqBody, "post")

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(Equal(mockResult))
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is unsupported", func() {
		It("should return false", func() {
			result, msg, success := HealthHandler(keywords, reqBody, "unsupported")

			gomega.Expect(success).To(BeFalse())
			gomega.Expect(result).To(BeNil())
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is gethealthcheckcfg", func() {
		It("should call GetHealth and return its result", func() {
			mockResult := map[string]interface{}{"data": "mockData"}
			patcher.ApplyFunc(buildHealthRestfulReq, func(keyswords HealthWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(GetHealth, func(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
				return mockResult, "", true
			})

			result, msg, success := HealthHandler(keywords, reqBody, "gethealthcheckcfg")

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(Equal(mockResult))
			gomega.Expect(msg).To(Equal(""))
		})
	})

	Context("When method is gethealthcheckgpunodescfg", func() {
		It("should call GetHealth and return its result", func() {
			mockResult := map[string]interface{}{"data": "mockData"}
			patcher.ApplyFunc(buildHealthRestfulReq, func(keyswords HealthWsmKeywords, method string) (WsmRestfulParams, bool) {
				return WsmRestfulParams{URL: "http://mock.url"}, true
			})
			patcher.ApplyFunc(GetHealth, func(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
				return mockResult, "", true
			})

			result, msg, success := HealthHandler(keywords, reqBody, "gethealthcheckgpunodescfg")

			gomega.Expect(success).To(BeTrue())
			gomega.Expect(result).To(Equal(mockResult))
			gomega.Expect(msg).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:912d4dc2edi2f3b14929092d71066b121905f09c */

/* Started by AICoder, pid:n2546b5ecav0e2414901096d4087314e71f08e66 */
var _ = Describe("getHealthUrl", func() {
	var (
		urlPrefix string
	)

	BeforeEach(func() {
		urlPrefix = "http://example.com/api"
	})

	Context("When method is get", func() {
		It("should return the correct healthcheck URL", func() {
			gomega.Expect(getHealthUrl("get", urlPrefix)).To(Equal(fmt.Sprintf("%s/healthcheck", urlPrefix)))
		})
	})

	Context("When method is gethealthcheckcfg", func() {
		It("should return the correct healthcheckcfg URL", func() {
			gomega.Expect(getHealthUrl("gethealthcheckcfg", urlPrefix)).To(Equal(fmt.Sprintf("%s/healthcheckcfg", urlPrefix)))
		})
	})

	Context("When method is gethealthcheckgpunodescfg", func() {
		It("should return the correct healthcheckgpunodes URL", func() {
			gomega.Expect(getHealthUrl("gethealthcheckgpunodescfg", urlPrefix)).To(Equal(fmt.Sprintf("%s/healthcheckgpunodes", urlPrefix)))
		})
	})

	Context("When method is post", func() {
		It("should return the correct healthcheck URL", func() {
			gomega.Expect(getHealthUrl("post", urlPrefix)).To(Equal(fmt.Sprintf("%s/healthcheck", urlPrefix)))
		})
	})

	Context("When method is unsupported", func() {
		It("should return an empty string and log an error", func() {
			url := getHealthUrl("unsupported", urlPrefix)
			gomega.Expect(url).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:n2546b5ecav0e2414901096d4087314e71f08e66 */

/* Started by AICoder, pid:08041ve6ca32fcd14dc30a340018f058f7468e13 */
var _ = Describe("GetCodeStatusValues", func() {
	var (
		rspCode int
	)

	Context("When rspCode is 404", func() {
		It("should return Not Found message", func() {
			rspCode = http.StatusNotFound
			message, err := GetCodeStatusValues(rspCode)

			Expect(err).To(BeNil())
			Expect(message).To(Equal(" Not Found: The requested resource could not be found on the server."))
		})
	})

	Context("When rspCode is 401", func() {
		It("should return Unauthorized message", func() {
			rspCode = http.StatusUnauthorized
			message, err := GetCodeStatusValues(rspCode)

			Expect(err).To(BeNil())
			Expect(message).To(Equal(" Unauthorized: The client has not provided valid authentication credentials, and access to the resource is denied."))
		})
	})

	Context("When rspCode is 403", func() {
		It("should return Forbidden message", func() {
			rspCode = http.StatusForbidden
			message, err := GetCodeStatusValues(rspCode)

			Expect(err).To(BeNil())
			Expect(message).To(Equal(" Forbidden: The client is forbidden from accessing the requested resource."))
		})
	})

	Context("When rspCode is 502", func() {
		It("should return Bad Gateway message", func() {
			rspCode = http.StatusBadGateway
			message, err := GetCodeStatusValues(rspCode)

			Expect(err).To(BeNil())
			Expect(message).To(Equal(" Bad Gateway: The server, while acting as a gateway or proxy, received an invalid response from an upstream server it accessed in attempting to fulfill the request."))
		})
	})

	Context("When rspCode is 501", func() {
		It("should return  Not Implemented message", func() {
			rspCode = http.StatusNotImplemented
			message, err := GetCodeStatusValues(rspCode)

			Expect(err).To(BeNil())
			Expect(message).To(Equal(" Not Implemented: The server does not support the functionality required to fulfill the request."))
		})
	})

	Context("When rspCode is 504", func() {
		It("should return  Gateway Timeout message", func() {
			rspCode = http.StatusGatewayTimeout
			message, err := GetCodeStatusValues(rspCode)

			Expect(err).To(BeNil())
			Expect(message).To(Equal(" Gateway Timeout: The server, acting as a gateway or proxy, did not receive a timely response from an upstream server."))
		})
	})

	Context("When rspCode is 505", func() {
		It("should return  Gateway Timeout message", func() {
			rspCode = http.StatusHTTPVersionNotSupported
			message, err := GetCodeStatusValues(rspCode)

			Expect(err).To(BeNil())
			Expect(message).To(Equal(" HTTP Version Not Supported: The server does not support the HTTP protocol version used in the request."))
		})
	})

	Context("When rspCode is unsupported", func() {
		It("should return an error", func() {
			rspCode = 999
			message, err := GetCodeStatusValues(rspCode)

			Expect(err).ToNot(BeNil())
			Expect(message).To(Equal(""))
			Expect(err.Error()).To(Equal("get code status failed rspCode is:999"))
		})
	})
})

/* Ended by AICoder, pid:08041ve6ca32fcd14dc30a340018f058f7468e13 */

/* Started by AICoder, pid:0a0358a9bb5b0191437d0a96c040fe8a8846af70 */
var _ = Describe("TestGetHealth", func() {
	var (
		patcher       *gomonkey.Patches
		restfulParams WsmRestfulParams
	)

	BeforeEach(func() {
		sslAuth := &restful.SSLAuth{
			Method:      "TLS",
			SSLProtocol: "TLSv1.2",
			RootCert:    "/path/to/rootCert",
			ClientCert:  "/path/to/clientCert",
			ClientKey:   "/path/to/clientKey",
		}
		restfulParams = WsmRestfulParams{
			Header: map[string]string{"Content-Type": "application/json"},
			URL:    "http://mock.url",
			SSL:    sslAuth,
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When there is an error while getting health", func() {
		It("should return an error message", func() {
			body := []byte(`{"err": "some error occurred"}`)
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return body, nil, 500, errors.New("some error occurred")
			})

			result, errMsg, suc := GetHealth(restfulParams)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the response code is not 200 and there is no error message", func() {
		It("should return an internal error message", func() {
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 500, nil
			})

			result, errMsg, suc := GetHealth(restfulParams)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the response code is 200 but the JSON unmarshal fails", func() {
		It("should return an error on unmarshal", func() {
			body := []byte(`invalid json`)
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return body, nil, 200, nil
			})

			result, errMsg, suc := GetHealth(restfulParams)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When the response code is 200 and the JSON unmarshal succeeds", func() {
		It("should return the unmarshaled data", func() {
			body := []byte(`{"status": "healthy"}`)
			patcher.ApplyFunc(restful.GetMethod, func(url string, header map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return body, nil, 200, nil
			})

			result, errMsg, suc := GetHealth(restfulParams)

			Expect(result).NotTo(BeNil())
			Expect(result).To(Equal(map[string]interface{}{"status": "healthy"}))
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeTrue())
		})
	})
})

/* Ended by AICoder, pid:0a0358a9bb5b0191437d0a96c040fe8a8846af70 */

/* Started by AICoder, pid:acadc6d5bazb866142f10966a0a23c7d6cd9ad81 */
var _ = Describe("TestGetModelinspection", func() {
	var (
		restfulParams WsmRestfulParams
		patcher       *gomonkey.Patches
	)

	BeforeEach(func() {
		restfulParams = WsmRestfulParams{
			Header: map[string]string{"Content-Type": "application/json"},
			URL:    "http://mock.url",
			SSL:    nil,
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetFromWsmToGetStatus returns an error", func() {
		It("should return the error message", func() {
			mockError := errors.New("get code status values failed")
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, mockError
			})

			result, msg, success := GetModelinspection(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(mockError.Error()))
		})

		It("should return the error message from JSON unmarshalling if possible", func() {
			mockErrorJSON := `{"err": "invalid request"}`
			mockError := errors.New(mockErrorJSON)
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, mockError
			})

			result, msg, success := GetModelinspection(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal("get code status values failed"))
		})
	})

	Context("When JSON unmarshalling fails", func() {
		It("should return false", func() {
			invalidJSON := []byte(`{invalid json}`)
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return invalidJSON, nil, 200, nil
			})

			result, msg, success := GetModelinspection(restfulParams)

			Expect(success).To(BeFalse())
			Expect(result).To(BeNil())
			Expect(msg).To(Equal(""))
		})
	})

	Context("When everything is successful", func() {
		It("should return the data and true", func() {
			mockResponseBody := `{"key": "value"}`
			mockResponseData := map[string]interface{}{"key": "value"}
			patcher.ApplyFunc(GetFromWsmToGetStatus, func(url string, header map[string]string, ssl *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(mockResponseBody), nil, 200, nil
			})

			result, msg, success := GetModelinspection(restfulParams)

			Expect(success).To(BeTrue())
			Expect(result).To(Equal(mockResponseData))
			Expect(msg).To(Equal(""))
		})
	})
})

/* Ended by AICoder, pid:acadc6d5bazb866142f10966a0a23c7d6cd9ad81 */

/* Started by AICoder, pid:ta88b54592e705014ee10b9ff0e38b8ce277073e */
var _ = Describe("TestPostModelinspection", func() {
	var (
		patcher          *gomonkey.Patches
		restfulParams    WsmRestfulParams
		reqBody          []byte
		mockErrorMessage = `{"err": "mocked error message"}`
		mockResponseBody = `{"data": "mocked response"}`
	)

	BeforeEach(func() {
		sslAuth := &restful.SSLAuth{
			Method:      "TLS",
			SSLProtocol: "TLSv1.2",
			RootCert:    "/path/to/rootCert",
			ClientCert:  "/path/to/clientCert",
			ClientKey:   "/path/to/clientKey",
		}
		restfulParams = WsmRestfulParams{
			URL:    "http://example.com/modelinspection",
			Header: map[string]string{"Authorization": "Bearer token"},
			SSL:    sslAuth,
		}
		reqBody = []byte(`{"model": "sample"}`)
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When PostMethod returns an error", func() {
		It("should return an error message", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New("some error occurred")
			})

			result, errMsg, suc := PostModelinspection(restfulParams, reqBody)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal("get code status values failed"))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When PostMethod returns an error with a JSON error message", func() {
		It("should return the JSON error message", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(mockErrorMessage), nil, 500, errors.New("unmarshal error")
			})

			result, errMsg, suc := PostModelinspection(restfulParams, reqBody)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal("get code status values failed"))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When PostMethod returns no error but the response is not successful", func() {
		It("should return false and an empty error message", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 500, nil
			})

			result, errMsg, suc := PostModelinspection(restfulParams, reqBody)

			Expect(result).To(BeNil())
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeFalse())
		})
	})

	Context("When PostMethod returns a successful response", func() {
		It("should return the unmarshaled data", func() {
			patcher.ApplyFunc(restful.PostMethod, func(url string, header map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(mockResponseBody), nil, 200, nil
			})

			result, errMsg, suc := PostModelinspection(restfulParams, reqBody)

			expectedResult := map[string]interface{}{"data": "mocked response"}
			Expect(result).To(Equal(expectedResult))
			Expect(errMsg).To(Equal(""))
			Expect(suc).To(BeTrue())
		})
	})
})

/* Ended by AICoder, pid:ta88b54592e705014ee10b9ff0e38b8ce277073e */

