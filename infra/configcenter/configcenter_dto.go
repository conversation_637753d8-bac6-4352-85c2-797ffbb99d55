package configcenter

import "sync"

var (
	faultanalaSwitchValue bool
	retentionSwitchValue  bool
	retentionDayValue     int
	retentionNumberValue  int
	mu                    sync.Mutex
)

func GetFaultAnalaSwitchValue() bool {
	mu.Lock()
	defer mu.Unlock()
	return faultanalaSwitchValue
}

func SetFaultAnalaSwitchValue(val bool) {
	mu.Lock()
	defer mu.Unlock()
	faultanalaSwitchValue = val
}

func GetRetentionSwitchValue() bool {
	mu.Lock()
	defer mu.Unlock()
	return retentionSwitchValue
}

func SetRetentionSwitchValue(val bool) {
	mu.Lock()
	defer mu.Unlock()
	retentionSwitchValue = val
}

func GetRetentionDayValue() int {
	mu.Lock()
	defer mu.Unlock()
	return retentionDayValue
}

func SetRetentionDayValue(val int) {
	mu.Lock()
	defer mu.Unlock()
	retentionDayValue = val
}

func GetRetentionNumberValue() int {
	mu.Lock()
	defer mu.Unlock()
	return retentionNumberValue
}

func SetRetentionNumberValue(val int) {
	mu.Lock()
	defer mu.Unlock()
	retentionNumberValue = val
}
