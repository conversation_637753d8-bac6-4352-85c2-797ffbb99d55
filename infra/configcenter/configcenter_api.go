package configcenter

import (
	"cwsm/infra/authorization"
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strconv"
	"sync"
	"time"

	"cwsm/tools/commontools/otcp/configcenter"

	"zte.com.cn/cms/crmX/commontools/infa/util"

	"zte.com.cn/cms/crmX/commontools-base/restful"

	"zte.com.cn/cms/crmX/commontools-msb/provider"
)

/* Started by AICoder, pid:1c85db85dcz13a214ac30a51e0c891271821d93d */
func RegisterVcjobRetentionConfigToConfigCenter() {
	defer func() {
		if err := recover(); err != nil {
			logger.Error("RegisterVcjobRetentionConfigToConfigCenter panic:%v,stack:%s", err, string(debug.Stack()))
		}
	}()

	body, err := util.ReadFile(util.GetFilePath("conf/RegisterVcjobRetentionConfig.json"))
	if err != nil {
		logger.Errorf("Read file from RegisterVcjobRetentionConfig.json failed:%s", err.Error())
		return
	}

	respCode, _ := configcenter.RegisterToConfigCenter(body)
	for respCode >= 400 {
		time.Sleep(time.Duration(10) * time.Second)
		respCode, _ = configcenter.RegisterToConfigCenter(body)
	}
}

/* Ended by AICoder, pid:1c85db85dcz13a214ac30a51e0c891271821d93d */

/* Started by AICoder, pid:ncf0dq8a9dcfef214b2208d200d69e28b9528530 */
func GetSwitchAndDayAndNumberConfigCenterValue() {
	if err := GetConfigCenterValue(constant.VcjobFaultanalaSwitchConfig, constant.ResourceFaultanalaSwitch); err != nil {
		logger.Errorf("Failed to get config for faultanala switch: %v", err)
	}

	if err := GetConfigCenterValue(constant.VcjobRetentionConfig, constant.ResourceRetentionSwitch); err != nil {
		logger.Errorf("Failed to get config for retention switch: %v", err)
	}

	if err := GetConfigCenterValue(constant.VcjobRetentionConfig, constant.ResourceRetentionDay); err != nil {
		logger.Errorf("Failed to get config for retention day: %v", err)
	}

	if err := GetConfigCenterValue(constant.VcjobRetentionConfig, constant.ResourceRetentionNumber); err != nil {
		logger.Errorf("Failed to get config for retention number: %v", err)
	}

}

/* Ended by AICoder, pid:ncf0dq8a9dcfef214b2208d200d69e28b9528530 */

/* Started by AICoder, pid:72c69h67e5z592a1440b096a600e15346821a2d9 */
func GetConfigCenterValue(group string, resource string) error {
	url := fmt.Sprintf("%s/%s/%s",
		constant.ConfigcenterConfigserver, group, resource)

	Configcenter, _, err := provider.Get(url, restful.DefaultHeaders(), constant.ConfigcenterName, "v1")
	if err != nil {
		logger.Errorf("HTTP GET failed, url: %s, error: %v", url, err)
		return err
	}

	var result map[string]interface{}
	if err = json.Unmarshal(Configcenter, &result); err != nil {
		logger.Errorf("JSON Unmarshal error: %v, response body: %s", err, string(Configcenter))
		return fmt.Errorf("json.Unmarshal error: %v", err)
	}

	isUpdateConfigSwitchValuesTrue := UpdateConfigSwitchValues(resource, result)
	if !isUpdateConfigSwitchValuesTrue {
		logger.Errorf("get update config switch values failed")
	}
	return nil
}

/* Ended by AICoder, pid:72c69h67e5z592a1440b096a600e15346821a2d9 */

/* Started by AICoder, pid:x02e83d4fd96f7514c43091930d8eb56b616714a */
func UpdateConfigSwitchValues(resource string, result map[string]interface{}) bool {
	if value, ok := result["value"].(string); ok {
		switch resource {
		case "resource-faultanala-switch":
			boolValue, err := strconv.ParseBool(value)
			if err != nil {
				logger.Errorf("Invalid value for resource-faultanala-switch: %v, expected bool", value)
				return false
			}

			SetFaultAnalaSwitchValue(boolValue)
			UpdateValueOfFaultanalySwitch()
			return true

		case "resource-retention-day":
			intValue, err := strconv.Atoi(value)
			if err != nil {
				logger.Errorf("Invalid value for resource-retention-day: %v, expected int", value)
				return false
			}

			SetRetentionDayValue(intValue)
			return true

		case "resource-retention-switch":
			boolValue, err := strconv.ParseBool(value)
			if err != nil {
				logger.Errorf("Invalid value for resource-retention-switch: %v, expected bool", value)
				return false
			}

			SetRetentionSwitchValue(boolValue)
			return true

		case "resource-retention-number":
			intValue, err := strconv.Atoi(value)
			if err != nil {
				logger.Errorf("Invalid value for resource-retention-number: %v, expected int", value)
				return false
			}

			SetRetentionNumberValue(intValue)
			return true
		}
	} else {
		logger.Errorf("No value found in result for resource: %s", resource)
		return false
	}
	return false
}

/* Ended by AICoder, pid:x02e83d4fd96f7514c43091930d8eb56b616714a */

/* Started by AICoder, pid:0ac44ca63fe60a514c29089e908e6712c985defd */
func ActivateVcjobConfig(clusterId string, reqBody []byte) (interface{}, error) {
	keywords := wsm.VcjobEventsWsmKeywords{
		ClusterId: clusterId,
	}

	vcjobcfg, err, suc := wsm.VcjobOneEventsHandler(keywords, reqBody, "post")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}
	return vcjobcfg, nil
}

/* Ended by AICoder, pid:0ac44ca63fe60a514c29089e908e6712c985defd */

/* Started by AICoder, pid:m971en6f48035a514a250992f0ceb42aede62a77 */
func UpdateValueOfFaultanalySwitch() {
	uuidList, err := authorization.GetClusterUuidFromPvrmByMap()
	if err != nil {
		logger.Errorf("Error getting UUID list: %v", err)
	}

	faultanalaSwitchValue := GetFaultAnalaSwitchValue()

	var wg sync.WaitGroup

	for _, uuidMap := range uuidList {
		if uuid, ok := uuidMap["uuid"].(string); ok {
			wg.Add(1)
			go func(uuid string) {
				defer wg.Done()
				logger.Infof("UpdateValueOfFaultanalySwitch uuid:%s", uuid)
				errPostValueOfFaultanalySwitch := PostValueOfFaultanalySwitch(uuid, faultanalaSwitchValue)
				if errPostValueOfFaultanalySwitch != nil {
					logger.Errorf("Error comparing status of switch for UUID %s: %v", uuid, errPostValueOfFaultanalySwitch)
				}
			}(uuid)
		}
	}

	wg.Wait()
}

/* Ended by AICoder, pid:m971en6f48035a514a250992f0ceb42aede62a77 */

/* Started by AICoder, pid:s43a9y185bq792414d950813a024b8200070424b */
func PostValueOfFaultanalySwitch(uuid string, faultanalaSwitchValue bool) error {
	if faultanalaSwitchValue {
		reqBody := []byte(`{"faultAnalaSwitch": "1"}`)
		_, err := ActivateVcjobConfig(uuid, reqBody)
		if err != nil {
			return fmt.Errorf("failed to activate config: %w", err)
		}
		return nil
	} else {
		reqBody := []byte(`{"faultAnalaSwitch": "0"}`)
		_, err := ActivateVcjobConfig(uuid, reqBody)
		if err != nil {
			return fmt.Errorf("failed to activate config: %w", err)
		}
		return nil
	}
}

/* Ended by AICoder, pid:s43a9y185bq792414d950813a024b8200070424b */

/* Started by AICoder, pid:b1604t6692dbf93149cd082310bf1429c35479c9 */
func GetNotificationTypeForChangeSwitchDayNumber(notificationType string, notification map[string]interface{}) bool {
	switch notificationType {
	case "startup":
		GetSwitchAndDayAndNumberConfigCenterValue()
		return true
	case "refresh":
		if microservices, ok := notification["microservices"].([]interface{}); ok && len(microservices) > 0 {
			AnalysisMicroservices(microservices)
		} else {
			logger.Errorf("Microservices is not a list or is empty: %v", notification["microservices"])
		}
		return true

	case "uploadAllNotification":
		return true

	default:
		logger.Errorf("get notificationtype for change switch day number failed")
		return false
	}
}

/* Ended by AICoder, pid:b1604t6692dbf93149cd082310bf1429c35479c9 */

/* Started by AICoder, pid:nfe601c79cg1c40143290be2f06f311bf5237589 */
func AnalysisMicroservices(microservices []interface{}) {
	if microservice, ok := microservices[0].(map[string]interface{}); ok {
		if changedKeys, ok := microservice["changedKeys"].([]interface{}); ok {
			for _, key := range changedKeys {
				AnalysisKeyStr(key)
			}
		} else {
			logger.Errorf("Changed keys not found or not a list: %v", microservice["changedKeys"])
		}
	} else {
		logger.Errorf("Failed to assert microservice: %v", microservices[0])
	}
}

/* Ended by AICoder, pid:nfe601c79cg1c40143290be2f06f311bf5237589 */

/* Started by AICoder, pid:q8704c73776eccf149ce0ba5300aad109eb23c02 */
func AnalysisKeyStr(key interface{}) {
	if keyStr, ok := key.(string); ok {
		group, isGroupOrKeystrTrue := GetGroupAndKeyStr(keyStr)
		if !isGroupOrKeystrTrue {
			logger.Errorf("Failed to get config for group or keystr")
		} else {
			if err := GetConfigCenterValue(group, keyStr); err != nil {
				logger.Errorf("Failed to get config for %s: %v", keyStr, err)
			}
		}
	}
}

/* Ended by AICoder, pid:q8704c73776eccf149ce0ba5300aad109eb23c02 */

/* Started by AICoder, pid:dc89dh4b1c555c31448609e8d02ed8110634850d */
func GetGroupAndKeyStr(keyStr string) (string, bool) {
	var group string
	switch keyStr {
	case constant.ResourceFaultanalaSwitch:
		group = constant.VcjobFaultanalaSwitchConfig
		return group, true
	case constant.ResourceRetentionSwitch, constant.ResourceRetentionDay, constant.ResourceRetentionNumber:
		group = constant.VcjobRetentionConfig
		return group, true
	default:
		logger.Errorf("Unknown changed key: %s", keyStr)
		return "", false
	}
}

/* Ended by AICoder, pid:dc89dh4b1c555c31448609e8d02ed8110634850d */
