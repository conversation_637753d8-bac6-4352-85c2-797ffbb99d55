package configcenter

import (
	"cwsm/infra/authorization"
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/otcp/configcenter"
	"encoding/json"
	"errors"
	"fmt"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"zte.com.cn/cms/crmX/commontools-base/restful"
	"zte.com.cn/cms/crmX/commontools-msb/provider"
)

/* Started by AICoder, pid:h3eaak5333qa88014ecb0872f038d1405ed737a8 */
var _ = Describe("TestRegisterVcjobRetentionConfigToConfigCenter", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When reading the config file succeeds", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(util.ReadFile, func(path string) ([]byte, error) {
				return []byte(`{"key": "value"}`), nil
			})

			patcher.ApplyFunc(configcenter.RegisterToConfigCenter, func(body []byte) (int, error) {
				return 200, nil // Simulate successful registration
			})
		})

		It("should register the config successfully", func() {
			RegisterVcjobRetentionConfigToConfigCenter()
			Expect(true).To(BeTrue())
		})
	})

	Context("When reading the config file fails", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(util.ReadFile, func(path string) ([]byte, error) {
				return nil, errors.New("file not found")
			})
		})

		It("should log an error when reading the config file fails", func() {
			RegisterVcjobRetentionConfigToConfigCenter()
			Expect(true).To(BeTrue())
		})
	})

	Context("When a panic occurs", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(util.ReadFile, func(path string) ([]byte, error) {
				panic("unexpected panic") // Simulate a panic
			})
		})

		It("should recover from panic and log the error", func() {
			RegisterVcjobRetentionConfigToConfigCenter()
			Expect(true).To(BeTrue())
		})
	})
})

/* Ended by AICoder, pid:h3eaak5333qa88014ecb0872f038d1405ed737a8 */

/* Started by AICoder, pid:oe7d7v965cqd57214f9b0a29118ba826a00358cd */
var _ = Describe("TestGetSwitchAndDayAndNumberConfigCenterValue", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When fetching config values successfully", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				response := map[string]interface{}{
					"value": "true",
				}
				responseBody, _ := json.Marshal(response)
				return responseBody, 200, nil
			})
		})

		It("should retrieve and update all configuration values correctly", func() {
			GetSwitchAndDayAndNumberConfigCenterValue()
			Expect(GetFaultAnalaSwitchValue()).To(BeTrue())
			Expect(GetRetentionSwitchValue()).To(BeTrue())
		})
	})

	Context("When fetching faultanala switch config fails", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(GetConfigCenterValue, func(key string, resource string) error {
				if key == constant.VcjobFaultanalaSwitchConfig && resource == constant.ResourceFaultanalaSwitch {
					return errors.New("failed to get faultanala switch config")
				}
				return nil
			})
		})

		It("should log an error for faultanala switch", func() {
			GetSwitchAndDayAndNumberConfigCenterValue()

		})
	})

	Context("When fetching retention switch config fails", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(GetConfigCenterValue, func(key string, resource string) error {
				if key == constant.VcjobRetentionConfig && resource == constant.ResourceRetentionSwitch {
					return errors.New("failed to get retention switch config")
				}
				return nil
			})
		})

		It("should log an error for retention switch", func() {
			GetSwitchAndDayAndNumberConfigCenterValue()

		})
	})

	Context("When fetching retention day config fails", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(GetConfigCenterValue, func(key string, resource string) error {
				if key == constant.VcjobRetentionConfig && resource == constant.ResourceRetentionDay {
					return errors.New("failed to get retention day config")
				}
				return nil
			})
		})

		It("should log an error for retention day", func() {
			GetSwitchAndDayAndNumberConfigCenterValue()

		})
	})

	Context("When fetching retention number config fails", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(GetConfigCenterValue, func(key string, resource string) error {
				if key == constant.VcjobRetentionConfig && resource == constant.ResourceRetentionNumber {
					return errors.New("failed to get retention number config")
				}
				return nil
			})
		})

		It("should log an error for retention number", func() {
			GetSwitchAndDayAndNumberConfigCenterValue()

		})
	})
})

/* Ended by AICoder, pid:oe7d7v965cqd57214f9b0a29118ba826a00358cd */

/* Started by AICoder, pid:qcaddg67abl178014a6d0b6581effa0b8962e433 */
var _ = Describe("TestGetConfigCenterValue", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When fetching config center values successfully", func() {
		It("should return no error and update values correctly", func() {
			patcher.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				response := map[string]interface{}{
					"value": "true",
				}
				responseBody, _ := json.Marshal(response)
				return responseBody, 200, nil // Change here to match the signature
			})

			// Call the function
			err := GetConfigCenterValue("test-group", "resource-faultanala-switch")

			// Verify results
			Expect(err).To(BeNil())
			Expect(GetFaultAnalaSwitchValue()).To(BeTrue())
		})
	})

	Context("When HTTP GET fails", func() {
		It("should return an error", func() {

			patcher.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, 0, errors.New("http get failed")
			})

			err := GetConfigCenterValue("test-group", "resource-faultanala-switch")
			Expect(err).ToNot(BeNil())
			Expect(err.Error()).To(ContainSubstring("http get failed"))
		})
	})

	Context("When JSON unmarshal fails", func() {
		It("should return an error", func() {
			patcher.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return []byte("invalid json"), 200, nil
			})

			err := GetConfigCenterValue("test-group", "resource-faultanala-switch")
			Expect(err).ToNot(BeNil())
			Expect(err.Error()).To(ContainSubstring("json.Unmarshal error: invalid character 'i' looking for beginning of value"))
		})
	})

	Context("When UpdateConfigSwitchValues fails", func() {
		It("should log an error but not return it", func() {
			patcher.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				response := map[string]interface{}{
					"value": "not_a_bool", // Invalid value for boolean
				}
				responseBody, _ := json.Marshal(response)
				return responseBody, 200, nil
			})

			err := GetConfigCenterValue("test-group", "resource-faultanala-switch")
			Expect(err).To(BeNil())                         // Should not return an error
			Expect(GetFaultAnalaSwitchValue()).To(BeTrue()) // Should not update the value
		})
	})
})

/* Ended by AICoder, pid:qcaddg67abl178014a6d0b6581effa0b8962e433 */

/* Started by AICoder, pid:f0e69mfeaecf622148480acb31ad620b8a43ebda */
var _ = Describe("TestUpdateConfigSwitchValues", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When resource-faultanala-switch receives a valid boolean value", func() {
		It("should update FaultanalaSwitchValue and return true", func() {
			result := map[string]interface{}{"value": "true"}
			success := UpdateConfigSwitchValues("resource-faultanala-switch", result)

			Expect(success).To(BeTrue())
			Expect(GetFaultAnalaSwitchValue()).To(BeTrue())
		})
	})

	Context("When resource-faultanala-switch receives an invalid boolean value", func() {
		It("should log an error and return false", func() {
			result := map[string]interface{}{"value": "invalid"}
			success := UpdateConfigSwitchValues("resource-faultanala-switch", result)

			Expect(success).To(BeFalse())
			Expect(GetFaultAnalaSwitchValue()).To(BeTrue()) // Assuming default is false
		})
	})

	Context("When resource-retention-day receives a valid integer value", func() {
		It("should update RetentionDayValue and return true", func() {
			result := map[string]interface{}{"value": "30"}
			success := UpdateConfigSwitchValues("resource-retention-day", result)

			Expect(success).To(BeTrue())
			Expect(GetRetentionDayValue()).To(Equal(30))
		})
	})

	Context("When resource-retention-day receives an invalid integer value", func() {
		It("should log an error and return false", func() {
			result := map[string]interface{}{"value": "invalid"}
			success := UpdateConfigSwitchValues("resource-retention-day", result)

			Expect(success).To(BeFalse())
			Expect(GetRetentionDayValue()).To(Equal(30)) // Assuming default is 0
		})
	})

	Context("When resource-retention-switch receives a valid boolean value", func() {
		It("should update RetentionSwitchValue and return true", func() {
			result := map[string]interface{}{"value": "false"}
			success := UpdateConfigSwitchValues("resource-retention-switch", result)

			Expect(success).To(BeTrue())
			Expect(GetRetentionSwitchValue()).To(BeFalse())
		})
	})

	Context("When resource-retention-switch receives an invalid boolean value", func() {
		It("should log an error and return false", func() {
			result := map[string]interface{}{"value": "invalid"}
			success := UpdateConfigSwitchValues("resource-retention-switch", result)

			Expect(success).To(BeFalse())
			Expect(GetRetentionSwitchValue()).To(BeFalse()) // Assuming default is true
		})
	})

	Context("When resource-retention-number receives a valid integer value", func() {
		It("should update RetentionNumberValue and return true", func() {
			result := map[string]interface{}{"value": "100"}
			success := UpdateConfigSwitchValues("resource-retention-number", result)

			Expect(success).To(BeTrue())
			Expect(GetRetentionNumberValue()).To(Equal(100))
		})
	})

	Context("When resource-retention-number receives an invalid integer value", func() {
		It("should log an error and return false", func() {
			result := map[string]interface{}{"value": "invalid"}
			success := UpdateConfigSwitchValues("resource-retention-number", result)

			Expect(success).To(BeFalse())
			Expect(GetRetentionNumberValue()).To(Equal(100)) // Assuming default is 0
		})
	})

	Context("When no value is found in result", func() {
		It("should log an error and return false", func() {
			result := map[string]interface{}{}
			success := UpdateConfigSwitchValues("resource-faultanala-switch", result)

			Expect(success).To(BeFalse())
			Expect(GetFaultAnalaSwitchValue()).To(BeTrue()) // Assuming default is false
		})
	})
})

/* Ended by AICoder, pid:f0e69mfeaecf622148480acb31ad620b8a43ebda */

/* Started by AICoder, pid:nd2d4a93748a8f3140cf0b9520a451892f942c4c */
var _ = Describe("TestGetNotificationTypeForChangeSwitchDayNumber", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When notification type is startup", func() {
		It("should call GetSwitchAndDayAndNumberConfigCenterValue and return true", func() {
			patcher.ApplyFunc(GetSwitchAndDayAndNumberConfigCenterValue, func() {
				return
			})

			result := GetNotificationTypeForChangeSwitchDayNumber("startup", nil)
			Expect(result).To(BeTrue())
		})

		It("should log an error if GetSwitchAndDayAndNumberConfigCenterValue fails", func() {
			patcher.ApplyFunc(GetSwitchAndDayAndNumberConfigCenterValue, func() {
				return
			})

			result := GetNotificationTypeForChangeSwitchDayNumber("startup", nil)
			Expect(result).To(BeTrue())
		})
	})

	Context("When notification type is refresh", func() {
		It("should call AnalysisMicroservices if microservices are present", func() {
			microservices := []interface{}{
				map[string]interface{}{
					"changedKeys": []interface{}{"key1", "key2"},
				},
			}
			notification := map[string]interface{}{
				"microservices": microservices,
			}

			patcher.ApplyFunc(AnalysisMicroservices, func(ms []interface{}) {
			})

			result := GetNotificationTypeForChangeSwitchDayNumber("refresh", notification)
			Expect(result).To(BeTrue())
		})

		It("should log an error if microservices is not a valid list", func() {
			notification := map[string]interface{}{
				"microservices": "not_a_list",
			}

			result := GetNotificationTypeForChangeSwitchDayNumber("refresh", notification)
			Expect(result).To(BeTrue())
		})

		It("should log an error if microservices is empty", func() {
			notification := map[string]interface{}{
				"microservices": []interface{}{},
			}

			result := GetNotificationTypeForChangeSwitchDayNumber("refresh", notification)
			Expect(result).To(BeTrue())
		})
	})

	Context("When notification type is uploadAllNotification", func() {
		It("should log the appropriate info and return true", func() {
			result := GetNotificationTypeForChangeSwitchDayNumber("uploadAllNotification", nil)
			Expect(result).To(BeTrue())
		})
	})

	Context("When notification type is unknown", func() {
		It("should log an error and return false", func() {
			result := GetNotificationTypeForChangeSwitchDayNumber("unknown", nil)
			Expect(result).To(BeFalse())
		})
	})
})

/* Ended by AICoder, pid:nd2d4a93748a8f3140cf0b9520a451892f942c4c */

/* Started by AICoder, pid:vf59dn217ar6f791402d088d60fec3754b43eaba */
var _ = Describe("TestActivateVcjobConfig", func() {
	var (
		reqParam wsm.VcjobEventsWsmKeywords
		reqBody  []byte
		patcher  *gomonkey.Patches
	)

	BeforeEach(func() {
		reqParam = wsm.VcjobEventsWsmKeywords{
			ClusterId: "cluster123",
		}
		reqBody, _ = json.Marshal(map[string]interface{}{"key": "value"}) // 模拟请求体
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.VcjobOneEventsHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.VcjobOneEventsHandler, func(keywords wsm.VcjobEventsWsmKeywords, reqBody []byte, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := ActivateVcjobConfig(reqParam.ClusterId, reqBody)

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.VcjobOneEventsHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"id": 1, "status": "activated"}
			patcher.ApplyFunc(wsm.VcjobOneEventsHandler, func(keywords wsm.VcjobEventsWsmKeywords, reqBody []byte, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := ActivateVcjobConfig(reqParam.ClusterId, reqBody)

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.VcjobOneEventsHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.VcjobOneEventsHandler, func(keywords wsm.VcjobEventsWsmKeywords, reqBody []byte, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := ActivateVcjobConfig(reqParam.ClusterId, reqBody)

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.VcjobOneEventsHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.VcjobOneEventsHandler, func(keywords wsm.VcjobEventsWsmKeywords, reqBody []byte, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := ActivateVcjobConfig(reqParam.ClusterId, reqBody)

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil()) // Expecting nil result for empty response
		})
	})
})

/* Ended by AICoder, pid:vf59dn217ar6f791402d088d60fec3754b43eaba */

/* Started by AICoder, pid:sd423wc2273464e146a508fcc0ca6c4b4cf179c0 */
var _ = Describe("TestGetGroupAndKeyStr", func() {
	Context("When keyStr is ResourceFaultanalaSwitch", func() {
		It("should return the correct group and true", func() {
			group, ok := GetGroupAndKeyStr(constant.ResourceFaultanalaSwitch)
			Expect(ok).To(BeTrue())
			Expect(group).To(Equal(constant.VcjobFaultanalaSwitchConfig))
		})
	})

	Context("When keyStr is ResourceRetentionSwitch", func() {
		It("should return the correct group and true", func() {
			group, ok := GetGroupAndKeyStr(constant.ResourceRetentionSwitch)
			Expect(ok).To(BeTrue())
			Expect(group).To(Equal(constant.VcjobRetentionConfig))
		})
	})

	Context("When keyStr is ResourceRetentionDay", func() {
		It("should return the correct group and true", func() {
			group, ok := GetGroupAndKeyStr(constant.ResourceRetentionDay)
			Expect(ok).To(BeTrue())
			Expect(group).To(Equal(constant.VcjobRetentionConfig))
		})
	})

	Context("When keyStr is ResourceRetentionNumber", func() {
		It("should return the correct group and true", func() {
			group, ok := GetGroupAndKeyStr(constant.ResourceRetentionNumber)
			Expect(ok).To(BeTrue())
			Expect(group).To(Equal(constant.VcjobRetentionConfig))
		})
	})

	Context("When keyStr is unknown", func() {
		It("should return an empty group and false", func() {
			group, ok := GetGroupAndKeyStr("unknown_key")
			Expect(ok).To(BeFalse())
			Expect(group).To(BeEmpty())
		})
	})
})

/* Ended by AICoder, pid:sd423wc2273464e146a508fcc0ca6c4b4cf179c0 */

/* Started by AICoder, pid:i701ddb2cbp7616149f70bf1a039a552cba5e7d3 */
var _ = Describe("TestAnalysisMicroservices", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When microservices contains a valid microservice with changedKeys", func() {
		It("should call AnalysisKeyStr for each changed key", func() {
			microservices := []interface{}{
				map[string]interface{}{
					"changedKeys": []interface{}{"key1", "key2"},
				},
			}

			patcher.ApplyFunc(AnalysisKeyStr, func(key interface{}) {
			})

			AnalysisMicroservices(microservices)
		})
	})

	Context("When microservices does not contain a valid microservice", func() {
		It("should log an error", func() {
			microservices := []interface{}{"invalid_microservice"}
			AnalysisMicroservices(microservices)
		})
	})

	Context("When changedKeys is not a list", func() {
		It("should log an error", func() {
			microservices := []interface{}{
				map[string]interface{}{
					"changedKeys": "not_a_list",
				},
			}
			AnalysisMicroservices(microservices)
		})
	})

	Context("When changedKeys is not found", func() {
		It("should log an error", func() {
			microservices := []interface{}{
				map[string]interface{}{},
			}
			AnalysisMicroservices(microservices)
		})
	})
})

/* Ended by AICoder, pid:i701ddb2cbp7616149f70bf1a039a552cba5e7d3 */

/* Started by AICoder, pid:wd5f8o1b0f0cc4d14dd60bff20948a64a8a69adf */
var _ = Describe("TestAnalysisKeyStr", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When key is a valid string and group is found", func() {
		It("should call GetConfigCenterValue successfully", func() {
			key := constant.ResourceFaultanalaSwitch
			group := constant.VcjobFaultanalaSwitchConfig

			patcher.ApplyFunc(GetGroupAndKeyStr, func(keyStr string) (string, bool) {
				return group, true
			})

			patcher.ApplyFunc(GetConfigCenterValue, func(group string, resource string) error {
				return nil
			})

			AnalysisKeyStr(key)
		})
	})

	Context("When key is a valid string but group is not found", func() {
		It("should log an error", func() {
			key := "unknown_key"

			patcher.ApplyFunc(GetGroupAndKeyStr, func(keyStr string) (string, bool) {
				return "", false
			})

			AnalysisKeyStr(key)
		})
	})

	Context("When key is not a string", func() {
		It("should not call GetGroupAndKeyStr", func() {
			key := 12345
			AnalysisKeyStr(key)
		})
	})

	Context("When GetConfigCenterValue fails", func() {
		It("should log an error if unable to get config", func() {
			key := constant.ResourceRetentionSwitch
			group := constant.VcjobRetentionConfig

			patcher.ApplyFunc(GetGroupAndKeyStr, func(keyStr string) (string, bool) {
				return group, true
			})

			patcher.ApplyFunc(GetConfigCenterValue, func(group string, resource string) error {
				return errors.New("config center error")
			})

			AnalysisKeyStr(key)
		})
	})
})

/* Ended by AICoder, pid:wd5f8o1b0f0cc4d14dd60bff20948a64a8a69adf */

/* Started by AICoder, pid:l8144c3f63c770614a9b0a21a0287e620ca18e18 */
var _ = Describe("PostValueOfFaultanalySwitch", func() {
	var patches *gomonkey.Patches

	BeforeEach(func() {
		patches = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patches.Reset()
	})

	Context("When FaultanalaSwitchValue is true", func() {
		It("should successfully post value '1'", func() {
			patches.ApplyFunc(ActivateVcjobConfig, func(clusterId string, reqBody []byte) (interface{}, error) {
				return nil, nil // Simulate successful activation
			})

			// Set the switch value to true
			mu.Lock()
			faultanalaSwitchValue = true
			mu.Unlock()
			err := PostValueOfFaultanalySwitch("test-uuid", faultanalaSwitchValue)
			Expect(err).To(BeNil())
		})
	})

	Context("When FaultanalaSwitchValue is false", func() {
		It("should successfully post value '0'", func() {
			patches.ApplyFunc(ActivateVcjobConfig, func(clusterId string, reqBody []byte) (interface{}, error) {
				return nil, nil // Simulate successful activation
			})

			// Set the switch value to false
			mu.Lock()
			faultanalaSwitchValue = false
			mu.Unlock()
			err := PostValueOfFaultanalySwitch("test-uuid", faultanalaSwitchValue)
			Expect(err).To(BeNil())
		})
	})

	Context("When ActivateVcjobConfig fails and FaultanalaSwitchValue is true", func() {
		It("should return an error", func() {
			patches.ApplyFunc(ActivateVcjobConfig, func(clusterId string, reqBody []byte) (interface{}, error) {
				return nil, errors.New("failed to activate config") // Simulate activation failure
			})

			// Set the switch value to true
			mu.Lock()
			faultanalaSwitchValue = true
			mu.Unlock()
			err := PostValueOfFaultanalySwitch("test-uuid", faultanalaSwitchValue)
			Expect(err).ToNot(BeNil())
			Expect(err.Error()).To(ContainSubstring("failed to activate config"))
		})
	})

	Context("When ActivateVcjobConfig fails and FaultanalaSwitchValue is false", func() {
		It("should return an error", func() {
			patches.ApplyFunc(ActivateVcjobConfig, func(clusterId string, reqBody []byte) (interface{}, error) {
				return nil, errors.New("failed to activate config") // Simulate activation failure
			})

			// Set the switch value to false
			mu.Lock()
			faultanalaSwitchValue = false
			mu.Unlock()
			err := PostValueOfFaultanalySwitch("test-uuid", faultanalaSwitchValue)
			Expect(err).ToNot(BeNil())
			Expect(err.Error()).To(ContainSubstring("failed to activate config"))
		})
	})
})

/* Ended by AICoder, pid:l8144c3f63c770614a9b0a21a0287e620ca18e18 */

/* Started by AICoder, pid:818c1k16684666114f3a09b9f01bc9645c1221a8 */
var _ = Describe("TestUpdateValueOfFaultanalySwitch", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetClusterUuidFromPvrmByMap returns valid UUIDs", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(authorization.GetClusterUuidFromPvrmByMap, func() ([]map[string]interface{}, error) {
				return []map[string]interface{}{
					{"uuid": "test-uuid-1"},
					{"uuid": "test-uuid-2"},
				}, nil
			})

			patcher.ApplyFunc(PostValueOfFaultanalySwitch, func(uuid string, faultanalaSwitchValue bool) error {
				return nil
			})
		})

		It("should process all UUIDs without errors", func() {
			UpdateValueOfFaultanalySwitch()
		})
	})

	Context("When GetClusterUuidFromPvrmByMap returns an error", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(authorization.GetClusterUuidFromPvrmByMap, func() ([]map[string]interface{}, error) {
				return nil, errors.New("failed to get UUIDs")
			})
		})

		It("should log the error and return without processing", func() {
			UpdateValueOfFaultanalySwitch()
		})
	})

	Context("When PostValueOfFaultanalySwitch returns an error", func() {
		BeforeEach(func() {
			patcher.ApplyFunc(authorization.GetClusterUuidFromPvrmByMap, func() ([]map[string]interface{}, error) {
				return []map[string]interface{}{
					{"uuid": "test-uuid-1"},
				}, nil
			})

			patcher.ApplyFunc(PostValueOfFaultanalySwitch, func(uuid string, faultanalaSwitchValue bool) error {
				return fmt.Errorf("failed to post value")
			})
		})

		It("should log the error when posting value fails", func() {
			UpdateValueOfFaultanalySwitch()
		})
	})
})

/* Ended by AICoder, pid:818c1k16684666114f3a09b9f01bc9645c1221a8 */
