package cwsmutils

import (
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"fmt"
	"log"
	"reflect"
	"strconv"
	"strings"
	"time"

	beego "github.com/beego/beego/v2/server/web"
)

type Controller struct {
	beego.Controller
}

func NewUuid(prefix string) string {
	return prefix + strconv.FormatInt(time.Now().UnixNano(), 10)
}

/* Started by AICoder, pid:cc9054d278e44d348463fca569ef99a1 */
func NewTimeUuid(prefix string) string {
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	strTimestamp := strconv.FormatInt(timestamp, 10)
	return prefix + strTimestamp
}

/* Ended by AICoder, pid:cc9054d278e44d348463fca569ef99a1 */

/* Started by AICoder, pid:d93365322eb5446cb5749d4280d4d7b7 */
func Struct2Map(in interface{}) (map[string]interface{}, error) {
	m := make(map[string]interface{})

	j, err := json.Marshal(in)
	if err != nil {
		return m, err
	}

	err = json.Unmarshal(j, &m)
	if err != nil {
		return m, err
	}

	var data = make(map[string]interface{})
	t := reflect.TypeOf(in)
	v := reflect.ValueOf(in)

	for i := 0; i < t.NumField(); i++ {
		for key := range m {
			if strings.EqualFold(key, t.Field(i).Name) {
				data[key] = v.Field(i).Interface()
			}
		}
	}

	return data, nil
}

/* Ended by AICoder, pid:d93365322eb5446cb5749d4280d4d7b7 */

/* Started by AICoder, pid:fda4b5a5155348fe9b88ebe99692a33e */
func StringToMap(s string) map[string]interface{} {
	var m map[string]interface{}
	err := json.Unmarshal([]byte(s), &m)
	if err != nil {
		log.Printf("string to map failed %s", s)
	}
	return m
}

/* Ended by AICoder, pid:fda4b5a5155348fe9b88ebe99692a33e */

/* Started by AICoder, pid:cffd7feafc0841538b8c3980196c0ab8 */
func MapSlicetoMap(in []map[string]interface{}) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	for _, m := range in {
		for key, value := range m {
			result[key] = value
		}
	}
	return result, nil
}

/* Ended by AICoder, pid:cffd7feafc0841538b8c3980196c0ab8 */

/* Started by AICoder, pid:4e7d863fd2404e81a91f9465a574f8db */
func StringToSlice(s string) ([]string, error) {
	var slice []string
	err := json.Unmarshal([]byte(s), &slice)
	if err != nil {
		logger.Errorf("string to slice failed %s", s)
		return slice, fmt.Errorf("string to slice failed %s", err.Error())
	}
	return slice, nil
}

/* Ended by AICoder, pid:4e7d863fd2404e81a91f9465a574f8db */

func SliceToString(s []string) (string, error) {
	bytes, err := json.Marshal(s)
	if err != nil {
		log.Printf("slice to string failed: %v", s)
		return "", fmt.Errorf("slice to string failed: %s", err.Error())
	}
	return string(bytes), nil
}

/* Started by AICoder, pid:9e2cff2ba0464c86a5704b6bb563d132 */
func StringToMapSlice(str string) []map[string]interface{} {
	var result []map[string]interface{}
	err := json.Unmarshal([]byte(str), &result)
	if err != nil {
		logger.Errorf("string to map slice failed %v", str)
	}
	return result
}

/* Ended by AICoder, pid:9e2cff2ba0464c86a5704b6bb563d132 */

/* Started by AICoder, pid:8a1bddeebf2246aca7f9fe7576bc2abf */
func PrintJsonData(input map[string]interface{}) {
	output, _ := json.MarshalIndent(input, "", "  ")
	logger.Errorf(string(output))
}

/* Ended by AICoder, pid:8a1bddeebf2246aca7f9fe7576bc2abf */

/* Started by AICoder, pid:7cd9c5ac43b94bb190a0844c02716f6e */
func PostPrintJsonData(input interface{}) {
	output, _ := json.MarshalIndent(input, "", "  ")
	logger.Errorf(string(output))
}

/* Ended by AICoder, pid:7cd9c5ac43b94bb190a0844c02716f6e */

func (c *Controller) ApiResponse(codeStatus int, data interface{}) {
	c.Ctx.Output.SetStatus(codeStatus)
	if data == nil {
		c.Data["json"] = map[string]string{}
	} else {
		c.Data["json"] = data
	}
	c.ServeJSON()
	if codeStatus >= 300 {
		logger.Debug("HttpResponse Method:%s, Url:%s, Code:%d, Body:%+v",
			c.Ctx.Request.Method, c.Ctx.Request.URL.Path, codeStatus, c.Data["json"])
	}
}

/* Started by AICoder, pid:943b0d534f4347e4aea4d68ed27bc361 */
func IsEleInSlice(slice []string, ele string) bool {
	for _, v := range slice {
		if v == ele {
			return true
		}
	}
	return false
}

/* Ended by AICoder, pid:943b0d534f4347e4aea4d68ed27bc361 */

/* Started by AICoder, pid:aeb19bcbfa314623bb09e36e6b5fa1c1 */
func CopyMap(in map[string]interface{}) map[string]interface{} {
	out := make(map[string]interface{})
	for k, v := range in {
		out[k] = v
	}
	return out
}

/* Ended by AICoder, pid:aeb19bcbfa314623bb09e36e6b5fa1c1 */
