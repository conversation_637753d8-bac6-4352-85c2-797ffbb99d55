package cwsmutils

import (
	"testing"

	"github.com/onsi/ginkgo"
	"github.com/onsi/gomega"
)

/* Started by AICoder, pid:m534c3d3ddu22f1143b50b7980c17d2d878599ac */
var _ = ginkgo.Describe("SliceToString", func() {
	ginkgo.Context("When input is valid", func() {
		ginkgo.It("should return a JSON string representation of the slice", func() {
			input := []string{"apple", "banana", "cherry"}
			expected := `["apple","banana","cherry"]`

			result, err := SliceToString(input)

			gomega.Expect(err).To(gomega.BeNil())
			gomega.Expect(result).To(gomega.Equal(expected))
		})
	})

	ginkgo.Context("When input is empty", func() {
		ginkgo.It("should return a JSON string representation of an empty slice", func() {
			input := []string{}
			expected := `[]`

			result, err := SliceToString(input)

			gomega.Expect(err).To(gomega.BeNil())
			gomega.Expect(result).To(gomega.Equal(expected))
		})
	})
})

/* Ended by AICoder, pid:m534c3d3ddu22f1143b50b7980c17d2d878599ac */

// Run the tests
func TestYourPackage(t *testing.T) {
	gomega.RegisterFailHandler(ginkgo.Fail)
	ginkgo.RunSpecs(t, "YourPackage Suite")
}
