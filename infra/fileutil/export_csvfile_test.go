package fileutil

import (
	"fmt"
	"io/ioutil"
	"os"

	"strings"
	"testing"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func TestCreateCsvFile(t *testing.T) {
	var _ = Describe("TestCreateCsvFile", func() {
		data := [][]string{
			{"gpuUuid", "内存占用", "aveGpuComputeUtilizationRate"},
			{"1", "33.6", "0.6"},
			{"2", "22.3", "0.5"},
		}
		filePath := "test.csv"
		Context("when create csv file ", func() {
			It("create success", func() {
				err := CreateCsvFile(filePath, data)
				Expect(err).To(BeNil())

				file, err := ioutil.ReadFile(filePath)
				fmt.Println(string(file))
				Expect(err).To(BeNil())

				content := []string{}
				for _, value := range data {
					content = append(content, strings.Join(value, ","))
				}

				fmt.Println(strings.Join(content, "\n"))
				Expect(string(file)).To(BeIdenticalTo(strings.Join(content, "\n")))
				err = os.Remove(filePath)

				Expect(err).To(BeNil())
			})
		})
	})
}
