package fileutil

import (
	"cwsm/tools/commontools/logger"
	"encoding/csv"
	"os"
	"path/filepath"
)

func CreateCsvFile(filePath string, data [][]string) error {
	f, err := os.OpenFile(filepath.Clean(filePath), os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0600)
	if err != nil {
		logger.Errorf("create csv file fail : ", err, filePath)
		return err
	}
	defer func() {
		err = f.Close()
		if err != nil {
			logger.Errorf("close file fail : ", err)
		}
	}()

	w := csv.NewWriter(f)
	err = w.Write<PERSON>ll(data)
	if err != nil {
		return err
	}
	w.Flush()
	return nil
}
