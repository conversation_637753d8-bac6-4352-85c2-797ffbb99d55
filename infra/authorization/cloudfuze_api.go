package authorization

import (
	"cwsm/infra/constant"
	"cwsm/infra/cwsmutils"
	"cwsm/tools/commontools/logger"
	"encoding/base64"
	"encoding/json"
	"errors"
	"net"

	"zte.com.cn/cms/crmX/commontools-base/restful"
	"zte.com.cn/cms/crmX/commontools-msb/provider"
)

// func ClusterId2EnvId(clusterId string) string {
// 	tmpId := uuid.FromStringOrNil(clusterId)
// 	envId := uuid.NewV5(tmpId, "").String()
// 	return envId
// }

func GetFromCloudfuzeToGetCloudEnv(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte,
	map[string][]string, int, error) {
	return restful.GetMethod(urlpath, reqHeaders, auth)
}

/* Started by AICoder, pid:76868o7001p9c88143820a32e02df71cdad2fc7c */
func PostTokensReqBody() (map[string]interface{}, error) {
	reqbody := PostTokens{
		Interfaces: []string{"all"},
		CataLog:    []string{"openpalette"},
		Refresh:    false,
	}
	res, err := cwsmutils.Struct2Map(reqbody)
	if err != nil {
		return nil, err
	}
	return res, nil
}

/* Ended by AICoder, pid:76868o7001p9c88143820a32e02df71cdad2fc7c */

func GetKstoneParamsFromCloudfuze(clusterId string, cloudEnv string) (CloudenvParams, error) {
	cloudenvUrl := GetCloudenvsUrl(clusterId, cloudEnv)
	rsp, rspCode, err := provider.Get(cloudenvUrl, restful.DefaultHeaders(), constant.CludFuzeName, "v1.0")
	if err != nil {
		logger.Errorf("get info from cloudfuze failed to get, status code: %v, err: %v", rspCode, err.Error())
		return CloudenvParams{}, err
	}
	logger.Info("get info from cloudfuze response code:%v", rspCode)
	var cloudenv CloudEnvironment
	if err = json.Unmarshal(rsp, &cloudenv); err != nil {
		logger.Errorf("get info from cloudfuze json.Unmarshal failed: %v", err)
		return CloudenvParams{}, err
	}
	params, err := parseEnvParams(cloudenv)
	if err != nil {
		logger.Errorf("get info from cloudfuze parse env params failed: %s", err.Error())
	}
	return params, err
}

func parseEnvParams(env CloudEnvironment) (CloudenvParams, error) {
	for _, endpoint := range env.Cloudenv.Endpoints {
		if endpoint.Name == "public-auth" {
			decoded, err := base64.StdEncoding.DecodeString(endpoint.Keys)
			if err != nil {
				logger.Errorf("parse env params decode failed: %s", err.Error())
				return CloudenvParams{}, err
			}

			params := CloudenvParams{
				URL:      endpoint.URL,
				UserName: endpoint.UserName,
				Keyword:  string(decoded),
				SSL: &restful.SSLAuth{
					Method:      endpoint.SslAuthentication.Method,
					SSLProtocol: endpoint.SslAuthentication.SSLProtocol,
					RootCert:    endpoint.SslAuthentication.RootCert,
					ClientCert:  endpoint.SslAuthentication.ClientCert,
					ClientKey:   endpoint.SslAuthentication.ClientKey,
				}}
			return params, nil
		}
	}

	return CloudenvParams{}, errors.New("not find public-auth endpoint")
}

func GetCloudenvsUrl(clusterId string, envId string) string {
	// envId := ClusterId2EnvId(clusterId)
	url := constant.CloudfuzeInnerCloudenvs + envId
	logger.Info("get cloudenvs url :%s", url)
	return url
}

/* Started by AICoder, pid:d8015hfadeb65191490708ad002fb00fcba5c214 */
func GetCloudenvsTokensUrl(envId string) string {
	url := constant.CloudfuzeCloudenvs + envId + "/tokens"
	logger.Info("get cloudenvs url :%s", url)
	return url
}

/* Ended by AICoder, pid:d8015hfadeb65191490708ad002fb00fcba5c214 */
func getIPType(ip string) string {
	netIP := net.ParseIP(ip)
	if netIP == nil {
		return "invalid ip"
	}
	if netIP.To4() != nil {
		return "IPv4"
	}
	if netIP.To16() != nil {
		return "IPv6"
	}
	return "unknown ip type"
}
