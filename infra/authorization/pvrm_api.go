package authorization

import (
	"cwsm/infra/constant"
	"cwsm/tools/commontools/logger"
	"net/http"

	"encoding/json"
	"fmt"

	"zte.com.cn/cms/crmX/commontools-base/restful"
	"zte.com.cn/cms/crmX/commontools-msb/provider"
)

func GetCloudenvsFromPvrm(clusterId string) (string, string, error) {
	body, rspCode, err := provider.Get(constant.PvrmContainerClusters, restful.DefaultHeaders(), constant.PvrmName, "v1.0")
	if err != nil {
		logger.Errorf("get cloud envs url from pvrm Query container clusters failed: status code: %v, err: %s", rspCode, err)
		return "", "", err
	}
	var containerClusters ContainerClusters
	if err = json.Unmarshal(body, &containerClusters); err != nil {
		logger.Errorf("get cloud envs url from pvrm Query container clusters, body:%s json.Unmarshal failed:%s", string(body), err)
		return "", "", err
	}
	for _, containerCluster := range containerClusters.Clusters {
		if containerCluster.Uuid == clusterId {
			return containerCluster.ContainerUuid, containerCluster.Name, nil
		}
	}
	return "", "", fmt.Errorf("get cloud envs url from pvrm cannot find envid of clusterid %s", clusterId)
}

func GetInfoFromPvrm(envId string) (OpenpaletteInfo, error) {
	body, rspCode, err := provider.Get(constant.PvrmContainerClouds, restful.DefaultHeaders(), constant.PvrmName, "v1.0")
	if err != nil {
		logger.Errorf("get url from pvrm query container clouds failed: status code: %v, err: %s", rspCode, err)
		return OpenpaletteInfo{}, err
	}
	var containerClouds ContainerClouds
	if err = json.Unmarshal(body, &containerClouds); err != nil {
		logger.Errorf("get url from pvrm query container clouds, body:%s json.Unmarshal failed:%s", string(body), err)
		return OpenpaletteInfo{}, err
	}
	for _, containerCloud := range containerClouds.ContainerCloud {
		if containerCloud.Uuid == envId {
			if containerCloud.Status == "abnormal" {
				return OpenpaletteInfo{}, fmt.Errorf("status is abnormal envId %s", envId)
			}
			return OpenpaletteInfo{MsbURL: containerCloud.Uri,
				EnvType: containerCloud.EnvType,
				SSL: &restful.SSLAuth{
					Method:      restful.SSLMethod(containerCloud.SslAuthentication.Method),
					SSLProtocol: restful.SSLProtocol(containerCloud.SslAuthentication.SslProtocol),
					RootCert:    constant.DirectorCa + containerCloud.SslAuthentication.RootCert,
					ClientCert:  constant.DirectorCrt + containerCloud.SslAuthentication.ClientCert,
					ClientKey:   constant.DirectorCrt + containerCloud.SslAuthentication.ClientKey,
				}}, nil
		}
	}
	return OpenpaletteInfo{}, fmt.Errorf("get url from pvrm cannot find uri of envId %s", envId)
}

/* Started by AICoder, pid:31b91q11f2l61f6140a4090590c57e6e6b75bfb5 */
func GetClusterUuidFromPvrm(cloudUuid string) (string, error) {
	body, rspCode, err := provider.Get(constant.PvrmContainerClusters, restful.DefaultHeaders(), constant.PvrmName, "v1.0")
	if err != nil {
		logger.Errorf("Failed to query container clusters from PVRM: status code: %v, err: %s", rspCode, err)
		return "", err
	}
	var containerClustersCfg ContainerClustersCfg
	if err = json.Unmarshal(body, &containerClustersCfg); err != nil {
		logger.Errorf("Failed to unmarshal response body: %s, error: %s", string(body), err)
		return "", err
	}
	logger.Info("Container clusters configuration: %v", containerClustersCfg)

	for _, cluster := range containerClustersCfg.Clusters {
		if cluster.ContainerUuid == cloudUuid {
			logger.Info("Matched UUID found: %v", cluster.Uuid)
			return cluster.Uuid, nil // Return the matching UUID
		}
	}
	return "", fmt.Errorf("no matching UUID found")
}

/* Ended by AICoder, pid:31b91q11f2l61f6140a4090590c57e6e6b75bfb5 */

func GetVcjobInfoFromPvrm() ([]byte, error) {
	body, rspCode, err := provider.Get(constant.PvrmVcjobs, restful.DefaultHeaders(), constant.PvrmName, "v1.0")
	if err != nil {
		logger.Errorf("get vcjobInfo from pvrm Query container clusters failed: status code: %v, err: %s", rspCode, err)
		return nil, err
	}
	return body, nil
}

/* Started by AICoder, pid:j8770c104360e71145e309bb40ca173aff41bc6b */
func GetClusterUuidFromPvrmByMap() ([]map[string]interface{}, error) {
	body, rspCode, err := provider.Get(constant.PvrmContainerClusters, restful.DefaultHeaders(), constant.PvrmName, "v1.0")
	if err != nil {
		logger.Errorf("get cloud envs url from pvrm Query container clusters failed: status code: %v, err: %s", rspCode, err)
		return nil, err
	}
	logger.Info("GetClusterUuidFromPvrmByMap get body:%s ", string(body))
	var containerClustersCfg ContainerClustersCfg
	if err = json.Unmarshal(body, &containerClustersCfg); err != nil {
		logger.Errorf("get cloud envs url from pvrm Query container clusters, body:%s json.Unmarshal failed:%s", string(body), err)
		return nil, err
	}
	var uuid []map[string]interface{}
	for _, cluster := range containerClustersCfg.Clusters {
		clusterMap := map[string]interface{}{
			"uuid": cluster.Uuid,
		}
		uuid = append(uuid, clusterMap)
	}
	logger.Info("finished GetClusterUuidFromPvrmByMap")
	return uuid, nil
}

/* Ended by AICoder, pid:j8770c104360e71145e309bb40ca173aff41bc6b */

func PostVcjobFaultInfo(request []byte, vcjobId string) error {
	_, rspCode, err := provider.Post(constant.FaultManagementFault, restful.DefaultHeaders(), request, constant.FaultManagementName, "v1.0")
	if err != nil {
		logger.Errorf("post vcjob %v faultInfo failed: status code: %v, err: %s", vcjobId, rspCode, err)
		return err
	}
	if rspCode != http.StatusOK {
		logger.Errorf("post vcjob %v faultInfo failed,status: %v", vcjobId, rspCode)
		return fmt.Errorf("post vcjob %v faultInfo failed,status: %v", vcjobId, rspCode)
	}
	return nil
}

func PutVcjobFaultInfo(request []byte, vcjobId string) error {
	_, rspCode, err := provider.Put(constant.FaultManagementFault, restful.DefaultHeaders(), request, constant.FaultManagementName, "v1.0")
	if err != nil {
		logger.Errorf("put vcjob %v faultInfo failed: status code: %v, err: %s", vcjobId, rspCode, err)
		return err
	}
	if rspCode != http.StatusOK {
		logger.Errorf("put vcjob %v faultInfo failed,status: %v", vcjobId, rspCode)
		return fmt.Errorf("put vcjob %v faultInfo failed,status: %v", vcjobId, rspCode)
	}
	return nil
}
