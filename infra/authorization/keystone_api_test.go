package authorization

import (
	"encoding/json"
	"errors"
	"net/http"

	. "github.com/onsi/gomega"

	. "github.com/onsi/ginkgo"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	"zte.com.cn/cms/crmX/commontools-base/restful"
	"zte.com.cn/cms/crmX/commontools-msb/provider"
)

/* Started by AICoder, pid:pbefcle285173bf14d6d0b0a31dcfe2a3be2d6b5 */
var _ = Describe("GetOpenpaletteInfo", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When getting Openpalette info succeeds", func() {
		It("should return OpenpaletteInfo without error", func() {
			clusterId := "mock-cluster-id"
			cloudEnv := "mock-env"

			expectedResponse := `{
                "tokens": {
                    "catalog": [
                        {
                            "type": "paas",
                            "endpoints": [
                                {
                                    "url": "https://example.com/public",
                                    "interface": "public"
                                }
                            ]
                        }
                    ],
                    "token": {
                        "X-Auth-Token": "mock-auth-token"
                    },
                    "sslAuth": {
                        "method": "two-way",
                        "sslProtocol": "SSLv3"
                    }
                }
            }`

			var cloudEnvParams CloudenvParams

			patcher.ApplyFunc(GetKstoneParamsFromCloudfuze, func(clusterId string, cloudEnv string) (CloudenvParams, error) {
				return cloudEnvParams, nil
			})

			patcher.ApplyFunc(GetOpenpaletteToken, func(cloudEnvParams CloudenvParams, clusterId string, cloudEnv string) (OpenpaletteInfo, error) {
				var info OpenpaletteInfo
				if err := json.Unmarshal([]byte(expectedResponse), &info); err != nil {
					return OpenpaletteInfo{}, err
				}
				return info, nil
			})

			info, err := GetOpenpaletteInfo(clusterId, cloudEnv,"")
			Expect(err).To(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{MsbURL: "", Token: "", SSL: nil, EnvType: ""}))
		})
	})

	Context("When failing to get Cloudfuze IP and port", func() {
		It("should return an error", func() {
			clusterId := "mock-cluster-id"
			cloudEnv := "mock-env"

			info, err := GetOpenpaletteInfo(clusterId, cloudEnv,"")
			Expect(err).ToNot(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{}))
		})
	})

	Context("When GetKstoneParamsFromCloudfuze fails", func() {
		It("should return an error", func() {
			clusterId := "mock-cluster-id"
			cloudEnv := "mock-env"

			patcher.ApplyFunc(GetKstoneParamsFromCloudfuze, func(clusterId string, cloudEnv string) (CloudenvParams, error) {
				return CloudenvParams{}, errors.New("failed to get Kstone params")
			})

			info, err := GetOpenpaletteInfo(clusterId, cloudEnv,"")
			Expect(err).ToNot(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{}))
		})
	})

	Context("When GetOpenpaletteToken fails", func() {
		It("should return an error", func() {
			clusterId := "mock-cluster-id"
			cloudEnv := "mock-env"

			patcher.ApplyFunc(GetKstoneParamsFromCloudfuze, func(clusterId string, cloudEnv string) (CloudenvParams, error) {
				return CloudenvParams{}, nil
			})

			patcher.ApplyFunc(GetOpenpaletteToken, func(cloudEnvParams CloudenvParams, clusterId string, cloudEnv string) (OpenpaletteInfo, error) {
				return OpenpaletteInfo{}, errors.New("failed to get openpalette token")
			})

			info, err := GetOpenpaletteInfo(clusterId, cloudEnv,"")
			Expect(err).ToNot(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{}))
		})
	})
})

/* Ended by AICoder, pid:pbefcle285173bf14d6d0b0a31dcfe2a3be2d6b5 */

/* Started by AICoder, pid:5719bj94fav49a3147d2097df15575488f295434 */
var _ = Describe("GetOpenpaletteToken", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When getting the token succeeds", func() {
		It("should return OpenpaletteInfo without error", func() {
			clusterId := "mock-cluster-id"
			cloudEnv := "mock-env-id"
			expectedResponse := `{
                "tokens": {
                    "catalog": [
                        {
                            "type": "paas",
                            "endpoints": [
                                {
                                    "url": "https://example.com/public",
                                    "interface": "public"
                                }
                            ]
                        }
                    ],
                    "token": {
                        "X-Auth-Token": "mock-auth-token"
                    },
                    "sslAuth": {
                        "method": "two-way",
                        "sslProtocol": "SSLv3"
                    }
                }
            }`

			var cloudEnvParams CloudenvParams

			patcher.ApplyFunc(GetCloudenvsTokensUrl, func(envId string) string {
				return "http://mock-url/api/v1.0/cloudfuze/cloudenvs/" + envId + "/tokens"
			})

			patcher.ApplyFunc(PostTokensReqBody, func() (map[string]interface{}, error) {
				return map[string]interface{}{
					"Interfaces": []string{"all"},
					"CataLog":    []string{"openpalette"},
					"Refresh":    false,
				}, nil
			})

			patcher.ApplyFunc(provider.Post, func(urlSuffix string, headers map[string]string, body []byte, serviceName, serviceVersion string) ([]byte, int, error) {
				return []byte(expectedResponse), 200, nil
			})

			info, err := GetOpenpaletteToken(cloudEnvParams, clusterId, cloudEnv)
			Expect(err).To(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{
				Token:   "mock-auth-token",
				MsbURL:  "https://example.com/public",
				SSL:     &restful.SSLAuth{Method: "two-way", SSLProtocol: "SSLv3"},
				EnvType: "tcf-k8s",
			}))
		})
	})

	Context("When PostTokensReqBody fails", func() {
		It("should return an error", func() {
			clusterId := "mock-cluster-id"
			cloudEnv := "mock-env-id"

			var cloudEnvParams CloudenvParams

			patcher.ApplyFunc(GetCloudenvsTokensUrl, func(envId string) string {
				return "http://mock-url/api/v1.0/cloudfuze/cloudenvs/" + envId + "/tokens"
			})

			patcher.ApplyFunc(PostTokensReqBody, func() (map[string]interface{}, error) {
				return nil, errors.New("failed to create request body")
			})

			info, err := GetOpenpaletteToken(cloudEnvParams, clusterId, cloudEnv)
			Expect(err).ToNot(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{}))
		})
	})

	Context("When PostToGetToken fails", func() {
		It("should return an error", func() {
			clusterId := "mock-cluster-id"
			cloudEnv := "mock-env-id"

			var cloudEnvParams CloudenvParams

			patcher.ApplyFunc(GetCloudenvsTokensUrl, func(envId string) string {
				return "http://mock-url/api/v1.0/cloudfuze/cloudenvs/" + envId + "/tokens"
			})

			patcher.ApplyFunc(PostTokensReqBody, func() (map[string]interface{}, error) {
				return map[string]interface{}{
					"Interfaces": []string{"all"},
					"CataLog":    []string{"openpalette"},
					"Refresh":    false,
				}, nil
			})

			patcher.ApplyFunc(PostToGetToken, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, http.StatusInternalServerError, errors.New("failed to get token")
			})

			info, err := GetOpenpaletteToken(cloudEnvParams, clusterId, cloudEnv)
			Expect(err).ToNot(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{}))
		})
	})

	Context("When ParseOpenpaletteAuth fails", func() {
		It("should return an error", func() {
			clusterId := "mock-cluster-id"
			cloudEnv := "mock-env-id"
			invalidResponse := `invalid json`

			var cloudEnvParams CloudenvParams

			patcher.ApplyFunc(GetCloudenvsTokensUrl, func(envId string) string {
				return "http://mock-url/api/v1.0/cloudfuze/cloudenvs/" + envId + "/tokens"
			})

			patcher.ApplyFunc(PostTokensReqBody, func() (map[string]interface{}, error) {
				return map[string]interface{}{
					"Interfaces": []string{"all"},
					"CataLog":    []string{"openpalette"},
					"Refresh":    false,
				}, nil
			})

			patcher.ApplyFunc(PostToGetToken, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(invalidResponse), nil, http.StatusOK, nil
			})

			info, err := GetOpenpaletteToken(cloudEnvParams, clusterId, cloudEnv)
			Expect(err).ToNot(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{}))
		})
	})
})

/* Ended by AICoder, pid:5719bj94fav49a3147d2097df15575488f295434 */

/* Started by AICoder, pid:dc9a1x51fcj1dfb148b70ab07167e32d443589f0 */
var _ = Describe("ParseOpenpaletteAuth", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When the response is valid", func() {
		It("should return OpenpaletteInfo without error", func() {
			clusterId := "mock-cluster-id"
			rsp := `{
	            "tokens": {
	                "catalog": [
	                    {
	                        "type": "paas",
	                        "endpoints": [
	                            {
	                                "url": "https://example.com/public",
	                                "interface": "public"
	                            }
	                        ]
	                    }
	                ],
	                "token": {
	                    "X-Auth-Token": "mock-auth-token"
	                },
	                "sslAuth": {
	                    "method": "two-way",
	                    "sslProtocol": "SSLv3"
	                }
	            }
	        }`

			var cloudEnvParams CloudenvParams

			info, err := ParseOpenpaletteAuth(cloudEnvParams, []byte(rsp), clusterId)
			Expect(err).To(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{
				Token:   "mock-auth-token",
				MsbURL:  "https://example.com/public",
				SSL:     &restful.SSLAuth{Method: "two-way", SSLProtocol: "SSLv3"},
				EnvType: "tcf-k8s",
			}))
		})
	})

	Context("When the response fails to unmarshal", func() {
		It("should return an error", func() {
			clusterId := "mock-cluster-id"
			rsp := `invalid json`

			var cloudEnvParams CloudenvParams

			info, err := ParseOpenpaletteAuth(cloudEnvParams, []byte(rsp), clusterId)
			Expect(err).ToNot(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{}))
		})
	})

	Context("When no public URL is found in the catalog", func() {
		It("should return an error", func() {
			clusterId := "mock-cluster-id"
			rsp := `{
                "tokens": {
                    "catalog": [
                        {
                            "type": "paas",
                            "endpoints": [
                                {
                                    "url": "https://example.com/internal",
                                    "interface": "internal"
                                }
                            ]
                        }
                    ],
                    "token": {
                        "X-Auth-Token": "mock-auth-token"
                    }
                }
            }`

			var cloudEnvParams CloudenvParams

			info, err := ParseOpenpaletteAuth(cloudEnvParams, []byte(rsp), clusterId)
			Expect(err).ToNot(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{}))
		})
	})

	Context("When the catalog type is not 'paas'", func() {
		It("should return an error", func() {
			clusterId := "mock-cluster-id"
			rsp := `{
                "tokens": {
                    "catalog": [
                        {
                            "type": "non-paas",
                            "endpoints": [
                                {
                                    "url": "https://example.com/public",
                                    "interface": "public"
                                }
                            ]
                        }
                    ],
                    "token": {
                        "X-Auth-Token": "mock-auth-token"
                    }
                }
            }`

			var cloudEnvParams CloudenvParams

			info, err := ParseOpenpaletteAuth(cloudEnvParams, []byte(rsp), clusterId)
			Expect(err).ToNot(BeNil())
			Expect(info).To(Equal(OpenpaletteInfo{}))
		})
	})
})

/* Ended by AICoder, pid:dc9a1x51fcj1dfb148b70ab07167e32d443589f0 */

/* Started by AICoder, pid:f80f2j96dcvfa4a14003099280599d9338271f41 */
var _ = Describe("parseKeystoneParams", func() {
	var (
		patcher     *gomonkey.Patches
		v3TokenResp V3TokenResp
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When no public URL is found", func() {
		BeforeEach(func() {
			v3TokenResp = V3TokenResp{
				Tokens: KeysStoneToken{
					Catalog: []Catalog{
						{
							Type: "paas",
							Endpoints: []KeystoneEndpoint{
								{
									URL:       "https://example.com/internal",
									Interface: "internal",
								},
							},
						},
					},
				},
			}
		})

		It("should return an error", func() {
			url, err := parseKeystoneParams(v3TokenResp)

			Expect(err).To(HaveOccurred())
			Expect(url).To(Equal(""))
			Expect(err.Error()).To(Equal("not find portal public url"))
		})
	})

	Context("When catalog type is not paas", func() {
		BeforeEach(func() {
			v3TokenResp = V3TokenResp{
				Tokens: KeysStoneToken{
					Catalog: []Catalog{
						{
							Type: "non-paas",
							Endpoints: []KeystoneEndpoint{
								{
									URL:       "https://example.com/public",
									Interface: "public",
								},
							},
						},
					},
				},
			}
		})

		It("should return an error", func() {
			url, err := parseKeystoneParams(v3TokenResp)

			Expect(err).To(HaveOccurred())
			Expect(url).To(Equal(""))
			Expect(err.Error()).To(Equal("not find portal public url"))
		})
	})
})

/* Ended by AICoder, pid:f80f2j96dcvfa4a14003099280599d9338271f41 */

var _ = Describe("GetOpenpaletteInfoFromPvrm", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When getting Openpalette info from PVRM succeeds", func() {
		It("should return OpenpaletteInfo and cloudEnv without error", func() {
			clusterId := "mock-cluster-id"
			expectedCloudEnv := "mock-cloud-env"
			expectedClusterName := "mock-cluster"
			expectedInfo := OpenpaletteInfo{
				MsbURL:      "https://example.com",
				EnvType:     "tcf-k8s",
				ClusterName: expectedClusterName,
			}

			patcher.ApplyFunc(GetCloudenvsFromPvrm, func(clusterId string) (string, string, error) {
				return expectedCloudEnv, expectedClusterName, nil
			})

			patcher.ApplyFunc(GetInfoFromPvrm, func(cloudEnv string) (OpenpaletteInfo, error) {
				return expectedInfo, nil
			})

			info, cloudEnv, err := GetOpenpaletteInfoFromPvrm(clusterId)
			Expect(err).To(BeNil())
			Expect(cloudEnv).To(Equal(expectedCloudEnv))
			Expect(info).To(Equal(expectedInfo))
		})
	})

	Context("When GetCloudenvsFromPvrm fails", func() {
		It("should return an error", func() {
			clusterId := "mock-cluster-id"

			patcher.ApplyFunc(GetCloudenvsFromPvrm, func(clusterId string) (string, string, error) {
				return "", "", errors.New("failed to get cloud envs from PVRM")
			})

			info, cloudEnv, err := GetOpenpaletteInfoFromPvrm(clusterId)
			Expect(err).ToNot(BeNil())
			Expect(err.Error()).To(Equal("failed to get cloud envs from PVRM"))
			Expect(cloudEnv).To(Equal(""))
			Expect(info).To(Equal(OpenpaletteInfo{}))
		})
	})

	Context("When GetInfoFromPvrm fails", func() {
		It("should return an error", func() {
			clusterId := "mock-cluster-id"
			expectedCloudEnv := "mock-cloud-env"
			expectedClusterName := "mock-cluster"

			patcher.ApplyFunc(GetCloudenvsFromPvrm, func(clusterId string) (string, string, error) {
				return expectedCloudEnv, expectedClusterName, nil
			})

			patcher.ApplyFunc(GetInfoFromPvrm, func(cloudEnv string) (OpenpaletteInfo, error) {
				return OpenpaletteInfo{}, errors.New("failed to get info from PVRM")
			})

			info, cloudEnv, err := GetOpenpaletteInfoFromPvrm(clusterId)
			Expect(err).ToNot(BeNil())
			Expect(err.Error()).To(Equal("failed to get info from PVRM"))
			Expect(cloudEnv).To(Equal(""))
			Expect(info).To(Equal(OpenpaletteInfo{}))
		})
	})
})
