package authorization

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"testing"

	"zte.com.cn/cms/crmX/commontools-base/restful"
	"zte.com.cn/cms/crmX/commontools-msb/provider"

	// "github.com/agiledragon/gomonkey"
	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/smartystreets/goconvey/convey"
)

/* Started by AICoder, pid:n051613d81rf39e146760ba4104e03674b564751 */
var _ = Describe("TestGetClusterUuidFromPvrmByMap", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetMethod returns an error", func() {
		It("should return the error", func() {
			patcher.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, 500, errors.New("failed to get cloud envs")
			})
			_, err := GetClusterUuidFromPvrmByMap()
			Expect(err).To(MatchError("failed to get cloud envs"))
		})
	})

	Context("When unmarshal json returns an error", func() {
		It("should return the error", func() {
			patcher.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				// Simulate a valid response but with invalid JSON
				return []byte("invalid json"), 200, nil
			})

			_, err := GetClusterUuidFromPvrmByMap()
			Expect(err).To(MatchError(ContainSubstring("invalid character")))
		})
	})

	Context("When all functions succeed", func() {
		It("should return a list of UUIDs and no error", func() {
			patcher.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return []byte(`{"clusters":[{"containerUuid":"test-cluster-id", "uuid": "123"}]}`), 200, nil
			})
			uuids, err := GetClusterUuidFromPvrmByMap()
			Expect(err).To(BeNil())
			Expect(uuids).To(HaveLen(1))
			Expect(uuids[0]).To(HaveKeyWithValue("uuid", "123"))
		})
	})
})

/* Ended by AICoder, pid:n051613d81rf39e146760ba4104e03674b564751 */

/* Started by AICoder, pid:tda363f562v0698142230a166005815e8f7660f2 */
func TestPostVcjobFaultInfo(t *testing.T) {
	convey.Convey("Given a request to post Vcjob fault info", t, func() {

		convey.Convey("Case 1: Valid request should succeed", func() {
			patcher := gomonkey.ApplyFunc(provider.Post, func(urlSuffix string, headers map[string]string,
				body []byte, serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, http.StatusOK, nil
			})
			defer patcher.Reset()

			err := PostVcjobFaultInfo([]byte("{}"), "vcjob1")
			convey.So(err, convey.ShouldBeNil)
		})

		convey.Convey("Case 2: PostMethod failure should return an error", func() {
			patcher := gomonkey.ApplyFunc(provider.Post, func(urlSuffix string, headers map[string]string,
				body []byte, serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, 500, errors.New("failed to post fault info")
			})
			defer patcher.Reset()

			err := PostVcjobFaultInfo([]byte("{}"), "vcjob1")
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, "failed to post fault info")
		})

		convey.Convey("Case 3: Non-200 status code should return an error", func() {
			patcher := gomonkey.ApplyFunc(provider.Post, func(urlSuffix string, headers map[string]string,
				body []byte, serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, http.StatusInternalServerError, nil
			})
			defer patcher.Reset()

			err := PostVcjobFaultInfo([]byte("{}"), "vcjob1")
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, fmt.Sprintf("post vcjob %v faultInfo failed,status: %v", "vcjob1", http.StatusInternalServerError))
		})
	})
}

/* Ended by AICoder, pid:tda363f562v0698142230a166005815e8f7660f2 */

/* Started by AICoder, pid:z3c03t3db1gb5aa14d5d09f8a0335655b02617b1 */
func TestPutVcjobFaultInfo(t *testing.T) {
	convey.Convey("Given a request to put Vcjob fault info", t, func() {

		convey.Convey("Case 1: Valid request should succeed", func() {
			patcher := gomonkey.ApplyFunc(provider.Put, func(urlSuffix string, headers map[string]string, body []byte,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, http.StatusOK, nil
			})
			defer patcher.Reset()

			err := PutVcjobFaultInfo([]byte("{}"), "vcjob1")
			convey.So(err, convey.ShouldBeNil)
		})

		convey.Convey("Case 2: PutMethod failure should return an error", func() {
			patcher := gomonkey.ApplyFunc(provider.Put, func(urlSuffix string, headers map[string]string, body []byte,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, 500, errors.New("failed to put fault info")
			})
			defer patcher.Reset()

			err := PutVcjobFaultInfo([]byte("{}"), "vcjob1")
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, "failed to put fault info")
		})

		convey.Convey("Case 3: Non-200 status code should return an error", func() {
			patcher := gomonkey.ApplyFunc(provider.Put, func(urlSuffix string, headers map[string]string, body []byte,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, http.StatusInternalServerError, nil
			})
			defer patcher.Reset()

			err := PutVcjobFaultInfo([]byte("{}"), "vcjob1")
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, fmt.Sprintf("put vcjob %v faultInfo failed,status: %v", "vcjob1", http.StatusInternalServerError))
		})
	})
}

/* Ended by AICoder, pid:z3c03t3db1gb5aa14d5d09f8a0335655b02617b1 */

/* Started by AICoder, pid:m3198bce8et023114446086350d753554610fa6d */
func TestGetInfoFromPvrm(t *testing.T) {
	convey.Convey("Given various scenarios for GetInfoFromPvrm", t, func() {

		body := []byte(`{"containerclouds":[{"uri":"https://10.166.201.142:5005","envType":"tcf-k8s","status":"normal","uuid":"85f87b4c-e857-52a9-ad2d-bc4cf50da524","sslAuthentication":{"method":"two-way","sslProtocol":"TLSv1.2","rootCert":"ccm-cim-cacert","clientCert":"ccm-cim-clientcert","clientKey":"ccm-cim-clientcert.key"}},{"uri":"https://10.166.201.137:5005","envType":"tcf-k8s","status":"normal","uuid":"adce995e-292e-5632-a3b3-c56c45192594","name":"testCluster1","sslAuthentication":{"method":"two-way","sslProtocol":"TLSv1.2","rootCert":"ccm-cim-cacert","clientCert":"ccm-cim-clientcert","clientKey":"ccm-cim-clientcert.key"}}]}`)

		patcher := gomonkey.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
			serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
			return body, 200, nil
		})
		defer patcher.Reset()

		convey.Convey("When the environment ID matches and status is normal", func() {
			info, err := GetInfoFromPvrm("85f87b4c-e857-52a9-ad2d-bc4cf50da524")
			convey.So(err, convey.ShouldBeNil)
			convey.So(info.MsbURL, convey.ShouldEqual, "https://10.166.201.142:5005")
			convey.So(info.EnvType, convey.ShouldEqual, "tcf-k8s")
		})

		convey.Convey("When the environment ID matches but status is abnormal", func() {
			abnormalBody := []byte(`{"containerclouds":[{"uri":"https://10.166.201.142:5005","envType":"tcf-k8s","status":"abnormal","uuid":"85f87b4c-e857-52a9-ad2d-bc4cf50da524","sslAuthentication":{"method":"two-way","sslProtocol":"TLSv1.2","rootCert":"ccm-cim-cacert","clientCert":"ccm-cim-clientcert","clientKey":"ccm-cim-clientcert.key"}}]}`)
			patcher.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return abnormalBody, 200, nil
			})
			_, err := GetInfoFromPvrm("85f87b4c-e857-52a9-ad2d-bc4cf50da524")
			convey.So(err, convey.ShouldNotBeNil)
		})

		convey.Convey("When the environment ID does not match any records", func() {
			_, err := GetInfoFromPvrm("nonexistent-env-id")
			convey.So(err, convey.ShouldNotBeNil)
		})

		convey.Convey("When there is an error in the HTTP request", func() {
			errorPatcher := gomonkey.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, 0, fmt.Errorf("http error")
			})
			defer errorPatcher.Reset()
			_, err := GetInfoFromPvrm("85f87b4c-e857-52a9-ad2d-bc4cf50da524")
			convey.So(err, convey.ShouldNotBeNil)
		})

		convey.Convey("When there is an error in JSON unmarshalling", func() {
			unmarshalPatcher := gomonkey.ApplyFunc(json.Unmarshal, func(data []byte, v interface{}) error {
				return fmt.Errorf("unmarshal error")
			})
			defer unmarshalPatcher.Reset()
			_, err := GetInfoFromPvrm("85f87b4c-e857-52a9-ad2d-bc4cf50da524")
			convey.So(err, convey.ShouldNotBeNil)
		})
	})
}

/* Ended by AICoder, pid:m3198bce8et023114446086350d753554610fa6d */

func TestGetCloudenvsFromPvrm(t *testing.T) {
	convey.Convey("Given various scenarios for GetCloudenvsFromPvrm", t, func() {

		body := []byte(`{"clusters":[{"uuid":"85f87b4c-e857-52a9-ad2d-bc4cf50da524","containerUuid":"*************-529d-89fc-00607b7cde76","name":"cluster1"},{"uuid":"adce995e-292e-5632-a3b3-c56c45192594","containerUuid":"*************-529d-89fc-00607b7cde79","name":"cluster2"}]}`)

		patcher := gomonkey.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
			serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
			return body, 200, nil
		})
		defer patcher.Reset()

		convey.Convey("When the cluster ID matches a record", func() {
			containerUuid, name, err := GetCloudenvsFromPvrm("85f87b4c-e857-52a9-ad2d-bc4cf50da524")
			convey.So(err, convey.ShouldBeNil)
			convey.So(containerUuid, convey.ShouldEqual, "*************-529d-89fc-00607b7cde76")
			convey.So(name, convey.ShouldEqual, "cluster1")
		})

		convey.Convey("When the cluster ID does not match any records", func() {
			_, _, err := GetCloudenvsFromPvrm("nonexistent-cluster-id")
			convey.So(err, convey.ShouldNotBeNil)
		})

		convey.Convey("When there is an error in the HTTP request", func() {
			errorPatcher := gomonkey.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, 0, fmt.Errorf("http error")
			})
			defer errorPatcher.Reset()
			_, _, err := GetCloudenvsFromPvrm("85f87b4c-e857-52a9-ad2d-bc4cf50da524")
			convey.So(err, convey.ShouldNotBeNil)
		})

		convey.Convey("When there is an error in JSON unmarshalling", func() {
			unmarshalPatcher := gomonkey.ApplyFunc(json.Unmarshal, func(data []byte, v interface{}) error {
				return fmt.Errorf("unmarshal error")
			})
			defer unmarshalPatcher.Reset()
			_, _, err := GetCloudenvsFromPvrm("85f87b4c-e857-52a9-ad2d-bc4cf50da524")
			convey.So(err, convey.ShouldNotBeNil)
		})
	})
}

func TestGetVcjobInfoFromPvrm(t *testing.T) {
	convey.Convey("Given various scenarios for GetVcjobInfoFromPvrm", t, func() {
		mockBody := []byte(`{"vcjobs": [{"id": "123", "name": "test-job"}]}`)

		patcher := gomonkey.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
			serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
			return mockBody, 200, nil
		})
		defer patcher.Reset()

		convey.Convey("When the request is successful", func() {
			body, err := GetVcjobInfoFromPvrm()
			convey.So(err, convey.ShouldBeNil)
			convey.So(body, convey.ShouldResemble, mockBody)
		})

		convey.Convey("When there is an error in the HTTP request", func() {
			errorPatcher := gomonkey.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, http.StatusInternalServerError, fmt.Errorf("http error")
			})
			defer errorPatcher.Reset()

			body, err := GetVcjobInfoFromPvrm()
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, "http error")
			convey.So(body, convey.ShouldBeNil)
		})
	})
}

func TestGetClusterUuidFromPvrm(t *testing.T) {
	convey.Convey("Given various scenarios for GetClusterUuidFromPvrm", t, func() {
		body := []byte(`{
			"clusters": [
				{
					"uuid": "cluster-uuid-1",
					"containerUuid": "cloud-uuid-1",
					"name": "cluster1"
				},
				{
					"uuid": "cluster-uuid-2",
					"containerUuid": "cloud-uuid-2",
					"name": "cluster2"
				}
			]
		}`)

		patcher := gomonkey.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
			serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
			return body, 200, nil
		})
		defer patcher.Reset()

		convey.Convey("When the cloud UUID matches", func() {
			uuid, err := GetClusterUuidFromPvrm("cloud-uuid-1")
			convey.So(err, convey.ShouldBeNil)
			convey.So(uuid, convey.ShouldEqual, "cluster-uuid-1")
		})

		convey.Convey("When the cloud UUID does not match any records", func() {
			_, err := GetClusterUuidFromPvrm("nonexistent-cloud-uuid")
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, "no matching UUID found")
		})

		convey.Convey("When there is an error in the HTTP request", func() {
			errorPatcher := gomonkey.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return nil, http.StatusInternalServerError, fmt.Errorf("http error")
			})
			defer errorPatcher.Reset()

			_, err := GetClusterUuidFromPvrm("cloud-uuid-1")
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, "http error")
		})

		convey.Convey("When there is an error in JSON unmarshalling", func() {
			unmarshalPatcher := gomonkey.ApplyFunc(provider.Get, func(urlSuffix string, headers map[string]string,
				serviceName, serviceVersion string, opts ...restful.Option) ([]byte, int, error) {
				return []byte("invalid json"), 200, nil
			})
			defer unmarshalPatcher.Reset()

			_, err := GetClusterUuidFromPvrm("cloud-uuid-1")
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "invalid")
		})
	})
}
