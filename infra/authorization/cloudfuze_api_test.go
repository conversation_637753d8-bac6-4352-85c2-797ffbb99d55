package authorization

import (
	"cwsm/infra/constant"
	"cwsm/infra/cwsmutils"
	"errors"

	. "github.com/onsi/ginkgo"
	"github.com/onsi/gomega"

	gomonkey "github.com/agiledragon/gomonkey/v2"
)

/* Started by AICoder, pid:5a5f7f8218309b114ed80b664099ba4397257f9c */
var _ = Describe("PostTokensReqBody", func() {
	var (
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When Struct2Map succeeds", func() {
		It("should return the correct map representation of PostTokens", func() {
			expectedResult := map[string]interface{}{
				"interfaces": []string{"all"},
				"catalog":    []string{"openpalette"},
				"refresh":    false,
			}

			patcher.ApplyFunc(cwsmutils.Struct2Map, func(input interface{}) (map[string]interface{}, error) {
				return expectedResult, nil
			})

			result, err := PostTokensReqBody()

			gomega.Expect(err).To(gomega.BeNil())
			gomega.Expect(result).To(gomega.Equal(expectedResult))
		})
	})

	Context("When Struct2Map fails", func() {
		It("should return an error", func() {
			patcher.ApplyFunc(cwsmutils.Struct2Map, func(input interface{}) (map[string]interface{}, error) {
				return nil, errors.New("mock error")
			})

			result, err := PostTokensReqBody()

			gomega.Expect(err).To(gomega.HaveOccurred())
			gomega.Expect(result).To(gomega.BeNil())
		})
	})
})

/* Ended by AICoder, pid:5a5f7f8218309b114ed80b664099ba4397257f9c */

/* Started by AICoder, pid:6814898bbdqdebf14d280885603a1a31c4695222 */
var _ = Describe("GetCloudenvsTokensUrl", func() {
	var (
		patcher *gomonkey.Patches
		envId   string
	)

	BeforeEach(func() {
		envId = "test-env-id"
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When CloudfuzeIpPort is set", func() {
		BeforeEach(func() {
			CloudfuzeIpPort = "*************:5005"
		})

		It("should return the correct URL", func() {
			expectedUrl := "http://*************:5005/api/v1.0/cloudfuze/cloudenvs/test-env-id/tokens"
			result := constant.HTTPPrefix + CloudfuzeIpPort + GetCloudenvsTokensUrl(envId)
			gomega.Expect(result).To(gomega.Equal(expectedUrl))
		})
	})

	Context("When CloudfuzeIpPort is not set", func() {
		BeforeEach(func() {
			CloudfuzeIpPort = ""
		})

		It("should return an incomplete URL", func() {
			expectedUrl := "http:///api/v1.0/cloudfuze/cloudenvs/test-env-id/tokens"
			result := constant.HTTPPrefix + GetCloudenvsTokensUrl(envId)
			gomega.Expect(result).To(gomega.Equal(expectedUrl))
		})
	})
})

/* Ended by AICoder, pid:6814898bbdqdebf14d280885603a1a31c4695222 */
