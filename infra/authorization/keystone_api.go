package authorization

import (
	"cwsm/infra/constant"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"errors"
	"net/http"

	"zte.com.cn/cms/crmX/commontools-base/restful"
	"zte.com.cn/cms/crmX/commontools-msb/provider"
)

/* Started by AICoder, pid:43c42xa39fp599b14dd3097e20d0131c3bb9ecdc */
func GetOpenpaletteInfo(clusterId string, cloudEnv string, clusterName string) (OpenpaletteInfo, error) {
	cloudEnvParams, err := GetKstoneParamsFromCloudfuze(clusterId, cloudEnv)
	if err != nil {
		logger.Errorf("get info from cloudfuze json.Unmarshal failed: %v", err)
		return OpenpaletteInfo{}, err
	}

	info, err1 := GetOpenpaletteToken(cloudEnvParams, clusterId, cloudEnv)
	if err1 != nil {
		logger.Errorf("get info from cloudfuze get openpalette auth from keystone failed: %v", err1)
		return OpenpaletteInfo{}, err1
	}
	info.ClusterName = clusterName
	return info, nil
}

/* Ended by AICoder, pid:43c42xa39fp599b14dd3097e20d0131c3bb9ecdc */

/* Started by AICoder, pid:306f0m30e3r91f914d61082300c31d2eb6f1b36d */
func GetOpenpaletteToken(cloudEnvParams CloudenvParams, clusterId string, cloudEnv string) (OpenpaletteInfo, error) {
	cloudenvTokensUrl := GetCloudenvsTokensUrl(cloudEnv)
	postTokensReqBody, err := PostTokensReqBody()
	if err != nil {
		return OpenpaletteInfo{}, err
	}

	bodyBytes, err := json.Marshal(postTokensReqBody)
	if err != nil {
		logger.Errorf("post to get token body marshal fail:", err)
		return OpenpaletteInfo{}, err
	}

	rsp, rspCode, err := provider.Post(cloudenvTokensUrl, restful.DefaultHeaders(), bodyBytes, constant.CludFuzeName, "v1.0")
	if err != nil {
		logger.Errorf("get info from cloudfuze failed to get, status code: %v, err: %v", rspCode, err.Error())
		return OpenpaletteInfo{}, err
	}

	info, err := ParseOpenpaletteAuth(cloudEnvParams, rsp, clusterId)
	if err != nil {
		logger.Errorf("get info from cloudfuze failed to get, err: %v", err.Error())
		return OpenpaletteInfo{}, err
	}

	return info, nil
}

/* Ended by AICoder, pid:306f0m30e3r91f914d61082300c31d2eb6f1b36d */

/* Started by AICoder, pid:yf830a5588u7b8d14c35098a2000ad2697c28393 */
func ParseOpenpaletteAuth(cloudEnvParams CloudenvParams, rsp []byte, clusterId string) (OpenpaletteInfo, error) {
	var v3TokenResp V3TokenResp
	if err := json.Unmarshal(rsp, &v3TokenResp); err != nil {
		logger.Errorf("failed to unmarshal response: %s", err.Error())
		return OpenpaletteInfo{}, err
	}

	msbUrl, err := parseKeystoneParams(v3TokenResp)
	if err != nil {
		logger.Errorf("get openpalette auth from keystone parse keystone param failed: %s", err.Error())
		return OpenpaletteInfo{}, err
	}

	openpaletteInfo := OpenpaletteInfo{
		Token:   v3TokenResp.Tokens.Token.XAuthToken,
		MsbURL:  msbUrl,
		SSL:     v3TokenResp.Tokens.SSL,
		EnvType: "tcf-k8s",
	}

	return openpaletteInfo, nil
}

/* Ended by AICoder, pid:yf830a5588u7b8d14c35098a2000ad2697c28393 */

func GetOpenpaletteInfoFromPvrm(clusterId string) (OpenpaletteInfo, string, error) {
	cloudEnv, clusterName, err := GetCloudenvsFromPvrm(clusterId)
	if err != nil {
		logger.Errorf("get openpalette info from pvrm json.Unmarshal failed: %v", err)
		return OpenpaletteInfo{}, "", err
	}
	info, err := GetInfoFromPvrm(cloudEnv)
	if err != nil {
		logger.Errorf("get openpalette info from pvrm json.Unmarshal failed: %v", err)
		return OpenpaletteInfo{}, "", err
	}
	info.ClusterName = clusterName
	return info, cloudEnv, nil
}

func PostToGetToken(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
	map[string][]string, int, error) {
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		logger.Errorf("post to get token body marshal fail:", err)
		return nil, nil, http.StatusInternalServerError, err
	}
	return restful.PostMethod(urlpath, reqHeaders, bodyBytes, auth)
}

/* Started by AICoder, pid:pcaa2l6adez0c8714c910bff509e2d1d28630f2b */
func parseKeystoneParams(v3TokenResp V3TokenResp) (string, error) {
	for _, catalog := range v3TokenResp.Tokens.Catalog {
		if catalog.Type != "paas" {
			continue
		}
		for _, endpoint := range catalog.Endpoints {
			if endpoint.Interface == "public" {
				return endpoint.URL, nil
			}
		}
	}
	return "", errors.New("not find portal public url")
}

/* Ended by AICoder, pid:pcaa2l6adez0c8714c910bff509e2d1d28630f2b */
