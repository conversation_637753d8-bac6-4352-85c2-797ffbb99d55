package authorization

import "zte.com.cn/cms/crmX/commontools-base/restful"

var (
	CloudfuzeIpPort = ""
	PvrmIpPort      = ""
)

type CloudenvParams struct {
	URL      string           `json:"url"`
	UserName string           `json:"userName"`
	Keyword  string           `json:"password"`
	SSL      *restful.SSLAuth `json:"sslAuthentication"`
}

type OpenpaletteInfo struct {
	MsbURL      string           `json:"msbUrl"`
	Token       string           `json:"token"`
	SSL         *restful.SSLAuth `json:"sslAuthentication"`
	EnvType     string           `json:"envType"`
	ClusterName string           `json:"clusterName"`
}

type KeystoneReq struct {
	Auth Auth `json:"auth"`
}
type Auth struct {
	IdentityInfo Identity `json:"identity"`
	ScopeInfo    Scope    `json:"scope"`
}
type Identity struct {
	Methods     []string `json:"methods"`
	KeywordInfo Keyword  `json:"password"`
}
type Keyword struct {
	UserInfo User `json:"user"`
}
type User struct {
	Name        string `json:"name"`
	DomainValue Domain `json:"domain"`
	Keyword     string `json:"password"`
}
type Domain struct {
	Id string `json:"id"`
}
type Scope struct {
	ProjectValue Project `json:"project"`
}
type Project struct {
	DomainValue Domain `json:"domain"`
	Name        string `json:"name"`
}

type CloudEnvironment struct {
	Cloudenv Cloudenv `json:"cloudenv"`
}

type Cloudenv struct {
	Endpoints []CloudfuzeEndpoint `json:"endpoints"`
}

type CloudfuzeEndpoint struct {
	URL               string          `json:"url"`
	Name              string          `json:"name"`
	Scope             string          `json:"scope"`
	Version           string          `json:"version"`
	Keys              string          `json:"password"`
	UserName          string          `json:"userName"`
	TenantName        string          `json:"tenantName"`
	KmsPassword       string          `json:"kmsPassword"`
	KmsKeyID          string          `json:"kmsKeyId"`
	SslAuthentication restful.SSLAuth `json:"sslAuthentication"`
}
type V3TokenResp struct {
	Tokens KeysStoneToken `json:"tokens"`
}

type KeysStoneToken struct {
	Catalog []Catalog        `json:"catalog"`
	Request RequestInfo      `json:"request"`
	Token   TokenInfo        `json:"token"`
	SSL     *restful.SSLAuth `json:"sslAuth"`
}

type RequestInfo struct {
	URL  string `json:"url"`
	User string `json:"user"`
}

type TokenInfo struct {
	XAuthToken string `json:"X-Auth-Token"`
}

type Catalog struct {
	Type      string             `json:"type"`
	ID        string             `json:"id"`
	Name      string             `json:"name"`
	Endpoints []KeystoneEndpoint `json:"endpoints"`
}

type KeystoneEndpoint struct {
	URL       string `json:"url"`
	ID        string `json:"id"`
	Interface string `json:"interface"`
	RegionId  string `json:"region_id"`
	Region    string `json:"region"`
}

type ContainerClouds struct {
	ContainerCloud []ContainerCloud `json:"containerclouds"`
}

type ContainerCloud struct {
	Uuid              string            `json:"uuid"`
	Uri               string            `json:"uri"`
	EnvType           string            `json:"envType"`
	SslAuthentication SslAuthentication `json:"sslAuthentication"`
	Status            string            `json:"status"`
}

type SslAuthentication struct {
	Method      string `json:"method"`
	SslProtocol string `json:"sslProtocol"`
	RootCert    string `json:"rootCert"`
	ClientCert  string `json:"clientCert"`
	ClientKey   string `json:"clientKey"`
}

type ContainerClusters struct {
	Clusters []ContainerCluster `json:"clusters"`
}

type ContainerCluster struct {
	Uuid          string `json:"uuid"`
	ContainerUuid string `json:"containerUuid"`
	Name          string `json:"name"`
}

type ContainerClustersCfg struct {
	Clusters []ContainerClusterUuid `json:"clusters"`
}
type ContainerClusterUuid struct {
	Uuid          string `json:"uuid"`
	ContainerUuid string `json:"containerUuid"`
}

type PostTokens struct {
	Interfaces []string `json:"interfaces"`
	CataLog    []string `json:"catalog"`
	Refresh    bool     `json:"refresh"`
}
