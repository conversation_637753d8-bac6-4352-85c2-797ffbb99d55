releaseName: cwsm
version: "appVersion_will_replaced"
description: "cwsm microservice"
certificate: 
  - '{"name": "director-ca",
      "certificates": [],
      "displayName": "director-ca",
      "certStorage": {
        "type": "secret",
        "conf": {
          "clusterId": "",
          "secretName": "manager-cert-director-ca"}}}'
  - '{"name": "director-ca-ex",
      "certificates": [],
      "displayName": "director-ca-ex",
      "certStorage": {
        "type": "secret",
        "conf": {
          "clusterId": "",
          "secretName": "manager-cert-director-ca-ex"}}}'
  - '{"name": "director-south-server",
      "certificates": [],
      "displayName": "director-south-server",
      "certStorage": {
        "type": "secret",
        "conf": {
          "clusterId": "",
          "secretName": "manager-cert-director-south-server"}}}'
parameters:
  values:
    redis_director_openpalette_secret_name: get_property:[$(${ES_INS_NAME}),openpalette_secret_name,${ES_TYPE}]
    zenap_elasticsearch_openpalette_secret_name: get_property:[$(${ES_INS_NAME}),openpalette_secret_name,${ES_TYPE}]
    cwsm_db_openpalette_secret_name: get_property:[$(${PG_CWSM_INS_NAME}),openpalette_secret_name,${PG_TYPE}]
    CERT_CENTER_SECRET_NAME: get_property:[$(${ES_INS_NAME}),openpalette_secret_name,${ES_TYPE}]
    OPENPALETTE_REDIS_ADDRESS: get_property:[$(${REDIS_INS_NAME}),OPENPALETTE_REDIS_ADDRESS,${REDIS_TYPE}]
    OPENPALETTE_REDIS_PORT: get_property:[$(${REDIS_INS_NAME}),OPENPALETTE_REDIS_PORT,${REDIS_TYPE}]
    OPENPALETTE_REDIS_PASSWORD: get_property:[$(${REDIS_INS_NAME}),OPENPALETTE_REDIS_PASSWORD,${REDIS_TYPE}]
    OPENPALETTE_REDIS_SENTINEL_ADDRESS: get_property:[$(${REDIS_INS_NAME}),OPENPALETTE_REDIS_SENTINEL_ADDRESS,${REDIS_TYPE}]
    OPENPALETTE_REDIS_SENTINEL_PORT: get_property:[$(${REDIS_INS_NAME}),OPENPALETTE_REDIS_SENTINEL_PORT,${REDIS_TYPE}]
    OPENPALETTE_REDIS_SENTINEL_MASTERNAME: get_property:[$(${REDIS_INS_NAME}),OPENPALETTE_REDIS_SENTINEL_MASTERNAME,${REDIS_TYPE}]
    OPENPALETTE_PG_ADDRESS: get_property:[$(${PG_CWSM_INS_NAME}),OPENPALETTE_PG_ADDRESS,${PG_TYPE}]
    OPENPALETTE_PG_PORT: get_property:[$(${PG_CWSM_INS_NAME}),OPENPALETTE_PG_PORT,${PG_TYPE}]
    OPENPALETTE_PG_DBNAME: get_property:[$(${PG_CWSM_INS_NAME}),OPENPALETTE_PG_DBNAME,${PG_TYPE}]
    OPENPALETTE_PG_USERNAME: get_property:[$(${PG_CWSM_INS_NAME}),OPENPALETTE_PG_USERNAME,${PG_TYPE}]
    OPENPALETTE_PG_PASSWORD: get_property:[$(${PG_CWSM_INS_NAME}),OPENPALETTE_PG_PASSWORD,${PG_TYPE}]
    OPENPALETTE_KAFKA_ADDRESS: get_property:[$(${KAFKA_INS_NAME}),OPENPALETTE_KAFKA_ADDRESS,${KAFKA_TYPE}]
    OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS: get_property:[$(${KAFKA_INS_NAME}),OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS,${KAFKA_TYPE}]
    OPENPALETTE_KAFKA_PORT: get_property:[$(${KAFKA_INS_NAME}),OPENPALETTE_KAFKA_PORT,${KAFKA_TYPE}]
    OPENPALETTE_KAFKA_ZOOKEEPER_PORT: get_property:[$(${KAFKA_INS_NAME}),OPENPALETTE_KAFKA_ZOOKEEPER_PORT,${KAFKA_TYPE}]
    OPENPALETTE_LOGSTASH_COS_TCP_PORT: get_property:[$(${LOGSTASH_INS_NAME}), OPENPALETTE_LOGSTASH_COS_TCP_PORT,${LOGSTASH_TYPE}]
    OPENPALETTE_LOGSTASH_OEM_TCP_PORT: get_property:[$(${LOGSTASH_INS_NAME}), OPENPALETTE_LOGSTASH_OEM_TCP_PORT,${LOGSTASH_TYPE}]
    OPENPALETTE_LOGSTASH_SHIPPER_ADDRESS: get_property:[$(${LOGSTASH_INS_NAME}), OPENPALETTE_LOGSTASH_SHIPPER_ADDRESS,${LOGSTASH_TYPE}]
    OPENPALETTE_LOGSTASH_SHIPPER_HTTP_PORT: get_property:[$(${LOGSTASH_INS_NAME}), OPENPALETTE_LOGSTASH_SHIPPER_HTTP_PORT,${LOGSTASH_TYPE}]
    OPENPALETTE_LOGSTASH_SHIPPER_PORT: get_property:[$(${LOGSTASH_INS_NAME}), OPENPALETTE_LOGSTASH_SHIPPER_PORT,${LOGSTASH_TYPE}]
    OPENPALETTE_LOGSTASH_UMF_TCP_PORT: get_property:[$(${LOGSTASH_INS_NAME}), OPENPALETTE_LOGSTASH_UMF_TCP_PORT,${LOGSTASH_TYPE}]
    OPENPALETTE_LOGSTASH_UMF_UDP_PORT: get_property:[$(${LOGSTASH_INS_NAME}), OPENPALETTE_LOGSTASH_UMF_UDP_PORT,${LOGSTASH_TYPE}]
   
    pg_cwsm_cluster:
      PG_TYPE: ${PG_TYPE}
      PG_ENTITY_NAME: ${PG_ENTITY_NAME}
      PG_CWSM_INS_NAME: ${PG_CWSM_INS_NAME}
      PG_CWSM_USERNAME: cwsm-db_director
      PG_CWSM_DBNAME: db_cwsm-db_director
   
    # network
    annotations:
      k8s.v1.cni.cncf.io/networks: ${app_network_annotation}
    # 资源定义
    cwsm_cwsm_cpu_limit: ${cwsm_cwsm_cpu_limit}
    cwsm_cwsm_mem_limit: ${cwsm_cwsm_mem_limit}
    cwsm_cwsm_cpu_request: ${cwsm_cwsm_cpu_request}
    cwsm_cwsm_mem_request: ${cwsm_cwsm_mem_request} 
    # env
    language: ${language}
    logo: ${logo}
    version_no: ${version_no}
    product_version_no: ${product_version_no}
    openstackNativeInterface: ${openstackNativeInterface}
    deploymode: ${deploymode}
    scene: ${scene}
    enable_ssl: ${enable_ssl}
    scheme: ${scheme}
    business_network_ip_stack: ${business_network_ip_stack}
    mq_host: ${mq_host}
    gateway_host: inner-router.${OPENPALETTE_NAMESPACE}
    original_interface_protocol: ${original_interface_protocol}
    cassandra_host: ${cassandra_host}
    es_host: ${es_host}
    zk_host: ${zk_host}
    etcd_host: ${etcd_host}
    north_external_vip: ${north_external_vip}
    south_external_vip: ${south_external_vip}
    node_nums_for_kafka: ${node_nums_for_kafka}
    director_scale_mode: ${director_scale_mode}
    north_external_vip_ipv6: ${north_external_vip_ipv6}
    south_external_vip_ipv6: ${south_external_vip_ipv6}
    kafka_host: ${kafka_host}
    kafka_ssl_host: ${kafka_ssl_host}
    cwsm_cwsm_replicas: ${cwsm_cwsm_replicas}
    cwsm_cwsm_replicasMax: ${cwsm_cwsm_replicasMax}
    cwsm_cwsm_replicasMin: ${cwsm_cwsm_replicasMin}
    logpath_type: ${logpath_type}
    logpath_key: ${logpath_key}
    logpath_value: ${logpath_value}
    caas_type: ${caas_type}
    # pbc
    idn:
        pbcEnabled: ${pbcEnabled}
  replaces:
    public:
      # common service
      - name: redis_ins_name
        type: default
        value: redis-director
      - name: KAFKA_INS_NAME
        type: default
        value: KafkaResourceInstance，kafka-director
      - name: logstash_ins_name
        type: default
        value: LogstashInstance,zenap_logstash
      - name: KAFKA_TYPE
        type: default
        value: csm
      - name: PG_TYPE
        type: default
        value: ts_secret
      - name: PG_ENTITY_NAME
        type: default
        value: postgresql
      - name: PG_CWSM_INS_NAME
        type: default
        value: Pgtask,cwsm-db
      - name: LOGSTASH_INS_NAME
        type: default
        value: LogstashInstance,zenap-logstash
      - name: LOGSTASH_TYPE
        type: default
        value: csm
      - name: ES_INS_NAME
        type: default
        value: EsResourceInstance,zenap-elasticsearch
      - name: ES_TYPE
        type: default
        value: csm
      - name: REDIS_INS_NAME
        type: default
        value: REDISInstance,zenap-redis
      - name: REDIS_TYPE
        type: default
        value: csm
      # resources
      - name: cwsm_cwsm_cpu_limit
        type: default
        value: 24
      - name: cwsm_cwsm_mem_limit
        type: default
        value: 3Gi
      - name: cwsm_cwsm_cpu_request
        type: default
        value: 0.01
      - name: cwsm_cwsm_mem_request
        type: default
        value: 64Mi
      # env
      - name: language
        type: default
        value: EN
      - name: logo
        type: default
        value: 0
      - name: version_no
        type: default
        value: ""
      - name: product_version_no
        type: default
        value: ""
      - name: openstackNativeInterface
        type: default
        value: true
      - name: deploymode
        type: default
        value: director
      - name: scene
        type: default
        value: 2
      - name: enable_ssl
        type: default
        value: "false"
      - name: scheme
        type: default
        value: "HTTP"
      - name: business_network_ip_stack
        type: default
        value: double
      - name: mq_host
        type: default
        value: rabbitmq-director
      - name: original_interface_protocol
        type: default
        value: https
      - name: cassandra_host
        type: default
        value: cassandra1-director,cassandra2-director,cassandra3-director
      - name: es_host
        type: default
        value: es1-director,es2-director,es3-director
      - name: zk_host
        type: default
        value: zookeeper-director
      - name: etcd_host
        type: default
        value: etcd-director
      - name: north_external_vip
        type: default
        value: ""
      - name: south_external_vip
        type: default
        value: ""
      - name: node_nums_for_kafka
        type: default
        value: 3
      - name: director_scale_mode
        type: default
        value: small
      - name: north_external_vip_ipv6
        type: default
        value: ""
      - name: south_external_vip_ipv6
        type: default
        value: ""
      - name: kafka_host
        type: default
        value: ""
      - name: kafka_ssl_host
        type: default
        value: ""
      - name: cwsm_cwsm_replicas
        type: default
        value: 1
      - name: cwsm_cwsm_replicasMax
        type: default
        value: 1
      - name: cwsm_cwsm_replicasMin
        type: default
        value: 1
      - name: logpath_type
        type: default
        value: hostPath
      - name: logpath_key
        type: default
        value: path
      - name: logpath_value
        type: default
        value: /var/zte-log
      - name: pbcEnabled
        type: default
        value: false
      - name: app_network_annotation
        type: default
        value: '[{"name":"net-api","namespace":"director", "interface":"eth1", "cni-args": {"dpdk": "false"}}]' 