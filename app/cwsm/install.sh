#!/bin/bash
#上传director-dci服务到paas软件仓库
set -e
set -x
DIRNAME=`dirname $0`
WORK_HOME=`cd $DIRNAME/; pwd`
ZARTCLI=/root/zartcli/zartcli
VERSION_NO=appVersion_will_replaced
SERVICE_NAME=cwsm
log_file=${SERVICE_NAME}_install.log
log_path=""

CHART_NAME=${SERVICE_NAME}
CHART_VERSION=Version_will_replaced

if [ -z "$TENANTS" ]; then
  TENANTS=director
fi

if [ -z $DEPLOYTYPE ];then
    DEPLOYTYPE=native
fi

function uploadChartBP() {
    log "run into function uploadChartBP"
    local chartBPPath=$WORK_HOME/blueprint/director_"$CHART_NAME"_chart
    list_bp_result=$(ls $chartBPPath)
    log "files under chartBPPath is ${list_bp_result}"
    if [ ! -d ${chartBPPath} ]
    then
        log "chart blueprint dir doesnot exist,path:${chartBPPath},please check."
	    exit 2
    fi
    log "uploadChartBP: start to delete chart $CHART_NAME $CHART_VERSION"
	$ZARTCLI -o delete -i $TENANTS -m chart -n $CHART_NAME -v $CHART_VERSION || true >/dev/null
    log "uploadChartBP: delete chart $CHART_NAME $CHART_VERSION res is $?"

    log "uploadChartBP: start to upload chart $CHART_NAME $CHART_VERSION"
	$ZARTCLI -o upload -i $TENANTS -m chart -n $CHART_NAME -v $CHART_VERSION -b yes -p $chartBPPath
    log "uploadChartBP: upload chart $CHART_NAME $CHART_VERSION res is $?"

    if $? !=0
    then
        log "uploadChartBP $CHART_NAME $CHART_VERSION failed "
    fi
    log "uploadChartBP $CHART_NAME $CHART_VERSION from ${chartBPPath} succ"
}

# ============================================================================
# log log_text
# ============================================================================
function log(){
    if [ -z "$log_path" ]; then
        log_path=/var/log/$SERVICE_NAME
    fi

    if [ ! -d $log_path ]; then
        mkdir -p $log_path
    fi

    chmod 700 $log_path

    local target=$log_path/$log_file
    if [ ! -f $target ]; then
        touch $target
    fi

    chmod 640 $target

    log_text="`date +%Y-%m-%d\ %T` [$SERVICE_NAME] $1"

    echo "$log_text" >> $target
}

function clearLog(){
    if [ -z "$log_path" ]; then
        log_path=/var/log/$SERVICE_NAME
    fi

    if [ ! -d $log_path ]; then
        mkdir -p $log_path
    fi

    local target=$log_path/$log_file
    if [ -f $target ]; then
        rm -rf $target
    fi
}

checkImageState() {
    if [ $# -ne 2 ]; then
        log usage:checkImageState IMAGE_NAME IMAGE_NO
        exit 1
    fi
    local IMAGE_NAME=$1
    local IMAGE_NO=$2
    local image_state=""
    for i in {1..200}; do
        image_state=$($ZARTCLI -o=query -i=$TENANTS -m=image -n=$IMAGE_NAME -v=$IMAGE_NO | grep '"status":' | awk '{print $2}' | sed 's/,$//g' | sed 's/^"//g' | sed 's/"$//g')
        if [ "$image_state" = "available" ] || [ "$image_state" = "unavailable" ]; then
            log "status of image $IMAGE_NAME: $image_state"
            break
        else
            log "status of image $IMAGE_NAME: $image_state"
            sleep 3
            continue
        fi
    done
    echo $image_state
}

uploadServiceBP() {
	if [ $# -ne 2 ]
	then
		echo usage:uploadServiceBP SERVICE_NAME VER_NO
		exit 1
	fi
	local SERVICE_NAME=$1
	local VER_NO=$2
	local serviceBPPath=$WORK_HOME/blueprint/director_${SERVICE_NAME}_service
	local servicePBCPath=$serviceBPPath/pbc-"$SERVICE_NAME".yaml

  if [ ! -d ${serviceBPPath} ]
  then
      echo "Service blueprint dir does not exist,path:${serviceBPPath},please check."
    exit 2
  fi

  if [ -f $servicePBCPath ]; then
    mv $servicePBCPath $servicePBCPath.bak
  fi

	$ZARTCLI -o=delete -i=$TENANTS -m=bp -t=service -n=$SERVICE_NAME -v=$VER_NO || true >/dev/null
	$ZARTCLI -o=upload -i=$TENANTS -m=bp -t=service -n=$SERVICE_NAME -v=$VER_NO -b=yes -p=$serviceBPPath

	if [ -f $servicePBCPath.bak ]; then
    mv $servicePBCPath.bak $servicePBCPath
  fi
}

uploadImage() {
    if [ $# -ne 2 ]; then
        log usage:uploadImage IMAGE_NAME IMAGE_NO
        exit 1
    fi
    local IMAGE_NAME=$1
    local IMAGE_NO=$2
    local IMAGE_STAT=""
    local imagePath=$WORK_HOME/"$IMAGE_NAME"
    if [ ! -d ${imagePath} ]; then
        log "image dir doesnot exist,path:${imagePath},please check."
        exit 2
    fi
    image_state=$($ZARTCLI -o=query -i=$TENANTS -m=image -n=$IMAGE_NAME -v=$IMAGE_NO | grep '"status":' | awk '{print $2}' | sed 's/,$//g' | sed 's/^"//g' | sed 's/"$//g')
    if [ "$image_state" = "available" ]; then
        log "status of image $IMAGE_NAME: $image_state, no need to build"
        return
    fi
    for i in {1..2}; do
        $ZARTCLI -o delete -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_NO || true >/dev/null
        $ZARTCLI -o=upload -i=$TENANTS -m=image -n=$IMAGE_NAME -v=$IMAGE_NO -b=yes -p=$imagePath
        IMAGE_STAT=$(checkImageState $IMAGE_NAME $IMAGE_NO)
        if [ "$IMAGE_STAT" = "available" ]; then
            log "status of image $IMAGE_NAME: $IMAGE_STAT, build success."
            break
        elif [ "$IMAGE_STAT" = "unavailable" ]; then
            if [ $i -eq 1 ]; then
                log "status of image $IMAGE_NAME: $IMAGE_STAT, retry..."
                sleep 5
                continue
            fi
            if [ $i -eq 2 ]; then
                log "status of image $IMAGE_NAME: $IMAGE_STAT, build failed."
                exit 1
            fi
        else
            log "status of image $IMAGE_NAME: $IMAGE_STAT, unknown error."
            exit 1
        fi
    done
}

uploadBaseImage() {
    if [ $# -ne 2 ]; then
        log usage:uploadBaseImage IMAGE_NAME IMAGE_NO
        exit 1
    fi
    local IMAGE_NAME=$1
    local IMAGE_NO=$2
    local IMAGE_STAT=""
    local imagePath=$WORK_HOME

    log "uploadBaseImage WORK_HOME: $WORK_HOME"
    log "uploadBaseImage Image Name: $IMAGE_NAME"
    log "uploadBaseImage Image Number: $IMAGE_NO"
    log "uploadBaseImage Image Path: $imagePath"

    for i in {1..2}; do
        $ZARTCLI -o=delete -i=$TENANTS -m=image -n=$IMAGE_NAME -v=$IMAGE_NO || true >/dev/null
        $ZARTCLI -o=upload -i=$TENANTS -m=image -n=$IMAGE_NAME -v=$IMAGE_NO -b=yes -p=$imagePath
        IMAGE_STAT=$(checkImageState $IMAGE_NAME $IMAGE_NO)
        if [ "$IMAGE_STAT" = "available" ]; then
            log "status of base image $IMAGE_NAME: $IMAGE_STAT, upload success."
            break
        elif [ "$IMAGE_STAT" = "unavailable" ]; then
            if [ $i -eq 1 ]; then
                log "status of base image $IMAGE_NAME: $IMAGE_STAT, retry..."
                sleep 5
                continue
            fi
            if [ $i -eq 2 ]; then
                log "status of base image $IMAGE_NAME: $IMAGE_STAT, upload failed."
                exit 1
            fi
        else
            log "status of base image $IMAGE_NAME: $IMAGE_STAT, upload unknown error."
            exit 1
        fi
    done
}


buildImage() {
    if [ $# -ne 2 ]; then
        log usage:buildImage IMAGE_NAME IMAGE_NO
        exit 1
    fi
    local IMAGE_NAME=$1
    local IMAGE_NO=$2
    local IMAGE_STAT=""
    local dockerFilePath=$WORK_HOME/"$IMAGE_NAME"

    log "buildImage WORK_HOME: $WORK_HOME"
    log "buildImage Image Name: $IMAGE_NAME"
    log "buildImage Image Number: $IMAGE_NO"
    log "buildImage Image Path: $imagePath"

    if [ ! -d ${dockerFilePath} ]; then
        log "The directory for Dockerfile does not exist,path:${dockerFilePath},please check."
        exit 2
    else
        sed -i "s/FROM director/FROM ${TENANTS}/g" $dockerFilePath/Dockerfile
    fi
    image_state=$($ZARTCLI -o=query -i=$TENANTS -m=image -n=$IMAGE_NAME -v=$IMAGE_NO | grep '"status":' | awk '{print $2}' | sed 's/,$//g' | sed 's/^"//g' | sed 's/"$//g')
    if [ "$image_state" = "available" ]; then
        log "status of image $IMAGE_NAME: $image_state, no need to build"
        return
    fi
    for i in {1..2}; do
        $ZARTCLI -o delete -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_NO || true >/dev/null
        $ZARTCLI -o build -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_NO -b yes -p $dockerFilePath
        IMAGE_STAT=$(checkImageState $IMAGE_NAME $IMAGE_NO)
        if [ "$IMAGE_STAT" = "available" ]; then
            log "status of image $IMAGE_NAME: $IMAGE_STAT, build success."
            break
        elif [ "$IMAGE_STAT" = "unavailable" ]; then
            if [ $i -eq 1 ]; then
                log "status of image $IMAGE_NAME: $IMAGE_STAT, retry..."
                sleep 5
                continue
            fi
            if [ $i -eq 2 ]; then
                log "status of image $IMAGE_NAME: $IMAGE_STAT, build failed."
                exit 1
            fi
        else
            log "status of image $IMAGE_NAME: $IMAGE_STAT, build unknown error."
            exit 1
        fi
    done
}

#Begin
clearLog

log "================================"
log "@WORK_HOME@ $WORK_HOME"
log "@VERSION_NO@ $VERSION_NO"
log "@TENANTS@ $TENANTS"
log "================================"

log "=========Begin Upload $SERVICE_NAME========="
cd $WORK_HOME
#构建组件的微服务镜像和上传微服务蓝图
for ms in $(ls)
do
    if [ -d $ms ] && [ $ms != blueprint ];then
        sed -i "s/director\/$ms:/$TENANTS\/$ms:/g" $WORK_HOME/blueprint/director_${SERVICE_NAME}_service/template_cwsm.json
        sed -i "s/director\/backup-base:/$TENANTS\/backup-base:/g" $WORK_HOME/blueprint/director_${SERVICE_NAME}_service/template_cwsm.json
        if [ -f $ms/Dockerfile ];then
            buildImage $ms $VERSION_NO
        else
            uploadImage $ms $VERSION_NO
        fi
    fi
done

#上传组件服务蓝图
if [ "$DEPLOYTYPE" == "native" ]; then
    log "start to uploadChartBP"
    uploadChartBP
    log "uploadChartBP succ"
else
    log "start to uploadServiceBP"
    uploadServiceBP $SERVICE_NAME $VERSION_NO
    log "uploadServiceBP succ"
fi

if $? !=0;then
    log "upload $SERVICE_NAME $VERSION_NO blueprint fail!"
    exit 1
fi

log "install success!"
exit 0
