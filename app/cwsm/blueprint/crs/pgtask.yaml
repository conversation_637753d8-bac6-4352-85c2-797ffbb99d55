{{- if eq .parameters.values.pg_cwsm_cluster.PG_TYPE "ts_secret" -}}
apiVersion: {{ if .parameters.values.global.tsmApiGroupPrefix }}{{ .parameters.values.global.tsmApiGroupPrefix }}.{{ end }}ebaset.zte.com.cn/v1
kind: Pgtask
metadata:
  annotations:
    "oki.zte.com.cn/weight": "-200"
  labels:
    pg-cluster: {{ .parameters.values.pg_cwsm_cluster.PG_ENTITY_NAME }}
  name: {{ .parameters.values.pg_cwsm_cluster.PG_CWSM_INS_NAME }}
spec:
  name: {{ .parameters.values.pg_cwsm_cluster.PG_CWSM_INS_NAME }}
  secretName: {{ .parameters.values.pg_cwsm_cluster.PG_CWSM_INS_NAME }}
  parameters:
    user-name: {{ .parameters.values.pg_cwsm_cluster.PG_CWSM_USERNAME }}
    db-name: {{ .parameters.values.pg_cwsm_cluster.PG_CWSM_DBNAME }}
    pg-cluster: {{ .parameters.values.pg_cwsm_cluster.PG_ENTITY_NAME }}
    Override: "true"
    IsSuper: "false"
    secretMode: kms
  tasktype: pginstcreate
{{- end }}