{{/* 

OKI TSM 租户级 domain

*/}}

{{- define "oki.tsmApiGroupPrefix" -}}

  {{- if .Values.global.tsmApiGroupPrefix -}}

    {{ .Values.global.tsmApiGroupPrefix }}.

  {{- end -}}

{{- end -}}



{{/* 

OKI MSB APIVersion

*/}}

{{- define "oki.msb.apiVersion" -}}

  {{- if .Values.global.usePlatformMsb -}}

    {{- printf "msb.zte.com.cn/v1" -}}

  {{- else -}}

    {{- if ne "" .Values.global.tsmApiGroupPrefix -}}

      {{- printf "%s.ts.msb.zte.com.cn/v1" .Values.global.tsmApiGroupPrefix -}}

    {{- else -}}

      {{- printf "ts.msb.zte.com.cn/v1" -}}

    {{- end -}}

  {{- end -}}

{{- end -}}



{{/* 

OKI IDN PBC APIVersion

*/}}

{{- define "oki.pbc.apiVersion" -}}

  {{- if ne "" .Values.global.tsmApiGroupPrefix -}}

    {{- printf "%s.idn.msb.zte.com.cn/v1" .Values.global.tsmApiGroupPrefix -}}

  {{- else -}}

    {{- printf "idn.msb.zte.com.cn/v1" -}}

  {{- end -}}

{{- end -}}



{{/* 

OKI 容器 env 规范:

  支持 value 和 valueFrom

*/}}

{{- define "oki.containerEnvs" -}}

- name: {{ .name }}

{{- if ne (quote .value) "" }}

  value: {{ .value | quote }}

{{- else }}

  {{- if or .configMapKeyRef .secretKeyRef .fieldPath}}

  valueFrom:

    {{- if .configMapKeyRef }}

    configMapKeyRef:

      name: {{ .configMapKeyRef.name }}

      key: {{ .configMapKeyRef.key }}

      optional: {{ .configMapKeyRef.optional }}

    {{- else if .secretKeyRef }}

    secretKeyRef:

      name: {{ .secretKeyRef.name }}

      key: {{ .secretKeyRef.key }}

      optional: {{ .secretKeyRef.optional }}

    {{- else }}

    fieldRef:

      fieldPath: {{ .fieldPath }}

    {{- end }}    

  {{- else }}

  value: ""

  {{- end }}

{{- end }}

{{- end }}



{{- /*

OKI 命名规范：

$base := default (printf "%s-%s" .Release.Name .Chart.Name) .Values.fullname.override: 如果.Values.fullname.override存在，则将变量$base设置为其值，否则使用printf函数将发布名称和图表名称以连字符（-）连接起来。注意，若.Chart.Name包含.Release.Name，则只保留.Release.Name

$pre := default "" .Values.fullname.prefix: 如果.Values.fullname.prefix存在，则将变量$pre设置为其值，否则将空字符串作为默认值。

$suf := default "" .Values.fullname.suffix: 如果.Values.fullname.suffix存在，则将变量$suf设置为其值，否则将空字符串作为默认值。

$name := printf "%s-%s-%v" $pre $base $suf: 将$pre、$base、$suf变量连接在一起。

$name | lower | trunc 53 | trimPrefix "-" | trimSuffix "-" : 将$name转换为小写，将其截断为最多53个字符，并去除头和末尾的连字符（-）。

请注意，.Release.Name命名请尽量简洁清楚，避免因.Release.Name过长引起资源重复

Parameters:

- .Values.fullname.override: Replaces the computed name with this given name

- .Values.fullname.prefix: Prefix

- .Values.fullname.suffix: Suffix（建议取6位随机字符）

The applied order is: "prefix + name + suffix"

Usage: 'name: "{{ include "oki.fullname" . }}-$资源类型"'

*/ -}}

{{- define "oki.fullname"}}

  {{- $base := .Chart.Name -}}

  {{- if .Values.fullname.override -}}

    {{- $base = .Values.fullname.override | trimSuffix "-" -}}

  {{- else -}}

    {{- if contains $base .Release.Name -}}

      {{- $base = .Release.Name | trimSuffix "-" -}}

    {{- else -}}

      {{- $base = printf "%s-%s" .Release.Name $base | trimSuffix "-" -}}

    {{- end -}}

  {{- end -}}

  {{- $pre := default "" .Values.fullname.prefix -}}

  {{- $suf := default "" .Values.fullname.suffix -}}

  {{- $name := printf "%s-%s-%v" $pre $base $suf -}}

  {{- $name | lower | trunc 53 | trimPrefix "-" | trimSuffix "-" -}}

{{- end -}}