{{- if and .Values.idn.pbcEnabled -}}

apiversion: idn.zte.com.cn/v1alpha1
kind: Component
metadata:
    name: '{{ .Release.Name }}'
    namespace: '{{ .Release.Namespace | quote }}'
    labels:
        componentName: '{{ .Release.Name }}'
spec:
    selector:
        matchLabels:
            componentName: '{{ .Release.Name }}'
    type: ""
    version: '{{ .Chart.AppVersion }}'
    description:
        zh-CN: ""
        en-US: ""
    oweners: []
    coreFunction:
        exposedAPIs:
            - name: mecappm-lcm-v1
              specification: http://idn.api/mecappm-lcm/v1/swagger.json
              implementation: '{{ .Release.Name }}-mecappm-lcm-v1'
              path: /api/app_lcm/v1
              developerUI: /api/app_lcm/v1/docs
            - name: mecappm-lcm-iui-v1
              specification: http://idn.api/mecappm-lcm-iui/v1/swagger.json
              implementation: '{{ .Release.Name }}-mecappm-lcm-iui-v1'
              path: /mecappm-lcm-ui
              developerUI: /mecappm-lcm-ui/docs
        dependentAPIs:
            - name: res-v1
              specification: ""
              implementation: '{{ .Release.Name }}-res-v1'
              developerUI: /api/res/v1/docs
            - name: backup-service-v1
              specification: ""
              implementation: '{{ .Release.Name }}-backup-service-v1'
              developerUI: /api/backup-service/v1/docs
            - name: backup-service-v2
              specification: ""
              implementation: '{{ .Release.Name }}-backup-service-v2'
              developerUI: /api/backup-service/v2/docs
            - name: gr-mdr-service-v2
              specification: ""
              implementation: '{{ .Release.Name }}-gr-mdr-service-v2'
              developerUI: /api/gr-mdr-service/v2/docs
    eventNotification:
        publishedEvents: []
        subscribedEvents: []
    security:
        securitySchemes:
            authtype: ""
    management: []

{{- end }}