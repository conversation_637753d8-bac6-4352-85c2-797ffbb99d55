apiVersion: apps/v1
kind: Deployment
metadata:
  name: cwsm
  namespace: {{ .Values.global.OPENPALETTE_NAMESPACE }}
  labels:
    name: cwsm
spec:
  selector:
    matchLabels:
      name: {{ .Values.name }}
  replicas: {{ .Values.cwsm_cwsm_replicas }}
  template:
    metadata:
      labels:
        pod_affinity_key: pod_affinity_value
        name: cwsm
        #advancedServiceAccount: admin
      annotations:
      {{- toYaml .Values.annotations | nindent 8 }}
    spec:
      securityContext:
        sysctls:
        fsGroup: 6166
      hostNetwork: false
      terminationGracePeriodSeconds: 30
      restartPolicy: Always
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: pod_affinity_key
                  operator: In
                  values:
                  - pod_affinity_value
              topologyKey: kubernetes.io/hostname
      {{- if .Values.labelselector }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
              {{- toYaml .Values.labelselector | nindent 16 }}
      {{- end }}
      volumes:
      - name: clock-zone
        hostPath:
          path: "/usr/share/zoneinfo"  
      - name: logpath
        {{ .Values.logpath_type }}:
          {{ .Values.logpath_key }}: {{ .Values.logpath_value }}
      - name: passwd-es
        secret:
          secretName: {{ quote .Values.zenap_elasticsearch_openpalette_secret_name }}
          defaultMode: 0440
      - name: passwd-pg
        secret:
          secretName: {{ quote .Values.cwsm_db_openpalette_secret_name }}
      - name: director-ca
        secret:
          secretName: manager-cert-director-ca
          defaultMode: 0440
      - name: director-ca-ex
        secret:
          secretName: manager-cert-director-ca-ex
          defaultMode: 0440
      - name: director-south-server
        secret:
          secretName: manager-cert-director-south-server
          defaultMode: 0440
      - name: certcenter
        secret:
          secretName: {{ .Values.CERT_CENTER_SECRET_NAME }}
          defaultMode: 0440
      tolerations:
      - key: node.kubernetes.io/not-ready
        operator: Exists
        tolerationSeconds: 0
        effect: NoExecute 
      - key: node.kubernetes.io/unreachable
        operator: Exists
        tolerationSeconds: 0
        effect: NoExecute
      initContainers:
      - name: "init-{{ .Values.name }}"
        image: "{{ .Values.repository }}/{{ .Values.global.OPENPALETTE_NAMESPACE }}/{{ .Values.name }}:{{ .Chart.AppVersion }}"
        tty: false
        securityContext:
          runAsUser: 0
        volumeMounts:
        - name: logpath
          mountPath: "/var/zte-log"
        command:
        - sh
        - "-c"
        - mkdir -p /var/zte-log/testlog/zte-cwsm/logs/all && mkdir -p /var/zte-log/testlog/zte-cwsm/logs/error
          && chown -R 6166:6166 /var/zte-log/testlog/zte-cwsm && chmod 755 /var
          && chmod 755 /var/zte-log && chmod 755 /var/zte-log/testlog && chmod 700
          /var/zte-log/testlog/zte-cwsm
        env: []
        imagePullPolicy: IfNotPresent
        resources:
          requests:
            cpu: {{ .Values.cwsm_init_cpu_requests }}
            memory: {{ .Values.cwsm_init_memory_requests }}
          limits:
            cpu: {{ .Values.cwsm_init_cpu_limits }}
            memory: {{ .Values.cwsm_init_memory_limits }}
      containers:
      - name: "cwsm"
        image: "{{ .Values.repository }}/{{ .Values.global.OPENPALETTE_NAMESPACE }}/{{ .Values.name }}:{{ .Chart.AppVersion }}"
        tty: false
        securityContext:
          privileged: false
          allowPrivilegeEscalation: false
        stdin: false
        volumeMounts:
        - name: logpath
          readOnly: false
          mountPath: "/var/zte-log"
        - name: clock-zone
          readOnly: true
          mountPath: "/usr/share/zoneinfo"
        - name: passwd-es
          readOnly: false
          mountPath: "/etc/secrets/oes/es"
        - name: passwd-pg
          readOnly: false
          mountPath: "/usr/share/director/pg"
        - name: director-ca
          readOnly: false
          mountPath: "/usr/share/ssl/director-ca"
        - name: director-ca-ex
          readOnly: false
          mountPath: "/usr/share/ssl/director-ca-ex"
        - name: director-south-server
          readOnly: false
          mountPath: "/usr/share/ssl/director-south-server" 
        - name: certcenter
          readOnly: false
          mountPath: "/etc/secrets/oes/cert"
        livenessProbe:
          tcpSocket:
            port: {{ .Values.ports.cwsm }}
          initialDelaySeconds: 120
          periodSeconds: 120
        command: []
        env:
        - name: TMOUT
          value: '600'
        - name: log
          value: '3'
        - name: language
          value: {{ quote .Values.language }}
        - name: logo
          value: {{ quote .Values.logo }}
        - name: version_no
          value: {{ quote .Values.version_no }}
        - name: product_version_no
          value: {{ quote .Values.product_version_no }}
        - name: enable_ssl
          value: {{ quote .Values.enable_ssl }}
        - name: scheme
          value: {{ quote .Values.scheme }}
        - name: openstackNativeInterface
          value: {{ quote .Values.openstackNativeInterface }}
        - name: deploymode
          value: {{ quote .Values.deploymode }}
        - name: scene
          value: {{ quote .Values.scene }}
        - name: caas_type
          value: {{ quote .Values.caas_type }}
        - name: business_network_ip_stack
          value: {{ quote .Values.business_network_ip_stack }}
        - name: mq_host
          value: {{ quote .Values.mq_host }}
        - name: gateway_host
          value: {{ quote .Values.gateway_host }}
        - name: original_interface_protocol
          value: {{ quote .Values.original_interface_protocol }}
        - name: cassandra_host
          value: {{ quote .Values.cassandra_host }}
        - name: es_host
          value: {{ quote .Values.es_host }}
        - name: zk_host
          value: {{ quote .Values.zk_host }}
        - name: etcd_host
          value: {{ quote .Values.etcd_host }}
        - name: north_external_vip
          value: {{ quote .Values.north_external_vip }}
        - name: south_external_vip
          value: {{ quote .Values.south_external_vip }}
        - name: node_nums_for_kafka
          value: {{ quote .Values.node_nums_for_kafka }}
        - name: director_scale_mode
          value: {{ quote .Values.director_scale_mode }}
        - name: north_external_vip_ipv6
          value: {{ quote .Values.north_external_vip_ipv6 }}
        - name: south_external_vip_ipv6
          value: {{ quote .Values.south_external_vip_ipv6 }}
        - name: kafka_host
          value: {{ quote .Values.kafka_host }}
        - name: kafka_ssl_host
          value: {{ quote .Values.kafka_ssl_host }}
        - name: OPENPALETTE_ELASTICSEARCH_ADDRESS
          value: {{ quote .Values.OPENPALETTE_ELASTICSEARCH_ADDRESS }}
        - name: OPENPALETTE_ELASTICSEARCH_HTTP_PORT
          value: {{ quote .Values.OPENPALETTE_ELASTICSEARCH_HTTP_PORT }}
        - name: OPENPALETTE_ELASTICSEARCH_TCP_PORT
          value: {{ quote .Values.OPENPALETTE_ELASTICSEARCH_TCP_PORT }}
        - name: OPENPALETTE_ELASTICSEARCH_CLUSTER_NAME
          value: {{ quote .Values.OPENPALETTE_ELASTICSEARCH_CLUSTER_NAME }}
        - name: OPENPALETTE_ELASTICSEARCH_SECURITY_REINFORCE
          value: {{ quote .Values.OPENPALETTE_ELASTICSEARCH_SECURITY_REINFORCE }}
        - name: OPENPALETTE_ELASTICSEARCH_USERNAME
          value: {{ quote .Values.OPENPALETTE_ELASTICSEARCH_USERNAME }}
        - name: OPENPALETTE_ELASTICSEARCH_PASSWORD
          value: {{ quote .Values.OPENPALETTE_ELASTICSEARCH_PASSWORD }}
        # extra kafka
        - name: OPENPALETTE_KAFKA_ADDRESS
          value: {{ quote .Values.OPENPALETTE_KAFKA_ADDRESS }}
        - name: OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS
          value: {{ quote .Values.OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS }}
        - name: OPENPALETTE_KAFKA_PORT
          value: {{ quote .Values.OPENPALETTE_KAFKA_PORT }}
        - name: OPENPALETTE_KAFKA_ZOOKEEPER_PORT
          value: {{ quote .Values.OPENPALETTE_KAFKA_ZOOKEEPER_PORT }}
        # extra pg
        - name: OPENPALETTE_PG_ADDRESS
          value: {{ quote .Values.OPENPALETTE_PG_ADDRESS }}
        - name: OPENPALETTE_PG_PORT
          value: {{ quote .Values.OPENPALETTE_PG_PORT }}
        - name: OPENPALETTE_PG_DBNAME
          value: {{ quote .Values.OPENPALETTE_PG_DBNAME }}
        - name: OPENPALETTE_PG_USERNAME
          value: {{ quote .Values.OPENPALETTE_PG_USERNAME }}
        - name: OPENPALETTE_PG_PASSWORD
          value: {{ quote .Values.OPENPALETTE_PG_PASSWORD }}
        # extra params
        - name: OPENPALETTE_LOGSTASH_OEM_TCP_PORT
          value: {{ quote .Values.OPENPALETTE_LOGSTASH_OEM_TCP_PORT }}
        - name: OPENPALETTE_LOGSTASH_COS_TCP_PORT
          value: {{ quote .Values.OPENPALETTE_LOGSTASH_COS_TCP_PORT }}
        - name: OPENPALETTE_LOGSTASH_UMF_TCP_PORT
          value: {{ quote .Values.OPENPALETTE_LOGSTASH_UMF_TCP_PORT }}
        - name: OPENPALETTE_LOGSTASH_UMF_UDP_PORT
          value: {{ quote .Values.OPENPALETTE_LOGSTASH_UMF_UDP_PORT }}
        - name: OPENPALETTE_LOGSTASH_SHIPPER_HTTP_PORT
          value: {{ quote .Values.OPENPALETTE_LOGSTASH_SHIPPER_HTTP_PORT }}
        - name: OPENPALETTE_LOGSTASH_SHIPPER_ADDRESS
          value: {{ quote .Values.OPENPALETTE_LOGSTASH_SHIPPER_ADDRESS }}
        - name: OPENPALETTE_LOGSTASH_SHIPPER_PORT
          value: {{ quote .Values.OPENPALETTE_LOGSTASH_SHIPPER_PORT }}
        # global properties
        - name: OPENPALETTE_MSB_IP
          value: {{ .Values.global.OPENPALETTE_MSB_IP }}
        - name: OPENPALETTE_MSB_PORT
          value: {{ .Values.global.OPENPALETTE_MSB_PORT | quote }}
        - name: OPENPALETTE_MSB_ROUTER_IP
          value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_IP }}
        - name: OPENPALETTE_MSB_ROUTER_PORT
          value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_PORT | quote }}   
        - name: OPENPALETTE_MSB_ROUTER_HTTPS_PORT
          value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_HTTPS_PORT | quote }}     
        - name: OPENPALETTE_NAMESPACE
          value: {{ .Values.global.OPENPALETTE_NAMESPACE }}
        - name: TZ
          value: {{ quote .Values.global.TZ }}
        - name: logpath
          value: "/var/zte-log/testlog/zte-cwsm/logs/all/cwsm.log"
        - name: LOGPATH
          value: "/var/zte-log/testlog/zte-cwsm/logs/all/cwsm.log"
        - name: DEFAULT_MAX_DUMP_RATIO
          value: '67'
        - name: zenap_elasticsearch_openpalette_secret_name
          value: 'cwsm'          
        imagePullPolicy: Always
        ports:
          - containerPort: {{ .Values.ports.cwsm }}
            hostPort: {{ .Values.ports.cwsm }}
            protocol: "TCP"
        resources:
          requests:
            cpu:  {{ .Values.cwsm_cwsm_cpu_request }}
            memory: {{ .Values.cwsm_cwsm_mem_request }}
          limits:
            cpu: {{ .Values.cwsm_cwsm_cpu_limit }}
            memory: {{ .Values.cwsm_cwsm_mem_limit }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%

