apiVersion: {{ template "oki.msb.apiVersion" . }}
kind: MsbRoute
spec:
  routeClassName: apigateway
  httpRules:
    - match:
        path: /api/v1.0/{{ .Values.serviceName.cwsm }}
        protocol: REST
        rewriteTarget: /api/v1.0/cwsm
      backend:
        service:
          name: serviceslice-{{ .Values.serviceName.cwsm }}-{{ .Chart.Version }}
          portName: httpPort
      advancedConfig:
        lbPolicy: round-robin
metadata:
  name: route-{{ .Values.serviceName.cwsm }}-{{ .Chart.Version }}
  namespace: {{ .Values.global.OPENPALETTE_NAMESPACE }}
