{"kind": "Template", "apiVersion": "v1", "namespace": "${NAMESPACE}", "metadata": {"name": "cwsm", "labels": {"name": "cwsm"}}, "objects": [{"kind": "DeploymentConfig", "apiVersion": "v1", "metadata": {"name": "cwsm", "namespace": "${NAMESPACE}"}, "spec": {"selector": {"name": "cwsm"}, "template": {"metadata": {"labels": {"name": "cwsm", "advancedServiceAccount": "admin", "pod_affinity_key": "pod_affinity_value_cwsm"}, "annotations": {"cnrm.knitter.io/enhance_config": "{\"containers\":[{\"name\":\"cwsm\",\"oomKillDisable\":false},{\"name\":\"init-cwsm\",\"oomKillDisable\":false}]}", "zte.com.cn/numa_request": "{\"numaAffinityPolicy\":\"none\"}"}}, "spec": {"containers": [{"name": "cwsm", "image": "/director/cwsm:appVers<PERSON>_will_replaced", "imagePullPolicy": "Always", "tty": false, "securityContext": {"privileged": false, "allowPrivilegeEscalation": false}, "stdin": false, "command": [], "env": [{"name": "TMOUT", "value": "600"}, {"name": "log", "value": "3"}, {"name": "language", "value": "${language}"}, {"name": "logo", "value": "${logo}"}, {"name": "version_no", "value": "${version_no}"}, {"name": "product_version_no", "value": "${product_version_no}"}, {"name": "scheme", "value": "${scheme}"}, {"name": "enable_ssl", "value": "${enable_ssl}"}, {"name": "openstackNativeInterface", "value": "${openstackNativeInterface}"}, {"name": "deploymode", "value": "${deploymode}"}, {"name": "scene", "value": "${scene}"}, {"name": "business_network_ip_stack", "value": "${business_network_ip_stack}"}, {"name": "mq_host", "value": "${mq_host}"}, {"name": "gateway_host", "value": "${gateway_host}"}, {"name": "original_interface_protocol", "value": "${original_interface_protocol}"}, {"name": "cassandra_host", "value": "${cassandra_host}"}, {"name": "es_host", "value": "${es_host}"}, {"name": "zk_host", "value": "${zk_host}"}, {"name": "etcd_host", "value": "${etcd_host}"}, {"name": "TZ", "value": "${TZ}"}, {"name": "north_external_vip", "value": "${north_external_vip}"}, {"name": "south_external_vip", "value": "${south_external_vip}"}, {"name": "director_scale_mode", "value": "${director_scale_mode}"}, {"name": "north_external_vip_ipv6", "value": "${north_external_vip_ipv6}"}, {"name": "south_external_vip_ipv6", "value": "${south_external_vip_ipv6}"}, {"name": "logpath", "value": "/var/zte-log/testlog/zte-cwsm/logs/all/cwsm.log"}, {"name": "LOGPATH", "value": "/var/zte-log/testlog/zte-cwsm/logs/all/cwsm.log"}, {"name": "DEFAULT_MAX_DUMP_RATIO", "value": "67"}, {"name": "ENABLE_TRACE", "value": "false"}, {"name": "node_nums_for_kafka", "value": "${node_nums_for_kafka}"}, {"name": "kafka_host", "value": "${kafka_host}"}, {"name": "kafka_ssl_host", "value": "${kafka_ssl_host}"}, {"name": "OPENPALETTE_LOGSTASH_SHIPPER_ADDRESS", "value": "get_property:[${zenap_logstash},OPENPALETTE_LOGSTASH_SHIPPER_ADDRESS]"}, {"name": "OPENPALETTE_LOGSTASH_SHIPPER_HTTP_PORT", "value": "get_property:[${zenap_logstash},OPENPALETTE_LOGSTASH_SHIPPER_HTTP_PORT]"}, {"name": "OPENPALETTE_LOGSTASH_SHIPPER_PORT", "value": "get_property:[${zenap_logstash},OPENPALETTE_LOGSTASH_SHIPPER_PORT]"}, {"name": "OPENPALETTE_LOGSTASH_OEM_TCP_PORT", "value": "get_property:[${zenap_logstash},OPENPALETTE_LOGSTASH_OEM_TCP_PORT]"}, {"name": "OPENPALETTE_LOGSTASH_UMF_TCP_PORT", "value": "get_property:[${zenap_logstash},OPENPALETTE_LOGSTASH_UMF_TCP_PORT]"}, {"name": "OPENPALETTE_LOGSTASH_UMF_UDP_PORT", "value": "get_property:[${zenap_logstash},OPENPALETTE_LOGSTASH_UMF_UDP_PORT]"}, {"name": "OPENPALETTE_LOGSTASH_COS_TCP_PORT", "value": "get_property:[${zenap_logstash},OPENPALETTE_LOGSTASH_COS_TCP_PORT]"}, {"name": "OPENPALETTE_ELASTICSEARCH_ADDRESS", "value": "get_property:[${zenap_elasticsearch},OPENPALETTE_ELASTICSEARCH_ADDRESS]"}, {"name": "OPENPALETTE_ELASTICSEARCH_HTTP_PORT", "value": "get_property:[${zenap_elasticsearch},OPENPALETTE_ELASTICSEARCH_HTTP_PORT]"}, {"name": "OPENPALETTE_ELASTICSEARCH_TCP_PORT", "value": "get_property:[${zenap_elasticsearch},OPENPALETTE_ELASTICSEARCH_TCP_PORT]"}, {"name": "OPENPALETTE_ELASTICSEARCH_CLUSTER_NAME", "value": "get_property:[${zenap_elasticsearch},OPENPALETTE_ELASTICSEARCH_CLUSTER_NAME]"}, {"name": "OPENPALETTE_ELASTICSEARCH_SECURITY_REINFORCE", "value": "get_property:[${zenap_elasticsearch},OPENPALETTE_ELASTICSEARCH_SECURITY_REINFORCE]"}, {"name": "OPENPALETTE_ELASTICSEARCH_USERNAME", "value": "get_property:[${zenap_elasticsearch},OPENPALETTE_ELASTICSEARCH_USERNAME]"}, {"name": "OPENPALETTE_ELASTICSEARCH_PASSWORD", "value": "get_property:[${zenap_elasticsearch},OPENPALETTE_ELASTICSEARCH_PASSWORD]"}, {"name": "OPENPALETTE_PG_ADDRESS", "value": "get_property:[${cwsm-db},OPENPALETTE_PG_ADDRESS]"}, {"name": "OPENPALETTE_PG_PORT", "value": "get_property:[${cwsm-db},OPENPALETTE_PG_PORT]"}, {"name": "OPENPALETTE_PG_DBNAME", "value": "get_property:[${cwsm-db},OPENPALETTE_PG_DBNAME]"}, {"name": "OPENPALETTE_PG_USERNAME", "value": "get_property:[${cwsm-db},OPENPALETTE_PG_USERNAME]"}, {"name": "OPENPALETTE_PG_PASSWORD", "value": "get_property:[${cwsm-db},OPENPALETTE_PG_PASSWORD]"}, {"name": "OPENPALETTE_KAFKA_ADDRESS", "value": "get_property:[${kafka-director},OPENPALETTE_KAFKA_ADDRESS]"}, {"name": "OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS", "value": "get_property:[${kafka-director},OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS]"}, {"name": "OPENPALETTE_KAFKA_PORT", "value": "get_property:[${kafka-director},OPENPALETTE_KAFKA_PORT]"}, {"name": "OPENPALETTE_KAFKA_ZOOKEEPER_PORT", "value": "get_property:[${kafka-director},OPENPALETTE_KAFKA_ZOOKEEPER_PORT]"}], "ports": [{"protocol": "TCP", "containerPort": 8080}], "volumeMounts": [{"name": "clock-zone", "mountPath": "/usr/share/zoneinfo", "readOnly": true}, {"name": "password-es", "mountPath": "/etc/secrets/oes/es", "readOnly": false}, {"name": "password-pg", "mountPath": "/usr/share/director/pg", "readOnly": false}, {"name": "director-ca", "mountPath": "/usr/share/ssl/director-ca", "readOnly": false}, {"name": "director-ca-ex", "mountPath": "/usr/share/ssl/director-ca-ex", "readOnly": false}, {"name": "director-south-server", "mountPath": "/usr/share/ssl/director-south-server", "readOnly": false}, {"name": "logpath", "mountPath": "/var/zte-log", "readOnly": false}], "resources": {"requests": {"cpu": "#{cwsm_cwsm_cpu_request}", "memory": "#{cwsm_cwsm_mem_request}"}, "limits": {"cpu": "#{cwsm_cwsm_cpu_limit}", "memory": "#{cwsm_cwsm_mem_limit}"}}, "livenessProbe": {"tcpSocket": {"port": 8080}, "failureThreshold": 3, "initialDelaySeconds": 120, "periodSeconds": 120, "successThreshold": 1, "timeoutSeconds": 60}}], "initContainers": [{"name": "init-cwsm", "image": "director/backup-base:V5", "imagePullPolicy": "IfNotPresent", "tty": false, "securityContext": {"privileged": false, "allowPrivilegeEscalation": false}, "stdin": false, "command": ["sh", "-c", "mkdir -p /var/zte-log/testlog/zte-cwsm/logs/all && mkdir -p /var/zte-log/testlog/zte-cwsm/logs/error && chown -R 6166:6166 /var/zte-log/testlog/zte-cwsm && chmod 755 /var && chmod 755 /var/zte-log && chmod 755 /var/zte-log/testlog && chmod 700 /var/zte-log/testlog/zte-cwsm"], "env": [], "ports": [], "volumeMounts": [{"name": "logpath", "mountPath": "/var/zte-log", "readOnly": false}], "resources": {"requests": {"cpu": "#{cwsm_init_cpu_requests}", "memory": "#{cwsm_init_memory_requests}"}, "limits": {"cpu": "#{cwsm_init_cpu_limits}", "memory": "#{cwsm_init_memory_limits}"}}}], "restartPolicy": "Always", "volumes": [{"name": "clock-zone", "hostPath": {"path": "/usr/share/zoneinfo"}}, {"name": "password-es", "secret": {"secretName": "get_property:[${zenap_elasticsearch},openpalette_secret_name]", "defaultMode": 288}}, {"name": "password-pg", "secret": {"secretName": "get_property:[${cwsm-db},openpalette_secret_name]"}}, {"name": "director-ca", "secret": {"secretName": "manager-cert-director-ca", "defaultMode": 288}}, {"name": "director-ca-ex", "secret": {"secretName": "manager-cert-director-ca-ex", "defaultMode": 288}}, {"name": "director-south-server", "secret": {"secretName": "manager-cert-director-south-server", "defaultMode": 288}}, {"name": "logpath", "${logpath_type}": {"${logpath_key}": "${logpath_value}"}}], "terminationGracePeriodSeconds": 0, "hostNetwork": false, "securityContext": {"sysctls": [], "fsGroup": 6166}, "hostIPC": false, "affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"weight": 100, "podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "pod_affinity_key", "operator": "In", "values": ["pod_affinity_value_cwsm"]}]}, "topologyKey": "kubernetes.io/hostname"}}]}}}}, "replicas": "#{cwsm_cwsm_replicas}", "strategy": {"type": "Rolling", "rollingParams": {"timeoutSeconds": "600", "maxSurge": "25%", "maxUnavailable": "25%"}}}}], "vnpm_param": {"vnpm_object": [{"name": "cwsm", "route_list": [{"serviceName": "${cwsm-doc}", "protocol": "REST", "port": "8080", "visualRange": "1", "network_plane_type": "lan", "version": "v1.0", "enable_tls": "#{enable_ssl}", "lb_policy": "round-robin", "nic_name": "eth1", "function": "std", "url": "/api/v1.0/api-doc", "enable_client_verify": false, "enable_ssl": "#{enable_ssl}", "enable_http2": false}, {"serviceName": "${cwsm}", "protocol": "REST", "port": "8080", "visualRange": "1", "network_plane_type": "lan", "version": "v1.0", "enable_tls": "#{enable_ssl}", "lb_policy": "round-robin", "nic_name": "eth1", "function": "std", "url": "/api/v1.0/cwsm", "enable_client_verify": false, "enable_ssl": "#{enable_ssl}", "enable_http2": false}, {"serviceName": "${cwsm}-director", "protocol": "REST", "port": "8080", "visualRange": "1", "network_plane_type": "lan", "version": "v1.0", "enable_tls": "#{enable_ssl}", "lb_policy": "round-robin", "nic_name": "eth1", "function": "std", "url": "/api/v1.0/cwsm", "path": "/api/v1.0/cwsm", "enable_client_verify": false, "enable_ssl": "#{enable_ssl}", "enable_http2": false}], "common_service": [{"logicName": "${zenap_logstash}"}, {"logicName": "${zenap_elasticsearch}"}, {"logicName": "${cwsm-db}"}, {"logicName": "${kafka-director}"}], "isUseServiceDiscovery": true, "cluster_info": {"cluster_type": "kubernetes", "labelselector": []}, "microservice_labels": {}, "networks": {"ports": [{"attach_to_network": "net_api", "attributes": {"nic_name": "eth0", "function": "std", "nic_type": "normal", "combinable": "true", "layer_type": "layer3"}}, {"attach_to_network": "lan", "attributes": {"nic_name": "eth1", "function": "std", "nic_type": "normal", "combinable": "true", "layer_type": "layer3"}}], "version": "v1"}}], "service_labels": {}}, "eps_param": {"auto_policy": {"cwsm": {"cwsm": [], "init-cwsm": []}}, "scale_alg": {"cwsm": {"scale_in_forbidden_window": 300, "scale_out_forbidden_window": 180, "scale_decision_period": 30, "kpi_sample_num": 2, "scale_start_delay": 300, "step_params": {"step_mode": "auto"}}}, "replicasPara_list": [{"ms_name": "cwsm", "replicasMin": "#{cwsm_cwsm_replicasMin}", "replicasMax": "#{cwsm_cwsm_replicasMax}"}], "pod_migration": {"cwsm": {"enable": false, "migration_mode": "deleted_first"}}}, "cluster_resources": {"blueprints": [{"apiVersion": "v1", "kind": "Service", "metadata": {"name": "cwsm-k8s", "namespace": "${NAMESPACE}"}, "spec": {"ports": [{"port": 8080, "protocol": "TCP", "targetPort": 8080}], "selector": {"openpalette_deploy_name": "cwsm"}}}]}, "parameters": [{"name": "language", "displayName": "language", "value": "EN", "section": "env", "type": "string", "description": ""}, {"name": "logo", "displayName": "logo", "value": "0", "section": "env", "type": "string", "description": ""}, {"name": "version_no", "displayName": "version_no", "value": "", "section": "env", "type": "string", "description": ""}, {"name": "product_version_no", "displayName": "product_version_no", "value": "", "section": "env", "type": "string", "description": ""}, {"name": "enable_ssl", "displayName": "enable_ssl", "value": "", "section": "env", "type": "string", "description": ""}, {"name": "scheme", "displayName": "scheme", "value": "", "section": "env", "type": "string", "description": ""}, {"name": "openstackNativeInterface", "displayName": "openstackNativeInterface", "value": "true", "section": "env", "type": "string", "description": ""}, {"name": "deploymode", "displayName": "deploymode", "value": "director", "section": "env", "type": "string", "description": ""}, {"name": "scene", "displayName": "scene", "value": "2", "section": "env", "type": "string", "description": ""}, {"name": "business_network_ip_stack", "displayName": "business_network_ip_stack", "value": "double", "section": "env", "type": "string", "description": ""}, {"name": "mq_host", "displayName": "mq_host", "value": "rabbitmq-director", "section": "env", "type": "string", "description": ""}, {"name": "gateway_host", "displayName": "gateway_host", "value": "inner-router-director", "section": "env", "type": "string", "description": ""}, {"name": "cassandra_host", "displayName": "cassandra_host", "value": "cassandra1-director,cassandra2-director,cassandra3-director", "section": "env", "type": "string", "description": ""}, {"name": "es_host", "displayName": "es_host", "value": "es1-director,es2-director,es3-director", "section": "env", "type": "string", "description": ""}, {"name": "zk_host", "displayName": "zk_host", "value": "zookeeper-director", "section": "env", "type": "string", "description": ""}, {"name": "etcd_host", "displayName": "etcd_host", "value": "etcd-director", "section": "env", "type": "string", "description": ""}, {"name": "original_interface_protocol", "displayName": "original_interface_protocol", "value": "https", "section": "env", "type": "string", "description": ""}, {"name": "TZ", "displayName": "TZ", "value": "Asia/Shanghai", "section": "env", "type": "string", "description": ""}, {"name": "north_external_vip", "displayName": "north_external_vip", "value": "", "section": "env", "type": "string", "description": ""}, {"name": "north_external_vip_ipv6", "displayName": "north_external_vip_ipv6", "value": "", "section": "env", "type": "string", "description": ""}, {"name": "south_external_vip", "displayName": "south_external_vip", "value": "", "section": "env", "type": "string", "description": ""}, {"name": "south_external_vip_ipv6", "displayName": "south_external_vip_ipv6", "value": "", "section": "env", "type": "string", "description": ""}, {"name": "director_scale_mode", "displayName": "director_scale_mode", "value": "small", "section": "env", "type": "string", "description": ""}, {"name": "logpath_type", "displayName": "logpath_type", "value": "hostPath", "section": "env", "type": "string", "description": ""}, {"name": "logpath_key", "displayName": "logpath_key", "value": "path", "section": "env", "type": "string", "description": ""}, {"name": "logpath_value", "displayName": "logpath_value", "value": "/var/zte-log", "section": "env", "type": "string", "description": ""}, {"name": "permit_root_start", "displayName": "permit_root_start", "value": "false", "section": "env", "type": "string", "description": ""}, {"name": "node_nums_for_kafka", "displayName": "node_nums_for_kafka", "value": "3", "section": "env", "type": "string", "description": ""}, {"name": "kafka_host", "displayName": "kafka_host", "value": "", "section": "env", "type": "string", "description": ""}, {"name": "kafka_ssl_host", "displayName": "kafka_ssl_host", "value": "", "section": "env", "type": "string", "description": ""}, {"name": "runAsNonRoot", "displayName": "runAsNonRoot", "value": "false", "section": "other", "type": "boolean", "description": ""}, {"name": "runAsGroup", "displayName": "runAsGroup", "value": "0", "section": "other", "type": "int", "description": ""}, {"name": "cwsm_cwsm_mem_request", "displayName": "cwsm_cwsm_mem_request", "value": "64Mi", "section": "other", "type": "string", "description": ""}, {"name": "cwsm_cwsm_cpu_limit", "displayName": "cwsm_cwsm_cpu_limit", "value": "24", "section": "other", "type": "string", "description": ""}, {"name": "cwsm_cwsm_mem_limit", "displayName": "cwsm_cwsm_mem_limit", "value": "3Gi", "section": "other", "type": "string", "description": ""}, {"name": "cwsm_cwsm_cpu_request", "displayName": "cwsm_cwsm_cpu_request", "value": "0.01", "section": "other", "type": "string", "description": ""}, {"name": "cwsm_init_memory_requests", "displayName": "cwsm_init_memory_requests", "value": "0.01Gi", "section": "other", "type": "string", "description": ""}, {"name": "cwsm_init_cpu_limits", "displayName": "cwsm_init_cpu_limits", "value": "1", "section": "other", "type": "string", "description": ""}, {"name": "cwsm_init_memory_limits", "displayName": "cwsm_init_memory_limits", "value": "0.2Gi", "section": "other", "type": "string", "description": ""}, {"name": "cwsm_init_cpu_requests", "displayName": "cwsm_init_cpu_requests", "value": "0.01", "section": "other", "type": "string", "description": ""}, {"name": "cwsm_cwsm_replicasMax", "displayName": "cwsm_cwsm_replicasMax", "value": "1", "section": "other", "type": "string", "description": ""}, {"name": "cwsm_cwsm_replicasMin", "displayName": "cwsm_cwsm_replicasMin", "value": "1", "section": "other", "type": "string", "description": ""}, {"name": "cwsm_cwsm_replicas", "displayName": "cwsm_cwsm_replicas", "value": "1", "section": "other", "type": "string", "description": ""}, {"name": "runAsUser", "displayName": "runAsUser", "value": "0", "section": "other", "type": "int", "description": ""}, {"name": "cwsm", "displayName": "cwsm", "value": "cwsm", "section": "route", "type": "string", "description": ""}, {"name": "cwsm-doc", "displayName": "cwsm-doc", "value": "cwsm-doc", "section": "route", "type": "string", "description": ""}, {"name": "zenap_logstash", "displayName": "zenap_logstash", "description": "zenap_logstash", "value": "", "section": "commonService", "subSection": "Logstash", "type": "string"}, {"name": "zenap_elasticsearch", "displayName": "zenap_elasticsearch", "description": "zenap_elasticsearch", "value": "", "section": "commonService", "subSection": "Elasticsearch", "type": "string"}, {"name": "cwsm-db", "displayName": "cwsm-db", "description": "cwsm-db", "value": "", "section": "commonService", "subSection": "PostgreSQL", "type": "string"}, {"name": "kafka-director", "displayName": "kafka-director", "description": "kafka-director", "value": "", "section": "commonService", "subSection": "Kafka", "type": "string"}, {"name": "NAMESPACE", "displayName": "", "description": "", "value": "director", "section": "None", "type": "string"}], "view_params": [{"name": "cwsm", "position": {"width": 180, "height": 50, "left": 350, "top": 100}, "children": [{"name": "cwsm", "position": {"width": 180, "height": 50, "left": 0, "top": 0}, "children": [{"name": "eth0", "position": {"width": 16, "height": 16, "left": 0, "top": 0}, "children": []}, {"name": "cwsm", "position": {"width": 180, "height": 50, "left": 0, "top": 0}, "children": [{"name": "cwsm", "position": {"width": 16, "height": 16, "left": 0, "top": 0}, "children": []}, {"name": "cwsm-doc", "position": {"width": 16, "height": 16, "left": 0, "top": 0}, "children": []}]}, {"name": "init-cwsm", "position": {"width": 180, "height": 50, "left": 0, "top": 0}, "children": []}, {"name": "eth1", "position": {"width": 16, "height": 16, "left": 0, "top": 0}, "children": []}]}, {"name": "cwsm-k8s", "position": {"width": 180, "height": 50, "left": 0, "top": 0}, "children": []}]}, {"name": "net_api", "position": {"width": 6, "height": 600, "left": 720, "top": 100}, "children": []}, {"name": "zenap_logstash", "position": {"width": 180, "height": 50, "left": 80, "top": 100}, "children": []}, {"name": "zenap_elasticsearch", "position": {"width": 180, "height": 50, "left": 80, "top": 180}, "children": []}, {"name": "lan", "position": {"width": 6, "height": 600, "left": 750, "top": 100}, "children": []}, {"name": "cwsm-db", "position": {"width": 180, "height": 50, "left": 80, "top": 260}, "children": []}, {"name": "kafka-director", "position": {"width": 180, "height": 50, "left": 80, "top": 450}, "children": []}]}