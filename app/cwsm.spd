{"product_name": "cwsm", "version": "appVersion_will_replaced", "description": "cwsm", "service_list": [{"service_name": "cwsm", "version": "appVersion_will_replaced", "binding_port_list": [], "common_service_list": [{"common_service_name": "zenap_logstash", "resource_name": "zenap_logstash"}, {"common_service_name": "zenap_elasticsearch", "resource_name": "zenap_elasticsearch"}, {"common_service_name": "cwsm-db", "resource_name": "cwsm-db"}, {"common_service_name": "kafka-director", "resource_name": "kafka-director"}], "env_list": [{"name": "language", "value": "EN"}, {"name": "logo", "value": "0"}, {"name": "version_no", "value": ""}, {"name": "product_version_no", "value": ""}, {"name": "enable_ssl", "value": "false"}, {"name": "scheme", "value": "HTTP"}, {"name": "openstackNativeInterface", "value": "true"}, {"name": "deploymode", "value": "director"}, {"name": "scene", "value": "2"}, {"name": "business_network_ip_stack", "value": "double"}, {"name": "kafka_host", "value": ""}, {"name": "kafka_ssl_host", "value": ""}, {"name": "mq_host", "value": "rabbitmq-director"}, {"name": "gateway_host", "value": "inner-router-director"}, {"name": "cassandra_host", "value": "cassandra1-director,cassandra2-director,cassandra3-director"}, {"name": "es_host", "value": "es1-director,es2-director,es3-director"}, {"name": "zk_host", "value": "zookeeper-director"}, {"name": "etcd_host", "value": "etcd-director"}, {"name": "original_interface_protocol", "value": "https"}, {"name": "TZ", "value": "Asia/Shanghai"}, {"name": "north_external_vip", "value": ""}, {"name": "north_external_vip_ipv6", "value": ""}, {"name": "south_external_vip", "value": ""}, {"name": "south_external_vip_ipv6", "value": ""}, {"name": "node_nums_for_kafka", "value": "3"}, {"name": "director_scale_mode", "value": "small"}, {"name": "logpath_type", "value": "hostPath"}, {"name": "logpath_key", "value": "path"}, {"name": "logpath_value", "value": "/var/zte-log"}], "other_list": [{"name": "cwsm_cwsm_cpu_request", "value": "0.01"}, {"name": "cwsm_cwsm_mem_request", "value": "64Mi"}, {"name": "cwsm_cwsm_cpu_limit", "value": "1"}, {"name": "cwsm_cwsm_mem_limit", "value": "2Gi"}, {"name": "cwsm_cwsm_replicas", "value": "1"}, {"name": "cwsm_cwsm_replicasMax", "value": "1"}, {"name": "cwsm_cwsm_replicasMin", "value": "1"}], "volume_list": [{"value": "/var/zte-log", "type": "hostPath", "name": "logpath", "key": "path"}], "labelselector": []}], "certificate": [{"certificates": [], "displayName": "director-ca", "name": "director-ca", "certStorage": {"type": "secret", "conf": {"clusterId": "", "secretName": "manager-cert-director-ca"}}}, {"certificates": [], "displayName": "director-ca-ex", "name": "director-ca-ex", "certStorage": {"type": "secret", "conf": {"clusterId": "", "secretName": "manager-cert-director-ca-ex"}}}, {"certificates": [], "displayName": "director-south-server", "name": "director-south-server", "certStorage": {"type": "secret", "conf": {"clusterId": "", "secretName": "manager-cert-director-south-server"}}}]}